"""
Script pour ajouter directement des fonds au portefeuille d'un utilisateur.
"""
import os
import sys
import django
from decimal import Decimal

# Configurer l'environnement Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from payments.models import Wallet, Transaction
from django.utils import timezone

def add_wallet_funds(user_id=2, amount=Decimal('200.00')):
    """
    Ajoute directement des fonds au portefeuille d'un utilisateur.
    
    Args:
        user_id: ID de l'utilisateur (2 = client)
        amount: Montant à ajouter
    """
    try:
        # Récupérer le portefeuille
        wallet = Wallet.objects.get(user_id=user_id)
        
        # Solde avant recharge
        old_balance = wallet.balance
        
        # Ajouter le montant au solde
        wallet.balance += amount
        wallet.save()
        
        # Créer une transaction pour le rechargement
        transaction = Transaction.objects.create(
            wallet=wallet,
            type='CREDIT',
            amount=amount,
            description="Recharge de portefeuille via script",
            balance_after=wallet.balance
        )
        
        print(f"✓ Portefeuille rechargé avec succès!")
        print(f"  Utilisateur ID: {user_id}")
        print(f"  Montant ajouté: {amount}")
        print(f"  Ancien solde: {old_balance}")
        print(f"  Nouveau solde: {wallet.balance}")
        print(f"  Transaction ID: {transaction.id}")
        
    except Wallet.DoesNotExist:
        print(f"❌ Erreur: Le portefeuille pour l'utilisateur {user_id} n'existe pas!")
    except Exception as e:
        print(f"❌ Erreur lors de la recharge du portefeuille: {str(e)}")

if __name__ == "__main__":
    # Récupérer les arguments de la ligne de commande
    user_id = 2  # Par défaut : client
    amount = Decimal('200.00')  # Par défaut : 200 euros
    
    # Utiliser les arguments s'ils sont fournis
    if len(sys.argv) > 1:
        user_id = int(sys.argv[1])
    if len(sys.argv) > 2:
        amount = Decimal(sys.argv[2])
        
    # Ajouter les fonds
    add_wallet_funds(user_id, amount)
