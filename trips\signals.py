"""
Signaux Django pour l'application trips.

Ce module contient les signaux qui se déclenchent automatiquement
lors de certains événements sur les modèles Trip.
"""

from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Trip
from payments.views_wallet import auto_credit_captain_on_trip_completion


@receiver(post_save, sender=Trip)
def handle_trip_status_change(sender, instance, created, **kwargs):
    """
    Signal qui se déclenche après la sauvegarde d'un Trip.
    
    Gère automatiquement :
    - Le crédit du capitaine après complétion d'une course
    - Les notifications de changement de statut
    """
    
    # Si la course vient d'être marquée comme terminée
    if instance.status == Trip.Status.COMPLETED and not created:
        # Créditer automatiquement le capitaine
        auto_credit_captain_on_trip_completion(instance.id)
    
    # Si la course vient d'être acceptée
    if instance.status == Trip.Status.ACCEPTED and not created:
        # Ici on pourrait ajouter d'autres actions automatiques
        # comme l'envoi d'un email de confirmation
        pass
    
    # Si la course vient d'être annulée
    if instance.status in [Trip.Status.CANCELLED, Trip.Status.CANCELLED_BY_CLIENT, Trip.Status.CANCELLED_BY_CAPTAIN] and not created:
        # Ici on pourrait gérer les remboursements automatiques
        pass
