"""
Permissions personnalisées pour l'application boatman.
"""

from rest_framework import permissions


class IsCaptain(permissions.BasePermission):
    """
    Permission personnalisée pour vérifier que l'utilisateur est un capitaine.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur est authentifié et est un capitaine"""
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'captain')
        )


class IsCaptainOwner(permissions.BasePermission):
    """
    Permission pour vérifier que l'utilisateur est propriétaire du profil capitaine.
    """
    
    def has_object_permission(self, request, view, obj):
        """Vérifier que l'utilisateur est propriétaire de l'objet"""
        if hasattr(obj, 'captain'):
            return obj.captain.user == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        return False


class CanManageTrips(permissions.BasePermission):
    """
    Permission pour gérer les courses.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur peut gérer les courses"""
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'captain')
        )
    
    def has_object_permission(self, request, view, obj):
        """Vérifier que l'utilisateur peut gérer cette course spécifique"""
        if hasattr(obj, 'captain'):
            return obj.captain.user == request.user
        return False


class CanAccessWallet(permissions.BasePermission):
    """
    Permission pour accéder au portefeuille.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur peut accéder au portefeuille"""
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'captain')
        )


class CanModifyProfile(permissions.BasePermission):
    """
    Permission pour modifier le profil.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur peut modifier son profil"""
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'captain')
        )
    
    def has_object_permission(self, request, view, obj):
        """Vérifier que l'utilisateur peut modifier ce profil spécifique"""
        if hasattr(obj, 'user'):
            return obj.user == request.user
        return False


class IsActiveCaptain(permissions.BasePermission):
    """
    Permission pour vérifier que le capitaine est actif.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur est un capitaine actif"""
        if not (request.user and request.user.is_authenticated and hasattr(request.user, 'captain')):
            return False
        
        captain = request.user.captain
        return captain.is_available and captain.availability_status != 'OFFLINE'


class CanStartTrip(permissions.BasePermission):
    """
    Permission pour démarrer une course.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur peut démarrer des courses"""
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'captain') and
            request.user.captain.availability_status == 'AVAILABLE'
        )
    
    def has_object_permission(self, request, view, obj):
        """Vérifier que l'utilisateur peut démarrer cette course spécifique"""
        if hasattr(obj, 'captain'):
            return (
                obj.captain.user == request.user and
                obj.status == 'ACCEPTED'
            )
        return False


class CanCompleteTrip(permissions.BasePermission):
    """
    Permission pour terminer une course.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur peut terminer des courses"""
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'captain')
        )
    
    def has_object_permission(self, request, view, obj):
        """Vérifier que l'utilisateur peut terminer cette course spécifique"""
        if hasattr(obj, 'captain'):
            return (
                obj.captain.user == request.user and
                obj.status == 'IN_PROGRESS'
            )
        return False


class CanWithdrawFunds(permissions.BasePermission):
    """
    Permission pour retirer des fonds.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur peut retirer des fonds"""
        if not (request.user and request.user.is_authenticated and hasattr(request.user, 'captain')):
            return False
        
        # Vérifier que le capitaine a un portefeuille avec des fonds
        wallet = getattr(request.user, 'wallet', None)
        return wallet and wallet.balance > 0


def check_captain_permissions(user, action=None):
    """
    Fonction utilitaire pour vérifier les permissions d'un capitaine.
    
    Args:
        user: Instance de l'utilisateur
        action: Action spécifique à vérifier
        
    Returns:
        dict: Résultat de la vérification avec détails
    """
    result = {
        'is_captain': False,
        'is_active': False,
        'can_take_trips': False,
        'can_withdraw': False,
        'restrictions': []
    }
    
    if not user or not user.is_authenticated:
        result['restrictions'].append('Utilisateur non authentifié')
        return result
    
    if not hasattr(user, 'captain'):
        result['restrictions'].append('Compte capitaine requis')
        return result
    
    result['is_captain'] = True
    captain = user.captain
    
    # Vérifier le statut actif
    if captain.availability_status == 'AVAILABLE' and captain.is_available:
        result['is_active'] = True
        result['can_take_trips'] = True
    else:
        result['restrictions'].append(f'Statut: {captain.availability_status}')
    
    # Vérifier la possibilité de retrait
    wallet = getattr(user, 'wallet', None)
    if wallet and wallet.balance > 0:
        result['can_withdraw'] = True
    else:
        result['restrictions'].append('Solde insuffisant pour retrait')
    
    # Vérifications spécifiques par action
    if action == 'start_trip':
        if captain.availability_status != 'AVAILABLE':
            result['restrictions'].append('Doit être disponible pour démarrer une course')
    
    elif action == 'withdraw_funds':
        from payments.models import PaymentMethod
        if not PaymentMethod.objects.filter(user=user, is_active=True).exists():
            result['restrictions'].append('Méthode de paiement requise pour retrait')
    
    return result
