# Generated by Django 4.2.8 on 2025-06-10 14:42

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("boats", "0002_alter_boat_captain"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="boat",
            name="features",
            field=models.JSONField(
                blank=True,
                default=list,
                help_text="Liste des équipements",
                null=True,
                verbose_name="caractéristiques",
            ),
        ),
        migrations.AddField(
            model_name="boat",
            name="length",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=5,
                null=True,
                verbose_name="longueur (m)",
            ),
        ),
        migrations.AddField(
            model_name="boat",
            name="manufacturer",
            field=models.CharField(
                blank=True, max_length=100, null=True, verbose_name="fabricant"
            ),
        ),
        migrations.AddField(
            model_name="boat",
            name="model",
            field=models.CharField(
                blank=True, max_length=100, null=True, verbose_name="modèle"
            ),
        ),
        migrations.AddField(
            model_name="boat",
            name="year",
            field=models.IntegerField(
                blank=True, null=True, verbose_name="année de fabrication"
            ),
        ),
    ]
