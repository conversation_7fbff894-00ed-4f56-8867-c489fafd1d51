"""
Modèles pour le système de feedback utilisateur du chatbot RAG.

Ce module définit les modèles pour stocker et analyser les feedbacks des utilisateurs
sur les réponses du chatbot, afin d'améliorer continuellement la qualité des réponses.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from .models import ChatMessage

class ChatFeedback(models.Model):
    """
    Modèle pour stocker les feedbacks des utilisateurs sur les réponses du chatbot.
    
    Chaque feedback est lié à un message spécifique du chatbot et contient
    une évaluation (positif/négatif) ainsi que des commentaires optionnels.
    """
    
    # Choix pour le type de feedback
    FEEDBACK_POSITIVE = 'positive'
    FEEDBACK_NEGATIVE = 'negative'
    FEEDBACK_CHOICES = [
        (FEEDBACK_POSITIVE, _('Positif')),
        (FEEDBACK_NEGATIVE, _('Négatif')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(
        ChatMessage, 
        on_delete=models.CASCADE, 
        related_name='feedbacks',
        verbose_name=_('Message')
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='chat_feedbacks',
        verbose_name=_('Utilisateur')
    )
    feedback_type = models.CharField(
        max_length=10, 
        choices=FEEDBACK_CHOICES,
        verbose_name=_('Type de feedback')
    )
    comments = models.TextField(
        blank=True, 
        null=True,
        verbose_name=_('Commentaires')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Date de création')
    )
    ip_address = models.GenericIPAddressField(
        blank=True, 
        null=True,
        verbose_name=_('Adresse IP')
    )
    user_agent = models.TextField(
        blank=True, 
        null=True,
        verbose_name=_('User Agent')
    )
    
    class Meta:
        verbose_name = _('Feedback de chat')
        verbose_name_plural = _('Feedbacks de chat')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['feedback_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.get_feedback_type_display()} - {self.created_at.strftime('%d/%m/%Y %H:%M')}"
    
    @property
    def is_positive(self):
        """Indique si le feedback est positif."""
        return self.feedback_type == self.FEEDBACK_POSITIVE
    
    @property
    def is_negative(self):
        """Indique si le feedback est négatif."""
        return self.feedback_type == self.FEEDBACK_NEGATIVE
