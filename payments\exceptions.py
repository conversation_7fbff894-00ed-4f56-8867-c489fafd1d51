"""
Exceptions personnalisées pour le système de paiements et portefeuilles.
"""


class PaymentError(Exception):
    """Exception de base pour les erreurs de paiement"""
    pass


class WalletSecurityError(PaymentError):
    """Exception pour les erreurs de sécurité des portefeuilles"""
    pass


class InsufficientFundsError(PaymentError):
    """Exception pour les soldes insuffisants"""
    pass


class InvalidAmountError(PaymentError):
    """Exception pour les montants invalides"""
    pass


class WalletNotFoundError(PaymentError):
    """Exception pour les portefeuilles non trouvés"""
    pass


class DuplicateTransactionError(PaymentError):
    """Exception pour les transactions dupliquées"""
    pass


class PaymentProcessingError(PaymentError):
    """Exception pour les erreurs de traitement de paiement"""
    pass


class StripePaymentError(PaymentError):
    """Exception pour les erreurs Stripe"""
    pass


class WalletLockError(PaymentError):
    """Exception pour les erreurs de verrouillage de portefeuille"""
    pass


class TransactionValidationError(PaymentError):
    """Exception pour les erreurs de validation de transaction"""
    pass
