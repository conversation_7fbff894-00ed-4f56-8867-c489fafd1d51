from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock
from rest_framework.test import APIClient
from datetime import timedelta
from .models import Payment
# Utiliser directement le modèle Trip de l'application trips
from trips.models import Trip
from accounts.models import User, Client as Passenger
# Adaptation pour les modèles qui n'existent pas dans la structure actuelle
class ImpactStatistics:
    pass
from .views_auto_refunds import AutoRefundViewSet

class AutoRefundTestCase(TestCase):
    """Tests pour les remboursements automatiques"""

    def setUp(self):
        """Configuration initiale pour les tests"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='SecurePassword123!'
        )
        self.client.force_authenticate(user=self.user)
        
        self.impact_statistics = ImpactStatistics.objects.create()
        self.passenger = Passenger.objects.create(
            user=self.user,
            impact_statistics=self.impact_statistics
        )
        
        # Créer une réservation annulée avec départ dans 2 jours (remboursement à 100%)
        self.booking_full_refund = Trip.objects.create(
            passenger=self.passenger,
            status='CANCELED',
            departure_time=timezone.now() + timedelta(days=2)
        )
        self.payment_full_refund = Payment.objects.create(
            booking=self.booking_full_refund,
            amount=100.0,
            type='TRIP',
            status='COMPLETED',
            stripe_payment_id='pi_test123'
        )
        
        # Créer une réservation annulée avec départ dans 18 heures (remboursement à 50%)
        self.booking_partial_refund = Trip.objects.create(
            passenger=self.passenger,
            status='CANCELED',
            departure_time=timezone.now() + timedelta(hours=18)
        )
        self.payment_partial_refund = Payment.objects.create(
            booking=self.booking_partial_refund,
            amount=100.0,
            type='TRIP',
            status='COMPLETED',
            stripe_payment_id='pi_test456'
        )
        
        # Créer une réservation annulée avec départ dans 6 heures (pas de remboursement)
        self.booking_no_refund = Trip.objects.create(
            passenger=self.passenger,
            status='CANCELED',
            departure_time=timezone.now() + timedelta(hours=6)
        )
        self.payment_no_refund = Payment.objects.create(
            booking=self.booking_no_refund,
            amount=100.0,
            type='TRIP',
            status='COMPLETED',
            stripe_payment_id='pi_test789'
        )
    
    def test_calculate_refund_amount_full(self):
        """Test du calcul du montant de remboursement complet"""
        view = AutoRefundViewSet()
        amount = view._calculate_refund_amount(self.booking_full_refund, self.payment_full_refund)
        self.assertEqual(amount, 100.0)
    
    def test_calculate_refund_amount_partial(self):
        """Test du calcul du montant de remboursement partiel"""
        view = AutoRefundViewSet()
        amount = view._calculate_refund_amount(self.booking_partial_refund, self.payment_partial_refund)
        self.assertEqual(amount, 50.0)
    
    def test_calculate_refund_amount_none(self):
        """Test du calcul du montant de remboursement nul"""
        view = AutoRefundViewSet()
        amount = view._calculate_refund_amount(self.booking_no_refund, self.payment_no_refund)
        self.assertEqual(amount, 0)
    
    @patch('payments.stripe_utils.create_refund')
    def test_refund_canceled_booking_full(self, mock_create_refund):
        """Test du remboursement complet d'une réservation annulée"""
        # Simuler la réponse de Stripe
        mock_create_refund.return_value = MagicMock(
            id='re_test123',
            amount=10000,
            status='succeeded'
        )
        
        # Appeler la vue
        url = reverse('auto-refunds-refund-canceled-booking')
        response = self.client.post(url, {
            'booking_id': str(self.booking_full_refund.id),
            'reason': 'requested_by_customer'
        })
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['refund_id'], 're_test123')
        
        # Vérifier que le paiement a été mis à jour
        self.payment_full_refund.refresh_from_db()
        self.assertEqual(self.payment_full_refund.status, 'REFUNDED')
        self.assertEqual(self.payment_full_refund.refund_id, 're_test123')
        self.assertEqual(self.payment_full_refund.refund_amount, 100.0)
    
    @patch('payments.stripe_utils.create_refund')
    def test_refund_canceled_booking_partial(self, mock_create_refund):
        """Test du remboursement partiel d'une réservation annulée"""
        # Simuler la réponse de Stripe
        mock_create_refund.return_value = MagicMock(
            id='re_test456',
            amount=5000,
            status='succeeded'
        )
        
        # Appeler la vue
        url = reverse('auto-refunds-refund-canceled-booking')
        response = self.client.post(url, {
            'booking_id': str(self.booking_partial_refund.id),
            'reason': 'requested_by_customer'
        })
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['refund_id'], 're_test456')
        
        # Vérifier que le paiement a été mis à jour
        self.payment_partial_refund.refresh_from_db()
        self.assertEqual(self.payment_partial_refund.status, 'PARTIALLY_REFUNDED')
        self.assertEqual(self.payment_partial_refund.refund_id, 're_test456')
        self.assertEqual(self.payment_partial_refund.refund_amount, 50.0)
    
    def test_refund_canceled_booking_no_refund(self):
        """Test du remboursement d'une réservation annulée sans remboursement dû"""
        # Appeler la vue
        url = reverse('auto-refunds-refund-canceled-booking')
        response = self.client.post(url, {
            'booking_id': str(self.booking_no_refund.id),
            'reason': 'requested_by_customer'
        })
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data['error'], "Aucun remboursement n'est dû selon la politique d'annulation")
        
        # Vérifier que le paiement n'a pas été mis à jour
        self.payment_no_refund.refresh_from_db()
        self.assertEqual(self.payment_no_refund.status, 'COMPLETED')
        self.assertIsNone(self.payment_no_refund.refund_id)
    
    @patch('payments.stripe_utils.create_refund')
    def test_process_refund_requests(self, mock_create_refund):
        """Test du traitement en lot des demandes de remboursement"""
        # Simuler la réponse de Stripe
        mock_create_refund.return_value = MagicMock(
            id='re_test123',
            amount=10000,
            status='succeeded'
        )
        
        # Appeler la vue
        url = reverse('auto-refunds-process-refund-requests')
        response = self.client.post(url)
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 200)
        self.assertIn('processed', response.data)
        self.assertIn('failed', response.data)
        self.assertIn('details', response.data)
        
        # Vérifier que les paiements ont été mis à jour
        self.payment_full_refund.refresh_from_db()
        self.assertEqual(self.payment_full_refund.status, 'REFUNDED')
        
        self.payment_partial_refund.refresh_from_db()
        self.assertEqual(self.payment_partial_refund.status, 'PARTIALLY_REFUNDED')
        
        self.payment_no_refund.refresh_from_db()
        self.assertEqual(self.payment_no_refund.status, 'COMPLETED')
