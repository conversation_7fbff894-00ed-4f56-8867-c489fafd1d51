#!/usr/bin/env python
"""
Test du calcul de distance pour les navettes
"""

import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, Client
from trips.models import ShuttleTripRequest
from django.utils import timezone
from datetime import date, timedelta

def test_distance_calculation():
    print("🧮 Test du calcul de distance navette")
    print("=" * 50)
    
    # 1. Trouver un établissement avec coordonnées
    establishment = Establishment.objects.filter(
        longitude__isnull=False, 
        latitude__isnull=False
    ).first()
    
    if not establishment:
        print("❌ Aucun établissement avec coordonnées trouvé")
        return False
    
    print(f"✅ Établissement: {establishment.name}")
    print(f"   Coordonnées: {establishment.latitude}, {establishment.longitude}")
    
    # 2. Trouver un client
    client = Client.objects.first()
    if not client:
        print("❌ Aucun client trouvé")
        return False
    
    print(f"✅ Client: {client.user.email}")
    
    # 3. Créer une navette manuellement
    departure_location = {
        "city_name": "Port de Cannes",
        "coordinates": {
            "latitude": 43.5528,
            "longitude": 7.0174
        },
        "timestamp": timezone.now().isoformat()
    }
    
    # Créer la navette sans arrival_location
    shuttle = ShuttleTripRequest.objects.create(
        client=client,
        establishment=establishment,
        departure_location=departure_location,
        arrival_location={},  # Vide car on utilise les coordonnées de l'établissement
        passenger_count=2,
        departure_date=date.today() + timedelta(days=1),
        departure_time="19:30:00",
        message="Test calcul distance"
    )
    
    print(f"✅ Navette créée avec ID: {shuttle.id}")
    
    # 4. Tester le calcul de distance
    print("\n🧮 Calcul de la distance...")
    print(f"   Départ: {departure_location['coordinates']}")
    print(f"   Arrivée (établissement): lat={establishment.latitude}, lon={establishment.longitude}")
    
    distance = shuttle.calculate_distance()
    
    if distance:
        print(f"   ✅ Distance calculée: {distance} km")
        
        # Vérifier que la distance est raisonnable (entre Cannes et l'établissement)
        if 0 < distance < 100:  # Distance raisonnable
            print("   ✅ Distance semble correcte")
            return True
        else:
            print(f"   ⚠️  Distance semble incorrecte: {distance} km")
            return False
    else:
        print("   ❌ Échec du calcul de distance")
        return False

def test_haversine_formula():
    """Test direct de la formule de Haversine"""
    print("\n🧮 Test direct de la formule de Haversine")
    print("=" * 50)
    
    from math import radians, cos, sin, asin, sqrt
    
    # Coordonnées de test
    lat1, lon1 = 43.5528, 7.0174  # Port de Cannes
    lat2, lon2 = 43.5528, 7.0167  # Hôtel Marina (très proche)
    
    print(f"Point 1: {lat1}, {lon1}")
    print(f"Point 2: {lat2}, {lon2}")
    
    # Convertir en radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Formule de Haversine
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371  # Rayon de la Terre en kilomètres
    
    distance = round(c * r, 2)
    print(f"Distance calculée: {distance} km")
    
    # Cette distance devrait être très petite (quelques dizaines de mètres)
    if distance < 1:
        print("✅ Calcul Haversine correct")
        return True
    else:
        print("❌ Calcul Haversine semble incorrect")
        return False

if __name__ == "__main__":
    print("🧪 Tests de calcul de distance")
    print("=" * 60)
    
    success1 = test_haversine_formula()
    success2 = test_distance_calculation()
    
    overall_success = success1 and success2
    print(f"\n{'🎉 Tous les tests réussis!' if overall_success else '❌ Certains tests ont échoué'}")
