"""
Vues pour le système de feedback utilisateur du chatbot RAG.

Ce module définit les vues API pour enregistrer et analyser les feedbacks des utilisateurs
sur les réponses du chatbot.
"""

import logging
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from .models import ChatMessage
from .models_feedback import ChatFeedback

# Configurer le logger
logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([AllowAny])  # Pour les tests, à remplacer par IsAuthenticated en production
def submit_feedback(request):
    """
    API pour soumettre un feedback sur une réponse du chatbot.

    Paramètres:
    - message_id: ID du message sur lequel porte le feedback
    - feedback_type: 'positive' ou 'negative'
    - comments: Commentaires optionnels (requis si feedback_type est 'negative')

    Retourne:
    - 201 Created si le feedback a été enregistré avec succès
    - 400 Bad Request si les paramètres sont invalides
    - 404 Not Found si le message n'existe pas
    """
    message_id = request.data.get('message_id')
    feedback_type = request.data.get('feedback_type')
    comments = request.data.get('comments', '')

    # Valider les paramètres
    if not message_id:
        return Response({
            'error': _('L\'ID du message est requis')
        }, status=status.HTTP_400_BAD_REQUEST)

    if not feedback_type or feedback_type not in [ChatFeedback.FEEDBACK_POSITIVE, ChatFeedback.FEEDBACK_NEGATIVE]:
        return Response({
            'error': _('Le type de feedback doit être "positive" ou "negative"')
        }, status=status.HTTP_400_BAD_REQUEST)

    # Si le feedback est négatif, les commentaires sont obligatoires
    if feedback_type == ChatFeedback.FEEDBACK_NEGATIVE and not comments:
        return Response({
            'error': _('Les commentaires sont requis pour un feedback négatif')
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Récupérer le message
        message = ChatMessage.objects.get(id=message_id)

        # Créer le feedback
        feedback = ChatFeedback.objects.create(
            message=message,
            user=request.user if request.user.is_authenticated else None,
            feedback_type=feedback_type,
            comments=comments,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )

        logger.info(f"Feedback enregistré: {feedback.id} ({feedback.feedback_type})")

        # Formater la réponse selon la documentation
        from django.utils import timezone

        return Response({
            'id': str(feedback.id),
            'message_id': str(message.id),
            'feedback_type': feedback.feedback_type,
            'comments': feedback.comments,
            'created_at': timezone.now().isoformat()
        }, status=status.HTTP_201_CREATED)

    except ChatMessage.DoesNotExist:
        return Response({
            'error': _('Le message spécifié n\'existe pas')
        }, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        logger.error(f"Erreur lors de l'enregistrement du feedback: {str(e)}")
        return Response({
            'error': _('Une erreur est survenue lors de l\'enregistrement du feedback')
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
