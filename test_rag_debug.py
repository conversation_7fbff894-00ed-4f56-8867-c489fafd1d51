#!/usr/bin/env python
"""
Script de debug pour tester le RAG service étape par étape
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from rag.rag_service import RagService
from rag.models import ChatSession
from django.contrib.auth.models import User

def test_rag_step_by_step():
    print("🔍 Test du RAG service étape par étape")
    print("=" * 50)
    
    try:
        # 1. Initialisation du service
        print("1. Initialisation du service RAG...")
        service = RagService()
        print("   ✅ Service initialisé")
        
        # 2. Test des propriétés lazy
        print("2. Test des modèles...")
        embeddings = service.embeddings
        print("   ✅ Embeddings chargés")
        
        llm = service.llm
        print("   ✅ LLM chargé")
        
        text_splitter = service.text_splitter
        print("   ✅ Text splitter chargé")
        
        # 3. Créer une session de test
        print("3. Création d'une session de test...")
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.first()
        
        if not admin_user:
            print("   ❌ Aucun utilisateur trouvé")
            return False
            
        session = ChatSession.objects.create(
            user=admin_user,
            title="Test Debug"
        )
        print(f"   ✅ Session créée: {session.id}")
        
        # 4. Test de génération de réponse simple
        print("4. Test de génération de réponse...")
        test_message = "Bonjour"
        
        print(f"   Message: {test_message}")
        print("   Génération en cours...")
        
        response = service.generate_response(session, test_message, "Client")
        print(f"   ✅ Réponse générée: {response[:100]}...")
        
        # 5. Nettoyage
        print("5. Nettoyage...")
        session.delete()
        print("   ✅ Session supprimée")
        
        print("\n🎉 Tous les tests sont passés avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_rag_step_by_step()
    sys.exit(0 if success else 1)
