#!/usr/bin/env python
"""
Obtenir un token JWT pour un établissement
"""

import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import User
from rest_framework_simplejwt.tokens import RefreshToken

def get_establishment_token():
    # Trouver un utilisateur établissement
    establishment_user = User.objects.filter(establishment__isnull=False).first()
    
    if not establishment_user:
        print("❌ Aucun utilisateur établissement trouvé")
        return None
    
    # G<PERSON>érer un token JWT
    refresh = RefreshToken.for_user(establishment_user)
    access_token = str(refresh.access_token)
    
    print(f"✅ Token généré pour: {establishment_user.email}")
    print(f"Établissement: {establishment_user.establishment.name}")
    print(f"Token: {access_token}")
    
    return access_token

if __name__ == "__main__":
    get_establishment_token()
