"""
Module de gestion des QR codes pour les tickets de courses.

Ce module contient les vues pour la vérification et la validation
des QR codes générés pour les tickets de courses.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import Trip
from .qr_service import verify_qr_code
from .serializers import TripSerializer
from accounts.permissions import IsClientOrCaptainOrEstablishment
import json


class QRCodeVerificationView(APIView):
    """
    Endpoint de vérification des QR codes de tickets.
    
    Accepte soit un trip_id direct, soit les données complètes du QR code.
    Retourne les informations détaillées du ticket et son statut de validité.
    """
    permission_classes = [IsAuthenticated, IsClientOrCaptainOrEstablishment]

    def post(self, request):
        """
        Vérifie un QR code de ticket.
        
        Body attendu:
        {
            "trip_id": 123  // OU
            "qr_data": "json_string_from_qr"  // OU
            "verification_url": "https://app.com/verify/123"
        }
        
        Réponse:
        {
            "valid": true/false,
            "message": "Message d'état",
            "ticket_info": { ... },  // Si valide
            "error_code": "CODE"     // Si invalide
        }
        """
        
        # Récupérer les données de la requête
        trip_id = request.data.get('trip_id')
        qr_data = request.data.get('qr_data')
        verification_url = request.data.get('verification_url')
        
        # Validation des paramètres d'entrée
        if not any([trip_id, qr_data, verification_url]):
            return Response({
                'valid': False,
                'message': 'Paramètres manquants',
                'error_code': 'MISSING_PARAMETERS',
                'details': 'Vous devez fournir trip_id, qr_data ou verification_url'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Cas 1: trip_id direct
            if trip_id:
                trip = get_object_or_404(Trip, id=trip_id)
                verification_result = {
                    'valid': True,
                    'trip': trip,
                    'message': 'Ticket trouvé'
                }
            
            # Cas 2: URL de vérification
            elif verification_url:
                verification_result = verify_qr_code(verification_url)
            
            # Cas 3: Données QR complètes
            else:
                verification_result = verify_qr_code(qr_data)
            
            # Traitement du résultat de vérification
            if not verification_result['valid']:
                return Response({
                    'valid': False,
                    'message': verification_result['message'],
                    'error_code': self._get_error_code(verification_result['message']),
                    'timestamp': self._get_current_timestamp()
                }, status=status.HTTP_404_NOT_FOUND if 'non trouvé' in verification_result['message'] else status.HTTP_400_BAD_REQUEST)
            
            # Ticket valide - construire la réponse complète
            trip = verification_result['trip']
            
            # Vérifications supplémentaires de sécurité
            security_check = self._perform_security_checks(trip, request.user)
            if not security_check['valid']:
                return Response({
                    'valid': False,
                    'message': security_check['message'],
                    'error_code': security_check['error_code'],
                    'timestamp': self._get_current_timestamp()
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Construire la réponse complète
            response_data = self._build_ticket_response(trip, verification_result.get('qr_data'))
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Trip.DoesNotExist:
            return Response({
                'valid': False,
                'message': 'Ticket non trouvé',
                'error_code': 'TICKET_NOT_FOUND',
                'timestamp': self._get_current_timestamp()
            }, status=status.HTTP_404_NOT_FOUND)
            
        except Exception as e:
            return Response({
                'valid': False,
                'message': 'Erreur lors de la vérification',
                'error_code': 'VERIFICATION_ERROR',
                'details': str(e),
                'timestamp': self._get_current_timestamp()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _perform_security_checks(self, trip, user):
        """Effectue des vérifications de sécurité supplémentaires"""
        
        # Vérifier que l'utilisateur a le droit de voir ce ticket
        is_client = hasattr(user, 'client') and trip.client == user.client
        is_captain = hasattr(user, 'captain') and trip.captain == user.captain
        is_establishment = hasattr(user, 'establishment') and trip.establishment == user.establishment
        is_staff = user.is_staff
        
        if not any([is_client, is_captain, is_establishment, is_staff]):
            return {
                'valid': False,
                'message': 'Accès non autorisé à ce ticket',
                'error_code': 'ACCESS_DENIED'
            }
        
        return {'valid': True}

    def _build_ticket_response(self, trip, qr_data=None):
        """Construit la réponse complète pour un ticket valide"""
        
        return {
            'valid': True,
            'message': 'Ticket valide',
            'timestamp': self._get_current_timestamp(),
            
            # Informations principales du ticket
            'ticket_info': {
                'trip_id': trip.id,
                'status': trip.status,
                'status_display': trip.get_status_display(),
                
                # Informations du trajet
                'journey': {
                    'departure': trip.start_location,
                    'arrival': trip.end_location,
                    'scheduled_start': trip.scheduled_start_time,
                    'scheduled_end': trip.scheduled_end_time,
                    'actual_start': trip.actual_start_time,
                    'actual_end': trip.actual_end_time
                },
                
                # Informations du client
                'client': {
                    'name': f"{trip.client.user.first_name} {trip.client.user.last_name}",
                    'email': trip.client.user.email,
                    'phone': trip.client.user.phone_number,
                    'profile_picture': trip.client.user.profile_picture
                },
                
                # Informations du capitaine
                'captain': {
                    'name': f"{trip.captain.user.first_name} {trip.captain.user.last_name}",
                    'phone': trip.captain.user.phone_number,
                    'profile_picture': trip.captain.user.profile_picture,
                    'rating': float(trip.captain.average_rating) if trip.captain.average_rating else 0,
                    'license_number': trip.captain.license_number
                },
                
                # Informations du bateau
                'boat': {
                    'name': trip.boat.name if trip.boat else 'Non spécifié',
                    'type': trip.boat.boat_type if trip.boat else None,
                    'capacity': trip.boat.capacity if trip.boat else None,
                    'photos': trip.boat.photos if trip.boat else []
                },
                
                # Détails de la réservation
                'booking': {
                    'passenger_count': trip.passenger_count,
                    'passenger_names': trip.passenger_names,
                    'special_requests': trip.special_requests,
                    'created_at': trip.created_at
                },
                
                # Informations de paiement
                'payment': {
                    'base_price': float(trip.base_price),
                    'additional_charges': float(trip.additional_charges),
                    'tip': float(trip.tip),
                    'total_price': float(trip.total_price),
                    'payment_status': trip.payment_status,
                    'payment_method': trip.payment_method
                },
                
                # Statut et suivi
                'tracking': {
                    'current_location': trip.current_location,
                    'estimated_arrival': trip.estimated_arrival_time,
                    'delay_minutes': trip.delay_minutes,
                    'problem_description': trip.problem_description
                }
            },
            
            # Données QR originales (si disponibles)
            'qr_verification': {
                'qr_data_provided': qr_data is not None,
                'verification_method': 'qr_data' if qr_data else 'trip_id'
            },
            
            # Actions disponibles selon le statut
            'available_actions': self._get_available_actions(trip)
        }

    def _get_available_actions(self, trip):
        """Retourne les actions disponibles selon le statut du ticket"""
        
        actions = []
        
        if trip.status == Trip.Status.PENDING:
            actions.extend(['cancel', 'modify'])
        elif trip.status == Trip.Status.ACCEPTED:
            actions.extend(['start', 'cancel'])
        elif trip.status == Trip.Status.IN_PROGRESS:
            actions.extend(['complete', 'report_problem', 'update_location'])
        elif trip.status == Trip.Status.COMPLETED:
            actions.extend(['review', 'download_receipt'])
        
        return actions

    def _get_error_code(self, message):
        """Convertit un message d'erreur en code d'erreur"""
        
        error_mapping = {
            'Ticket non trouvé': 'TICKET_NOT_FOUND',
            'Ticket annulé': 'TICKET_CANCELLED',
            'QR code invalide': 'INVALID_QR_CODE',
            'Ticket expiré': 'TICKET_EXPIRED'
        }
        
        for key, code in error_mapping.items():
            if key in message:
                return code
        
        return 'UNKNOWN_ERROR'

    def _get_current_timestamp(self):
        """Retourne le timestamp actuel"""
        from django.utils import timezone
        return timezone.now().isoformat()


class QRCodeGenerationView(APIView):
    """
    Endpoint pour régénérer un QR code pour un ticket existant.
    """
    permission_classes = [IsAuthenticated, IsClientOrCaptainOrEstablishment]

    def post(self, request, trip_id):
        """
        Régénère le QR code pour un ticket donné.
        
        Réponse:
        {
            "success": true,
            "qr_code": "data:image/png;base64,..."
        }
        """
        
        try:
            trip = get_object_or_404(Trip, id=trip_id)
            
            # Vérifier les permissions
            is_client = hasattr(request.user, 'client') and trip.client == request.user.client
            is_captain = hasattr(request.user, 'captain') and trip.captain == request.user.captain
            is_staff = request.user.is_staff
            
            if not any([is_client, is_captain, is_staff]):
                return Response({
                    'success': False,
                    'message': 'Accès non autorisé'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Régénérer le QR code
            qr_code = trip.generate_qr_code()
            
            if qr_code:
                return Response({
                    'success': True,
                    'qr_code': qr_code,
                    'trip_id': trip.id,
                    'generated_at': self._get_current_timestamp()
                })
            else:
                return Response({
                    'success': False,
                    'message': 'Erreur lors de la génération du QR code'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Trip.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Ticket non trouvé'
            }, status=status.HTTP_404_NOT_FOUND)

    def _get_current_timestamp(self):
        """Retourne le timestamp actuel"""
        from django.utils import timezone
        return timezone.now().isoformat()
