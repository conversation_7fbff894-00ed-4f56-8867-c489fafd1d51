"""
Permissions personnalisées pour l'API Commodore.

Ce module définit les classes de permission pour contrôler l'accès
aux différentes vues de l'API selon le type d'utilisateur.
"""

from rest_framework.permissions import BasePermission, IsAuthenticated, SAFE_METHODS


class IsClient(BasePermission):
    """
    Permission permettant l'accès uniquement aux utilisateurs de type CLIENT.
    """
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and 
                   hasattr(request.user, 'client'))


class IsCaptain(BasePermission):
    """
    Permission permettant l'accès uniquement aux utilisateurs de type CAPTAIN.
    """
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and 
                   hasattr(request.user, 'captain'))


class IsBoatman(BasePermission):
    """
    Permission permettant l'accès uniquement aux bateliers
    (capitaines enregistrés par un établissement).
    """
    def has_permission(self, request, view):
        # L'utilisateur doit être authentifié
        if not (request.user and request.user.is_authenticated):
            return False

        # Vérifier qu'il existe bien un profil Captain lié
        from accounts.models import Captain  # import local pour éviter les cycles
        try:
            captain = request.user.captain
        except Captain.DoesNotExist:
            return False

        # Seuls les capitaines enregistrés par un établissement (is_boatman)
        return bool(captain.is_boatman)


class IsEstablishment(BasePermission):
    """
    Permission permettant l'accès uniquement aux utilisateurs de type ESTABLISHMENT.
    """
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and 
                   hasattr(request.user, 'establishment'))


class IsOwnerOrAdmin(BasePermission):
    """
    Permission permettant l'accès uniquement au propriétaire de la ressource
    ou à un administrateur.
    """
    def has_object_permission(self, request, view, obj):
        return bool(
            request.user and 
            request.user.is_authenticated and 
            (obj.user == request.user or request.user.is_staff)
        )


class IsReadOnly(BasePermission):
    """
    Permission permettant uniquement les méthodes de lecture (GET, HEAD, OPTIONS).
    """
    def has_permission(self, request, view):
        return request.method in SAFE_METHODS


# Permissions composées pour cas d'usage communs
class IsClientOrReadOnly(BasePermission):
    """
    Permission permettant l'accès en lecture à tous les utilisateurs authentifiés,
    mais limitant les écritures aux clients.
    """
    def has_permission(self, request, view):
        return bool(
            request.method in SAFE_METHODS or
            (request.user and request.user.is_authenticated and hasattr(request.user, 'client'))
        )


class IsCaptainOrReadOnly(BasePermission):
    """
    Permission permettant l'accès en lecture à tous les utilisateurs authentifiés,
    mais limitant les écritures aux capitaines.
    """
    def has_permission(self, request, view):
        return bool(
            request.method in SAFE_METHODS or
            (request.user and request.user.is_authenticated and hasattr(request.user, 'captain'))
        )


class IsEstablishmentOrReadOnly(IsEstablishment):
    """
    Permission autorisant les accès en lecture à tous, mais réservant écriture/modification aux établissements.
    """
    
    def has_permission(self, request, view):
        if request.method in SAFE_METHODS:
            return True
        return super().has_permission(request, view)


class IsClientOrCaptainOrEstablishment(BasePermission):
    """
    Permission autorisant uniquement les clients, les capitaines ou les établissements.
    Utilisée pour les actions qui peuvent être effectuées par n'importe lequel de ces trois types d'utilisateurs.
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
            
        return (hasattr(request.user, 'client') or 
                hasattr(request.user, 'captain') or 
                hasattr(request.user, 'establishment'))


class IsOwnerOnly(BasePermission):
    """
    Permission autorisant uniquement l'accès aux données dont l'utilisateur est le propriétaire.
    Utilise un champ 'user' sur l'objet pour vérifier la propriété.
    
    Cette permission doit être utilisée en combinaison avec IsAuthenticated.
    """
    
    def has_object_permission(self, request, view, obj):
        # Vérifier que l'objet a bien un attribut 'user'
        if not hasattr(obj, 'user'):
            return False
        
        # Vérifier que l'utilisateur est bien le propriétaire
        return obj.user == request.user
