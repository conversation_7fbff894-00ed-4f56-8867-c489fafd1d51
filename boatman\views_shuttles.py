"""
Vues de gestion des courses pour l'espace batelier.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Q
from django.core.paginator import Paginator
import json

from accounts.models import Captain
from trips.models import Trip
from notifications.services import create_notification
from accounts.permissions import IsBoatman
from .integration_services import (
    BoatmanTripIntegrationService,
    BoatmanEstablishmentIntegrationService,
    BoatmanQRValidationService
)


class BoatmanShuttlesView(APIView):
    """
    Liste des courses du batelier.

    GET /api/boatman/shuttles/
    """
    permission_classes = [IsAuth<PERSON>icated, <PERSON><PERSON><PERSON><PERSON>]

    def get(self, request):
        """Récupérer la liste des courses"""

        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)

        captain = request.user.captain

        # Paramètres de filtrage
        filter_status = request.GET.get('filter', 'all')
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))
        search = request.GET.get('search', '')

        # Utiliser le service d'intégration pour récupérer les courses
        queryset = BoatmanTripIntegrationService.get_captain_assigned_trips(
            captain=captain,
            status_filter=filter_status,
            search=search
        )

        # Pagination
        paginator = Paginator(queryset, limit)
        page_obj = paginator.get_page(page)

        # Sérialiser les données
        shuttles_data = []
        for trip in page_obj:
            shuttles_data.append({
                'shuttle_id': str(trip.id),
                'client': trip.client.user.get_full_name() if trip.client else 'Client inconnu',
                'client_phone': trip.client.user.phone_number if trip.client else None,
                'date': trip.scheduled_start_time.isoformat(),
                'departure': trip.start_location,
                'destination': trip.end_location,
                'passengers': trip.passenger_count,
                'status': trip.status,
                'status_display': self._get_status_display(trip.status),
                'estimated_duration': trip.estimated_duration or 30,
                'distance_km': float(trip.distance_km) if trip.distance_km else 0.0,
                'boat': {
                    'name': trip.boat.name if trip.boat else 'Bateau non assigné',
                    'capacity': trip.boat.capacity if trip.boat else 0
                },
                'payment': {
                    'amount': float(trip.total_price),
                    'status': trip.payment_status,
                    'method': trip.payment_method
                },
                'special_requests': trip.special_requests or '',
                'created_at': trip.created_at.isoformat(),
                'can_start': trip.status in ['ACCEPTED'] and trip.scheduled_start_time <= timezone.now() + timezone.timedelta(minutes=30),
                'can_complete': trip.status == 'IN_PROGRESS'
            })

        # Statistiques de filtrage
        filter_stats = {
            'all': Trip.objects.filter(captain=captain).count(),
            'À venir': Trip.objects.filter(captain=captain, status__in=['PENDING', 'ACCEPTED']).count(),
            'En cours': Trip.objects.filter(captain=captain, status='IN_PROGRESS').count(),
            'Terminées': Trip.objects.filter(captain=captain, status='COMPLETED').count(),
            'Annulées': Trip.objects.filter(captain=captain, status='CANCELLED').count()
        }

        return Response({
            'status': 'success',
            'data': {
                'shuttles': shuttles_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': paginator.count,
                    'total_pages': paginator.num_pages,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                },
                'filters': {
                    'current_filter': filter_status,
                    'search_term': search,
                    'available_filters': [
                        {'value': 'all', 'label': 'Toutes', 'count': filter_stats['all']},
                        {'value': 'À venir', 'label': 'À venir', 'count': filter_stats['À venir']},
                        {'value': 'En cours', 'label': 'En cours', 'count': filter_stats['En cours']},
                        {'value': 'Terminées', 'label': 'Terminées', 'count': filter_stats['Terminées']},
                        {'value': 'Annulées', 'label': 'Annulées', 'count': filter_stats['Annulées']}
                    ]
                }
            },
            'message': 'Courses récupérées avec succès',
            'timestamp': timezone.now().isoformat()
        })

    def _get_status_display(self, status):
        """Obtenir l'affichage du statut"""
        status_mapping = {
            'PENDING': 'En attente',
            'ACCEPTED': 'À venir',
            'IN_PROGRESS': 'En cours',
            'COMPLETED': 'Terminée',
            'CANCELLED': 'Annulée'
        }
        return status_mapping.get(status, status)


class BoatmanShuttleDetailView(APIView):
    """
    Détails d'une course spécifique.

    GET /api/boatman/shuttle/{shuttle_id}/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def get(self, request, shuttle_id):
        """Récupérer les détails d'une course"""

        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)

        captain = request.user.captain

        try:
            trip = BoatmanTripIntegrationService.validate_trip_ownership(captain, shuttle_id)
        except Trip.DoesNotExist:
            return Response({
                'status': 'error',
                'error': 'Course non trouvée',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)

        # Calculer les pourboires si la course est terminée
        tip_amount = 0.0
        if trip.status == 'COMPLETED':
            from payments.models import Payment
            tip_payment = Payment.objects.filter(
                trip=trip,
                type=Payment.PaymentType.TIP,
                status=Payment.Status.COMPLETED
            ).first()
            if tip_payment:
                tip_amount = float(tip_payment.amount)

        # Données de localisation (simulation)
        current_location = "Position actuelle"
        if trip.status == 'IN_PROGRESS':
            current_location = f"En route vers {trip.end_location}"
        elif trip.status == 'COMPLETED':
            current_location = trip.end_location

        return Response({
            'status': 'success',
            'data': {
                'shuttle_id': str(trip.id),
                'status': trip.status,
                'status_display': self._get_status_display(trip.status),
                'date': trip.scheduled_start_time.isoformat(),
                'departure': trip.start_location,
                'destination': trip.end_location,
                'passengers': trip.passenger_count,
                'estimated_duration': trip.estimated_duration or 30,
                'distance_km': float(trip.distance_km) if trip.distance_km else 0.0,
                'client': {
                    'name': trip.client.user.get_full_name() if trip.client else 'Client inconnu',
                    'phone': trip.client.user.phone_number if trip.client else None,
                    'email': trip.client.user.email if trip.client else None,
                    'profile_picture': getattr(trip.client, 'profile_picture', None) if trip.client else None
                },
                'boat': {
                    'name': trip.boat.name if trip.boat else 'Bateau non assigné',
                    'capacity': trip.boat.capacity if trip.boat else 0,
                    'type': trip.boat.boat_type if trip.boat else 'N/A'
                },
                'payment': {
                    'amount': float(trip.total_price),
                    'status': trip.payment_status,
                    'method': trip.payment_method,
                    'tip': tip_amount
                },
                'location': {
                    'current': current_location,
                    'distance_remaining': 0 if trip.status == 'COMPLETED' else None
                },
                'special_requests': trip.special_requests or '',
                'timeline': self._get_trip_timeline(trip),
                'actions': self._get_available_actions(trip)
            },
            'message': 'Détails de la course récupérés',
            'timestamp': timezone.now().isoformat()
        })

    def _get_status_display(self, status):
        """Obtenir l'affichage du statut"""
        status_mapping = {
            'PENDING': 'En attente',
            'ACCEPTED': 'À venir',
            'IN_PROGRESS': 'En cours',
            'COMPLETED': 'Terminée',
            'CANCELLED': 'Annulée'
        }
        return status_mapping.get(status, status)

    def _get_trip_timeline(self, trip):
        """Obtenir la timeline de la course"""
        timeline = [
            {
                'event': 'Course créée',
                'timestamp': trip.created_at.isoformat(),
                'status': 'completed'
            }
        ]

        if trip.status in ['ACCEPTED', 'IN_PROGRESS', 'COMPLETED']:
            timeline.append({
                'event': 'Course acceptée',
                'timestamp': trip.updated_at.isoformat(),
                'status': 'completed'
            })

        if trip.status in ['IN_PROGRESS', 'COMPLETED']:
            timeline.append({
                'event': 'Course démarrée',
                'timestamp': trip.actual_start_time.isoformat() if trip.actual_start_time else trip.updated_at.isoformat(),
                'status': 'completed'
            })

        if trip.status == 'COMPLETED':
            timeline.append({
                'event': 'Course terminée',
                'timestamp': trip.actual_end_time.isoformat() if trip.actual_end_time else trip.updated_at.isoformat(),
                'status': 'completed'
            })

        return timeline

    def _get_available_actions(self, trip):
        """Obtenir les actions disponibles pour la course"""
        actions = []

        if trip.status == 'ACCEPTED':
            # Vérifier si on peut démarrer (dans les 30 minutes)
            if trip.scheduled_start_time <= timezone.now() + timezone.timedelta(minutes=30):
                actions.append({
                    'action': 'start',
                    'label': 'Démarrer la course',
                    'method': 'POST',
                    'url': f'/api/boatman/shuttle/{trip.id}/start/',
                    'requires_qr': True
                })

        elif trip.status == 'IN_PROGRESS':
            actions.append({
                'action': 'complete',
                'label': 'Terminer la course',
                'method': 'POST',
                'url': f'/api/boatman/shuttle/{trip.id}/end/',
                'requires_qr': False
            })

            actions.append({
                'action': 'track',
                'label': 'Suivre la course',
                'method': 'GET',
                'url': f'/api/boatman/shuttle/{trip.id}/track/',
                'requires_qr': False
            })

        return actions


class BoatmanShuttleStartView(APIView):
    """
    Démarrer une course.

    POST /api/boatman/shuttle/{shuttle_id}/start/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def post(self, request, shuttle_id):
        """Démarrer une course"""

        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)

        captain = request.user.captain

        try:
            trip = BoatmanTripIntegrationService.validate_trip_ownership(captain, shuttle_id)
        except Trip.DoesNotExist:
            return Response({
                'status': 'error',
                'error': 'Course non trouvée',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)

        # Utiliser le service d'intégration pour démarrer la course
        result = BoatmanTripIntegrationService.start_trip(trip)

        if not result['success']:
            return Response({
                'status': 'error',
                'error': result['error'],
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)

        # Notifier l'établissement
        BoatmanEstablishmentIntegrationService.notify_establishment_trip_update(trip, 'started')

        return Response({
            'status': 'success',
            'data': {
                'shuttle_id': str(result['trip_id']),
                'status': 'IN_PROGRESS',
                'started_at': result['started_at'].isoformat(),
                'estimated_arrival': result['estimated_arrival'].isoformat()
            },
            'message': 'Course démarrée avec succès',
            'timestamp': timezone.now().isoformat()
        })


class BoatmanShuttleEndView(APIView):
    """
    Terminer une course.

    POST /api/boatman/shuttle/{shuttle_id}/end/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def post(self, request, shuttle_id):
        """Terminer une course"""

        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)

        captain = request.user.captain

        try:
            trip = BoatmanTripIntegrationService.validate_trip_ownership(captain, shuttle_id)
        except Trip.DoesNotExist:
            return Response({
                'status': 'error',
                'error': 'Course non trouvée',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)

        # Utiliser le service d'intégration pour terminer la course
        result = BoatmanTripIntegrationService.complete_trip(trip)

        if not result['success']:
            return Response({
                'status': 'error',
                'error': result['error'],
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)

        # Notifier l'établissement
        BoatmanEstablishmentIntegrationService.notify_establishment_trip_update(trip, 'completed')

        return Response({
            'status': 'success',
            'data': {
                'shuttle_id': str(result['trip_id']),
                'status': 'COMPLETED',
                'completed_at': result['completed_at'].isoformat(),
                'actual_duration': result['actual_duration'],
                'estimated_duration': trip.estimated_duration
            },
            'message': 'Course terminée avec succès',
            'timestamp': timezone.now().isoformat()
        })


class BoatmanShuttleTrackView(APIView):
    """
    Suivre une course en cours.

    GET /api/boatman/shuttle/{shuttle_id}/track/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def get(self, request, shuttle_id):
        """Suivre une course"""

        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)

        captain = request.user.captain

        try:
            trip = Trip.objects.select_related('client__user').get(
                id=shuttle_id, captain=captain
            )
        except Trip.DoesNotExist:
            return Response({
                'status': 'error',
                'error': 'Course non trouvée',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)

        # Calculer les informations de suivi
        current_location = "Position actuelle"
        distance_remaining = 0
        estimated_arrival = None

        if trip.status == 'IN_PROGRESS':
            # Simulation de données de localisation
            current_location = f"En route vers {trip.end_location}"
            distance_remaining = float(trip.distance_km) * 0.3 if trip.distance_km else 2.5  # Simulation

            if trip.actual_start_time and trip.estimated_duration:
                estimated_arrival = trip.actual_start_time + timezone.timedelta(minutes=trip.estimated_duration)

        elif trip.status == 'COMPLETED':
            current_location = trip.end_location
            distance_remaining = 0

        # Temps écoulé depuis le début
        elapsed_time = 0
        if trip.actual_start_time:
            elapsed_time = int((timezone.now() - trip.actual_start_time).total_seconds() / 60)

        return Response({
            'status': 'success',
            'data': {
                'shuttle_id': str(trip.id),
                'status': trip.status,
                'current_location': current_location,
                'distance_remaining': distance_remaining,
                'destination': trip.end_location,
                'passengers': trip.passenger_count,
                'client': trip.client.user.get_full_name() if trip.client else 'Client inconnu',
                'tracking_info': {
                    'elapsed_time_minutes': elapsed_time,
                    'estimated_arrival': estimated_arrival.isoformat() if estimated_arrival else None,
                    'progress_percentage': max(0, min(100, (1 - distance_remaining / float(trip.distance_km)) * 100)) if trip.distance_km else 50
                },
                'coordinates': {
                    'latitude': 43.5528,  # Simulation - Cannes
                    'longitude': 7.0174
                }
            },
            'message': 'Informations de suivi récupérées',
            'timestamp': timezone.now().isoformat()
        })


class BoatmanQRValidationView(APIView):
    """
    Valider un QR code pour démarrer une course.

    POST /api/boatman/shuttle/{shuttle_id}/validate-qr/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def post(self, request, shuttle_id):
        """Valider le QR code"""

        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)

        captain = request.user.captain
        qr_code = request.data.get('qr_code')

        if not qr_code:
            return Response({
                'status': 'error',
                'error': 'QR code requis',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            trip = BoatmanTripIntegrationService.validate_trip_ownership(captain, shuttle_id)
        except Trip.DoesNotExist:
            return Response({
                'status': 'error',
                'error': 'Course non trouvée',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)

        # Utiliser le service d'intégration pour valider le QR code
        is_valid, message = BoatmanQRValidationService.validate_qr_code(trip, qr_code)

        if not is_valid:
            return Response({
                'status': 'error',
                'error': message,
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'status': 'success',
            'data': {
                'shuttle_id': str(trip.id),
                'qr_validated': True,
                'client': trip.client.user.get_full_name() if trip.client else 'Client inconnu',
                'passengers': trip.passenger_count,
                'destination': trip.end_location,
                'can_start': True
            },
            'message': 'QR code validé, embarquement confirmé',
            'timestamp': timezone.now().isoformat()
        })
