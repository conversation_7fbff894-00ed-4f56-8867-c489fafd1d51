"""
URLs pour l'application establishments.
"""

from django.urls import path
from . import views, views_shuttle, views_boatman, views_payments

app_name = 'establishments'

urlpatterns = [
    # Dashboard et gestion générale
    path('dashboard/', views.EstablishmentDashboardView.as_view(), name='dashboard'),
    path('toggle-availability/', views.EstablishmentAvailabilityToggleView.as_view(), name='toggle-availability'),
    
    # Gestion des navettes
    path('shuttles/', views.EstablishmentShuttlesView.as_view(), name='shuttles'),
    path('shuttle-requests/', views.EstablishmentShuttleRequestsView.as_view(), name='shuttle-requests'),
    # Alias assign (équivalent à accept)
    path('shuttle-requests/<int:request_id>/assign/', views_shuttle.EstablishmentShuttleAcceptView.as_view(), name='shuttle-assign'),
    path('shuttle-requests/<int:request_id>/quotes/', views_shuttle.EstablishmentShuttleQuotesView.as_view(), name='shuttle-quotes'),
    path('shuttle-requests/<int:request_id>/accept/', views_shuttle.EstablishmentShuttleAcceptView.as_view(), name='shuttle-accept'),
    path('shuttle-requests/<int:request_id>/reject/', views_shuttle.EstablishmentShuttleRejectView.as_view(), name='shuttle-reject'),
    path('available-resources/', views_shuttle.EstablishmentAvailableResourcesView.as_view(), name='available-resources'),
    
    # Gestion des bateliers
    path('register-boatman/', views_boatman.EstablishmentRegisterBoatmanView.as_view(), name='register-boatman'),
    path('boatmen/', views_boatman.EstablishmentBoatmenListView.as_view(), name='boatmen-list'),
    path('boatmen/<int:captain_id>/', views_boatman.EstablishmentBoatmanDetailView.as_view(), name='boatman-detail'),
    
    # Gestion des paiements et portefeuille
    path('wallet/', views_payments.EstablishmentWalletView.as_view(), name='wallet'),
    path('wallet/add-funds/', views_payments.EstablishmentAddFundsView.as_view(), name='add-funds'),
    path('payments/history/', views_payments.EstablishmentPaymentHistoryView.as_view(), name='payment-history'),
    path('payments/stats/', views_payments.EstablishmentPaymentStatsView.as_view(), name='payment-stats'),
]
