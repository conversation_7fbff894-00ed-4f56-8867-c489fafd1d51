import pytest
from unittest.mock import patch, MagicMock
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from payments.models import Payment
# Utiliser directement le modèle Trip de l'application trips
from trips.models import Trip
from accounts.models import User, Client as Passenger
# Adaptation pour les modèles qui n'existent pas dans la structure actuelle
class ImpactStatistics:
    pass

pytestmark = pytest.mark.unit

@pytest.fixture
def api_client():
    """Fixture pour le client API"""
    return APIClient()

@pytest.fixture
def user():
    """Fixture pour un utilisateur"""
    return User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='SecurePassword123!'
    )

@pytest.fixture
def impact_statistics():
    """Fixture pour les statistiques d'impact"""
    return ImpactStatistics.objects.create()

@pytest.fixture
def passenger(user, impact_statistics):
    """Fixture pour un passager"""
    return Passenger.objects.create(
        user=user,
        impact_statistics=impact_statistics
    )

@pytest.fixture
def booking(passenger):
    """Fixture pour une réservation"""
    return Trip.objects.create(
        passenger=passenger,
        status='COMPLETED'
    )

@pytest.fixture
def payment(booking):
    """Fixture pour un paiement"""
    return Payment.objects.create(
        booking=booking,
        amount=100.0,
        type='TRIP',
        status='COMPLETED',
        stripe_payment_id='pi_test123'
    )

@pytest.mark.django_db
class TestRefundViewSet:
    """Tests pour RefundViewSet"""

    @patch('payments.stripe_utils.create_refund')
    def test_refund_payment(self, mock_create_refund, api_client, user, payment):
        """Test de la méthode refund_payment"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_create_refund.return_value = MagicMock(
            id='re_test123',
            amount=10000,
            status='succeeded'
        )
        
        # Appeler la vue
        url = reverse('refunds-refund-payment')
        data = {
            'payment_id': str(payment.id),
            'reason': 'requested_by_customer'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert 'status' in response.data
        assert 'refund_id' in response.data
        assert response.data['refund_id'] == 're_test123'
        
        # Vérifier que le paiement a été mis à jour
        payment.refresh_from_db()
        assert payment.status == 'REFUNDED'
        assert payment.refund_id == 're_test123'
        assert payment.refund_reason == 'requested_by_customer'
    
    @patch('payments.stripe_utils.create_refund')
    def test_refund_payment_partial(self, mock_create_refund, api_client, user, payment):
        """Test de la méthode refund_payment avec un remboursement partiel"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_create_refund.return_value = MagicMock(
            id='re_test123',
            amount=5000,
            status='succeeded'
        )
        
        # Appeler la vue
        url = reverse('refunds-refund-payment')
        data = {
            'payment_id': str(payment.id),
            'amount': '50.00',
            'reason': 'requested_by_customer'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert 'status' in response.data
        assert 'refund_id' in response.data
        assert response.data['refund_id'] == 're_test123'
        assert response.data['amount'] == '50.00'
        
        # Vérifier que le paiement a été mis à jour
        payment.refresh_from_db()
        assert payment.status == 'PARTIALLY_REFUNDED'
        assert payment.refund_id == 're_test123'
        assert payment.refund_amount == 50.0
        assert payment.refund_reason == 'requested_by_customer'
    
    @patch('stripe.Refund.retrieve')
    def test_get_refund(self, mock_retrieve, api_client, user):
        """Test de la méthode get_refund"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_retrieve.return_value = MagicMock(
            id='re_test123',
            amount=10000,
            currency='eur',
            status='succeeded',
            created=1625097600,
            reason='requested_by_customer',
            payment_intent='pi_test123'
        )
        
        # Appeler la vue
        url = reverse('refunds-get-refund')
        response = api_client.get(url, {'refund_id': 're_test123'})
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == 're_test123'
        assert response.data['amount'] == 100.0
        assert response.data['currency'] == 'eur'
        assert response.data['status'] == 'succeeded'
        assert response.data['reason'] == 'requested_by_customer'
        assert response.data['payment_intent'] == 'pi_test123'
    
    @patch('stripe.Refund.list')
    def test_list_refunds(self, mock_list, api_client, user, payment):
        """Test de la méthode list_refunds"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_refund = MagicMock(
            id='re_test123',
            amount=10000,
            currency='eur',
            status='succeeded',
            created=1625097600,
            reason='requested_by_customer'
        )
        mock_list.return_value = MagicMock(
            data=[mock_refund]
        )
        
        # Appeler la vue
        url = reverse('refunds-list-refunds')
        response = api_client.get(url, {'payment_id': str(payment.id)})
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['id'] == 're_test123'
        assert response.data[0]['amount'] == 100.0
        assert response.data[0]['currency'] == 'eur'
        assert response.data[0]['status'] == 'succeeded'
        assert response.data[0]['reason'] == 'requested_by_customer'
