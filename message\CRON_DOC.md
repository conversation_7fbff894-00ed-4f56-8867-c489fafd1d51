# Suppression automatique des anciens messages dans Commodore

## Fonctionnement

- Les messages de chat plus vieux que 12 mois sont supprimés automatiquement tous les jours grâce à une tâche django-cron.
- Cela permet de respecter la confidentialité, de limiter la taille de la base de données et d’être conforme au RGPD.

## Où est le code ?
- La logique se trouve dans `rag/cron.py` (classe `DeleteOldMessagesJob`).

## Activation en production

1. **Installer django-cron** :
   ```bash
   pip install django-cron
   ```
2. **Ajouter à `INSTALLED_APPS` dans `settings.py`** :
   ```python
   INSTALLED_APPS = [
       ...
       'django_cron',
   ]
   ```
3. **Déclarer la tâche dans `settings.py`** :
   ```python
   CRON_CLASSES = [
       "rag.cron.DeleteOldMessagesJob",
   ]
   ```
4. **Lancer la tâche automatique** (à faire tourner en parallèle du serveur web) :
   ```bash
   python manage.py runcrons
   ```
   > Utilise un gestionnaire de services (systemd, supervisord, etc.) pour automatiser le lancement en production.

## Résumé
- Plus besoin de supprimer les messages à la main : tout est automatique !
- Tu peux modifier la durée de conservation (12 mois par défaut) dans `rag/cron.py`.
