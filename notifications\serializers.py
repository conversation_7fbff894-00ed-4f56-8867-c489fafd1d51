"""
Module de sérialisation pour l'application notifications.

Ce module contient les sérialiseurs pour les modèles de l'application notifications,
permettant la conversion entre les objets Python et les formats de données comme JSON.
"""

from rest_framework import serializers
from django.contrib.contenttypes.models import ContentType
from .models import Notification, Device

class ContentObjectRelatedField(serializers.RelatedField):
    """
    Champ personnalisé pour sérialiser l'objet associé à une notification.
    """
    def to_representation(self, value):
        """
        Convertit l'objet associé en représentation JSON.
        
        Args:
            value: L'objet associé
            
        Returns:
            dict: Représentation JSON de l'objet
        """
        if hasattr(value, 'to_dict'):
            return value.to_dict()
        
        # Représentation par défaut
        return {
            'id': value.id,
            'type': value.__class__.__name__,
            'str': str(value)
        }

class NotificationSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour le modèle Notification.
    
    Ce sérialiseur gère la conversion des objets Notification en JSON et vice-versa.
    """
    content_object = ContentObjectRelatedField(read_only=True)
    type_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'user', 'type', 'type_display', 'title', 'message', 
            'is_read', 'created_at', 'content_object',
            'delivery_status', 'error_message', 'sent_at'
        ]
        read_only_fields = [
            'id', 'user', 'type', 'title', 'message', 'created_at',
            'content_object', 'delivery_status', 'error_message', 'sent_at'
        ]
    
    def get_type_display(self, obj):
        """
        Récupère l'affichage du type de notification.
        
        Args:
            obj: Objet Notification
            
        Returns:
            str: Affichage du type de notification
        """
        # Utilise la méthode utilitaire générée par Django pour obtenir la valeur affichée
        return obj.get_type_display()

class DeviceSerializer(serializers.ModelSerializer):
    """Sérialiseur pour le modèle Device."""
    
    class Meta:
        model = Device
        fields = ['id', 'device_id', 'device_type', 'name', 'push_token', 'is_active', 'last_used', 'created_at']
        read_only_fields = ['id', 'user', 'last_used', 'created_at']
        
    def validate_push_token(self, value):
        """Valide le token push.
        
        Args:
            value: Token push
            
        Returns:
            str: Token push validé
        """
        if not value or len(value) < 10:
            raise serializers.ValidationError("Token invalide")
        return value
        
    def validate_device_id(self, value):
        """Valide l'identifiant de l'appareil.
        
        Args:
            value: Identifiant de l'appareil
            
        Returns:
            str: Identifiant de l'appareil validé
        """
        if not value or len(value) < 5:
            raise serializers.ValidationError("Identifiant d'appareil invalide")
        return value
