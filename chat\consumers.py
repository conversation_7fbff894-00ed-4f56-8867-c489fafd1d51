import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.utils import timezone
from .models import ChatRoom, Message
from accounts.models import User

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f'chat_{self.room_id}'
        self.user = self.scope['user']

        # Vérifier si l'utilisateur a accès à ce salon
        if await self.can_access_room():
            # Rejoindre le groupe de la room
            await self.channel_layer.group_add(
                self.room_group_name,
                self.channel_name
            )
            await self.accept()
        else:
            await self.close()

    async def disconnect(self, close_code):
        # Quitter le groupe de la room
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        data = json.loads(text_data)
        message_type = data.get('type', 'TEXT')
        content = data.get('content')
        attachment = data.get('attachment')
        metadata = data.get('metadata', {})

        # <PERSON><PERSON>garder le message
        message = await self.save_message(
            content=content,
            message_type=message_type,
            attachment=attachment,
            metadata=metadata
        )

        # Envoyer le message au groupe
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message',
                'message': {
                    'id': message.id,
                    'sender': self.user.id,
                    'sender_name': self.user.get_full_name() or self.user.email,
                    'type': message_type,
                    'content': content,
                    'attachment': attachment.url if attachment else None,
                    'metadata': metadata,
                    'created_at': message.created_at.isoformat(),
                }
            }
        )

    async def chat_message(self, event):
        message = event['message']
        # Envoyer le message au WebSocket
        await self.send(text_data=json.dumps(message))

    @database_sync_to_async
    def can_access_room(self):
        try:
            room = ChatRoom.objects.get(id=self.room_id)
            return room.participants.filter(id=self.user.id).exists()
        except ChatRoom.DoesNotExist:
            return False

    @database_sync_to_async
    def save_message(self, content, message_type='TEXT', attachment=None, metadata=None):
        room = ChatRoom.objects.get(id=self.room_id)
        message = Message.objects.create(
            room=room,
            sender=self.user,
            type=message_type,
            content=content,
            attachment=attachment,
            metadata=metadata or {}
        )
        return message
