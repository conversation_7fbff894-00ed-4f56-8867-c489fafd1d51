"""
Test simplifié et efficace des endpoints PATCH
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from accounts.models import C<PERSON>, Captain, Establishment
from boats.models import Boat

User = get_user_model()

def test_patch_endpoints():
    """Test rapide des endpoints PATCH"""
    print("🚀 TEST RAPIDE DES ENDPOINTS PATCH")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    results = []
    
    # 1. Test Client PATCH
    print("\n👤 TEST CLIENT PATCH")
    print("-" * 30)
    
    try:
        # Créer un client de test
        try:
            user = User.objects.get(email='<EMAIL>')
            user.delete()
        except User.DoesNotExist:
            pass
        
        user = User.objects.create_user(
            email='<EMAIL>',
            password='Test123!',
            first_name='Test',
            last_name='Client',
            type='CLIENT'
        )
        
        Client.objects.create(
            user=user,
            date_of_birth='1990-01-01',
            nationality='Française'
        )
        
        token = Token.objects.create(user=user)
        
        # Test PATCH
        headers = {
            'Authorization': f'Token {token.key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "first_name": "TestUpdated",
            "client_profile": {
                "nationality": "Italienne"
            }
        }
        
        response = requests.patch(f"{base_url}/api/profile/", json=data, headers=headers)
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Client PATCH: SUCCÈS")
            results.append("Client PATCH: PASSED")
        else:
            print(f"❌ Client PATCH: ÉCHEC - {response.text}")
            results.append("Client PATCH: FAILED")
            
    except Exception as e:
        print(f"❌ Erreur Client PATCH: {str(e)}")
        results.append("Client PATCH: ERROR")
    
    # 2. Test Captain PATCH
    print("\n⛵ TEST CAPTAIN PATCH")
    print("-" * 30)
    
    try:
        # Créer un capitaine de test
        try:
            user = User.objects.get(email='<EMAIL>')
            user.delete()
        except User.DoesNotExist:
            pass
        
        user = User.objects.create_user(
            email='<EMAIL>',
            password='Test123!',
            first_name='Test',
            last_name='Captain',
            type='CAPTAIN'
        )
        
        Captain.objects.create(
            user=user,
            experience='5 ans',
            license_number='CAP123',
            years_of_experience=5,
            rate_per_hour=45.00,
            rate_per_km=2.50
        )
        
        token = Token.objects.create(user=user)
        
        # Test PATCH
        headers = {
            'Authorization': f'Token {token.key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "first_name": "CaptainUpdated",
            "captain_profile": {
                "experience": "10 ans d'expérience"
            }
        }
        
        response = requests.patch(f"{base_url}/api/profile/", json=data, headers=headers)
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Captain PATCH: SUCCÈS")
            results.append("Captain PATCH: PASSED")
        else:
            print(f"❌ Captain PATCH: ÉCHEC - {response.text}")
            results.append("Captain PATCH: FAILED")
            
    except Exception as e:
        print(f"❌ Erreur Captain PATCH: {str(e)}")
        results.append("Captain PATCH: ERROR")
    
    # 3. Test Boatman PATCH
    print("\n🚤 TEST BOATMAN PATCH")
    print("-" * 30)
    
    try:
        # Créer un batelier de test
        try:
            user = User.objects.get(email='<EMAIL>')
            user.delete()
        except User.DoesNotExist:
            pass
        
        user = User.objects.create_user(
            email='<EMAIL>',
            password='Test123!',
            first_name='Test',
            last_name='Boatman',
            type='CAPTAIN'
        )
        
        Captain.objects.create(
            user=user,
            experience='3 ans',
            license_number='BOAT123',
            years_of_experience=3,
            rate_per_hour=40.00,
            rate_per_km=2.00
        )
        
        token = Token.objects.create(user=user)
        
        # Test PATCH
        headers = {
            'Authorization': f'Token {token.key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "first_name": "BoatmanUpdated",
            "experience": "5 ans d'expérience maritime"
        }
        
        response = requests.patch(f"{base_url}/api/boatman/profile/", json=data, headers=headers)
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Boatman PATCH: SUCCÈS")
            results.append("Boatman PATCH: PASSED")
        else:
            print(f"❌ Boatman PATCH: ÉCHEC - {response.text}")
            results.append("Boatman PATCH: FAILED")
            
    except Exception as e:
        print(f"❌ Erreur Boatman PATCH: {str(e)}")
        results.append("Boatman PATCH: ERROR")
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS PATCH")
    print("=" * 50)
    
    passed = [r for r in results if "PASSED" in r]
    failed = [r for r in results if "FAILED" in r or "ERROR" in r]
    
    print(f"✅ Tests réussis: {len(passed)}")
    print(f"❌ Tests échoués: {len(failed)}")
    
    for result in results:
        status_icon = "✅" if "PASSED" in result else "❌"
        print(f"{status_icon} {result}")
    
    if len(failed) == 0:
        print("\n🎉 TOUS LES TESTS PATCH ONT RÉUSSI!")
        return True
    else:
        print("\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ")
        return False

if __name__ == "__main__":
    success = test_patch_endpoints()
    sys.exit(0 if success else 1)
