# API Endpoints - RAG (Retrieval Augmented Generation)

## 1. Chatbot et recherche d'informations

### 1.1. Poser une question au chatbot
- **Endpoint**: POST /api/rag/chat/
- **Description**: Envoyer une question au chatbot RAG et obtenir une réponse générée avec les sources pertinentes
- **Auth Required**: <PERSON><PERSON> (JWT Token)
- **Request Body**:
```json
{
  "query": "Comment réserver un taxi boat?",
  "session_id": "unique-session-id-123",
  "user_profile": "CLIENT",
  "max_sources": 3,
  "locale": "fr"
}
```
- **Response (200 OK)**:
```json
{
  "response": "Pour réserver un bateau-taxi avec Commodore Taxi Boat, suivez ces étapes :\n\n1. **Téléchargez l'application** Commodore Taxi Boat.\n2. **Créez votre compte** utilisateur.\n3. **Indiquez votre lieu de prise en charge et votre destination** dans l'application.\n\nTous les trajets sont 100 % privatisés : aucun partage avec d'autres passagers.\n\nSi vous rencontrez des difficultés, contactez notre support client à <EMAIL>.",
  "sources": [
    {
      "title": "Guide de réservation",
      "url": "/documentation/reservation",
      "content_snippet": "La réservation d'un taxi boat se fait en trois étapes simples...",
      "relevance_score": 0.92
    },
    {
      "title": "FAQ",
      "url": "/documentation/faq",
      "content_snippet": "Les questions fréquentes sur la réservation de taxi boats...",
      "relevance_score": 0.85
    }
  ],
  "session_id": "unique-session-id-123",
  "processing_time": 3.2
}
```

### 1.2. Récupérer l'historique d'une session
- **Endpoint**: GET /api/rag/chat/history/{session_id}/
- **Description**: Récupérer l'historique complet des échanges pour une session spécifique
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "session_id": "unique-session-id-123",
  "created_at": "2025-05-25T10:30:00Z",
  "last_updated": "2025-05-25T11:45:00Z",
  "interactions": [
    {
      "id": "int-456",
      "query": "Comment réserver un taxi boat?",
      "response": "Pour réserver un bateau-taxi...",
      "timestamp": "2025-05-25T10:30:00Z",
      "sources": [
        {
          "title": "Guide de réservation",
          "relevance_score": 0.92
        }
      ]
    },
    {
      "id": "int-457",
      "query": "Quels sont les tarifs?",
      "response": "Les tarifs de Commodore Taxi Boat varient...",
      "timestamp": "2025-05-25T11:45:00Z",
      "sources": [
        {
          "title": "Grille tarifaire",
          "relevance_score": 0.95
        }
      ]
    }
  ]
}
```

### 1.3. Recherche simple dans la base de connaissances
- **Endpoint**: GET /api/rag/search/?query=reservation&limit=5
- **Description**: Effectuer une recherche sémantique dans la base de connaissances sans génération de réponse
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "results": [
    {
      "id": "doc-123",
      "title": "Guide de réservation",
      "url": "/documentation/reservation",
      "content_snippet": "La réservation d'un taxi boat se fait en trois étapes simples...",
      "relevance_score": 0.92
    },
    {
      "id": "doc-124",
      "title": "FAQ - Réservations",
      "url": "/documentation/faq/reservations",
      "content_snippet": "Questions fréquentes sur le processus de réservation...",
      "relevance_score": 0.85
    }
  ],
  "total_results": 24,
  "processing_time": 0.8
}
```

## 2. Gestion du feedback

### 2.1. Soumettre un feedback sur une réponse
- **Endpoint**: POST /api/rag/feedback/
- **Description**: Soumettre un retour d'utilisateur sur la qualité d'une réponse
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "interaction_id": "int-456",
  "rating": 4,
  "comment": "Réponse claire et précise, mais il manque l'information sur les délais de réservation",
  "helpful": true,
  "tags": ["reservation", "informations_manquantes"]
}
```
- **Response (201 Created)**:
```json
{
  "id": "feedback-789",
  "interaction_id": "int-456",
  "created_at": "2025-05-25T12:30:00Z",
  "status": "received"
}
```

### 2.2. Récupérer les statistiques de feedback
- **Endpoint**: GET /api/rag/feedback/stats/
- **Description**: Obtenir des statistiques sur les feedbacks des utilisateurs
- **Auth Required**: Oui (JWT Token + Admin)
- **Response (200 OK)**:
```json
{
  "total_feedbacks": 1245,
  "average_rating": 4.2,
  "helpful_percentage": 87,
  "common_tags": [
    {
      "tag": "reservation",
      "count": 342
    },
    {
      "tag": "tarifs",
      "count": 256
    }
  ],
  "rating_distribution": {
    "5": 560,
    "4": 412,
    "3": 180,
    "2": 65,
    "1": 28
  }
}
```

## 3. Support hors ligne

### 3.1. Récupérer les données pour le mode hors ligne
- **Endpoint**: GET /api/rag/offline/bundle/?profile=CLIENT
- **Description**: Télécharger un ensemble de données pour permettre le fonctionnement en mode hors ligne
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "version": "2025.05.25.1",
  "created_at": "2025-05-25T00:00:00Z",
  "bundle_size_kb": 1250,
  "faqs": [
    {
      "question": "Comment réserver un taxi boat?",
      "answer": "Pour réserver un bateau-taxi, téléchargez l'application, créez un compte, et indiquez votre lieu de prise en charge et destination.",
      "category": "reservation",
      "priority": 1
    },
    {
      "question": "Quels sont les tarifs?",
      "answer": "Les tarifs varient selon la distance et la durée. Consultez l'application pour un devis instantané.",
      "category": "tarifs",
      "priority": 2
    }
  ],
  "embeddings": {
    "format": "float32",
    "dimensions": 384,
    "data": "base64-encoded-binary-data"
  }
}
```

### 3.2. Récupérer uniquement les FAQ
- **Endpoint**: GET /api/rag/offline/faqs/?profile=CLIENT&limit=5
- **Description**: Récupérer uniquement les FAQ pour le support hors ligne
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "version": "2025.05.25.1",
  "total_faqs": 50,
  "faqs": [
    {
      "question": "Comment réserver un taxi boat?",
      "answer": "Pour réserver un bateau-taxi, téléchargez l'application, créez un compte, et indiquez votre lieu de prise en charge et destination.",
      "category": "reservation",
      "priority": 1
    },
    {
      "question": "Quels sont les tarifs?",
      "answer": "Les tarifs varient selon la distance et la durée. Consultez l'application pour un devis instantané.",
      "category": "tarifs",
      "priority": 2
    }
  ]
}
```

## Fonctionnalités implémentées

1. ✅ **Chatbot RAG** : Système de chatbot basé sur la Retrieval Augmented Generation avec réponses contextuelles de haute qualité.

2. ✅ **Recherche sémantique** : Recherche intelligente dans la base de connaissances utilisant des embeddings vectoriels.

3. ✅ **Gestion des sessions** : Maintien de l'historique des conversations pour des réponses contextuelles.

4. ✅ **Système de feedback** : Collecte et analyse des retours utilisateurs pour amélioration continue.

5. ✅ **Support hors ligne** : Fourniture de données pour permettre un fonctionnement basique en mode hors connexion.

## Fonctionnalités à implémenter

1. **Amélioration multilingue** : Support complet pour d'autres langues que le français.

2. **Personnalisation avancée** : Adaptation plus fine des réponses selon le type d'utilisateur et son historique.

3. **Intégration d'images** : Capacité à répondre à des questions basées sur des images (document scanning, photos de bateaux, etc.).

4. **Optimisation des performances** : Réduction du temps de réponse à moins de 2 secondes.

5. **Auto-apprentissage** : Système automatique d'amélioration basé sur les feedbacks utilisateurs sans intervention humaine.
