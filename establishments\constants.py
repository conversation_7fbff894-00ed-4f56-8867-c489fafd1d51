"""
Constantes pour l'application establishments.
"""

# Types d'établissements
ESTABLISHMENT_TYPES = [
    ('RESTAURANT', 'Restaurant'),
    ('HOTEL', 'Hôtel'),
    ('PRIVATE_BEACH', 'Plage privée'),
]

# Statuts des demandes de navettes
SHUTTLE_REQUEST_STATUS = [
    ('PENDING', 'En attente'),
    ('ACCEPTED', 'Acceptée'),
    ('REJECTED', 'Refusée'),
    ('IN_PROGRESS', 'En cours'),
    ('COMPLETED', 'Terminée'),
    ('CANCELLED', 'Annulée'),
]

# Types de notifications pour les établissements
ESTABLISHMENT_NOTIFICATION_TYPES = [
    ('SHUTTLE_REQUEST', 'Nouvelle demande de navette'),
    ('SHUTTLE_ACCEPTED', 'Navette acceptée'),
    ('SHUTTLE_REJECTED', 'Navette refusée'),
    ('SHUTTLE_COMPLETED', 'Navette terminée'),
    ('BOATMAN_REGISTERED', 'Nouveau batelier enregistré'),
    ('WALLET_LOW_BALANCE', 'Solde faible'),
    ('PAYMENT_RECEIVED', 'Paiement reçu'),
]

# Méthodes de paiement
PAYMENT_METHODS = [
    ('CARD', 'Carte bancaire'),
    ('BANK_TRANSFER', 'Virement bancaire'),
    ('PAYPAL', 'PayPal'),
    ('WALLET', 'Portefeuille'),
]

# Statuts de disponibilité
AVAILABILITY_STATUS = [
    ('AVAILABLE', 'Disponible'),
    ('BUSY', 'Occupé'),
    ('OFFLINE', 'Hors ligne'),
    ('MAINTENANCE', 'Maintenance'),
]

# Types de bateaux par défaut
DEFAULT_BOAT_TYPES = [
    ('classic', 'Classique'),
    ('speedboat', 'Hors-bord'),
    ('yacht', 'Yacht'),
    ('catamaran', 'Catamaran'),
]

# Paramètres par défaut pour les nouveaux bateliers
DEFAULT_BOATMAN_SETTINGS = {
    'rate_per_km': 25.00,
    'rate_per_hour': 50.00,
    'boat_capacity': 6,
    'boat_type': 'classic',
    'availability_status': 'AVAILABLE',
    'zone_radius': 20,
}

# Paramètres d'email
EMAIL_SETTINGS = {
    'welcome_subject_template': "Bienvenue chez {establishment_name} - Vos identifiants Commodore",
    'password_length': 8,
    'password_characters': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
}

# Limites et validations
VALIDATION_LIMITS = {
    'max_boatmen_per_establishment': 50,
    'max_shuttles_per_day': 20,
    'min_password_length': 8,
    'max_experience_length': 500,
    'max_license_number_length': 50,
}

# Messages d'erreur standardisés
ERROR_MESSAGES = {
    'ACCESS_DENIED': 'Accès refusé - Seuls les établissements peuvent accéder à cette ressource',
    'BOATMAN_NOT_FOUND': 'Batelier non trouvé',
    'SHUTTLE_REQUEST_NOT_FOUND': 'Demande de navette non trouvée',
    'INVALID_AMOUNT': 'Montant invalide',
    'INSUFFICIENT_FUNDS': 'Fonds insuffisants',
    'EMAIL_ALREADY_EXISTS': 'Un utilisateur avec cet email existe déjà',
    'REQUIRED_FIELDS_MISSING': 'Champs requis manquants',
    'WALLET_NOT_FOUND': 'Portefeuille non trouvé',
    'EMAIL_SENDING_FAILED': 'Erreur lors de l\'envoi de l\'email',
    'CAPTAIN_NOT_AVAILABLE': 'Capitaine non disponible',
    'BOAT_NOT_AVAILABLE': 'Bateau non disponible',
}

# Messages de succès standardisés
SUCCESS_MESSAGES = {
    'BOATMAN_REGISTERED': 'Batelier enregistré avec succès',
    'SHUTTLE_ACCEPTED': 'Demande de navette acceptée',
    'SHUTTLE_REJECTED': 'Demande de navette rejetée',
    'FUNDS_ADDED': 'Fonds ajoutés avec succès',
    'AVAILABILITY_UPDATED': 'Disponibilité mise à jour',
    'EMAIL_SENT': 'Email envoyé avec succès',
}

# Configuration des horaires par défaut
DEFAULT_OPENING_HOURS = {
    'monday': {'open': '08:00', 'close': '18:00', 'closed': False},
    'tuesday': {'open': '08:00', 'close': '18:00', 'closed': False},
    'wednesday': {'open': '08:00', 'close': '18:00', 'closed': False},
    'thursday': {'open': '08:00', 'close': '18:00', 'closed': False},
    'friday': {'open': '08:00', 'close': '18:00', 'closed': False},
    'saturday': {'open': '09:00', 'close': '17:00', 'closed': False},
    'sunday': {'open': '09:00', 'close': '17:00', 'closed': False}
}

# Services par défaut
DEFAULT_SERVICES = [
    'Navettes gratuites',
    'Transport aéroport',
    'Excursions',
    'Transport événements',
]

# Réseaux sociaux par défaut
DEFAULT_SOCIAL_MEDIA = {
    'facebook': '',
    'instagram': '',
    'twitter': '',
    'website': ''
}

# Codes d'erreur HTTP personnalisés
HTTP_ERROR_CODES = {
    'ESTABLISHMENT_ACCESS_REQUIRED': 4001,
    'BOATMAN_REGISTRATION_FAILED': 4002,
    'SHUTTLE_MANAGEMENT_ERROR': 4003,
    'WALLET_OPERATION_FAILED': 4004,
    'EMAIL_DELIVERY_FAILED': 5001,
}

# Paramètres de pagination
PAGINATION_SETTINGS = {
    'default_page_size': 20,
    'max_page_size': 100,
    'page_size_query_param': 'limit',
    'page_query_param': 'page',
}

# Paramètres de filtrage
FILTER_SETTINGS = {
    'shuttle_status_filters': [
        'En attente',
        'À venir', 
        'En cours',
        'Terminé',
        'Annulé'
    ],
    'payment_type_filters': [
        'SHUTTLE_PAYMENT',
        'WALLET_RECHARGE',
        'REFUND'
    ],
    'date_range_formats': [
        '%Y-%m-%d',
        '%Y-%m-%dT%H:%M:%S',
        '%Y-%m-%dT%H:%M:%SZ'
    ]
}
