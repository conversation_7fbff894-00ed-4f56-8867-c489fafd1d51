"""
Signaux pour l'application boatman.
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model

from accounts.models import Captain
from trips.models import Trip
from payments.models import Payment, Wallet
from notifications.services import create_notification

User = get_user_model()


@receiver(post_save, sender=Captain)
def create_captain_wallet(sender, instance, created, **kwargs):
    """
    Créer automatiquement un portefeuille pour un nouveau capitaine.
    """
    if created:
        # Créer le portefeuille
        Wallet.objects.get_or_create(user=instance.user)


@receiver(post_save, sender=Trip)
def notify_captain_trip_updates(sender, instance, created, **kwargs):
    """
    Notifier le capitaine des mises à jour de courses.
    """
    if instance.captain:
        captain_user = instance.captain.user

        if created:
            # Nouvelle course assignée
            create_notification(
                user=captain_user,
                title="Nouvelle course assignée",
                message=f"Course vers {instance.end_location} le {instance.scheduled_start_time.strftime('%d/%m/%Y à %H:%M')}",
                notification_type="TRIP_ASSIGNED"
            )

        else:
            # Mise à jour de course
            if instance.status == 'CANCELLED':
                create_notification(
                    user=captain_user,
                    title="Course annulée",
                    message=f"La course vers {instance.end_location} a été annulée",
                    notification_type="TRIP_CANCELLED",
                    related_object_id=instance.id
                )


@receiver(post_save, sender=Payment)
def notify_captain_payments(sender, instance, created, **kwargs):
    """
    Notifier le capitaine des paiements reçus.
    """
    if instance.user and hasattr(instance.user, 'captain'):
        captain_user = instance.user

        if created and instance.type in [Payment.PaymentType.TRIP, Payment.PaymentType.TIP]:
            if instance.status == Payment.Status.COMPLETED:
                # Paiement reçu
                payment_type = "Pourboire" if instance.type == Payment.PaymentType.TIP else "Paiement de course"

                create_notification(
                    user=captain_user,
                    title=f"{payment_type} reçu",
                    message=f"Vous avez reçu {instance.amount}€",
                    notification_type="PAYMENT_RECEIVED",
                    related_object_id=instance.id
                )

        elif not created and instance.type == Payment.PaymentType.WITHDRAWAL:
            if instance.status == Payment.Status.COMPLETED:
                # Retrait traité
                create_notification(
                    user=captain_user,
                    title="Retrait traité",
                    message=f"Votre retrait de {instance.amount}€ a été traité",
                    notification_type="WITHDRAWAL_COMPLETED",
                    related_object_id=instance.id
                )
            elif instance.status == Payment.Status.FAILED:
                # Retrait échoué
                create_notification(
                    user=captain_user,
                    title="Retrait échoué",
                    message=f"Votre retrait de {instance.amount}€ a échoué. Veuillez vérifier vos informations de paiement.",
                    notification_type="WITHDRAWAL_FAILED",
                    related_object_id=instance.id
                )


@receiver(pre_save, sender=Captain)
def track_availability_changes(sender, instance, **kwargs):
    """
    Suivre les changements de disponibilité du capitaine.
    """
    if instance.pk:  # Mise à jour d'un capitaine existant
        try:
            old_instance = Captain.objects.get(pk=instance.pk)

            # Vérifier si la disponibilité a changé
            if old_instance.availability_status != instance.availability_status:
                # Mettre à jour les bateaux associés
                if instance.availability_status == 'AVAILABLE':
                    instance.boats.update(is_available=True)
                else:
                    instance.boats.update(is_available=False)

                # Notifier si le capitaine devient indisponible et a des courses en attente
                if instance.availability_status != 'AVAILABLE':
                    pending_trips = Trip.objects.filter(
                        captain=instance,
                        status__in=['PENDING', 'ACCEPTED']
                    ).count()

                    if pending_trips > 0:
                        create_notification(
                            user=instance.user,
                            title="Courses en attente",
                            message=f"Vous avez {pending_trips} course{'s' if pending_trips > 1 else ''} en attente. Pensez à les gérer avant de vous mettre hors ligne.",
                            notification_type="PENDING_TRIPS_WARNING",
                            related_object_id=None
                        )

        except Captain.DoesNotExist:
            pass


@receiver(post_save, sender=User)
def setup_captain_defaults(sender, instance, created, **kwargs):
    """
    Configurer les paramètres par défaut pour un nouveau capitaine.
    """
    if created and hasattr(instance, 'captain'):
        captain = instance.captain

        # Configurer les métadonnées par défaut
        if not hasattr(captain, 'metadata') or not captain.metadata:
            captain.metadata = {
                'email_notifications': True,
                'sms_notifications': True,
                'push_notifications': True,
                'auto_accept_shuttles': False,
                'preferred_working_hours': {
                    'start': '08:00',
                    'end': '20:00'
                },
                'preferred_zones': [],
                'languages': ['fr'],
                'emergency_contact': {}
            }
            captain.save()


def send_trip_reminder_notifications():
    """
    Fonction utilitaire pour envoyer des rappels de courses.
    À appeler via une tâche cron ou Celery.
    """
    from django.utils import timezone
    from datetime import timedelta

    # Courses qui commencent dans 30 minutes
    upcoming_time = timezone.now() + timedelta(minutes=30)
    upcoming_trips = Trip.objects.filter(
        status='ACCEPTED',
        scheduled_start_time__lte=upcoming_time,
        scheduled_start_time__gt=timezone.now()
    ).select_related('captain__user')

    for trip in upcoming_trips:
        if trip.captain:
            create_notification(
                user=trip.captain.user,
                title="Course dans 30 minutes",
                message=f"Votre course vers {trip.end_location} commence dans 30 minutes",
                notification_type="TRIP_REMINDER",
                related_object_id=trip.id
            )


def send_daily_summary_notifications():
    """
    Fonction utilitaire pour envoyer un résumé quotidien.
    À appeler via une tâche cron quotidienne.
    """
    from django.utils import timezone
    from datetime import timedelta
    from django.db.models import Sum, Count

    today = timezone.now().date()
    yesterday = today - timedelta(days=1)

    # Pour chaque capitaine actif
    active_captains = Captain.objects.filter(
        is_available=True,
        availability_status='AVAILABLE'
    ).select_related('user')

    for captain in active_captains:
        # Statistiques d'hier
        yesterday_trips = Trip.objects.filter(
            captain=captain,
            scheduled_start_time__date=yesterday,
            status='COMPLETED'
        )

        trips_count = yesterday_trips.count()

        if trips_count > 0:
            # Calculer les gains d'hier
            yesterday_earnings = Payment.objects.filter(
                user=captain.user,
                type__in=[Payment.PaymentType.TRIP, Payment.PaymentType.TIP],
                status=Payment.Status.COMPLETED,
                created_at__date=yesterday
            ).aggregate(total=Sum('amount'))['total'] or 0

            create_notification(
                user=captain.user,
                title="Résumé d'hier",
                message=f"Vous avez effectué {trips_count} course{'s' if trips_count > 1 else ''} et gagné {yesterday_earnings}€",
                notification_type="DAILY_SUMMARY",
                related_object_id=None
            )
