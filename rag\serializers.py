from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from .models import Document, DocumentChunk, ChatSession, ChatMessage


class DocumentChunkSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour les chunks de documents.
    """
    class Meta:
        model = DocumentChunk
        fields = ['id', 'document', 'content', 'chunk_index', 'embedding_generated']
        read_only_fields = ['id', 'document', 'chunk_index', 'embedding_generated']


class DocumentSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour les documents.
    """
    chunks = DocumentChunkSerializer(many=True, read_only=True)

    class Meta:
        model = Document
        fields = ['id', 'title', 'content', 'source', 'url', 'category', 'tags',
                  'created_at', 'updated_at', 'embedding_generated', 'chunks']
        read_only_fields = ['id', 'created_at', 'updated_at', 'embedding_generated', 'chunks']


class DocumentCreateSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour la création de documents.
    """
    class Meta:
        model = Document
        fields = ['title', 'content', 'source', 'url', 'category', 'tags']


class ChatMessageSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour les messages de chat.
    """
    retrieved_documents = serializers.SerializerMethodField()

    class Meta:
        model = ChatMessage
        fields = ['id', 'role', 'content', 'created_at', 'retrieved_documents']
        read_only_fields = ['id', 'created_at', 'retrieved_documents']

    def get_retrieved_documents(self, obj):
        """
        Retourne les informations sur les documents récupérés pour ce message.
        """
        documents = []
        for chunk in obj.retrieved_documents.all():
            documents.append({
                'document_id': str(chunk.document.id),
                'document_title': chunk.document.title,
                'chunk_id': str(chunk.id),
                'chunk_index': chunk.chunk_index,
                'relevance': 'high'  # À améliorer avec un score réel
            })
        return documents


class ChatSessionSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour les sessions de chat.
    """
    messages = ChatMessageSerializer(many=True, read_only=True)
    message_count = serializers.SerializerMethodField()

    class Meta:
        model = ChatSession
        fields = ['id', 'title', 'user', 'created_at', 'updated_at', 'message_count', 'messages']
        read_only_fields = ['id', 'created_at', 'updated_at', 'message_count', 'messages']

    def get_message_count(self, obj):
        return obj.messages.count()


class ChatSessionCreateSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour la création de sessions de chat.
    """
    class Meta:
        model = ChatSession
        fields = ['id', 'title', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class ChatMessageCreateSerializer(serializers.Serializer):
    """
    Sérialiseur pour la création de messages de chat.
    """
    content = serializers.CharField(required=True)

    def validate_content(self, value):
        """
        Valide que le contenu du message n'est pas vide.
        """
        if not value.strip():
            raise serializers.ValidationError(_("Le contenu du message ne peut pas être vide."))
        return value
