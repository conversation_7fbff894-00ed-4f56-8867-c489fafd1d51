"""
Script pour extraire les VRAIES réponses JSON de tous les endpoints du système Commodore.
Ce script teste chaque endpoint et capture les réponses réelles pour créer la documentation.
"""

import os
import django
import requests
import json
from datetime import datetime, timedelta
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token

User = get_user_model()

class APIResponseExtractor:
    def __init__(self):
        self.base_url = 'http://127.0.0.1:8000'
        self.tokens = {}
        self.responses = {}

    def get_auth_headers(self, user_type):
        """Récupérer les headers d'authentification pour un type d'utilisateur"""
        if user_type not in self.tokens:
            return {}

        return {
            'Authorization': f'Token {self.tokens[user_type]}',
            'Content-Type': 'application/json'
        }

    def make_request(self, method, endpoint, user_type='client', data=None):
        """Faire une requête API et capturer la réponse"""
        url = f"{self.base_url}{endpoint}"
        headers = self.get_auth_headers(user_type)

        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data)
            elif method.upper() == 'PATCH':
                response = requests.patch(url, headers=headers, json=data)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers)
            else:
                return None

            return {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'json': response.json() if response.content else None,
                'text': response.text if not response.content else None
            }
        except Exception as e:
            return {
                'error': str(e),
                'status_code': None
            }

    def setup_test_accounts(self):
        """Configurer les comptes de test et récupérer leurs tokens"""
        print("🔧 Configuration des comptes de test...")

        # Récupérer les tokens existants
        try:
            client_user = User.objects.get(email='<EMAIL>')
            client_token = Token.objects.get_or_create(user=client_user)[0]
            self.tokens['client'] = client_token.key
            print(f"  ✅ Client token: {client_token.key[:20]}...")
        except:
            print("  ❌ Pas de compte client trouvé")

        try:
            captain_user = User.objects.get(email='<EMAIL>')
            captain_token = Token.objects.get_or_create(user=captain_user)[0]
            self.tokens['captain'] = captain_token.key
            print(f"  ✅ Captain token: {captain_token.key[:20]}...")
        except:
            print("  ❌ Pas de compte captain trouvé")

        try:
            establishment_user = User.objects.get(email='<EMAIL>')
            establishment_token = Token.objects.get_or_create(user=establishment_user)[0]
            self.tokens['establishment'] = establishment_token.key
            print(f"  ✅ Establishment token: {establishment_token.key[:20]}...")
        except:
            print("  ❌ Pas de compte establishment trouvé")

    def test_authentication_endpoints(self):
        """Tester tous les endpoints d'authentification"""
        print("\n🔐 TEST ENDPOINTS AUTHENTIFICATION")
        print("-" * 40)

        # 1. Test inscription
        register_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "name": "Test User",
            "user_type": "CLIENT"
        }

        response = self.make_request('POST', '/api/register/', data=register_data)
        self.responses['register'] = {
            'endpoint': 'POST /api/register/',
            'description': 'Inscription d\'un nouvel utilisateur',
            'request_body': register_data,
            'response': response
        }
        print(f"  📝 Register: {response['status_code'] if response else 'ERROR'}")

        # 2. Test connexion
        login_data = {
            "email": "<EMAIL>",
            "password": "TestClient123!"
        }

        response = self.make_request('POST', '/api/login/', data=login_data)
        self.responses['login'] = {
            'endpoint': 'POST /api/login/',
            'description': 'Connexion utilisateur',
            'request_body': login_data,
            'response': response
        }
        print(f"  🔑 Login: {response['status_code'] if response else 'ERROR'}")

    def test_wallet_endpoints(self):
        """Tester tous les endpoints de portefeuille"""
        print("\n💰 TEST ENDPOINTS PORTEFEUILLE")
        print("-" * 40)

        # 1. Consulter portefeuille
        response = self.make_request('GET', '/api/payments/wallet/', 'client')
        self.responses['wallet_detail'] = {
            'endpoint': 'GET /api/payments/wallet/',
            'description': 'Consulter les détails du portefeuille',
            'response': response
        }
        print(f"  👛 Wallet Detail: {response['status_code'] if response else 'ERROR'}")

        # 2. Recharger portefeuille
        recharge_data = {
            "amount": 100.00,
            "payment_method_id": "pm_card_visa"
        }

        response = self.make_request('POST', '/api/payments/wallet/recharge/', 'client', recharge_data)
        self.responses['wallet_recharge'] = {
            'endpoint': 'POST /api/payments/wallet/recharge/',
            'description': 'Recharger le portefeuille avec Stripe',
            'request_body': recharge_data,
            'response': response
        }
        print(f"  💳 Wallet Recharge: {response['status_code'] if response else 'ERROR'}")

    def test_trip_endpoints(self):
        """Tester tous les endpoints de courses"""
        print("\n🚤 TEST ENDPOINTS COURSES")
        print("-" * 40)

        # 1. Créer demande course simple
        departure_time = datetime.now() + timedelta(hours=2)
        trip_data = {
            'departure_location': {
                'city_name': 'Port de Cannes',
                'coordinates': {'latitude': 43.5528, 'longitude': 7.0174},
                'timestamp': departure_time.isoformat()
            },
            'arrival_location': {
                'city_name': 'Îles de Lérins',
                'coordinates': {'latitude': 43.5184, 'longitude': 7.0457},
                'timestamp': departure_time.isoformat()
            },
            'departure_date': departure_time.date().isoformat(),
            'departure_time': departure_time.time().isoformat(),
            'passenger_count': 4,
            'boat_type': 'CLASSIC'
        }

        response = self.make_request('POST', '/api/trips/requests/simple/', 'client', trip_data)
        self.responses['create_simple_trip'] = {
            'endpoint': 'POST /api/trips/requests/simple/',
            'description': 'Créer une demande de course simple',
            'request_body': trip_data,
            'response': response
        }
        print(f"  🎯 Create Simple Trip: {response['status_code'] if response else 'ERROR'}")

        # Extraire l'ID de la demande pour les tests suivants
        trip_request_id = None
        quote_id = None
        if response and response.get('json') and response['json'].get('trip_request'):
            trip_request_id = response['json']['trip_request']['id']
            if response['json'].get('quotes'):
                quote_id = response['json']['quotes'][0]['id']

        # 2. Récupérer devis
        if trip_request_id:
            response = self.make_request('GET', f'/api/trips/requests/{trip_request_id}/', 'client')
            self.responses['get_trip_quotes'] = {
                'endpoint': f'GET /api/trips/requests/{trip_request_id}/',
                'description': 'Récupérer les devis pour une demande',
                'response': response
            }
            print(f"  📋 Get Trip Quotes: {response['status_code'] if response else 'ERROR'}")

        # 3. Accepter devis
        if quote_id:
            response = self.make_request('POST', f'/api/trips/quotes/{quote_id}/accept/', 'client')
            self.responses['accept_quote'] = {
                'endpoint': f'POST /api/trips/quotes/{quote_id}/accept/',
                'description': 'Accepter un devis et créer une course',
                'response': response
            }
            print(f"  ✅ Accept Quote: {response['status_code'] if response else 'ERROR'}")

    def test_shuttle_endpoints(self):
        """Tester tous les endpoints de navettes"""
        print("\n🚌 TEST ENDPOINTS NAVETTES")
        print("-" * 40)

        # 1. Créer demande navette
        shuttle_data = {
            'departure_location': {
                'city_name': 'Hôtel Paradise',
                'coordinates': {'latitude': 43.5528, 'longitude': 7.0174},
                'timestamp': (datetime.now() + timedelta(hours=2)).isoformat()
            },
            'arrival_location': {
                'city_name': 'Port de Cannes',
                'coordinates': {'latitude': 43.5184, 'longitude': 7.0457},
                'timestamp': (datetime.now() + timedelta(hours=2)).isoformat()
            },
            'passenger_count': 2,
            'establishment_id': 1,
            'notes': 'Navette pour clients de l\'hôtel'
        }

        response = self.make_request('POST', '/api/trips/requests/shuttle/', 'client', shuttle_data)
        self.responses['create_shuttle_request'] = {
            'endpoint': 'POST /api/trips/requests/shuttle/',
            'description': 'Créer une demande de navette gratuite',
            'request_body': shuttle_data,
            'response': response
        }
        print(f"  🚌 Create Shuttle: {response['status_code'] if response else 'ERROR'}")

        # 2. Établissement voit les demandes
        response = self.make_request('GET', '/api/establishments/shuttle-requests/', 'establishment')
        self.responses['get_shuttle_requests'] = {
            'endpoint': 'GET /api/establishments/shuttle-requests/',
            'description': 'Récupérer les demandes de navettes (établissement)',
            'response': response
        }
        print(f"  📋 Get Shuttle Requests: {response['status_code'] if response else 'ERROR'}")

    def test_accounts_endpoints(self):
        """Tester tous les endpoints de comptes"""
        print("\n👤 TEST ENDPOINTS COMPTES")
        print("-" * 40)

        # 1. Profil utilisateur
        response = self.make_request('GET', '/api/profile/', 'client')
        self.responses['user_profile'] = {
            'endpoint': 'GET /api/profile/',
            'description': 'Récupérer le profil utilisateur',
            'response': response
        }
        print(f"  👤 User Profile: {response['status_code'] if response else 'ERROR'}")

        # 2. Liste des capitaines
        response = self.make_request('GET', '/api/captains/', 'client')
        self.responses['captains_list'] = {
            'endpoint': 'GET /api/captains/',
            'description': 'Liste des capitaines disponibles',
            'response': response
        }
        print(f"  ⚓ Captains List: {response['status_code'] if response else 'ERROR'}")

        # 3. Liste des établissements
        response = self.make_request('GET', '/api/establishments/', 'client')
        self.responses['establishments_list'] = {
            'endpoint': 'GET /api/establishments/',
            'description': 'Liste des établissements',
            'response': response
        }
        print(f"  🏨 Establishments List: {response['status_code'] if response else 'ERROR'}")

    def test_boats_endpoints(self):
        """Tester tous les endpoints de bateaux"""
        print("\n⛵ TEST ENDPOINTS BATEAUX")
        print("-" * 40)

        # 1. Liste des bateaux
        response = self.make_request('GET', '/api/boats/', 'client')
        self.responses['boats_list'] = {
            'endpoint': 'GET /api/boats/',
            'description': 'Liste des bateaux disponibles',
            'response': response
        }
        print(f"  ⛵ Boats List: {response['status_code'] if response else 'ERROR'}")

        # 2. Bateaux disponibles
        response = self.make_request('GET', '/api/boats/available/', 'client')
        self.responses['available_boats'] = {
            'endpoint': 'GET /api/boats/available/',
            'description': 'Bateaux disponibles pour réservation',
            'response': response
        }
        print(f"  ✅ Available Boats: {response['status_code'] if response else 'ERROR'}")

    def test_establishment_endpoints(self):
        """Tester tous les endpoints d'établissements"""
        print("\n🏨 TEST ENDPOINTS ÉTABLISSEMENTS")
        print("-" * 40)

        # 1. Tableau de bord établissement
        response = self.make_request('GET', '/api/establishments/dashboard/', 'establishment')
        self.responses['establishment_dashboard'] = {
            'endpoint': 'GET /api/establishments/dashboard/',
            'description': 'Tableau de bord de l\'établissement',
            'response': response
        }
        print(f"  📊 Dashboard: {response['status_code'] if response else 'ERROR'}")

        # 2. Portefeuille établissement
        response = self.make_request('GET', '/api/establishments/wallet/', 'establishment')
        self.responses['establishment_wallet'] = {
            'endpoint': 'GET /api/establishments/wallet/',
            'description': 'Portefeuille de l\'établissement',
            'response': response
        }
        print(f"  💰 Wallet: {response['status_code'] if response else 'ERROR'}")

        # 3. Bateliers de l'établissement
        response = self.make_request('GET', '/api/establishments/boatmen/', 'establishment')
        self.responses['establishment_boatmen'] = {
            'endpoint': 'GET /api/establishments/boatmen/',
            'description': 'Liste des bateliers de l\'établissement',
            'response': response
        }
        print(f"  ⚓ Boatmen: {response['status_code'] if response else 'ERROR'}")

    def test_boatman_endpoints(self):
        """Tester tous les endpoints de bateliers"""
        print("\n🚤 TEST ENDPOINTS BATELIERS")
        print("-" * 40)

        # 1. Connexion batelier
        login_data = {
            "email": "<EMAIL>",
            "code": "123456"
        }

        response = self.make_request('POST', '/api/boatman/login/', data=login_data)
        self.responses['boatman_login'] = {
            'endpoint': 'POST /api/boatman/login/',
            'description': 'Connexion batelier avec code',
            'request_body': login_data,
            'response': response
        }
        print(f"  🔑 Boatman Login: {response['status_code'] if response else 'ERROR'}")

        # 2. Tableau de bord batelier
        response = self.make_request('GET', '/api/boatman/dashboard/', 'captain')
        self.responses['boatman_dashboard'] = {
            'endpoint': 'GET /api/boatman/dashboard/',
            'description': 'Tableau de bord du batelier',
            'response': response
        }
        print(f"  📊 Dashboard: {response['status_code'] if response else 'ERROR'}")

        # 3. Navettes du batelier
        response = self.make_request('GET', '/api/boatman/shuttles/', 'captain')
        self.responses['boatman_shuttles'] = {
            'endpoint': 'GET /api/boatman/shuttles/',
            'description': 'Navettes assignées au batelier',
            'response': response
        }
        print(f"  🚌 Shuttles: {response['status_code'] if response else 'ERROR'}")

    def test_payment_endpoints(self):
        """Tester tous les endpoints de paiements"""
        print("\n💳 TEST ENDPOINTS PAIEMENTS")
        print("-" * 40)

        # 1. Historique des transactions
        response = self.make_request('GET', '/api/payments/transactions/', 'client')
        self.responses['transactions_list'] = {
            'endpoint': 'GET /api/payments/transactions/',
            'description': 'Historique des transactions',
            'response': response
        }
        print(f"  📋 Transactions: {response['status_code'] if response else 'ERROR'}")

        # 2. Compensation carbone (nécessite un trip_id)
        carbon_data = {
            "payment_method_id": "pm_card_visa",
            "payment_method": "CARD"
        }

        response = self.make_request('POST', '/api/trips/1/carbon-compensation/', 'client', carbon_data)
        self.responses['carbon_compensation'] = {
            'endpoint': 'POST /api/trips/{trip_id}/carbon-compensation/',
            'description': 'Paiement compensation carbone',
            'request_body': carbon_data,
            'response': response
        }
        print(f"  🌱 Carbon Compensation: {response['status_code'] if response else 'ERROR'}")

        # 3. Pourboire
        tip_data = {
            "amount": 5.00,
            "payment_method_id": "pm_card_visa",
            "payment_method": "CARD"
        }

        response = self.make_request('POST', '/api/trips/1/tip/', 'client', tip_data)
        self.responses['tip_payment'] = {
            'endpoint': 'POST /api/trips/{trip_id}/tip/',
            'description': 'Paiement pourboire',
            'request_body': tip_data,
            'response': response
        }
        print(f"  💰 Tip Payment: {response['status_code'] if response else 'ERROR'}")

    def test_notifications_endpoints(self):
        """Tester tous les endpoints de notifications"""
        print("\n🔔 TEST ENDPOINTS NOTIFICATIONS")
        print("-" * 40)

        # 1. Liste des notifications
        response = self.make_request('GET', '/api/notifications/', 'client')
        self.responses['notifications_list'] = {
            'endpoint': 'GET /api/notifications/',
            'description': 'Liste des notifications utilisateur',
            'response': response
        }
        print(f"  🔔 Notifications: {response['status_code'] if response else 'ERROR'}")

        # 2. Marquer comme lu
        response = self.make_request('POST', '/api/notifications/1/mark-read/', 'client')
        self.responses['mark_notification_read'] = {
            'endpoint': 'POST /api/notifications/{id}/mark-read/',
            'description': 'Marquer une notification comme lue',
            'response': response
        }
        print(f"  ✅ Mark Read: {response['status_code'] if response else 'ERROR'}")

    def test_chat_endpoints(self):
        """Tester tous les endpoints de chat"""
        print("\n💬 TEST ENDPOINTS CHAT")
        print("-" * 40)

        # 1. Sessions de chat
        response = self.make_request('GET', '/api/chat/rooms/', 'client')
        self.responses['chat_rooms'] = {
            'endpoint': 'GET /api/chat/rooms/',
            'description': 'Liste des salons de chat',
            'response': response
        }
        print(f"  💬 Chat Rooms: {response['status_code'] if response else 'ERROR'}")

        # 2. Chatbot
        chatbot_data = {
            "message": "Bonjour, comment réserver une course ?",
            "session_id": "test-session-123"
        }

        response = self.make_request('POST', '/api/chat/chatbot/message/', 'client', chatbot_data)
        self.responses['chatbot_message'] = {
            'endpoint': 'POST /api/chat/chatbot/message/',
            'description': 'Envoyer un message au chatbot',
            'request_body': chatbot_data,
            'response': response
        }
        print(f"  🤖 Chatbot: {response['status_code'] if response else 'ERROR'}")

    def test_reviews_endpoints(self):
        """Tester tous les endpoints d'avis"""
        print("\n⭐ TEST ENDPOINTS AVIS")
        print("-" * 40)

        # 1. Créer un avis
        review_data = {
            "trip_id": 1,
            "rating": 5,
            "comment": "Excellent service, capitaine très professionnel !",
            "captain_rating": 5,
            "boat_rating": 4
        }

        response = self.make_request('POST', '/api/reviews/', 'client', review_data)
        self.responses['create_review'] = {
            'endpoint': 'POST /api/reviews/',
            'description': 'Créer un avis sur une course',
            'request_body': review_data,
            'response': response
        }
        print(f"  ⭐ Create Review: {response['status_code'] if response else 'ERROR'}")

        # 2. Liste des avis
        response = self.make_request('GET', '/api/reviews/', 'client')
        self.responses['reviews_list'] = {
            'endpoint': 'GET /api/reviews/',
            'description': 'Liste des avis',
            'response': response
        }
        print(f"  📋 Reviews List: {response['status_code'] if response else 'ERROR'}")

    def test_trip_management_endpoints(self):
        """Tester tous les endpoints de gestion des courses"""
        print("\n🎯 TEST ENDPOINTS GESTION COURSES")
        print("-" * 40)

        # 1. Démarrer une course (capitaine)
        response = self.make_request('POST', '/api/trips/1/start/', 'captain')
        self.responses['start_trip'] = {
            'endpoint': 'POST /api/trips/{trip_id}/start/',
            'description': 'Démarrer une course (capitaine)',
            'response': response
        }
        print(f"  🚀 Start Trip: {response['status_code'] if response else 'ERROR'}")

        # 2. Terminer une course
        response = self.make_request('POST', '/api/trips/1/complete/', 'captain')
        self.responses['complete_trip'] = {
            'endpoint': 'POST /api/trips/{trip_id}/complete/',
            'description': 'Terminer une course (capitaine)',
            'response': response
        }
        print(f"  ✅ Complete Trip: {response['status_code'] if response else 'ERROR'}")

        # 3. Accepter une course
        response = self.make_request('POST', '/api/trips/1/accept/', 'captain')
        self.responses['accept_trip'] = {
            'endpoint': 'POST /api/trips/{trip_id}/accept/',
            'description': 'Accepter une course assignée',
            'response': response
        }
        print(f"  ✅ Accept Trip: {response['status_code'] if response else 'ERROR'}")

        # 4. Annuler une course
        response = self.make_request('POST', '/api/trips/1/cancel/', 'client')
        self.responses['cancel_trip'] = {
            'endpoint': 'POST /api/trips/{trip_id}/cancel/',
            'description': 'Annuler une course',
            'response': response
        }
        print(f"  ❌ Cancel Trip: {response['status_code'] if response else 'ERROR'}")

        # 5. Statut détaillé d'une course
        response = self.make_request('GET', '/api/trips/1/status/', 'client')
        self.responses['trip_status'] = {
            'endpoint': 'GET /api/trips/{trip_id}/status/',
            'description': 'Statut détaillé d\'une course',
            'response': response
        }
        print(f"  📊 Trip Status: {response['status_code'] if response else 'ERROR'}")

        # 6. Suivi temps réel
        response = self.make_request('GET', '/api/trips/1/tracking/', 'client')
        self.responses['trip_tracking'] = {
            'endpoint': 'GET /api/trips/{trip_id}/tracking/',
            'description': 'Suivi temps réel d\'une course',
            'response': response
        }
        print(f"  📍 Trip Tracking: {response['status_code'] if response else 'ERROR'}")

    def test_captain_endpoints(self):
        """Tester tous les endpoints spécifiques aux capitaines"""
        print("\n⚓ TEST ENDPOINTS CAPITAINES")
        print("-" * 40)

        # 1. Courses en attente pour le capitaine
        response = self.make_request('GET', '/api/trips/pending/', 'captain')
        self.responses['captain_pending_trips'] = {
            'endpoint': 'GET /api/trips/pending/',
            'description': 'Courses en attente pour le capitaine',
            'response': response
        }
        print(f"  ⏳ Pending Trips: {response['status_code'] if response else 'ERROR'}")

        # 2. Historique des courses du capitaine
        response = self.make_request('GET', '/api/trips/captain/history/', 'captain')
        self.responses['captain_trip_history'] = {
            'endpoint': 'GET /api/trips/captain/history/',
            'description': 'Historique des courses du capitaine',
            'response': response
        }
        print(f"  📋 Trip History: {response['status_code'] if response else 'ERROR'}")

        # 3. Tableau de bord capitaine
        response = self.make_request('GET', '/api/trips/captain/dashboard/', 'captain')
        self.responses['captain_dashboard'] = {
            'endpoint': 'GET /api/trips/captain/dashboard/',
            'description': 'Tableau de bord du capitaine',
            'response': response
        }
        print(f"  📊 Dashboard: {response['status_code'] if response else 'ERROR'}")

        # 4. Disponibilité du capitaine
        response = self.make_request('GET', '/api/trips/captain/availability/', 'captain')
        self.responses['captain_availability'] = {
            'endpoint': 'GET /api/trips/captain/availability/',
            'description': 'Disponibilité du capitaine',
            'response': response
        }
        print(f"  ✅ Availability: {response['status_code'] if response else 'ERROR'}")

        # 5. Portefeuille capitaine
        response = self.make_request('GET', '/api/payments/wallet/captain/', 'captain')
        self.responses['captain_wallet'] = {
            'endpoint': 'GET /api/payments/wallet/captain/',
            'description': 'Portefeuille du capitaine',
            'response': response
        }
        print(f"  💰 Captain Wallet: {response['status_code'] if response else 'ERROR'}")

        # 6. Retrait de fonds
        withdraw_data = {
            "amount": 50.00,
            "bank_account": "***************************"
        }

        response = self.make_request('POST', '/api/payments/withdraw/', 'captain', withdraw_data)
        self.responses['withdraw_funds'] = {
            'endpoint': 'POST /api/payments/withdraw/',
            'description': 'Retrait de fonds du portefeuille',
            'request_body': withdraw_data,
            'response': response
        }
        print(f"  💸 Withdraw: {response['status_code'] if response else 'ERROR'}")

        # 7. Historique des revenus
        response = self.make_request('GET', '/api/payments/earnings/', 'captain')
        self.responses['trip_earnings'] = {
            'endpoint': 'GET /api/payments/earnings/',
            'description': 'Historique des revenus de courses',
            'response': response
        }
        print(f"  💰 Earnings: {response['status_code'] if response else 'ERROR'}")

    def test_qr_endpoints(self):
        """Tester tous les endpoints de QR codes"""
        print("\n📱 TEST ENDPOINTS QR CODES")
        print("-" * 40)

        # 1. Vérification QR code
        qr_data = {
            "qr_code": "QR_TEST_123456",
            "trip_id": 1
        }

        response = self.make_request('POST', '/api/trips/verify-qr/', 'captain', qr_data)
        self.responses['verify_qr'] = {
            'endpoint': 'POST /api/trips/verify-qr/',
            'description': 'Vérification d\'un QR code',
            'request_body': qr_data,
            'response': response
        }
        print(f"  📱 Verify QR: {response['status_code'] if response else 'ERROR'}")

        # 2. Génération QR code
        response = self.make_request('POST', '/api/trips/1/generate-qr/', 'client')
        self.responses['generate_qr'] = {
            'endpoint': 'POST /api/trips/{trip_id}/generate-qr/',
            'description': 'Générer un QR code pour une course',
            'response': response
        }
        print(f"  🔗 Generate QR: {response['status_code'] if response else 'ERROR'}")

    def test_hourly_trip_endpoints(self):
        """Tester les endpoints de courses horaires"""
        print("\n⏰ TEST ENDPOINTS COURSES HORAIRES")
        print("-" * 40)

        # 1. Créer demande course horaire
        departure_time = datetime.now() + timedelta(hours=3)
        hourly_data = {
            'departure_location': {
                'city_name': 'Port de Saint-Tropez',
                'coordinates': {'latitude': 43.2677, 'longitude': 6.6407},
                'timestamp': departure_time.isoformat()
            },
            'arrival_location': {
                'city_name': 'Plage de Pampelonne',
                'coordinates': {'latitude': 43.2384, 'longitude': 6.6789},
                'timestamp': departure_time.isoformat()
            },
            'departure_date': departure_time.date().isoformat(),
            'departure_time': departure_time.time().isoformat(),
            'duration_hours': 4,
            'passenger_count': 6,
            'boat_type': 'LUXURY'
        }

        response = self.make_request('POST', '/api/trips/requests/hourly/', 'client', hourly_data)
        self.responses['create_hourly_trip'] = {
            'endpoint': 'POST /api/trips/requests/hourly/',
            'description': 'Créer une demande de course horaire (mise à disposition)',
            'request_body': hourly_data,
            'response': response
        }
        print(f"  ⏰ Create Hourly Trip: {response['status_code'] if response else 'ERROR'}")

    def generate_documentation(self):
        """Générer la documentation JSON complète"""
        print("\n📚 GÉNÉRATION DE LA DOCUMENTATION")
        print("-" * 40)

        documentation = {
            "api_documentation": {
                "title": "Commodore API - Documentation Complète des Endpoints",
                "version": "1.0.0",
                "description": "Documentation extraite des VRAIES réponses API du système",
                "base_url": self.base_url,
                "authentication": "Token-based authentication",
                "generated_at": datetime.now().isoformat(),
                "total_endpoints_tested": len(self.responses)
            },
            "endpoints": self.responses
        }

        # Sauvegarder la documentation
        with open('REAL_API_DOCUMENTATION.json', 'w', encoding='utf-8') as f:
            json.dump(documentation, f, indent=2, ensure_ascii=False, default=str)

        print(f"  ✅ Documentation sauvegardée: REAL_API_DOCUMENTATION.json")
        print(f"  📊 {len(self.responses)} endpoints documentés")

        return documentation

    def run_complete_extraction(self):
        """Lancer l'extraction complète"""
        print("🚀 EXTRACTION COMPLÈTE DES RÉPONSES API RÉELLES")
        print("=" * 60)

        # 1. Configuration
        self.setup_test_accounts()

        # 2. Tests par catégorie - TOUS LES ENDPOINTS DU SYSTÈME
        self.test_authentication_endpoints()
        self.test_wallet_endpoints()
        self.test_trip_endpoints()
        self.test_hourly_trip_endpoints()
        self.test_shuttle_endpoints()
        self.test_trip_management_endpoints()
        self.test_captain_endpoints()
        self.test_qr_endpoints()
        self.test_accounts_endpoints()
        self.test_boats_endpoints()
        self.test_establishment_endpoints()
        self.test_boatman_endpoints()
        self.test_payment_endpoints()
        self.test_notifications_endpoints()
        self.test_chat_endpoints()
        self.test_reviews_endpoints()

        # 3. Génération documentation
        documentation = self.generate_documentation()

        print("\n" + "=" * 60)
        print("🎉 EXTRACTION TERMINÉE !")
        print(f"📋 {len(self.responses)} endpoints testés et documentés")
        print("📄 Fichier généré: REAL_API_DOCUMENTATION.json")
        print("\n🔍 ENDPOINTS COUVERTS :")
        print("✅ Authentification (inscription, connexion, tokens)")
        print("✅ Portefeuilles (consultation, recharge, transactions)")
        print("✅ Courses simples (création, devis, acceptation)")
        print("✅ Courses horaires (mise à disposition)")
        print("✅ Navettes (création, gestion établissement)")
        print("✅ Gestion courses (démarrer, terminer, annuler, statut)")
        print("✅ Capitaines (dashboard, historique, disponibilité)")
        print("✅ QR Codes (génération, vérification)")
        print("✅ Comptes (profils, capitaines, établissements)")
        print("✅ Bateaux (liste, disponibilité)")
        print("✅ Établissements (dashboard, bateliers)")
        print("✅ Bateliers (connexion, navettes)")
        print("✅ Paiements (compensation carbone, pourboires, retraits)")
        print("✅ Notifications (liste, lecture)")
        print("✅ Chat (salons, chatbot)")
        print("✅ Avis (création, liste)")
        print("✅ Suivi temps réel (tracking, statuts)")

        return documentation

if __name__ == '__main__':
    extractor = APIResponseExtractor()
    extractor.run_complete_extraction()
