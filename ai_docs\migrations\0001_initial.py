# Generated by Django 4.2.8 on 2025-06-26 09:04

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="APIDocumentation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        default="Commodore Taxi Boat API Documentation", max_length=200
                    ),
                ),
                ("content", models.TextField()),
                (
                    "generated_at",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("openai_model", models.CharField(default="gpt-4", max_length=50)),
                ("is_active", models.BooleanField(default=True)),
                ("version", models.Char<PERSON>ield(default="1.0", max_length=20)),
            ],
            options={
                "verbose_name": "Documentation API",
                "verbose_name_plural": "Documentations API",
                "ordering": ["-generated_at"],
            },
        ),
        migrations.CreateModel(
            name="APIEndpoint",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("path", models.CharField(max_length=200)),
                ("method", models.CharField(max_length=10)),
                ("description", models.TextField(blank=True)),
                ("app_name", models.CharField(max_length=50)),
                ("view_name", models.CharField(max_length=100)),
                ("parameters", models.JSONField(blank=True, default=dict)),
                ("response_example", models.JSONField(blank=True, default=dict)),
                ("is_documented", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["app_name", "path"],
                "unique_together": {("path", "method")},
            },
        ),
    ]
