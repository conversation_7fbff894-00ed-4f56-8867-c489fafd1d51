# Generated by Django 4.2.8 on 2025-06-01 06:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("trips", "0002_triprequest_hourlytriprequest_simpletriprequest_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="trip",
            name="cancelled_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="cancelled_trips",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="trip",
            name="captain_notes",
            field=models.TextField(blank=True, verbose_name="notes du capitaine"),
        ),
        migrations.AddField(
            model_name="trip",
            name="client_notes",
            field=models.TextField(blank=True, verbose_name="notes du client"),
        ),
        migrations.AddField(
            model_name="trip",
            name="delay_minutes",
            field=models.IntegerField(default=0, verbose_name="retard en minutes"),
        ),
        migrations.AddField(
            model_name="trip",
            name="estimated_arrival_time",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="heure d'arrivée estimée"
            ),
        ),
        migrations.AddField(
            model_name="trip",
            name="problem_description",
            field=models.TextField(blank=True, verbose_name="description du problème"),
        ),
        migrations.AlterField(
            model_name="trip",
            name="status",
            field=models.CharField(
                choices=[
                    ("PENDING", "En attente"),
                    ("ACCEPTED", "Acceptée"),
                    ("REJECTED", "Refusée"),
                    ("IN_PROGRESS", "En cours"),
                    ("COMPLETED", "Terminée"),
                    ("CANCELLED", "Annulée"),
                    ("CANCELLED_BY_CLIENT", "Annulée par le client"),
                    ("CANCELLED_BY_CAPTAIN", "Annulée par le capitaine"),
                    ("DELAYED", "Retardée"),
                    ("PROBLEM", "Problème technique"),
                ],
                default="PENDING",
                max_length=20,
                verbose_name="statut",
            ),
        ),
    ]
