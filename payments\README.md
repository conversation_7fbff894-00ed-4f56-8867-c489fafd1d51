# Configuration Stripe et Webhooks

## 1. Configuration des clés API Stripe

Les clés API sont déjà configurées dans le fichier `.env` :
```env
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_SUCCESS_URL=http://localhost:3000/payment/success
STRIPE_CANCEL_URL=http://localhost:3000/payment/cancel
```

## 2. Configuration des Webhooks dans le Dashboard Stripe

1. Connectez-vous au [Dashboard Stripe](https://dashboard.stripe.com)
2. Allez dans **Développeurs > Webhooks**
3. Cliquez sur **Ajouter un endpoint**
4. Configurez l'endpoint :
   - URL de production : `https://api.commodore.com/api/webhooks/stripe/`
   - URL de développement : `http://localhost:8000/api/webhooks/stripe/`

5. Sélectionnez les événements suivants :
   - `payment_intent.succeeded` : Paiement réussi
   - `payment_intent.payment_failed` : Échec du paiement
   - `charge.refunded` : Remboursement effectué
   - `customer.created` : Nouveau client créé
   - `payment_method.attached` : Nouvelle méthode de paiement

6. Copiez la clé secrète du webhook fournie par Stripe et ajoutez-la dans votre `.env` :
```env
STRIPE_WEBHOOK_SECRET=whsec_votre_cle_secrete
```

## 3. Tests en Local avec Stripe CLI

### Installation de Stripe CLI

1. Installez Stripe CLI avec Chocolatey :
```powershell
choco install stripe-cli
```

2. Connectez-vous à votre compte Stripe :
```powershell
stripe login
```

### Tester les Webhooks en Local

1. Démarrez le forwarding des webhooks :
```powershell
stripe listen --forward-to http://localhost:8000/api/webhooks/stripe/
```

2. Dans un autre terminal, testez les différents événements :
```powershell
# Test d'un paiement réussi
stripe trigger payment_intent.succeeded

# Test d'un paiement échoué
stripe trigger payment_intent.payment_failed

# Test d'un remboursement
stripe trigger charge.refunded

# Test de création de client
stripe trigger customer.created

# Test d'ajout de méthode de paiement
stripe trigger payment_method.attached
```

### Vérification des Webhooks

1. Vérifiez les logs Django pour confirmer la réception des événements
2. Vérifiez dans la base de données que les statuts sont correctement mis à jour :
   - Table `payments_payment` pour les paiements
   - Table `payments_wallet` pour les portefeuilles
   - Table `payments_transaction` pour les transactions

## 4. Structure des URLs

Les webhooks sont configurés dans `payments/urls.py` :
```python
from django.urls import path
from .views import StripeWebhookView

urlpatterns = [
    path('webhooks/stripe/', StripeWebhookView.as_view(), name='stripe-webhook'),
]
```

## 5. Sécurité

- Utilisez toujours HTTPS en production
- Ne partagez jamais les clés secrètes
- Vérifiez la signature des webhooks (déjà implémenté dans `StripeWebhookView`)
- Utilisez des transactions atomiques pour les opérations de paiement
- Gardez les logs pour le débogage

## 6. Dépannage

1. Si les webhooks ne sont pas reçus :
   - Vérifiez que l'URL est correcte
   - Vérifiez que le `STRIPE_WEBHOOK_SECRET` est correct
   - Vérifiez les logs Django et Stripe CLI

2. Si les événements ne sont pas traités :
   - Vérifiez les logs Django pour les erreurs
   - Vérifiez que l'événement est bien dans la liste des événements gérés
   - Vérifiez que les modèles existent en base de données

3. Pour les tests en local :
   - Assurez-vous que Django est en cours d'exécution
   - Vérifiez que Stripe CLI est connecté au bon compte
   - Utilisez le mode `--skip-verify` si nécessaire pour le développement
