# Documentation API Mobile - Commodore Taxi Boat

Ce document présente l'ensemble des API disponibles pour l'application mobile Commodore Taxi Boat, structurées par module.

## Authentification

Les API d'authentification permettent aux utilisateurs de s'inscrire, se connecter et gérer leur compte.

```
📁 Détails complets : /authentication/endpoints.txt
```

### Endpoints principaux :
- **POST /api/auth/register/** - Inscription d'un nouvel utilisateur
- **POST /api/auth/verify-email/** - Vérification de l'email
- **POST /api/auth/social-login/** - Connexion avec Facebook, Google ou Apple
- **POST /api/auth/login/** - Connexion avec email/mot de passe
- **POST /api/auth/token/refresh/** - Rafraîchissement du token JWT
- **POST /api/auth/password/reset/** - Demande de réinitialisation du mot de passe

## Profils utilisateurs

Les API de profils permettent de gérer les informations des utilisateurs (clients, capitaines, établissements).

```
📁 Détails complets : /accounts/endpoints.txt
```

### Endpoints principaux :
- **GET/PATCH /api/accounts/profile/** - Récupérer ou mettre à jour le profil
- **GET /api/accounts/captains/{id}/** - Profil public d'un capitaine
- **GET /api/accounts/establishments/{id}/** - Profil public d'un établissement
- **GET /api/accounts/captains/available/** - Liste des capitaines disponibles
- **POST /api/accounts/captains/availability/** - Mise à jour de la disponibilité

## Courses et navettes

Les API de courses permettent de réserver, suivre et gérer les courses et navettes.

```
📁 Détails complets : /trips/endpoints.txt
```

### Endpoints principaux :
- **GET/POST /api/trips/** - Liste et création de courses
- **GET/PATCH/DELETE /api/trips/{id}/** - Détails, modification et suppression
- **GET/POST /api/trips/{id}/tracking/** - Suivi en temps réel
- **GET/POST /api/shuttles/** - Liste et création de navettes

## Paiements

Les API de paiement permettent de gérer les transactions, les portefeuilles et les remboursements.

```
📁 Détails complets : /payments/endpoints.txt
```

### Endpoints principaux :
- **GET /api/payments/wallet/** - Consulter le solde du portefeuille
- **POST /api/payments/wallet/recharge/** - Recharger le portefeuille
- **POST /api/payments/trip/** - Payer une course ou une navette
- **POST /api/payments/tip/** - Ajouter un pourboire
- **POST /api/payments/refund/** - Demander un remboursement

## Bateaux

Les API de bateaux permettent de gérer les bateaux et leur maintenance.

```
📁 Détails complets : /boats/endpoints.txt
```

### Endpoints principaux :
- **GET/POST /api/boats/** - Liste et création de bateaux
- **GET/PATCH/DELETE /api/boats/{id}/** - Détails, modification et suppression
- **GET/POST /api/boats/{id}/maintenance/** - Historique et ajout de maintenance
- **GET /api/boats/available/** - Recherche de bateaux disponibles

## Chat et messagerie

Les API de chat permettent d'interagir avec un chatbot d'assistance intelligent pour les utilisateurs.

```
📁 Détails complets : /chat/endpoints.txt
```

### Endpoints principaux :
- **POST /api/chat/chatbot/message/** - Envoyer un message au chatbot Meta-Llama-3
- **GET /api/chat/chatbot/history/{session_id}/** - Récupérer l'historique des messages
- **POST /api/chat/chatbot/clear-session/** - Effacer une session de chat
- **POST /api/chat/feedback/** - Soumettre un feedback sur une réponse du chatbot

## Système RAG (Retrieval Augmented Generation)

Les API RAG fournissent un chatbot intelligent capable de répondre aux questions à partir de la base de connaissances de Commodore.

```
📁 Détails complets : /rag/endpoints.txt
```

### Endpoints principaux :
- **POST /api/rag/chat/** - Poser une question au système RAG et obtenir une réponse générée
- **GET /api/rag/chat/history/{session_id}/** - Récupérer l'historique des échanges
- **GET /api/rag/search/** - Rechercher dans la base de connaissances sans génération
- **POST /api/rag/feedback/** - Soumettre un feedback sur une réponse
- **GET /api/rag/offline/bundle/** - Télécharger les données pour le mode hors ligne
- **GET /api/rag/offline/faqs/** - Récupérer les FAQ pour le mode hors ligne

## Avis et évaluations

Les API d'avis permettent aux utilisateurs de noter et commenter leurs expériences.

```
📁 Détails complets : /reviews/endpoints.txt
```

### Endpoints principaux :
- **GET/POST /api/reviews/** - Liste et création d'avis
- **GET/PATCH/DELETE /api/reviews/{id}/** - Détails, modification et suppression
- **POST /api/reviews/{id}/responses/** - Répondre à un avis
- **GET /api/reviews/stats/{type}/{id}/** - Statistiques des avis

## Notifications

Les API de notifications permettent de gérer les alertes envoyées aux utilisateurs.

```
📁 Détails complets : /notifications/endpoints.txt
```

### Endpoints principaux :
- **GET /api/notifications/notifications/** - Liste des notifications
- **POST /api/notifications/notifications/{id}/mark_as_read/** - Marquer comme lue
- **GET /api/notifications/notifications/unread_count/** - Compteur non lues
- **POST /api/notifications/devices/** - Enregistrer un appareil pour les push

## WebSockets

Pour les fonctionnalités en temps réel (chat, suivi de course), utilisez ces connexions WebSocket:

- **ws://domain.com/ws/chat/{room_id}/** - Messages de chat en temps réel
- **ws://domain.com/ws/trips/{trip_id}/tracking/** - Suivi d'une course en temps réel

## Statut d'implémentation

| Module | Statut | Remarques |
|--------|--------|----------|
| Authentification | ✅ Implémenté | Social Login disponible |
| Profils & Favoris | ✅ Implémenté | Tous types d'utilisateurs avec gestion des favoris |
| Courses & Navettes | ✅ Implémenté | Fonctionnalités de base complètes, WebSockets à finaliser |
| Paiements | ✅ Implémenté | Portefeuille, méthodes de paiement et transactions |
| Bateaux | ✅ Implémenté | Gestion complète des bateaux et maintenance |
| Chat et RAG | ✅ Implémenté | Chatbot Meta-Llama-3 et système RAG intégrés |
| Avis et évaluations | ✅ Implémenté | Système complet avec réponses et modération |
| Notifications | ✅ Implémenté | Gestion des appareils et envoi de notifications push |

## Remboursements automatiques

Le système de remboursements automatiques est configuré selon les règles suivantes :

1. **Annulation avant confirmation** : Remboursement intégral
2. **Annulation par capitaine/établissement** : Remboursement intégral + compensation possible
3. **Problème pendant la course** : Remboursement partiel/intégral selon le cas

## Sécurité

Toutes les API sont protégées par JWT (JSON Web Token). Les tokens ont une durée de validité de 1 heure et doivent être inclus dans l'en-tête Authorization de chaque requête :

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Pour prolonger la session, utilisez le token de rafraîchissement avec l'endpoint `/api/auth/token/refresh/`.

## Environnement de test

Un environnement de test Insomnia est disponible dans `/docs/insomnia_export.json` contenant des exemples de toutes les requêtes API.
