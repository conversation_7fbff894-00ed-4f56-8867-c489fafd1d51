import os
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from rag.models import Document
from rag.services import rag_service

class Command(BaseCommand):
    help = 'Importe un document dans le système RAG'

    def add_arguments(self, parser):
        parser.add_argument('file_path', type=str, help='Chemin vers le fichier à importer')
        parser.add_argument('--title', type=str, help='Titre du document (par défaut: nom du fichier)')
        parser.add_argument('--category', type=str, help='Catégorie du document')
        parser.add_argument('--source', type=str, help='Source du document')
        parser.add_argument('--process', action='store_true', help='Traiter le document après importation (générer les embeddings)')

    def handle(self, *args, **options):
        file_path = options['file_path']
        
        # Vérifier que le fichier existe
        if not os.path.exists(file_path):
            raise CommandError(f"Le fichier {file_path} n'existe pas")
        
        # Lire le contenu du fichier
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
        except Exception as e:
            raise CommandError(f"Erreur lors de la lecture du fichier: {str(e)}")
        
        # Déterminer le titre du document
        title = options['title'] or os.path.basename(file_path)
        
        # Créer le document
        try:
            document = Document.objects.create(
                title=title,
                content=content,
                category=options['category'] or 'Documentation',
                source=options['source'] or 'Importation manuelle',
                embedding_generated=False
            )
            self.stdout.write(self.style.SUCCESS(f"Document '{title}' importé avec succès (ID: {document.id})"))
            
            # Traiter le document si demandé
            if options['process']:
                self.stdout.write("Traitement du document en cours...")
                rag_service.process_document(document)
                self.stdout.write(self.style.SUCCESS("Document traité avec succès"))
                
        except Exception as e:
            raise CommandError(f"Erreur lors de la création du document: {str(e)}")
