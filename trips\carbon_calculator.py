"""
Système de calcul d'empreinte carbone pour les voyages en bateau.
Basé sur les données ADEME et les standards du marché volontaire du carbone.
"""

from decimal import Decimal
from django.conf import settings
from boats.models import Boat


class CarbonFootprintCalculator:
    """Calculateur d'empreinte carbone pour les voyages en bateau"""
    
    # Facteurs d'émission ADEME (kg CO₂/L)
    EMISSION_FACTORS = {
        'gasoline': Decimal('2.32'),  # Essence
        'diesel': Decimal('2.68'),    # Diesel
        'electric': Decimal('0.00'),  # Électrique (émissions directes nulles)
        'hybrid': Decimal('1.16'),    # Hybride (50% réduction)
    }
    
    # Consommation moyenne par défaut (L/h)
    DEFAULT_CONSUMPTION = {
        'gasoline': Decimal('25.0'),  # 150hp moteur thermique
        'diesel': Decimal('22.0'),    # Diesel plus efficace
        'electric': Decimal('0.0'),   # Pas de carburant
        'hybrid': Decimal('12.5'),    # 50% réduction
    }
    
    # Prix de compensation carbone (€/tonne CO₂)
    CARBON_PRICE_PER_TONNE = Decimal('80.0')  # Marché volontaire 2024
    
    @classmethod
    def calculate_emissions(cls, boat, duration_minutes):
        """
        Calcule les émissions CO₂ pour un voyage.
        
        Args:
            boat: Instance du bateau
            duration_minutes: Durée du voyage en minutes
            
        Returns:
            dict: {
                'co2_kg': Decimal,
                'fuel_consumed_liters': Decimal,
                'compensation_cost_euros': Decimal,
                'is_eco_friendly': bool
            }
        """
        # Convertir la durée en heures
        duration_hours = Decimal(str(duration_minutes)) / Decimal('60')
        
        # Déterminer le type de carburant
        fuel_type = getattr(boat, 'fuel_type', 'gasoline').lower()
        if fuel_type not in cls.EMISSION_FACTORS:
            fuel_type = 'gasoline'  # Par défaut
        
        # Consommation du bateau (L/h)
        consumption_per_hour = getattr(boat, 'fuel_consumption', None)
        if not consumption_per_hour:
            consumption_per_hour = cls.DEFAULT_CONSUMPTION[fuel_type]
        else:
            consumption_per_hour = Decimal(str(consumption_per_hour))
        
        # Calcul de la consommation totale
        fuel_consumed = consumption_per_hour * duration_hours
        
        # Calcul des émissions CO₂
        emission_factor = cls.EMISSION_FACTORS[fuel_type]
        co2_kg = fuel_consumed * emission_factor
        
        # Calcul du coût de compensation
        co2_tonnes = co2_kg / Decimal('1000')
        compensation_cost = co2_tonnes * cls.CARBON_PRICE_PER_TONNE
        
        # Vérifier si c'est écologique
        is_eco_friendly = fuel_type in ['electric', 'hybrid']
        
        return {
            'co2_kg': co2_kg.quantize(Decimal('0.01')),
            'fuel_consumed_liters': fuel_consumed.quantize(Decimal('0.01')),
            'compensation_cost_euros': compensation_cost.quantize(Decimal('0.01')),
            'is_eco_friendly': is_eco_friendly,
            'fuel_type': fuel_type,
            'duration_hours': duration_hours
        }
    
    @classmethod
    def get_compensation_pricing_table(cls, fuel_type='gasoline', consumption_lh=25):
        """
        Génère un tableau de tarification pour différentes durées.
        
        Args:
            fuel_type: Type de carburant
            consumption_lh: Consommation en L/h
            
        Returns:
            list: Liste des tarifs par durée
        """
        durations = [10, 15, 20, 30, 45, 60, 90, 120]  # minutes
        pricing_table = []
        
        for duration in durations:
            # Créer un bateau fictif pour le calcul
            class MockBoat:
                def __init__(self, fuel_type, consumption):
                    self.fuel_type = fuel_type
                    self.fuel_consumption = consumption
            
            mock_boat = MockBoat(fuel_type, consumption_lh)
            result = cls.calculate_emissions(mock_boat, duration)
            
            pricing_table.append({
                'duration_minutes': duration,
                'duration_display': f"{duration} min",
                'co2_kg': result['co2_kg'],
                'compensation_cost': result['compensation_cost_euros']
            })
        
        return pricing_table
    
    @classmethod
    def format_eco_message(cls, calculation_result):
        """
        Formate le message écologique selon le type de bateau.
        
        Args:
            calculation_result: Résultat du calcul d'émissions
            
        Returns:
            dict: Message formaté
        """
        if calculation_result['is_eco_friendly']:
            if calculation_result['fuel_type'] == 'electric':
                return {
                    'message': "✅ Zéro émission directe – aucune compensation requise",
                    'badge': "100% écologique",
                    'color': 'green'
                }
            else:  # hybrid
                return {
                    'message': f"🌱 Bateau hybride – {calculation_result['co2_kg']} kg CO₂ (50% de réduction)",
                    'badge': "Éco-responsable",
                    'color': 'lightgreen'
                }
        else:
            return {
                'message': f"🌍 {calculation_result['co2_kg']} kg CO₂ – Compensation volontaire disponible",
                'badge': "Compensation carbone",
                'color': 'orange'
            }
    
    @classmethod
    def calculate_trip_carbon_data(cls, trip):
        """
        Calcule toutes les données carbone pour un voyage.
        
        Args:
            trip: Instance du voyage
            
        Returns:
            dict: Données complètes sur l'empreinte carbone
        """
        if not trip.boat:
            return None
        
        # Calculer la durée
        if trip.actual_start_time and trip.actual_end_time:
            duration = (trip.actual_end_time - trip.actual_start_time).total_seconds() / 60
        elif hasattr(trip, 'estimated_duration') and trip.estimated_duration:
            duration = trip.estimated_duration
        else:
            duration = 15  # Durée par défaut
        
        # Calcul des émissions
        calculation = cls.calculate_emissions(trip.boat, duration)
        
        # Message écologique
        eco_message = cls.format_eco_message(calculation)
        
        return {
            'calculation': calculation,
            'eco_message': eco_message,
            'can_compensate': not calculation['is_eco_friendly'],
            'compensation_optional': True
        }


class CarbonCompensationService:
    """Service de gestion de la compensation carbone"""
    
    @staticmethod
    def create_compensation_payment(trip, user, amount=None):
        """
        Crée un paiement de compensation carbone.
        
        Args:
            trip: Instance du voyage
            user: Utilisateur qui paie
            amount: Montant (calculé automatiquement si None)
            
        Returns:
            Payment: Instance du paiement créé
        """
        from payments.models import Payment
        
        if not amount:
            carbon_data = CarbonFootprintCalculator.calculate_trip_carbon_data(trip)
            if not carbon_data or not carbon_data['can_compensate']:
                raise ValueError("Compensation carbone non applicable pour ce voyage")
            amount = carbon_data['calculation']['compensation_cost_euros']
        
        payment = Payment.objects.create(
            user=user,
            trip=trip,
            amount=amount,
            type=Payment.PaymentType.CARBON_OFFSET,
            payment_method=Payment.PaymentMethod.CARD,  # Par défaut
            status=Payment.Status.PENDING,
            description=f"Compensation carbone - Voyage #{trip.id}",
            metadata={
                'carbon_offset': True,
                'co2_compensated_kg': str(CarbonFootprintCalculator.calculate_trip_carbon_data(trip)['calculation']['co2_kg'])
            }
        )
        
        return payment
    
    @staticmethod
    def get_user_carbon_stats(user):
        """
        Récupère les statistiques carbone d'un utilisateur.
        
        Args:
            user: Instance de l'utilisateur
            
        Returns:
            dict: Statistiques carbone
        """
        from payments.models import Payment
        from django.db.models import Sum
        
        # Paiements de compensation carbone
        carbon_payments = Payment.objects.filter(
            user=user,
            type=Payment.PaymentType.CARBON_OFFSET,
            status=Payment.Status.COMPLETED
        )
        
        total_compensated = carbon_payments.aggregate(
            total=Sum('amount')
        )['total'] or Decimal('0.00')
        
        # Calculer le CO₂ total compensé
        total_co2_kg = Decimal('0.00')
        for payment in carbon_payments:
            if payment.metadata and 'co2_compensated_kg' in payment.metadata:
                total_co2_kg += Decimal(payment.metadata['co2_compensated_kg'])
        
        return {
            'total_compensated_euros': total_compensated,
            'total_co2_compensated_kg': total_co2_kg,
            'compensation_count': carbon_payments.count(),
            'eco_score': min(100, int(total_co2_kg))  # Score écologique simple
        }
