"""
URLs pour l'application boatman.
"""

from django.urls import path
from . import views_auth, views_dashboard, views_shuttles, views_profile, views_wallet

app_name = 'boatman'

urlpatterns = [
    # Authentification
    path('login/', views_auth.BoatmanLoginView.as_view(), name='login'),
    path('forgot-password/', views_auth.BoatmanForgotPasswordView.as_view(), name='forgot_password'),
    path('verify-code/', views_auth.BoatmanVerifyCodeView.as_view(), name='verify_code'),
    path('change-password/', views_auth.BoatmanChangePasswordView.as_view(), name='change_password'),
    
    # Tableau de bord
    path('dashboard/', views_dashboard.BoatmanDashboardView.as_view(), name='dashboard'),
    path('availability/', views_dashboard.BoatmanAvailabilityView.as_view(), name='availability'),
    
    # Gestion des courses
    path('shuttles/', views_shuttles.BoatmanShuttlesView.as_view(), name='shuttles'),
    path('shuttle/<int:shuttle_id>/', views_shuttles.BoatmanShuttleDetailView.as_view(), name='shuttle_detail'),
    path('shuttle/<int:shuttle_id>/start/', views_shuttles.BoatmanShuttleStartView.as_view(), name='shuttle_start'),
    path('shuttle/<int:shuttle_id>/end/', views_shuttles.BoatmanShuttleEndView.as_view(), name='shuttle_end'),
    path('shuttle/<int:shuttle_id>/track/', views_shuttles.BoatmanShuttleTrackView.as_view(), name='shuttle_track'),
    path('shuttle/<int:shuttle_id>/validate-qr/', views_shuttles.BoatmanQRValidationView.as_view(), name='validate_qr'),
    
    # Profil
    path('profile/', views_profile.BoatmanProfileView.as_view(), name='profile'),
    path('boat/', views_profile.BoatmanBoatProfileView.as_view(), name='boat_profile'),
    
    # Portefeuille et paiements
    path('wallet/', views_wallet.BoatmanWalletView.as_view(), name='wallet'),
    path('wallet/withdraw/', views_wallet.BoatmanWithdrawView.as_view(), name='withdraw'),
    path('payment-methods/', views_wallet.BoatmanPaymentMethodsView.as_view(), name='payment_methods'),
]
