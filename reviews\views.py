from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.contrib.contenttypes.models import ContentType
from django.db.models import Avg, Count
from .models import Review, ReviewResponse, ReviewReport
from .serializers import (
    ReviewSerializer, ReviewListSerializer, ReviewResponseSerializer,
    ReviewReportSerializer, ReviewStatisticsSerializer, ReviewFilterSerializer
)
import logging
from drf_spectacular.utils import extend_schema, OpenApiParameter

logger = logging.getLogger(__name__)


@extend_schema(tags=["Reviews"])
class ReviewListCreateView(APIView):
    permission_classes = [IsAuthenticated]
    
    @extend_schema(tags=["Reviews"], responses=ReviewListSerializer(many=True))
    def get(self, request):
        # Filtrage par type d'objet évalué
        content_type_id = request.query_params.get('content_type_id')
        object_id = request.query_params.get('object_id')
        
        if not content_type_id or not object_id:
            return Response(
                {'error': 'Les paramètres content_type_id et object_id sont requis'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            content_type = ContentType.objects.get(id=content_type_id)
            reviews = Review.objects.filter(
                content_type=content_type,
                object_id=object_id,
                is_public=True
            )
            
            # Filtres additionnels
            filter_serializer = ReviewFilterSerializer(data=request.query_params)
            if filter_serializer.is_valid():
                filters = filter_serializer.validated_data
                
                if 'rating' in filters:
                    reviews = reviews.filter(rating=filters['rating'])
                if filters.get('verified_only'):
                    reviews = reviews.filter(is_verified=True)
                if filters.get('has_response'):
                    reviews = reviews.filter(responses__isnull=False).distinct()
                if filters.get('date_from'):
                    reviews = reviews.filter(created_at__gte=filters['date_from'])
                if filters.get('date_to'):
                    reviews = reviews.filter(created_at__lte=filters['date_to'])
                
                # Tri
                sort_by = filters.get('sort_by', '-created_at')
                reviews = reviews.order_by(sort_by)
            else:
                reviews = reviews.order_by('-created_at')
            
            serializer = ReviewListSerializer(reviews, many=True)
            return Response(serializer.data)
            
        except ContentType.DoesNotExist:
            return Response(
                {'error': 'Type de contenu invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @extend_schema(tags=["Reviews"], request=ReviewSerializer, responses=ReviewSerializer)
    def post(self, request):
        # Vérifier que l'utilisateur peut poster un avis
        # Par exemple, s'il s'agit d'un avis sur un voyage, l'utilisateur doit avoir participé à ce voyage
        
        data = request.data.copy()
        data['author'] = request.user.id
        
        # Valider le type d'objet évalué
        try:
            content_type = ContentType.objects.get(id=data.get('content_type'))
            reviewed_object = content_type.get_object_for_this_type(id=data.get('object_id'))
        except (ContentType.DoesNotExist, AttributeError):
            return Response(
                {'error': 'Type d\'objet évalué invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Objet évalué introuvable: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Vérifier si l'utilisateur a déjà posté un avis pour cet objet
        existing_review = Review.objects.filter(
            author=request.user,
            content_type=content_type,
            object_id=data.get('object_id')
        ).first()
        
        if existing_review:
            return Response(
                {'error': 'Vous avez déjà posté un avis pour cet objet'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = ReviewSerializer(data=data)
        if serializer.is_valid():
            review = serializer.save(author=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(tags=["Reviews"])
class ReviewDetailView(APIView):
    permission_classes = [IsAuthenticated]
    
    @extend_schema(tags=["Reviews"], responses=ReviewSerializer)
    def get(self, request, pk):
        review = get_object_or_404(Review, pk=pk)
        
        # Vérifier si l'avis est public ou si l'utilisateur est l'auteur
        if not review.is_public and review.author != request.user:
            return Response(
                {'error': 'Vous n\'avez pas accès à cet avis'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        serializer = ReviewSerializer(review)
        return Response(serializer.data)
    
    def patch(self, request, pk):
        review = get_object_or_404(Review, pk=pk)
        
        # Vérifier que l'utilisateur est l'auteur de l'avis
        if review.author != request.user:
            return Response(
                {'error': 'Vous n\'êtes pas autorisé à modifier cet avis'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        # Vérifier le délai de modification (par exemple, 48h après la création)
        from django.utils import timezone
        from datetime import timedelta
        
        edit_window = review.created_at + timedelta(hours=48)
        if timezone.now() > edit_window:
            return Response(
                {'error': 'Le délai de modification de l\'avis est dépassé (48h)'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        serializer = ReviewSerializer(review, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, pk):
        review = get_object_or_404(Review, pk=pk)
        
        # Vérifier que l'utilisateur est l'auteur de l'avis
        if review.author != request.user:
            return Response(
                {'error': 'Vous n\'êtes pas autorisé à supprimer cet avis'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        review.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


@extend_schema(tags=["Reviews"])
class ReviewResponseView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request, review_id):
        review = get_object_or_404(Review, pk=review_id)
        
        # Vérifier que l'utilisateur peut répondre à l'avis
        # Par exemple, s'il s'agit d'un avis sur un capitaine, seul ce capitaine peut répondre
        content_type = review.content_type
        reviewed_object = review.reviewed_object
        
        # Logique de vérification selon le type d'objet évalué
        can_respond = False
        
        # Exemple: si l'avis concerne un capitaine
        if content_type.model == 'captain' and hasattr(request.user, 'captain'):
            if request.user.captain.user.id == reviewed_object.user.id:  # Captain utilise user comme clé primaire
                can_respond = True
                
        # Si l'avis concerne un établissement
        elif content_type.model == 'establishment' and hasattr(request.user, 'establishment'):
            if request.user.establishment.id == reviewed_object.id:
                can_respond = True
                
        # Si l'utilisateur est un administrateur (à adapter selon votre logique)
        elif request.user.is_staff:
            can_respond = True
            
        if not can_respond:
            return Response(
                {'error': 'Vous n\'êtes pas autorisé à répondre à cet avis'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        # Vérifier si une réponse existe déjà
        existing_response = ReviewResponse.objects.filter(review=review, author=request.user).exists()
        if existing_response:
            return Response(
                {'error': 'Vous avez déjà répondu à cet avis'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        data = request.data.copy()
        data['review'] = review.id
        data['author'] = request.user.id
        
        serializer = ReviewResponseSerializer(data=data)
        if serializer.is_valid():
            response = serializer.save(review=review, author=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(tags=["Reviews"])
class ReviewResponseDetailView(APIView):
    permission_classes = [IsAuthenticated]
    
    def patch(self, request, pk):
        response = get_object_or_404(ReviewResponse, pk=pk)
        
        # Vérifier que l'utilisateur est l'auteur de la réponse
        if response.author != request.user:
            return Response(
                {'error': 'Vous n\'êtes pas autorisé à modifier cette réponse'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        serializer = ReviewResponseSerializer(response, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, pk):
        response = get_object_or_404(ReviewResponse, pk=pk)
        
        # Vérifier que l'utilisateur est l'auteur de la réponse
        if response.author != request.user:
            return Response(
                {'error': 'Vous n\'êtes pas autorisé à supprimer cette réponse'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        response.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


@extend_schema(tags=["Reviews"])
class ReviewReportView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request, review_id):
        review = get_object_or_404(Review, pk=review_id)
        
        # Vérifier si l'utilisateur a déjà signalé cet avis
        existing_report = ReviewReport.objects.filter(review=review, reporter=request.user).exists()
        if existing_report:
            return Response(
                {'error': 'Vous avez déjà signalé cet avis'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        data = request.data.copy()
        data['review'] = review.id
        data['reporter'] = request.user.id
        
        serializer = ReviewReportSerializer(data=data)
        if serializer.is_valid():
            report = serializer.save(review=review, reporter=request.user)
            
            # Incrémenter le compteur de signalements sur l'avis
            review.reported_count += 1
            review.save(update_fields=['reported_count'])
            
            # Si le nombre de signalements dépasse un seuil, masquer l'avis automatiquement
            if review.reported_count >= 5:  # Seuil configurable
                review.is_public = False
                review.save(update_fields=['is_public'])
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(tags=["Reviews"])
class ReviewStatisticsView(APIView):
    permission_classes = [IsAuthenticated]
    
    @extend_schema(tags=["Reviews"], responses=ReviewListSerializer(many=True))
    def get(self, request):
        # Filtrage par type d'objet évalué
        content_type_id = request.query_params.get('content_type_id')
        object_id = request.query_params.get('object_id')
        
        if not content_type_id or not object_id:
            return Response(
                {'error': 'Les paramètres content_type_id et object_id sont requis'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            content_type = ContentType.objects.get(id=content_type_id)
            reviews = Review.objects.filter(
                content_type=content_type,
                object_id=object_id,
                is_public=True
            )
            
            # Total des avis
            total_reviews = reviews.count()
            
            # Note moyenne
            average_rating = reviews.aggregate(avg=Avg('rating'))['avg'] or 0
            
            # Distribution des notes
            rating_distribution = {}
            for i in range(1, 6):
                rating_distribution[i] = reviews.filter(rating=i).count()
            
            # Avis récents
            recent_reviews = reviews.order_by('-created_at')[:5]
            
            # Total des avis vérifiés
            total_verified_reviews = reviews.filter(is_verified=True).count()
            
            # Taux de réponse
            reviews_with_response = reviews.annotate(response_count=Count('responses')).filter(response_count__gt=0).count()
            response_rate = (reviews_with_response / total_reviews) * 100 if total_reviews > 0 else 0
            
            # Préparer les données pour le sérialiseur
            data = {
                'total_reviews': total_reviews,
                'average_rating': average_rating,
                'rating_distribution': rating_distribution,
                'recent_reviews': ReviewListSerializer(recent_reviews, many=True).data,
                'total_verified_reviews': total_verified_reviews,
                'response_rate': response_rate
            }
            
            return Response(data)
            
        except ContentType.DoesNotExist:
            return Response(
                {'error': 'Type de contenu invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors du calcul des statistiques: {str(e)}")
            return Response(
                {'error': f'Erreur lors du calcul des statistiques: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@extend_schema(tags=["Reviews"])
class UserReviewsView(APIView):
    permission_classes = [IsAuthenticated]
    
    @extend_schema(tags=["Reviews"], responses=ReviewListSerializer(many=True))
    def get(self, request):
        # Récupérer tous les avis écrits par l'utilisateur connecté
        reviews = Review.objects.filter(author=request.user).order_by('-created_at')
        serializer = ReviewListSerializer(reviews, many=True)
        return Response(serializer.data)
