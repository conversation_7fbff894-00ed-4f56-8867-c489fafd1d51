"""
Script pour créer une invitation de paiement partagé valide pour les tests.
"""
import os
import sys
import django
import datetime

# Configurer l'environnement Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.utils import timezone
from django.contrib.auth import get_user_model
from trips.models import Trip
from trips.shared_payments import SharedPaymentInvitation

User = get_user_model()

def create_shared_payment_invitation(trip_id=1, amount=50.00, email="<EMAIL>"):
    """
    Crée une invitation de paiement partagé pour un trip existant.
    
    Args:
        trip_id: ID du trip existant
        amount: Montant de l'invitation
        email: Email de l'invité
    
    Returns:
        L'invitation créée
    """
    try:
        # Récupérer le trip
        trip = Trip.objects.get(id=trip_id)
        
        # Récupérer l'utilisateur client (ID 2)
        inviter = User.objects.get(id=2)  # Client: <EMAIL>
        
        # Créer une date d'expiration (7 jours dans le futur)
        expires_at = timezone.now() + datetime.timedelta(days=7)
        
        # Créer l'invitation
        invitation = SharedPaymentInvitation.objects.create(
            trip=trip,
            inviter=inviter,
            invitee_email=email,
            amount=amount,
            expires_at=expires_at,
            status='PENDING',
            message="Invitation de test pour le paiement partagé d'une course."
        )
        
        print(f"✓ Invitation de paiement partagé créée avec succès!")
        print(f"  ID: {invitation.id}")
        print(f"  Token: {invitation.token}")
        print(f"  Trip: {trip_id}")
        print(f"  Montant: {amount}")
        print(f"  Email invité: {email}")
        print(f"  Expire le: {expires_at}")
        print(f"\nUtilisez ce token dans le script de test:")
        print(f'$invitationToken = "{invitation.token}"')
        
        return invitation
        
    except Trip.DoesNotExist:
        print(f"❌ Erreur: Le trip avec l'ID {trip_id} n'existe pas!")
        return None
    except User.DoesNotExist:
        print("❌ Erreur: L'utilisateur client (ID 2) n'existe pas!")
        return None
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'invitation: {str(e)}")
        return None

if __name__ == "__main__":
    # Récupérer les arguments de la ligne de commande
    trip_id = 1
    amount = 50.00
    email = "<EMAIL>"
    
    # Utiliser les arguments s'ils sont fournis
    if len(sys.argv) > 1:
        trip_id = int(sys.argv[1])
    if len(sys.argv) > 2:
        amount = float(sys.argv[2])
    if len(sys.argv) > 3:
        email = sys.argv[3]
        
    # Créer l'invitation
    create_shared_payment_invitation(trip_id, amount, email)
