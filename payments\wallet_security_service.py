"""
Service sécurisé pour la gestion des portefeuilles.
Implémente toutes les protections contre les race conditions et les erreurs financières.
"""

import logging
import uuid
from decimal import Decimal, ROUND_HALF_UP
from django.db import transaction, models
from django.utils import timezone
from django.core.exceptions import ValidationError
from .models import Wallet, Transaction, Payment
from .exceptions import InsufficientFundsError, WalletSecurityError

logger = logging.getLogger(__name__)


class WalletSecurityService:
    """Service sécurisé pour toutes les opérations de portefeuille"""
    
    @staticmethod
    def validate_amount(amount):
        """Validation stricte des montants financiers"""
        if amount is None:
            raise ValueError("Le montant ne peut pas être None")
        
        # Convertir en Decimal pour éviter les erreurs de float
        if not isinstance(amount, Decimal):
            try:
                amount = Decimal(str(amount))
            except (ValueError, TypeError):
                raise ValueError("Montant invalide - doit être un nombre")
        
        # Arrondir à 2 décimales
        amount = amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        
        # Validations de sécurité
        if amount <= 0:
            raise ValueError("Le montant doit être positif")
        
        if amount > Decimal('50000.00'):
            raise ValueError("Montant trop élevé (max: 50,000€)")
        
        return amount
    
    @staticmethod
    @transaction.atomic
    def debit_wallet_secure(wallet_id, amount, description, reference=None, user=None):
        """
        Débite un portefeuille de manière sécurisée avec protection contre les race conditions.
        
        Args:
            wallet_id: ID du portefeuille
            amount: Montant à débiter (Decimal)
            description: Description de l'opération
            reference: Référence unique de l'opération
            user: Utilisateur effectuant l'opération
            
        Returns:
            dict: Résultat de l'opération avec nouveau solde
            
        Raises:
            InsufficientFundsError: Si le solde est insuffisant
            WalletSecurityError: En cas d'erreur de sécurité
        """
        # Validation du montant
        amount = WalletSecurityService.validate_amount(amount)
        
        # Générer une référence unique si non fournie
        if not reference:
            reference = f"DEBIT_{uuid.uuid4().hex[:12].upper()}"
        
        try:
            # Verrouiller le portefeuille pour éviter les race conditions
            wallet = Wallet.objects.select_for_update().get(id=wallet_id)
            
            # Vérifier le solde de manière atomique
            if wallet.balance < amount:
                logger.warning(f"Tentative de débit insuffisant - Wallet: {wallet_id}, "
                             f"Solde: {wallet.balance}, Montant: {amount}")
                raise InsufficientFundsError(
                    f"Solde insuffisant. Disponible: {wallet.balance}€, Requis: {amount}€"
                )
            
            # Sauvegarder l'ancien solde pour l'audit
            balance_before = wallet.balance
            
            # Effectuer le débit de manière atomique
            updated_rows = Wallet.objects.filter(
                id=wallet_id,
                balance__gte=amount  # Double vérification atomique
            ).update(
                balance=models.F('balance') - amount,
                total_spent=models.F('total_spent') + amount,
                last_transaction_at=timezone.now()
            )
            
            # Vérifier que la mise à jour a réussi
            if updated_rows == 0:
                logger.error(f"Échec mise à jour atomique - Wallet: {wallet_id}")
                raise WalletSecurityError("Échec de la mise à jour du portefeuille")
            
            # Recharger l'objet pour avoir le nouveau solde
            wallet.refresh_from_db()

            # --- Synchroniser le champ duplicatif sur le modèle Captain (si applicable) ---
            if hasattr(wallet.user, 'captain'):
                wallet.user.captain.wallet_balance = wallet.balance
                wallet.user.captain.save(update_fields=['wallet_balance'])
            
            # Créer l'enregistrement de transaction pour l'audit
            transaction_record = Transaction.objects.create(
                wallet=wallet,
                type=Transaction.TransactionType.DEBIT,
                amount=amount,
                balance_after=wallet.balance,
                description=description,
                metadata={
                    'reference': reference,
                    'balance_before': str(balance_before),
                    'operation_type': 'SECURE_DEBIT',
                    'user_id': user.id if user else None,
                    'timestamp': timezone.now().isoformat()
                }
            )
            
            logger.info(f"Débit sécurisé réussi - Wallet: {wallet_id}, "
                       f"Montant: {amount}, Nouveau solde: {wallet.balance}, "
                       f"Transaction: {transaction_record.id}")
            
            return {
                'success': True,
                'wallet_id': wallet_id,
                'amount_debited': amount,
                'balance_before': balance_before,
                'balance_after': wallet.balance,
                'transaction_id': transaction_record.id,
                'reference': reference
            }
            
        except Wallet.DoesNotExist:
            logger.error(f"Portefeuille inexistant - ID: {wallet_id}")
            raise WalletSecurityError("Portefeuille non trouvé")
        except Exception as e:
            logger.error(f"Erreur lors du débit sécurisé - Wallet: {wallet_id}, "
                        f"Erreur: {str(e)}")
            raise WalletSecurityError(f"Erreur lors du débit: {str(e)}")
    
    @staticmethod
    @transaction.atomic
    def credit_wallet_secure(wallet_id, amount, description, reference=None, user=None):
        """
        Crédite un portefeuille de manière sécurisée.
        
        Args:
            wallet_id: ID du portefeuille
            amount: Montant à créditer (Decimal)
            description: Description de l'opération
            reference: Référence unique de l'opération
            user: Utilisateur effectuant l'opération
            
        Returns:
            dict: Résultat de l'opération avec nouveau solde
        """
        # Validation du montant
        amount = WalletSecurityService.validate_amount(amount)
        
        # Générer une référence unique si non fournie
        if not reference:
            reference = f"CREDIT_{uuid.uuid4().hex[:12].upper()}"
        
        try:
            # Verrouiller le portefeuille
            wallet = Wallet.objects.select_for_update().get(id=wallet_id)
            
            # Sauvegarder l'ancien solde
            balance_before = wallet.balance
            
            # Effectuer le crédit de manière atomique
            Wallet.objects.filter(id=wallet_id).update(
                balance=models.F('balance') + amount,
                total_earned=models.F('total_earned') + amount,
                last_transaction_at=timezone.now()
            )
            
            # Recharger l'objet
            wallet.refresh_from_db()

            # --- Synchroniser le champ duplicatif sur le modèle Captain (si applicable) ---
            if hasattr(wallet.user, 'captain'):
                wallet.user.captain.wallet_balance = wallet.balance
                wallet.user.captain.save(update_fields=['wallet_balance'])
            
            # Créer l'enregistrement de transaction
            transaction_record = Transaction.objects.create(
                wallet=wallet,
                type=Transaction.TransactionType.CREDIT,
                amount=amount,
                balance_after=wallet.balance,
                description=description,
                metadata={
                    'reference': reference,
                    'balance_before': str(balance_before),
                    'operation_type': 'SECURE_CREDIT',
                    'user_id': user.id if user else None,
                    'timestamp': timezone.now().isoformat()
                }
            )
            
            logger.info(f"Crédit sécurisé réussi - Wallet: {wallet_id}, "
                       f"Montant: {amount}, Nouveau solde: {wallet.balance}")
            
            return {
                'success': True,
                'wallet_id': wallet_id,
                'amount_credited': amount,
                'balance_before': balance_before,
                'balance_after': wallet.balance,
                'transaction_id': transaction_record.id,
                'reference': reference
            }
            
        except Wallet.DoesNotExist:
            logger.error(f"Portefeuille inexistant - ID: {wallet_id}")
            raise WalletSecurityError("Portefeuille non trouvé")
        except Exception as e:
            logger.error(f"Erreur lors du crédit sécurisé - Wallet: {wallet_id}, "
                        f"Erreur: {str(e)}")
            raise WalletSecurityError(f"Erreur lors du crédit: {str(e)}")
    
    @staticmethod
    @transaction.atomic
    def transfer_funds_secure(from_wallet_id, to_wallet_id, amount, description, reference=None, user=None):
        """
        Transfère des fonds entre deux portefeuilles de manière sécurisée.
        
        Args:
            from_wallet_id: ID du portefeuille source
            to_wallet_id: ID du portefeuille destination
            amount: Montant à transférer
            description: Description du transfert
            reference: Référence unique
            user: Utilisateur effectuant l'opération
            
        Returns:
            dict: Résultat du transfert
        """
        # Validation du montant
        amount = WalletSecurityService.validate_amount(amount)
        
        # Générer une référence unique si non fournie
        if not reference:
            reference = f"TRANSFER_{uuid.uuid4().hex[:12].upper()}"
        
        # Vérifier que les portefeuilles sont différents
        if from_wallet_id == to_wallet_id:
            raise WalletSecurityError("Impossible de transférer vers le même portefeuille")
        
        try:
            # Débiter le portefeuille source
            debit_result = WalletSecurityService.debit_wallet_secure(
                wallet_id=from_wallet_id,
                amount=amount,
                description=f"Transfert vers wallet {to_wallet_id} - {description}",
                reference=f"{reference}_DEBIT",
                user=user
            )
            
            # Créditer le portefeuille destination
            credit_result = WalletSecurityService.credit_wallet_secure(
                wallet_id=to_wallet_id,
                amount=amount,
                description=f"Transfert depuis wallet {from_wallet_id} - {description}",
                reference=f"{reference}_CREDIT",
                user=user
            )
            
            logger.info(f"Transfert sécurisé réussi - De: {from_wallet_id} vers: {to_wallet_id}, "
                       f"Montant: {amount}, Référence: {reference}")
            
            return {
                'success': True,
                'transfer_reference': reference,
                'amount_transferred': amount,
                'from_wallet': {
                    'id': from_wallet_id,
                    'new_balance': debit_result['balance_after']
                },
                'to_wallet': {
                    'id': to_wallet_id,
                    'new_balance': credit_result['balance_after']
                },
                'debit_transaction_id': debit_result['transaction_id'],
                'credit_transaction_id': credit_result['transaction_id']
            }
            
        except Exception as e:
            logger.error(f"Erreur lors du transfert sécurisé - "
                        f"De: {from_wallet_id} vers: {to_wallet_id}, "
                        f"Montant: {amount}, Erreur: {str(e)}")
            raise WalletSecurityError(f"Erreur lors du transfert: {str(e)}")
    
    @staticmethod
    def get_wallet_balance_secure(wallet_id):
        """
        Récupère le solde d'un portefeuille de manière sécurisée.
        
        Args:
            wallet_id: ID du portefeuille
            
        Returns:
            Decimal: Solde actuel du portefeuille
        """
        try:
            wallet = Wallet.objects.get(id=wallet_id)
            return wallet.balance
        except Wallet.DoesNotExist:
            raise WalletSecurityError("Portefeuille non trouvé")
    
    @staticmethod
    def check_sufficient_funds(wallet_id, amount):
        """
        Vérifie si un portefeuille a suffisamment de fonds.
        
        Args:
            wallet_id: ID du portefeuille
            amount: Montant à vérifier
            
        Returns:
            bool: True si les fonds sont suffisants
        """
        try:
            amount = WalletSecurityService.validate_amount(amount)
            balance = WalletSecurityService.get_wallet_balance_secure(wallet_id)
            return balance >= amount
        except Exception:
            return False
