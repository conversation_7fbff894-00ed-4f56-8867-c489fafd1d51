from django.urls import path
from . import views

app_name = 'boats'

urlpatterns = [
    # Gestion des bateaux
    path('', views.BoatListView.as_view(), name='boat-list'),
    path('<int:pk>/', views.BoatDetailView.as_view(), name='boat-detail'),
    path('<int:pk>/toggle_availability/', views.ToggleBoatAvailabilityView.as_view(), name='toggle-boat-availability'),
    
    # Maintenance des bateaux
    path('<int:boat_id>/maintenance/', views.MaintenanceRecordView.as_view(), name='maintenance-list'),
    path('maintenance/<int:pk>/', views.MaintenanceRecordDetailView.as_view(), name='maintenance-detail'),
    
    # Disponibilité et recherche
    path('available/', views.AvailableBoatsView.as_view(), name='available-boats'),
    path('<int:pk>/availability/', views.BoatAvailabilityView.as_view(), name='boat-availability'),
]
