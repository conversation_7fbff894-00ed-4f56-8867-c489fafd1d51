# API Endpoints - Notifications

## 1. Gestion des notifications

### 1.1. Liste des notifications
- **Endpoint**: GET /api/notifications/notifications/
- **Description**: Récupérer la liste des notifications de l'utilisateur connecté
- **Auth Required**: <PERSON>ui (JWT <PERSON>ken)
- **Response (200 OK)**:
```json
[
  {
    "id": 123,
    "type": "TRIP_ACCEPTED",
    "channel": "PUSH",
    "title": "Course acceptée",
    "message": "Votre course a été acceptée par le capitaine <PERSON>",
    "content_type": "trips.trip",
    "object_id": 45,
    "is_read": false,
    "created_at": "2025-05-25T10:30:45Z",
    "sent_at": "2025-05-25T10:30:46Z"
  },
  {
    "id": 122,
    "type": "PAYMENT_RECEIVED",
    "channel": "PUSH",
    "title": "Paiement reçu",
    "message": "Vous avez reçu un paiement de 120€ pour la course #45",
    "content_type": "payments.payment",
    "object_id": 78,
    "is_read": true,
    "read_at": "2025-05-24T16:45:30Z",
    "created_at": "2025-05-24T16:30:10Z",
    "sent_at": "2025-05-24T16:30:12Z"
  },
  // ...
]
```

### 1.2. Détails d'une notification
- **Endpoint**: GET /api/notifications/notifications/{id}/
- **Description**: Récupérer les détails d'une notification spécifique
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 123,
  "type": "TRIP_ACCEPTED",
  "channel": "PUSH",
  "title": "Course acceptée",
  "message": "Votre course a été acceptée par le capitaine Jean Dupont",
  "content_type": "trips.trip",
  "object_id": 45,
  "is_read": false,
  "created_at": "2025-05-25T10:30:45Z",
  "sent_at": "2025-05-25T10:30:46Z",
  "delivery_status": "DELIVERED",
  "error_message": ""
}
```

### 1.3. Marquer une notification comme lue
- **Endpoint**: POST /api/notifications/notifications/{id}/mark_as_read/
- **Description**: Marquer une notification spécifique comme lue
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "status": "notification marked as read"
}
```

### 1.4. Marquer toutes les notifications comme lues
- **Endpoint**: POST /api/notifications/notifications/mark_all_as_read/
- **Description**: Marquer toutes les notifications non lues comme lues
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "status": "all notifications marked as read"
}
```

### 1.5. Compteur de notifications non lues
- **Endpoint**: GET /api/notifications/notifications/unread_count/
- **Description**: Récupérer le nombre de notifications non lues
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "count": 5
}
```

## 2. Gestion des appareils

### 2.1. Liste des appareils
- **Endpoint**: GET /api/notifications/devices/
- **Description**: Récupérer la liste des appareils de l'utilisateur connecté
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "device_id": "d6b31aaf-6721-4c56-9f8d-8a5966ef7389",
    "device_type": "ANDROID",
    "name": "Samsung Galaxy S20",
    "push_token": "fEb82aKsQYeJcLq5gdKqaz:APA91bHJjhL1j...",
    "is_active": true,
    "last_used": "2025-05-25T10:30:45Z",
    "created_at": "2025-05-20T14:15:32Z"
  },
  {
    "id": 2,
    "device_id": "a7c42bfe-9835-4d67-8b2e-7c5439812710",
    "device_type": "IOS",
    "name": "iPhone 13",
    "push_token": "fG5hsTrQpLkM9nB7vC6xZy:IOS83jKs9Lm...",
    "is_active": true,
    "last_used": "2025-05-25T09:45:12Z",
    "created_at": "2025-05-15T11:30:20Z"
  }
]
```

### 2.2. Enregistrer un appareil
- **Endpoint**: POST /api/notifications/devices/
- **Description**: Enregistrer un appareil pour les notifications push
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "device_id": "d6b31aaf-6721-4c56-9f8d-8a5966ef7389",
  "device_type": "ANDROID",
  "name": "Samsung Galaxy S20",
  "push_token": "fEb82aKsQYeJcLq5gdKqaz:APA91bHJjhL1j..."
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "device_id": "d6b31aaf-6721-4c56-9f8d-8a5966ef7389",
  "device_type": "ANDROID",
  "name": "Samsung Galaxy S20",
  "push_token": "fEb82aKsQYeJcLq5gdKqaz:APA91bHJjhL1j...",
  "is_active": true,
  "last_used": "2025-05-25T10:30:45Z",
  "created_at": "2025-05-25T10:30:45Z"
}
```

### 2.3. Désactiver un appareil
- **Endpoint**: POST /api/notifications/devices/{id}/deactivate/
- **Description**: Désactiver un appareil pour ne plus recevoir de notifications push
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "device_id": "d6b31aaf-6721-4c56-9f8d-8a5966ef7389",
  "device_type": "ANDROID",
  "name": "Samsung Galaxy S20",
  "push_token": "fEb82aKsQYeJcLq5gdKqaz:APA91bHJjhL1j...",
  "is_active": false,
  "last_used": "2025-05-25T10:30:45Z",
  "deactivated_at": "2025-05-25T13:50:22Z",
  "created_at": "2025-05-25T10:30:45Z",
  "message": "Notifications désactivées pour cet appareil"
}
```

### 2.4. Supprimer un appareil
- **Endpoint**: DELETE /api/notifications/devices/{id}/
- **Description**: Supprimer définitivement un appareil
- **Auth Required**: Oui (JWT Token)
- **Response (204 No Content)**

## Fonctionnalités à implémenter

1. **Préférences de notifications** : Permettre aux utilisateurs de configurer quels types de notifications ils souhaitent recevoir et sur quels canaux (push, email, SMS, in-app).

2. **Filtrage des notifications** : Ajouter des options pour filtrer les notifications par type, date, statut de lecture, etc.

3. **Tests des notifications push** : Implémenter un endpoint pour tester l'envoi de notifications push vers un appareil spécifique.

4. **Regroupement des notifications** : Regrouper les notifications similaires pour éviter de surcharger l'utilisateur.

5. **Suppression des notifications** : Permettre aux utilisateurs de supprimer des notifications qu'ils ne souhaitent plus voir.

6. **Tests du système de notifications** : Endpoints de test pour les administrateurs afin de vérifier le bon fonctionnement du système de notifications.
