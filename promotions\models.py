from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings
import uuid

class Promotion(models.Model):
    """
    Modèle pour les promotions de services (capitaines, bateaux, établissements).
    """
    TYPE_CHOICES = (
        ('FEATURED_LISTING', _('Mise en avant')),
        ('TOP_SEARCH', _('Top recherche')),
        ('SPONSORED', _('Sponsorisé')),
        ('PREMIUM', _('Premium')),
    )
    
    STATUS_CHOICES = (
        ('PENDING', _('En attente')),
        ('ACTIVE', _('Active')),
        ('EXPIRED', _('Expirée')),
        ('CANCELLED', _('Annulée')),
    )
    
    TARGET_CHOICES = (
        ('CAPTAIN', _('Capitaine')),
        ('BOAT', _('Bateau')),
        ('ESTABLISHMENT', _('Établissement')),
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='promotions')
    type = models.CharField(_('type'), max_length=20, choices=TYPE_CHOICES)
    target_type = models.CharField(_('type de cible'), max_length=20, choices=TARGET_CHOICES)
    target_id = models.IntegerField(_('ID de la cible'))
    
    start_date = models.DateTimeField(_('date de début'))
    end_date = models.DateTimeField(_('date de fin'))
    status = models.CharField(_('statut'), max_length=20, choices=STATUS_CHOICES, default='PENDING')
    
    amount_paid = models.DecimalField(_('montant payé'), max_digits=10, decimal_places=2)
    transaction_id = models.CharField(_('ID de transaction'), max_length=100, blank=True, null=True)
    
    features = models.JSONField(_('fonctionnalités'), default=list)
    metadata = models.JSONField(_('métadonnées'), default=dict, blank=True)
    
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    
    class Meta:
        verbose_name = _('promotion')
        verbose_name_plural = _('promotions')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.get_type_display()} - {self.get_target_type_display()} #{self.target_id}"
    
    def activate(self):
        """
        Active la promotion.
        """
        self.status = 'ACTIVE'
        self.save()
    
    def cancel(self):
        """
        Annule la promotion.
        """
        self.status = 'CANCELLED'
        
        # Calculer le remboursement au prorata
        if self.status == 'ACTIVE' and self.transaction_id:
            total_duration = (self.end_date - self.start_date).days
            elapsed_duration = (timezone.now() - self.start_date).days
            
            if elapsed_duration < total_duration:
                # Remboursement proportionnel au temps restant
                refund_ratio = (total_duration - elapsed_duration) / total_duration
                refund_amount = self.amount_paid * refund_ratio
                
                # Initier le remboursement
                from payments.services import PaymentService
                try:
                    PaymentService.refund_payment(
                        transaction_id=self.transaction_id,
                        amount=refund_amount,
                        reason=f"Annulation de la promotion {self.id}"
                    )
                except Exception as e:
                    # Logger l'erreur
                    print(f"Erreur lors du remboursement: {str(e)}")
        
        self.save()
    
    def is_expired(self):
        """
        Vérifie si la promotion est expirée.
        """
        return self.end_date < timezone.now()
    
    def check_status(self):
        """
        Met à jour le statut de la promotion en fonction de sa date de fin.
        """
        if self.status == 'ACTIVE' and self.is_expired():
            self.status = 'EXPIRED'
            self.save()
            return True
        return False
    
    @classmethod
    def create_from_payment(cls, user, payment_data, transaction_id):
        """
        Crée une promotion à partir des données de paiement.
        """
        promotion_type = payment_data.get("promotion_type")
        duration_days = payment_data.get("duration_days", 30)
        target_type = payment_data.get("target_type")
        target_id = payment_data.get("target_id")
        amount = payment_data.get("amount")
        
        start_date = timezone.now()
        end_date = start_date + timezone.timedelta(days=duration_days)
        
        # Déterminer les fonctionnalités selon le type de promotion
        features = {
            "FEATURED_LISTING": [
                "Mise en avant dans les résultats de recherche",
                "Badge 'Recommandé'",
                "Visibilité dans la section 'Top choix'"
            ],
            "TOP_SEARCH": [
                "Positionnement en tête des résultats de recherche",
                "Badge 'Top recherche'",
                "Visibilité dans la section 'Populaires'"
            ],
            "SPONSORED": [
                "Affichage sponsorisé dans les pages connexes",
                "Badge 'Sponsorisé'",
                "Inclusion dans les emails promotionnels"
            ],
            "PREMIUM": [
                "Tous les avantages des autres niveaux",
                "Badge 'Premium'",
                "Publicité exclusive dans l'application",
                "Référencement prioritaire"
            ]
        }
        
        promotion = cls.objects.create(
            user=user,
            type=promotion_type,
            target_type=target_type,
            target_id=target_id,
            start_date=start_date,
            end_date=end_date,
            status='ACTIVE',
            amount_paid=amount,
            transaction_id=transaction_id,
            features=features.get(promotion_type, [])
        )
        
        return promotion
