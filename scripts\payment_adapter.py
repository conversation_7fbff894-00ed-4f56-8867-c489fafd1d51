"""
Script d'adaptation pour tester les fonctionnalités de paiement Stripe
sans avoir à modifier tous les fichiers de l'application payments.

Ce script sert d'interface entre l'architecture actuelle du projet Commodore
et l'application payments importée d'un autre projet.
"""
import os
import sys
import json
import stripe
import django
from django.conf import settings

# Configuration de Django pour utiliser le script en dehors du projet
sys.path.append('d:/commodore')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

# Importation des modèles et services nécessaires
from accounts.models import User, Captain, Client, Establishment
from trips.models import Booking
from payments.services import PaymentService
from payments.models import Payment, Transaction

# Configuration de Stripe avec la clé API
stripe.api_key = settings.STRIPE_SECRET_KEY

class PaymentAdapter:
    """
    Classe d'adaptation pour utiliser les fonctionnalités de paiement
    avec l'architecture actuelle du projet Commodore.
    """
    
    @staticmethod
    def get_user_wallet(user_id):
        """
        Récupère le portefeuille d'un utilisateur en fonction de son type.
        
        Args:
            user_id: L'ID de l'utilisateur
            
        Returns:
            Un dictionnaire avec les informations du portefeuille
        """
        try:
            user = User.objects.get(id=user_id)
            wallet_balance = 0
            
            if hasattr(user, 'client') and user.client:
                wallet_balance = user.client.wallet_balance
                user_type = 'client'
            elif hasattr(user, 'captain') and user.captain:
                wallet_balance = user.captain.wallet_balance
                user_type = 'captain'
            elif hasattr(user, 'establishment') and user.establishment:
                wallet_balance = user.establishment.wallet_balance
                user_type = 'establishment'
            else:
                user_type = 'unknown'
                
            return {
                'id': f'wallet_{user.id}',
                'user_id': user.id,
                'balance': wallet_balance,
                'user_type': user_type
            }
        except User.DoesNotExist:
            return None
    
    @staticmethod
    def update_wallet_balance(user_id, amount, operation='add'):
        """
        Met à jour le solde du portefeuille d'un utilisateur.
        
        Args:
            user_id: L'ID de l'utilisateur
            amount: Le montant à ajouter ou soustraire
            operation: 'add' pour ajouter, 'subtract' pour soustraire
            
        Returns:
            Un booléen indiquant si l'opération a réussi
        """
        try:
            user = User.objects.get(id=user_id)
            
            if hasattr(user, 'client') and user.client:
                wallet = user.client
            elif hasattr(user, 'captain') and user.captain:
                wallet = user.captain
            elif hasattr(user, 'establishment') and user.establishment:
                wallet = user.establishment
            else:
                return False
                
            if operation == 'add':
                wallet.wallet_balance += amount
            elif operation == 'subtract':
                wallet.wallet_balance -= amount
                if wallet.wallet_balance < 0:
                    wallet.wallet_balance = 0
                    
            wallet.save()
            return True
        except User.DoesNotExist:
            return False
    
    @staticmethod
    def create_payment_method(card_details):
        """
        Crée une méthode de paiement avec Stripe.
        
        Args:
            card_details: Détails de la carte de paiement
            
        Returns:
            L'objet méthode de paiement Stripe
        """
        try:
            payment_method = stripe.PaymentMethod.create(
                type='card',
                card={
                    'number': card_details.get('number'),
                    'exp_month': card_details.get('exp_month'),
                    'exp_year': card_details.get('exp_year'),
                    'cvc': card_details.get('cvc')
                }
            )
            return payment_method
        except stripe.error.StripeError as e:
            print(f"Erreur lors de la création de la méthode de paiement: {str(e)}")
            return None
    
    @staticmethod
    def process_payment(user_id, amount, payment_method_id=None, description="", metadata=None):
        """
        Traite un paiement avec Stripe ou via le portefeuille.
        
        Args:
            user_id: L'ID de l'utilisateur effectuant le paiement
            amount: Le montant à payer
            payment_method_id: ID de la méthode de paiement ou "wallet"
            description: Description du paiement
            metadata: Métadonnées additionnelles
            
        Returns:
            Dict avec les détails de la transaction
        """
        try:
            user = User.objects.get(id=user_id)
            
            # Si paiement par portefeuille
            if payment_method_id == "wallet":
                wallet = PaymentAdapter.get_user_wallet(user_id)
                
                if not wallet or wallet['balance'] < amount:
                    return {
                        "success": False,
                        "error": "Solde insuffisant dans le portefeuille"
                    }
                
                # Mettre à jour le solde du portefeuille
                success = PaymentAdapter.update_wallet_balance(user_id, amount, 'subtract')
                
                if not success:
                    return {
                        "success": False,
                        "error": "Erreur lors de la mise à jour du portefeuille"
                    }
                
                # Créer un enregistrement de paiement
                payment = Payment.objects.create(
                    amount=amount,
                    status='COMPLETED',
                    payment_method='wallet',
                    description=description,
                    metadata=metadata or {}
                )
                
                return {
                    "success": True,
                    "id": payment.id,
                    "amount": amount,
                    "currency": "eur",
                    "status": "succeeded",
                    "created_at": payment.created_at,
                    "payment_method": "wallet"
                }
            
            # Si paiement par carte
            else:
                # Utiliser le service de paiement existant
                payment_service = PaymentService()
                result = payment_service.process_payment(
                    user=user,
                    amount=amount,
                    payment_method_id=payment_method_id,
                    description=description,
                    metadata=metadata
                )
                
                return {
                    "success": True,
                    **result
                }
                
        except User.DoesNotExist:
            return {
                "success": False,
                "error": f"Utilisateur {user_id} non trouvé"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    @staticmethod
    def refund_payment(payment_id, amount=None, reason=None):
        """
        Effectue un remboursement pour un paiement.
        
        Args:
            payment_id: L'ID du paiement à rembourser
            amount: Le montant à rembourser (ou None pour tout rembourser)
            reason: Raison du remboursement
            
        Returns:
            Dict avec les détails du remboursement
        """
        try:
            payment = Payment.objects.get(id=payment_id)
            
            # Si paiement par portefeuille
            if payment.payment_method == 'wallet':
                # Extraire l'ID utilisateur du metadata
                user_id = payment.metadata.get('user_id')
                
                if not user_id:
                    return {
                        "success": False,
                        "error": "ID utilisateur non trouvé dans les métadonnées"
                    }
                
                refund_amount = amount or payment.amount
                
                # Remettre l'argent dans le portefeuille
                success = PaymentAdapter.update_wallet_balance(user_id, refund_amount, 'add')
                
                if not success:
                    return {
                        "success": False,
                        "error": "Erreur lors de la mise à jour du portefeuille"
                    }
                
                # Mettre à jour le statut du paiement
                payment.status = 'REFUNDED'
                payment.save()
                
                return {
                    "success": True,
                    "id": f"rf_{payment.id}",
                    "amount": refund_amount,
                    "currency": "eur",
                    "status": "succeeded",
                    "payment_id": payment.id
                }
            
            # Si paiement par carte
            else:
                # Utiliser le service de paiement existant
                payment_service = PaymentService()
                result = payment_service.refund_payment(
                    payment=payment,
                    amount=amount,
                    reason=reason
                )
                
                return {
                    "success": True,
                    **result
                }
                
        except Payment.DoesNotExist:
            return {
                "success": False,
                "error": f"Paiement {payment_id} non trouvé"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }


# Fonctions pour tester l'adaptateur
def test_create_payment_method():
    """Teste la création d'une méthode de paiement Stripe"""
    card_details = {
        'number': '****************',
        'exp_month': 12,
        'exp_year': 2030,
        'cvc': '123'
    }
    
    payment_method = PaymentAdapter.create_payment_method(card_details)
    
    if payment_method:
        print(f"Méthode de paiement créée avec succès: {payment_method.id}")
        return payment_method.id
    else:
        print("Échec de la création de la méthode de paiement")
        return None

def test_process_payment(user_id, amount, payment_method_id=None):
    """Teste le traitement d'un paiement"""
    metadata = {
        'test': True,
        'user_id': user_id
    }
    
    result = PaymentAdapter.process_payment(
        user_id=user_id,
        amount=amount,
        payment_method_id=payment_method_id,
        description="Test de paiement",
        metadata=metadata
    )
    
    print(f"Résultat du paiement: {json.dumps(result, indent=2, default=str)}")
    return result

def test_refund_payment(payment_id, amount=None):
    """Teste le remboursement d'un paiement"""
    result = PaymentAdapter.refund_payment(
        payment_id=payment_id,
        amount=amount,
        reason="Test de remboursement"
    )
    
    print(f"Résultat du remboursement: {json.dumps(result, indent=2, default=str)}")
    return result

if __name__ == "__main__":
    # Exemple d'utilisation de l'adaptateur pour tester les paiements
    print("=== Test de l'adaptateur de paiement ===")
    
    # 1. Créer une méthode de paiement
    payment_method_id = test_create_payment_method()
    
    if payment_method_id:
        # 2. Trouver un utilisateur pour le test
        try:
            test_user = User.objects.first()
            if test_user:
                print(f"Utilisateur de test: {test_user.email} (ID: {test_user.id})")
                
                # 3. Tester un paiement par carte
                payment_result = test_process_payment(
                    user_id=test_user.id,
                    amount=25.00,
                    payment_method_id=payment_method_id
                )
                
                if payment_result.get("success"):
                    payment_id = payment_result.get("id")
                    
                    # 4. Tester un remboursement
                    test_refund_payment(payment_id)
                
                # 5. Tester un paiement par portefeuille
                test_process_payment(
                    user_id=test_user.id,
                    amount=10.00,
                    payment_method_id="wallet"
                )
            else:
                print("Aucun utilisateur trouvé pour le test")
        except Exception as e:
            print(f"Erreur lors du test: {str(e)}")
    
    print("=== Fin des tests ===")
