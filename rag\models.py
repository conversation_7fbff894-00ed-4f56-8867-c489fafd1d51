from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
import uuid

class Document(models.Model):
    """
    Modèle pour stocker les documents utilisés dans le système RAG.

    Ces documents serviront de base de connaissances pour le système de récupération
    et de génération augmentée.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(_("Titre"), max_length=255)
    content = models.TextField(_("Contenu"))
    source = models.CharField(_("Source"), max_length=255, blank=True, null=True)
    url = models.URLField(_("URL"), blank=True, null=True)
    created_at = models.DateTimeField(_("Date de création"), default=timezone.now)
    updated_at = models.DateTimeField(_("Date de mise à jour"), auto_now=True)

    # Champs pour la catégorisation
    category = models.Char<PERSON><PERSON>(_("Catégorie"), max_length=100, blank=True, null=True)
    tags = models.JSONField(_("Tags"), default=list, blank=True)

    # Champs pour le traitement RAG
    embedding_generated = models.BooleanField(_("Embedding généré"), default=False)
    embedding_updated_at = models.DateTimeField(_("Date de mise à jour de l'embedding"), null=True, blank=True)

    class Meta:
        verbose_name = _("Document")
        verbose_name_plural = _("Documents")
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        """
        Surcharge de la méthode save pour marquer l'embedding comme non généré
        lorsque le contenu est modifié.
        """
        if self.pk:
            try:
                old_instance = Document.objects.get(pk=self.pk)
                if old_instance.content != self.content:
                    self.embedding_generated = False
                    self.embedding_updated_at = None
            except Document.DoesNotExist:
                # Si l'instance n'existe pas encore, on ne fait rien
                pass

        super().save(*args, **kwargs)


class DocumentChunk(models.Model):
    """
    Modèle pour stocker les chunks (morceaux) de documents.

    Les documents sont divisés en chunks pour une meilleure récupération
    et pour respecter les limites de contexte des LLMs.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='chunks')
    content = models.TextField(_("Contenu du chunk"))
    chunk_index = models.IntegerField(_("Index du chunk"))
    embedding = models.JSONField(_("Embedding vectoriel"), null=True, blank=True)
    embedding_generated = models.BooleanField(_("Embedding généré"), default=False)
    embedding_updated_at = models.DateTimeField(_("Date de mise à jour de l'embedding"), null=True, blank=True)
    metadata = models.JSONField(_("Métadonnées"), default=dict, blank=True, help_text=_("Métadonnées du chunk (section, profil, etc.)"))

    class Meta:
        verbose_name = _("Chunk de document")
        verbose_name_plural = _("Chunks de documents")
        ordering = ['document', 'chunk_index']
        unique_together = ['document', 'chunk_index']

    def __str__(self):
        section_title = self.metadata.get('section_title', '') if self.metadata else ''
        profile = self.metadata.get('profile', '') if self.metadata else ''
        if section_title and profile:
            return f"{self.document.title} - {section_title} ({profile}) - Chunk {self.chunk_index}"
        return f"{self.document.title} - Chunk {self.chunk_index}"


class ChatSession(models.Model):
    """
    Modèle pour stocker les sessions de chat.

    Chaque session de chat représente une conversation entre un utilisateur
    et le système RAG.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='chat_sessions')
    title = models.CharField(_("Titre"), max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(_("Date de création"), default=timezone.now)
    updated_at = models.DateTimeField(_("Date de mise à jour"), auto_now=True)

    class Meta:
        verbose_name = _("Session de chat")
        verbose_name_plural = _("Sessions de chat")
        ordering = ['-updated_at']

    def __str__(self):
        return self.title or f"Session {self.id}"

    def save(self, *args, **kwargs):
        if not self.title:
            self.title = f"Session du {timezone.now().strftime('%d/%m/%Y à %H:%M')}"
        super().save(*args, **kwargs)


class ChatMessage(models.Model):
    """
    Modèle pour stocker les messages de chat.

    Chaque message appartient à une session de chat et peut être soit
    de l'utilisateur, soit du système.
    """
    ROLE_USER = 'user'
    ROLE_ASSISTANT = 'assistant'
    ROLE_SYSTEM = 'system'

    ROLE_CHOICES = [
        (ROLE_USER, _("Utilisateur")),
        (ROLE_ASSISTANT, _("Assistant")),
        (ROLE_SYSTEM, _("Système")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(ChatSession, on_delete=models.CASCADE, related_name='messages')
    role = models.CharField(_("Rôle"), max_length=10, choices=ROLE_CHOICES)
    content = models.TextField(_("Contenu"))
    created_at = models.DateTimeField(_("Date de création"), default=timezone.now)

    # Champs pour le traitement RAG
    retrieved_documents = models.ManyToManyField(DocumentChunk, blank=True, related_name='referenced_in_messages')

    class Meta:
        verbose_name = _("Message de chat")
        verbose_name_plural = _("Messages de chat")
        ordering = ['session', 'created_at']

    def __str__(self):
        return f"{self.get_role_display()}: {self.content[:50]}..."
