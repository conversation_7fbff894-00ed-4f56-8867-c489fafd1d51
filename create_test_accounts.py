"""
Script pour créer des comptes de test complets pour tous les types d'utilisateurs.
Ce script va créer des données réalistes pour tester l'ensemble du système.
"""

import os
import sys
import django
from django.contrib.auth import get_user_model
from decimal import Decimal
from django.utils import timezone
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Client, Captain, Establishment
from boats.models import Boat
from payments.models import Wallet
from rest_framework.authtoken.models import Token

User = get_user_model()

def create_test_accounts():
    """Créer tous les comptes de test nécessaires"""
    
    print("🚀 CRÉATION DES COMPTES DE TEST POUR COMMODORE")
    print("=" * 60)
    
    # 1. CRÉER UN CLIENT DE TEST
    print("\n1. 👤 CRÉATION CLIENT DE TEST")
    try:
        client_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestClient123!',
            first_name='<PERSON>',
            last_name='<PERSON><PERSON>',
            phone_number='+***********'
        )
        client_user.is_verified = True
        client_user.save()
        
        client_profile = Client.objects.create(
            user=client_user,
            date_of_birth='1990-05-15',
            emergency_contact_name='Pierre Dubois',
            emergency_contact_phone='+***********'
        )
        
        # Créer portefeuille avec solde
        client_wallet = Wallet.objects.create(
            user=client_user,
            balance=Decimal('500.00')
        )
        
        # Créer token API
        client_token = Token.objects.create(user=client_user)
        
        print(f"   ✅ Client créé: {client_user.email}")
        print(f"   💰 Portefeuille: {client_wallet.balance}€")
        print(f"   🔑 Token: {client_token.key}")
        
    except Exception as e:
        print(f"   ⚠️ Client existe déjà ou erreur: {e}")
        client_user = User.objects.get(email='<EMAIL>')
        client_profile = client_user.client
        client_token = Token.objects.get_or_create(user=client_user)[0]
    
    # 2. CRÉER UN ÉTABLISSEMENT DE TEST
    print("\n2. 🏨 CRÉATION ÉTABLISSEMENT DE TEST")
    try:
        establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestHotel123!',
            first_name='Hotel',
            last_name='Paradise',
            phone_number='+33444555666'
        )
        establishment_user.is_verified = True
        establishment_user.save()
        
        establishment = Establishment.objects.create(
            user=establishment_user,
            name='Hotel Paradise Beach',
            type='HOTEL',
            address='123 Promenade des Anglais, 06000 Cannes',
            phone_number='+33444555666',
            description='Hôtel 5 étoiles face à la mer',
            website='https://hotel-paradise.com'
        )
        
        # Créer portefeuille établissement
        establishment_wallet = Wallet.objects.create(
            user=establishment_user,
            balance=Decimal('1000.00')
        )
        
        # Créer token API
        establishment_token = Token.objects.create(user=establishment_user)
        
        print(f"   ✅ Établissement créé: {establishment.name}")
        print(f"   📍 Adresse: {establishment.address}")
        print(f"   💰 Portefeuille: {establishment_wallet.balance}€")
        print(f"   🔑 Token: {establishment_token.key}")
        
    except Exception as e:
        print(f"   ⚠️ Établissement existe déjà ou erreur: {e}")
        establishment_user = User.objects.get(email='<EMAIL>')
        establishment = establishment_user.establishment
        establishment_token = Token.objects.get_or_create(user=establishment_user)[0]
    
    # 3. CRÉER UN BATELIER ENREGISTRÉ PAR L'ÉTABLISSEMENT
    print("\n3. ⚓ CRÉATION BATELIER DE L'ÉTABLISSEMENT")
    try:
        boatman_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestBoatman123!',
            first_name='Pierre',
            last_name='Batelier',
            phone_number='+33111222333'
        )
        boatman_user.is_verified = True
        boatman_user.is_captain = True
        boatman_user.save()
        
        boatman = Captain.objects.create(
            user=boatman_user,
            experience='Batelier officiel Hotel Paradise - 5 ans d\'expérience navettes',
            license_number='BOAT_PARADISE_001',
            rate_per_km=Decimal('15.00'),
            rate_per_hour=Decimal('35.00'),
            is_available=True,
            availability_status='AVAILABLE',
            metadata={'registered_by_establishment_id': establishment.id}
        )
        
        # Créer bateau du batelier
        boatman_boat = Boat.objects.create(
            captain=boatman,
            name='Paradise Shuttle',
            boat_type='classic',
            capacity=8,
            fuel_type='gasoline',
            fuel_consumption=18.0,
            registration_number='PARADISE_001',
            is_available=True,
            description='Navette officielle Hotel Paradise'
        )
        
        # Créer portefeuille batelier
        boatman_wallet = Wallet.objects.create(
            user=boatman_user,
            balance=Decimal('200.00')
        )
        
        # Créer token API
        boatman_token = Token.objects.create(user=boatman_user)
        
        print(f"   ✅ Batelier créé: {boatman_user.get_full_name()}")
        print(f"   🚤 Bateau: {boatman_boat.name} (capacité: {boatman_boat.capacity})")
        print(f"   🏨 Établissement: {establishment.name}")
        print(f"   💰 Portefeuille: {boatman_wallet.balance}€")
        print(f"   🔑 Token: {boatman_token.key}")
        
    except Exception as e:
        print(f"   ⚠️ Batelier existe déjà ou erreur: {e}")
        boatman_user = User.objects.get(email='<EMAIL>')
        boatman = boatman_user.captain
        boatman_boat = boatman.boat
        boatman_token = Token.objects.get_or_create(user=boatman_user)[0]
    
    # 4. CRÉER UN CAPITAINE INDÉPENDANT
    print("\n4. 🧑‍✈️ CRÉATION CAPITAINE INDÉPENDANT")
    try:
        captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestCaptain123!',
            first_name='Jack',
            last_name='Sparrow',
            phone_number='+33777888999'
        )
        captain_user.is_verified = True
        captain_user.is_captain = True
        captain_user.save()
        
        captain = Captain.objects.create(
            user=captain_user,
            experience='Capitaine indépendant - 15 ans d\'expérience en mer',
            license_number='CAPTAIN_JACK_001',
            rate_per_km=Decimal('25.00'),
            rate_per_hour=Decimal('50.00'),
            is_available=True,
            availability_status='AVAILABLE'
        )
        
        # Créer bateau du capitaine
        captain_boat = Boat.objects.create(
            captain=captain,
            name='Black Pearl',
            boat_type='speedboat',
            capacity=12,
            fuel_type='diesel',
            fuel_consumption=22.0,
            registration_number='JACK_001',
            is_available=True,
            description='Speedboat de luxe pour excursions privées'
        )
        
        # Créer portefeuille capitaine
        captain_wallet = Wallet.objects.create(
            user=captain_user,
            balance=Decimal('300.00')
        )
        
        # Créer token API
        captain_token = Token.objects.create(user=captain_user)
        
        print(f"   ✅ Capitaine créé: {captain_user.get_full_name()}")
        print(f"   🚤 Bateau: {captain_boat.name} (capacité: {captain_boat.capacity})")
        print(f"   💰 Tarifs: {captain.rate_per_km}€/km - {captain.rate_per_hour}€/h")
        print(f"   💰 Portefeuille: {captain_wallet.balance}€")
        print(f"   🔑 Token: {captain_token.key}")
        
    except Exception as e:
        print(f"   ⚠️ Capitaine existe déjà ou erreur: {e}")
        captain_user = User.objects.get(email='<EMAIL>')
        captain = captain_user.captain
        captain_boat = captain.boat
        captain_token = Token.objects.get_or_create(user=captain_user)[0]
    
    # 5. CRÉER UN DEUXIÈME CAPITAINE POUR LES TESTS
    print("\n5. 🧑‍✈️ CRÉATION DEUXIÈME CAPITAINE")
    try:
        captain2_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestCaptain123!',
            first_name='James',
            last_name='Hook',
            phone_number='+33555666777'
        )
        captain2_user.is_verified = True
        captain2_user.is_captain = True
        captain2_user.save()
        
        captain2 = Captain.objects.create(
            user=captain2_user,
            experience='Capitaine expérimenté - 12 ans en Méditerranée',
            license_number='CAPTAIN_HOOK_001',
            rate_per_km=Decimal('20.00'),
            rate_per_hour=Decimal('45.00'),
            is_available=True,
            availability_status='AVAILABLE'
        )
        
        # Créer bateau du capitaine 2
        captain2_boat = Boat.objects.create(
            captain=captain2,
            name='Jolly Roger',
            boat_type='classic',
            capacity=10,
            fuel_type='gasoline',
            fuel_consumption=20.0,
            registration_number='HOOK_001',
            is_available=True,
            description='Bateau classique pour balades familiales'
        )
        
        # Créer portefeuille capitaine 2
        captain2_wallet = Wallet.objects.create(
            user=captain2_user,
            balance=Decimal('250.00')
        )
        
        # Créer token API
        captain2_token = Token.objects.create(user=captain2_user)
        
        print(f"   ✅ Capitaine 2 créé: {captain2_user.get_full_name()}")
        print(f"   🚤 Bateau: {captain2_boat.name} (capacité: {captain2_boat.capacity})")
        print(f"   💰 Tarifs: {captain2.rate_per_km}€/km - {captain2.rate_per_hour}€/h")
        print(f"   💰 Portefeuille: {captain2_wallet.balance}€")
        print(f"   🔑 Token: {captain2_token.key}")
        
    except Exception as e:
        print(f"   ⚠️ Capitaine 2 existe déjà ou erreur: {e}")
        captain2_user = User.objects.get(email='<EMAIL>')
        captain2 = captain2_user.captain
        captain2_boat = captain2.boat
        captain2_token = Token.objects.get_or_create(user=captain2_user)[0]
    
    print("\n" + "=" * 60)
    print("✅ TOUS LES COMPTES DE TEST SONT CRÉÉS !")
    print("\n📋 RÉSUMÉ DES COMPTES :")
    print(f"👤 Client: {client_user.email} (Token: {client_token.key[:20]}...)")
    print(f"🏨 Établissement: {establishment_user.email} (Token: {establishment_token.key[:20]}...)")
    print(f"⚓ Batelier: {boatman_user.email} (Token: {boatman_token.key[:20]}...)")
    print(f"🧑‍✈️ Capitaine 1: {captain_user.email} (Token: {captain_token.key[:20]}...)")
    print(f"🧑‍✈️ Capitaine 2: {captain2_user.email} (Token: {captain2_token.key[:20]}...)")
    
    return {
        'client': {'user': client_user, 'profile': client_profile, 'token': client_token.key},
        'establishment': {'user': establishment_user, 'profile': establishment, 'token': establishment_token.key},
        'boatman': {'user': boatman_user, 'profile': boatman, 'boat': boatman_boat, 'token': boatman_token.key},
        'captain1': {'user': captain_user, 'profile': captain, 'boat': captain_boat, 'token': captain_token.key},
        'captain2': {'user': captain2_user, 'profile': captain2, 'boat': captain2_boat, 'token': captain2_token.key}
    }

if __name__ == '__main__':
    accounts = create_test_accounts()
    print(f"\n🎉 COMPTES PRÊTS POUR LES TESTS API !")
