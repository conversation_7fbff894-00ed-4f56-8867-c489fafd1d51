from django.urls import path, include
from rest_framework_nested import routers
from .views import ChatRoomViewSet, MessageViewSet, ChatbotSessionViewSet, ChatbotMessageViewSet
from .api import chatbot_message, chatbot_history, chatbot_clear_session
from .feedback import submit_feedback, get_feedback_stats

router = routers.DefaultRouter()
router.register('rooms', ChatRoomViewSet, basename='chatroom')
router.register('chatbot/sessions', ChatbotSessionViewSet, basename='chatbot-session')

# Routeur imbriqué pour les messages dans un salon
rooms_router = routers.NestedDefaultRouter(router, 'rooms', lookup='room')
rooms_router.register('messages', MessageViewSet, basename='room-message')

# Routeur imbriqué pour les messages dans une session chatbot
chatbot_router = routers.NestedDefaultRouter(router, 'chatbot/sessions', lookup='session')
chatbot_router.register('messages', ChatbotMessageViewSet, basename='chatbot-message')

app_name = 'chat'

urlpatterns = [
    path('', include(router.urls)),
    path('', include(rooms_router.urls)),
    path('', include(chatbot_router.urls)),
    
    # Nouvelles API pour le chatbot avec Meta-Llama-3
    path('chatbot/message/', chatbot_message, name='chatbot-message-api'),
    path('chatbot/history/', chatbot_history, name='chatbot-history-all'),
    path('chatbot/history/<uuid:session_id>/', chatbot_history, name='chatbot-history-session'),
    path('chatbot/clear/<uuid:session_id>/', chatbot_clear_session, name='chatbot-clear-session'),
    
    # API pour le feedback sur les réponses du chatbot
    path('chatbot/feedback/', submit_feedback, name='chatbot-feedback'),
    path('chatbot/feedback/stats/', get_feedback_stats, name='chatbot-feedback-stats'),
]
