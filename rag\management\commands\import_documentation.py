"""
Commande Django pour importer la documentation Commodore dans le système RAG.

Usage:
    python manage.py import_documentation
"""

import os
import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from rag.models import Document
from rag.services import rag_service

# Configurer le logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Importe la documentation Commodore dans le système RAG'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Début de l\'importation de la documentation Commodore'))
        
        # Importer la documentation en sections
        documents = self.import_documentation_sections()
        
        if documents:
            self.stdout.write(self.style.SUCCESS(f'Importation terminée avec succès: {len(documents)} sections importées'))
        else:
            self.stdout.write(self.style.ERROR('Échec de l\'importation de la documentation'))

    def read_markdown_file(self, file_path):
        """
        Lit un fichier markdown et retourne son contenu.
        
        Args:
            file_path: Chemin vers le fichier markdown
            
        Returns:
            Le contenu du fichier
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            return content
        except Exception as e:
            logger.error(f"Erreur lors de la lecture du fichier {file_path}: {str(e)}")
            self.stdout.write(self.style.ERROR(f"Erreur lors de la lecture du fichier {file_path}: {str(e)}"))
            return None

    def import_documentation_sections(self):
        """
        Importe la documentation Commodore en sections distinctes pour une meilleure organisation.
        """
        # Chemin vers le fichier de documentation
        doc_path = 'doc_document.md'
        
        # Lire le contenu du fichier
        content = self.read_markdown_file(doc_path)
        if not content:
            logger.error("Impossible de lire le fichier de documentation")
            return []
        
        # Diviser le contenu en sections
        sections = []
        current_section = {"title": "Introduction Commodore", "content": ""}
        lines = content.split('\n')
        
        for line in lines:
            # Détecter les titres de section (lignes commençant par "I.", "II.", etc.)
            if line.strip().startswith(("I.", "II.", "III.", "IV.")):
                # Sauvegarder la section précédente si elle contient du contenu
                if current_section["content"].strip():
                    sections.append(current_section)
                
                # Créer une nouvelle section
                current_section = {
                    "title": line.strip(),
                    "content": line + "\n"
                }
            # Détecter les titres de section (lignes contenant "PROFIL CLIENT", "PROFIL CAPITAINE", etc.)
            elif any(keyword in line for keyword in ["PROFIL", "MODULE", "Mentions légales", "FAQ", "Centre d'Aide", "Qui sommes-nous"]):
                # Sauvegarder la section précédente si elle contient du contenu
                if current_section["content"].strip():
                    sections.append(current_section)
                
                # Créer une nouvelle section
                current_section = {
                    "title": line.strip(),
                    "content": line + "\n"
                }
            else:
                # Ajouter la ligne à la section courante
                current_section["content"] += line + "\n"
        
        # Ajouter la dernière section
        if current_section["content"].strip():
            sections.append(current_section)
        
        # Créer un document pour chaque section
        documents = []
        for section in sections:
            try:
                # Déterminer la catégorie en fonction du titre
                category = "Général"
                if "CLIENT" in section["title"]:
                    category = "Client"
                elif "CAPITAINE" in section["title"] or "Capitaine" in section["title"]:
                    category = "Capitaine"
                elif "ÉTABLISSEMENT" in section["title"] or "Établissement" in section["title"]:
                    category = "Établissement"
                elif "Mentions légales" in section["title"] or "CGU" in section["title"]:
                    category = "Légal"
                elif "FAQ" in section["title"] or "Centre d'Aide" in section["title"]:
                    category = "FAQ"
                
                # Déterminer les tags
                tags = ["commodore", "taxi-boat", "documentation"]
                if "paiement" in section["title"].lower() or "crédit" in section["title"].lower():
                    tags.append("paiement")
                if "QR code" in section["title"] or "QR code" in section["content"]:
                    tags.append("qr-code")
                if "annulation" in section["title"].lower() or "annulation" in section["content"].lower():
                    tags.append("annulation")
                
                # Créer le document
                document = Document.objects.create(
                    title=section["title"],
                    content=section["content"],
                    source="Fichier doc_document.md",
                    category=category,
                    tags=tags
                )
                self.stdout.write(self.style.SUCCESS(f"Document créé avec succès: {document.id} - {document.title}"))
                
                # Traiter le document pour générer des embeddings
                self.stdout.write(f"Traitement du document {document.title} en cours...")
                rag_service.process_document(document)
                self.stdout.write(self.style.SUCCESS(f"Document {document.title} traité avec succès"))
                
                documents.append(document)
            except Exception as e:
                logger.error(f"Erreur lors de l'importation de la section {section['title']}: {str(e)}")
                self.stdout.write(self.style.ERROR(f"Erreur lors de l'importation de la section {section['title']}: {str(e)}"))
        
        return documents
