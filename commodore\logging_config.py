"""
Configuration complète du système de logging pour Commodore.
Ce fichier configure tous les loggers, handlers et formatters pour un logging optimal.
"""

import os
import logging
from datetime import datetime
from pathlib import Path

# Créer le dossier logs s'il n'existe pas
LOGS_DIR = Path(__file__).parent.parent / 'logs'
LOGS_DIR.mkdir(exist_ok=True)

# Configuration des niveaux de log par environnement
LOG_LEVELS = {
    'development': logging.DEBUG,
    'staging': logging.INFO,
    'production': logging.WARNING,
}

# Obtenir l'environnement actuel
ENVIRONMENT = os.getenv('DJANGO_ENV', 'development')
DEFAULT_LOG_LEVEL = LOG_LEVELS.get(ENVIRONMENT, logging.DEBUG)

# Configuration des formatters
FORMATTERS = {
    'verbose': {
        'format': '{levelname} {asctime} {name} {process:d} {thread:d} {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S',
    },
    'simple': {
        'format': '{levelname} {asctime} {name}: {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S',
    },
    'json': {
        'format': '{{"level": "{levelname}", "time": "{asctime}", "logger": "{name}", "message": "{message}", "module": "{module}", "function": "{funcName}", "line": {lineno}}}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S',
    },
    'security': {
        'format': 'SECURITY {levelname} {asctime} {name} [{process:d}] {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S',
    },
    'payment': {
        'format': 'PAYMENT {levelname} {asctime} {name} [{process:d}] {message}',
        'style': '{',
        'datefmt': '%Y-%m-%d %H:%M:%S',
    },
}

# Configuration des handlers
HANDLERS = {
    'console': {
        'level': 'DEBUG',
        'class': 'logging.StreamHandler',
        'formatter': 'simple',
    },
    'file_debug': {
        'level': 'DEBUG',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'debug.log'),
        'maxBytes': 10 * 1024 * 1024,  # 10MB
        'backupCount': 5,
        'formatter': 'verbose',
    },
    'file_info': {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'info.log'),
        'maxBytes': 10 * 1024 * 1024,  # 10MB
        'backupCount': 5,
        'formatter': 'verbose',
    },
    'file_error': {
        'level': 'ERROR',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'error.log'),
        'maxBytes': 10 * 1024 * 1024,  # 10MB
        'backupCount': 10,
        'formatter': 'verbose',
    },
    'file_security': {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'security.log'),
        'maxBytes': 10 * 1024 * 1024,  # 10MB
        'backupCount': 10,
        'formatter': 'security',
    },
    'file_payment': {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'payments.log'),
        'maxBytes': 10 * 1024 * 1024,  # 10MB
        'backupCount': 10,
        'formatter': 'payment',
    },
    'file_api': {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'api.log'),
        'maxBytes': 10 * 1024 * 1024,  # 10MB
        'backupCount': 5,
        'formatter': 'json',
    },
    'file_database': {
        'level': 'WARNING',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'database.log'),
        'maxBytes': 10 * 1024 * 1024,  # 10MB
        'backupCount': 5,
        'formatter': 'verbose',
    },
    'file_trips': {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'trips.log'),
        'maxBytes': 10 * 1024 * 1024,  # 10MB
        'backupCount': 5,
        'formatter': 'verbose',
    },
    'file_chat': {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'chat.log'),
        'maxBytes': 5 * 1024 * 1024,  # 5MB
        'backupCount': 3,
        'formatter': 'verbose',
    },
    'file_notifications': {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOGS_DIR / 'notifications.log'),
        'maxBytes': 5 * 1024 * 1024,  # 5MB
        'backupCount': 3,
        'formatter': 'verbose',
    },
}

# Configuration des loggers
LOGGERS = {
    'django': {
        'handlers': ['console', 'file_info', 'file_error'],
        'level': 'INFO',
        'propagate': False,
    },
    'django.request': {
        'handlers': ['file_error', 'console'],
        'level': 'ERROR',
        'propagate': False,
    },
    'django.db.backends': {
        'handlers': ['file_database'],
        'level': 'WARNING',
        'propagate': False,
    },
    'django.security': {
        'handlers': ['file_security', 'console'],
        'level': 'INFO',
        'propagate': False,
    },
    'commodore': {
        'handlers': ['console', 'file_debug', 'file_info', 'file_error'],
        'level': DEFAULT_LOG_LEVEL,
        'propagate': False,
    },
    'commodore.payments': {
        'handlers': ['file_payment', 'console', 'file_error'],
        'level': 'INFO',
        'propagate': False,
    },
    'commodore.security': {
        'handlers': ['file_security', 'console'],
        'level': 'INFO',
        'propagate': False,
    },
    'commodore.api': {
        'handlers': ['file_api', 'console'],
        'level': 'INFO',
        'propagate': False,
    },
    'commodore.trips': {
        'handlers': ['file_trips', 'console', 'file_error'],
        'level': 'INFO',
        'propagate': False,
    },
    'commodore.chat': {
        'handlers': ['file_chat', 'console'],
        'level': 'INFO',
        'propagate': False,
    },
    'commodore.notifications': {
        'handlers': ['file_notifications', 'console'],
        'level': 'INFO',
        'propagate': False,
    },
    'stripe': {
        'handlers': ['file_payment', 'console'],
        'level': 'INFO',
        'propagate': False,
    },
    'payments.wallet_security_service': {
        'handlers': ['file_payment', 'file_security', 'console'],
        'level': 'INFO',
        'propagate': False,
    },
}

# Configuration complète du logging
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': FORMATTERS,
    'handlers': HANDLERS,
    'loggers': LOGGERS,
    'root': {
        'level': DEFAULT_LOG_LEVEL,
        'handlers': ['console', 'file_error'],
    },
}

def setup_logging():
    """Configure le système de logging"""
    import logging.config
    logging.config.dictConfig(LOGGING_CONFIG)

    # Logger de démarrage
    logger = logging.getLogger('commodore')
    logger.info(f"Système de logging configuré pour l'environnement: {ENVIRONMENT}")
    logger.info(f"Niveau de log par défaut: {logging.getLevelName(DEFAULT_LOG_LEVEL)}")
    logger.info(f"Dossier des logs: {LOGS_DIR}")

def get_logger(name):
    """Obtenir un logger configuré"""
    return logging.getLogger(f'commodore.{name}')

def log_api_request(request, response=None, extra_data=None):
    """Logger une requête API avec tous les détails"""
    logger = get_logger('api')

    log_data = {
        'method': request.method,
        'path': request.path,
        'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
        'ip': request.META.get('REMOTE_ADDR', 'Unknown'),
        'user_agent': request.META.get('HTTP_USER_AGENT', 'Unknown'),
    }

    if response:
        log_data['status_code'] = response.status_code
        log_data['response_time'] = getattr(response, '_response_time', 'Unknown')

    if extra_data:
        log_data.update(extra_data)

    logger.info(f"API Request: {log_data}")

def log_payment_operation(operation, user, amount, status, extra_data=None):
    """Logger une opération de paiement"""
    logger = get_logger('payments')

    log_data = {
        'operation': operation,
        'user_id': user.id if user else None,
        'user_email': user.email if user else None,
        'amount': str(amount),
        'status': status,
        'timestamp': datetime.now().isoformat(),
    }

    if extra_data:
        log_data.update(extra_data)

    logger.info(f"Payment Operation: {log_data}")

def log_security_event(event_type, user, details, severity='INFO'):
    """Logger un événement de sécurité"""
    logger = get_logger('security')

    log_data = {
        'event_type': event_type,
        'user_id': user.id if user else None,
        'user_email': user.email if user else None,
        'details': details,
        'timestamp': datetime.now().isoformat(),
    }

    if severity == 'ERROR':
        logger.error(f"Security Event: {log_data}")
    elif severity == 'WARNING':
        logger.warning(f"Security Event: {log_data}")
    else:
        logger.info(f"Security Event: {log_data}")

def log_trip_event(event_type, trip, user, details=None):
    """Logger un événement de course"""
    logger = get_logger('trips')

    log_data = {
        'event_type': event_type,
        'trip_id': trip.id if trip else None,
        'user_id': user.id if user else None,
        'user_email': user.email if user else None,
        'trip_status': trip.status if trip else None,
        'timestamp': datetime.now().isoformat(),
    }

    if details:
        log_data.update(details)

    logger.info(f"Trip Event: {log_data}")

# Initialiser le logging au démarrage
if __name__ != '__main__':
    setup_logging()
