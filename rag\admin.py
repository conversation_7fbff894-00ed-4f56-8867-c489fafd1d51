from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Document, DocumentChunk, ChatSession, ChatMessage
from .models_feedback import ChatFeedback


class DocumentChunkInline(admin.TabularInline):
    model = DocumentChunk
    extra = 0
    readonly_fields = ('embedding_generated', 'embedding_updated_at')
    fields = ('chunk_index', 'content', 'embedding_generated', 'embedding_updated_at')
    can_delete = False
    max_num = 0  # Empêche l'ajout manuel de chunks


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ('id','title', 'category', 'created_at', 'embedding_generated')
    list_filter = ('category', 'embedding_generated', 'created_at')
    search_fields = ('title', 'content', 'source')
    readonly_fields = ('embedding_generated', 'embedding_updated_at')
    fieldsets = (
        (None, {
            'fields': ('title', 'content', 'source', 'url')
        }),
        (_('Catégorisation'), {
            'fields': ('category', 'tags')
        }),
        (_('Métadonnées'), {
            'fields': ('created_at', 'updated_at', 'embedding_generated', 'embedding_updated_at'),
            'classes': ('collapse',)
        }),
    )
    inlines = [DocumentChunkInline]


@admin.register(DocumentChunk)
class DocumentChunkAdmin(admin.ModelAdmin):
    list_display = ('document', 'chunk_index', 'embedding_generated')
    list_filter = ('embedding_generated', 'document')
    search_fields = ('content', 'document__title')
    readonly_fields = ('document', 'chunk_index', 'embedding_generated', 'embedding_updated_at')
    fieldsets = (
        (None, {
            'fields': ('document', 'chunk_index', 'content')
        }),
        (_('Embedding'), {
            'fields': ('embedding_generated', 'embedding_updated_at'),
            'classes': ('collapse',)
        }),
    )


class ChatMessageInline(admin.TabularInline):
    model = ChatMessage
    extra = 0
    readonly_fields = ('role', 'content', 'created_at')
    fields = ('role', 'content', 'created_at')
    can_delete = False
    max_num = 0  # Empêche l'ajout manuel de messages


@admin.register(ChatSession)
class ChatSessionAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'created_at', 'updated_at', 'message_count')
    list_filter = ('created_at', 'user')
    search_fields = ('title', 'user__email')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [ChatMessageInline]

    def message_count(self, obj):
        return obj.messages.count()
    message_count.short_description = _("Nombre de messages")


@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ('session', 'role', 'short_content', 'created_at')
    list_filter = ('role', 'created_at', 'session')
    search_fields = ('content', 'session__title')
    readonly_fields = ('session', 'role', 'content', 'created_at')
    filter_horizontal = ('retrieved_documents',)

    def short_content(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    short_content.short_description = _("Contenu")


@admin.register(ChatFeedback)
class ChatFeedbackAdmin(admin.ModelAdmin):
    list_display = ('message', 'feedback_type', 'created_at', 'user', 'has_comments')
    list_filter = ('feedback_type', 'created_at')
    search_fields = ('comments', 'message__content', 'user__email')
    readonly_fields = ('message', 'user', 'feedback_type', 'comments', 'created_at', 'ip_address', 'user_agent')

    def has_comments(self, obj):
        return bool(obj.comments)
    has_comments.boolean = True
    has_comments.short_description = _("Commentaires")
