import os
import sys
import django
from datetime import datetime, timedelta
import jwt
from django.utils import timezone

# Configurer l'environnement Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

# Importer les modèles après avoir configuré Django
from django.contrib.auth import get_user_model
from django.conf import settings

User = get_user_model()

def generate_token_for_user(email):
    """
    Génère un token JWT pour l'utilisateur spécifié par son email.
    """
    try:
        user = User.objects.get(email=email)
        
        # Créer le payload JWT
        payload = {
            'token_type': 'access',
            'exp': timezone.now() + timedelta(days=1),  # Expiration dans 1 jour
            'iat': timezone.now(),
            'jti': f"{user.id}_{timezone.now().timestamp()}",
            'user_id': user.id
        }
        
        # Générer le token avec la clé secrète Django
        token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
        
        print(f"\nToken JWT généré pour {email} (ID: {user.id}):")
        print(f"\n{token}\n")
        print(f"Ce token est valide pour 24 heures à partir de maintenant.")
        print(f"Copiez ce token dans la variable $TOKEN du script test_payments.ps1\n")
        
        return token
    except User.DoesNotExist:
        print(f"Erreur: Utilisateur avec l'email {email} non trouvé.")
        return None
    except Exception as e:
        print(f"Erreur lors de la génération du token: {e}")
        return None

if __name__ == "__main__":
    print("\n=== GÉNÉRATEUR DE TOKEN JWT POUR LES TESTS DE PAIEMENT ===\n")
    
    # Lister les utilisateurs disponibles
    print("Utilisateurs disponibles pour la génération de token:")
    for user in User.objects.all():
        print(f"- {user.email} (ID: {user.id}, Type: {user.type})")
    
    # Générer un token pour l'utilisateur client par défaut
    print("\nGénération du token pour l'utilisateur client (recommandé pour les tests)...")
    generate_token_for_user('<EMAIL>')
    
    # Option pour générer un token pour un autre utilisateur
    print("Pour générer un token pour un autre utilisateur, vous pouvez modifier ce script.")
