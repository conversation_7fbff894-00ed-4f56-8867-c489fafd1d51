"""
Module d'adaptation pour les paiements de trips.

Ce module contient les adaptateurs pour les vues de paiement utilisant 
exclusivement la terminologie 'trip' au lieu de 'ride'.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from .views_api import TripPaymentView as BaseTripPaymentView
from .views_extended import SharedPaymentView as BaseSharedPaymentView
from trips.models import Trip
from django.shortcuts import get_object_or_404

class TripPaymentView(APIView):
    """
    Vue adaptée pour payer une course (trip).
    
    Cette vue adapte l'API pour accepter un trip_id dans l'URL.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, trip_id=None):
        """
        Paie une course avec le portefeuille de l'utilisateur connecté.
        
        Args:
            request: Requête HTTP
            trip_id: ID de la course (depuis l'URL)
            
        Returns:
            Response: Données de la transaction au format JSON
        """
        # Vérifier si trip_id est dans l'URL ou dans le corps de la requête
        body_trip_id = request.data.get('trip_id')
        
        if trip_id or body_trip_id:
            # Priorité à l'ID de l'URL s'il est fourni
            final_trip_id = trip_id if trip_id else body_trip_id
            
            # Vérifier que le trip existe
            trip = get_object_or_404(Trip, id=final_trip_id)
            
            # Ajouter trip_id aux données de la requête
            request._mutable = True
            request.data['trip_id'] = final_trip_id
            request._mutable = False
            
            # Déléguer à la vue de base
            base_view = BaseTripPaymentView()
            return base_view.post(request)
        else:
            return Response(
                {"error": "L'ID de la course (trip_id) est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )


class SharedTripPaymentView(APIView):
    """
    Vue adaptée pour payer une course partagée (shared trip).
    
    Cette vue adapte l'API pour accepter un trip_id dans l'URL.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, trip_id=None):
        """
        Paie une part de course partagée avec le portefeuille de l'utilisateur connecté.
        
        Args:
            request: Requête HTTP
            trip_id: ID de la course (depuis l'URL)
            
        Returns:
            Response: Données de la transaction au format JSON
        """
        # Vérifier que le trip existe
        if trip_id:
            trip = get_object_or_404(Trip, id=trip_id)
            
            # Ajouter ride_id aux données de la requête (pour compatibilité avec le code existant)
            request._mutable = True
            request.data['ride_id'] = trip_id
            request._mutable = False
            
            # Vérifier si une invitation de paiement partagé existe pour cette course et l'utilisateur connecté
            from trips.shared_payments import SharedPaymentInvitation
            
            # Récupérer le token d'invitation passé dans la requête
            token = request.data.get('token')
            
            if not token:
                return Response(
                    {"error": "Le token d'invitation est requis pour un paiement partagé."},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            try:
                # Rechercher l'invitation avec le token fourni et liée à cette course
                invitation = SharedPaymentInvitation.objects.get(token=token, trip=trip)
                
                # Vérifier que l'invitation est en attente ou acceptée
                if invitation.status not in ['PENDING', 'ACCEPTED']:
                    return Response(
                        {"error": f"Cette invitation de paiement est {invitation.get_status_display().lower()}."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Procéder au paiement avec le portefeuille de l'utilisateur
                # Utiliser le service de paiement existant pour traiter le paiement
                from payments.services import PaymentService
                
                try:
                    payment_result = PaymentService.process_payment(
                        user=request.user,
                        amount=invitation.amount,
                        payment_method_id="wallet",  # Toujours utiliser le portefeuille pour les paiements partagés
                        description=f"Paiement partagé pour course ID:{trip.id}",
                        metadata={
                            "trip_id": trip.id,
                            "invitation_id": invitation.id,
                            "shared_payment": True
                        },
                        payment_type="SHARED_TRIP"
                    )
                    
                    # Mettre à jour le statut de l'invitation
                    invitation.status = 'PAID'
                    invitation.save()
                    
                    return Response(payment_result, status=status.HTTP_201_CREATED)
                    
                except ValueError as e:
                    return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
                
            except SharedPaymentInvitation.DoesNotExist:
                return Response(
                    {"error": "Aucune invitation de paiement partagé valide n'a été trouvée pour cette course avec le token fourni."},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            return Response(
                {"error": "L'ID de la course (trip_id) est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )
