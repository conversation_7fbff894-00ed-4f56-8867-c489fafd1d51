import pytest
from unittest.mock import patch, MagicMock
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from datetime import timedelta
from payments.models import Payment
# Utiliser directement le modèle Trip de l'application trips
from trips.models import Trip
from accounts.models import User, Client as Passenger
# Adaptation pour les modèles qui n'existent pas dans la structure actuelle
class ImpactStatistics:
    pass

pytestmark = pytest.mark.unit

@pytest.fixture
def api_client():
    """Fixture pour le client API"""
    return APIClient()

@pytest.fixture
def user():
    """Fixture pour un utilisateur"""
    return User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='SecurePassword123!'
    )

@pytest.fixture
def impact_statistics():
    """Fixture pour les statistiques d'impact"""
    return ImpactStatistics.objects.create()

@pytest.fixture
def passenger(user, impact_statistics):
    """Fixture pour un passager"""
    return Passenger.objects.create(
        user=user,
        impact_statistics=impact_statistics
    )

@pytest.fixture
def canceled_booking(passenger):
    """Fixture pour une réservation annulée"""
    return Trip.objects.create(
        passenger=passenger,
        status='CANCELED',
        departure_time=timezone.now() + timedelta(days=2)  # Dans 2 jours
    )

@pytest.fixture
def completed_payment(canceled_booking):
    """Fixture pour un paiement complété"""
    return Payment.objects.create(
        booking=canceled_booking,
        amount=100.0,
        type='TRIP',
        status='COMPLETED',
        stripe_payment_id='pi_test123'
    )

@pytest.mark.django_db
class TestAutoRefundViewSet:
    """Tests pour AutoRefundViewSet"""

    @patch('payments.stripe_utils.create_refund')
    def test_refund_canceled_booking(self, mock_create_refund, api_client, user, canceled_booking, completed_payment):
        """Test de la méthode refund_canceled_booking"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_create_refund.return_value = MagicMock(
            id='re_test123',
            amount=10000,
            status='succeeded'
        )
        
        # Appeler la vue
        url = reverse('auto-refunds-refund-canceled-booking')
        data = {
            'booking_id': str(canceled_booking.id),
            'reason': 'requested_by_customer'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert 'status' in response.data
        assert 'refund_id' in response.data
        assert response.data['refund_id'] == 're_test123'
        
        # Vérifier que le paiement a été mis à jour
        completed_payment.refresh_from_db()
        assert completed_payment.status == 'REFUNDED'
        assert completed_payment.refund_id == 're_test123'
        assert completed_payment.refund_reason == 'requested_by_customer'
    
    @patch('payments.views_auto_refunds.AutoRefundViewSet._calculate_refund_amount')
    def test_calculate_refund_amount(self, mock_calculate_refund_amount, api_client, user, canceled_booking, completed_payment):
        """Test de la méthode _calculate_refund_amount"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler le calcul du remboursement
        mock_calculate_refund_amount.return_value = 100.0
        
        # Créer une instance de la vue
        from payments.views_auto_refunds import AutoRefundViewSet
        view = AutoRefundViewSet()
        
        # Appeler la méthode
        result = view._calculate_refund_amount(canceled_booking, completed_payment)
        
        # Vérifier le résultat
        assert result == 100.0
        
        # Tester avec une date de départ proche
        canceled_booking.departure_time = timezone.now() + timedelta(hours=6)
        canceled_booking.save()
        
        # Simuler le calcul du remboursement
        mock_calculate_refund_amount.return_value = 0.0
        
        # Appeler la méthode
        result = view._calculate_refund_amount(canceled_booking, completed_payment)
        
        # Vérifier le résultat
        assert result == 0.0
    
    @patch('payments.stripe_utils.create_refund')
    def test_process_refund_requests(self, mock_create_refund, api_client, user, canceled_booking, completed_payment):
        """Test de la méthode process_refund_requests"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_create_refund.return_value = MagicMock(
            id='re_test123',
            amount=10000,
            status='succeeded'
        )
        
        # Appeler la vue
        url = reverse('auto-refunds-process-refund-requests')
        response = api_client.post(url)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert 'processed' in response.data
        assert 'failed' in response.data
        assert 'details' in response.data
        
        # Vérifier que le paiement a été mis à jour
        completed_payment.refresh_from_db()
        assert completed_payment.status == 'REFUNDED'
