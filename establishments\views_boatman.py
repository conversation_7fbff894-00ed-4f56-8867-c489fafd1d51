"""
Vues pour l'enregistrement et la gestion des bateliers par les établissements.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
import random
import string
import logging

from accounts.models import Establishment, Captain
from boats.models import Boat
from notifications.services import create_notification
from accounts.permissions import IsEstablishment

User = get_user_model()
logger = logging.getLogger(__name__)


def generate_temporary_password(length=8):
    """Générer un mot de passe temporaire aléatoire"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))


class EstablishmentRegisterBoatmanView(APIView):
    """
    Enregistrer un nouveau batelier.

    POST /api/establishments/register-boatman/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def post(self, request):
        """Enregistrer un nouveau batelier avec envoi d'email"""

        # La permission IsEstablishment s'en charge déjà
        establishment = request.user.establishment
        
        # Vérifier si l'établissement a déjà enregistré un batelier
        if Captain.objects.filter(registered_by_establishment=establishment).exists():
            return Response({
                'status': 'error',
                'error': 'Vous avez déjà enregistré un batelier. Un établissement ne peut avoir qu\'un seul batelier.',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)

        # Récupérer les données du formulaire
        email = request.data.get('email')
        first_name = request.data.get('first_name')
        last_name = request.data.get('last_name')
        phone_number = request.data.get('phone_number')
        experience = request.data.get('experience', '')
        license_number = request.data.get('license_number', '')
        
        # Récupérer les données du bateau
        boat_data = request.data.get('boat', {})

        # Validation des champs requis pour le batelier
        if not all([email, first_name, last_name]):
            return Response({
                'error': 'Les champs email, first_name et last_name sont requis'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # Validation des champs requis pour le bateau
        required_boat_fields = ['name', 'registration_number', 'capacity', 'fuel_type', 'fuel_consumption']
        if not boat_data:
            return Response({
                'error': 'Les informations du bateau sont requises'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        for field in required_boat_fields:
            if field not in boat_data or not boat_data[field]:
                return Response({
                    'error': f'Le champ {field} du bateau est requis'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Vérifier si l'email existe déjà
        if User.objects.filter(email=email).exists():
            return Response({
                'error': 'Un utilisateur avec cet email existe déjà'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Générer un mot de passe temporaire
        temporary_password = generate_temporary_password()

        try:
            # PARTIE 1: Création de l'utilisateur
            try:
                user = User.objects.create_user(
                    email=email,
                    password=temporary_password,
                    first_name=first_name,
                    last_name=last_name,
                    phone_number=phone_number or '',
                    type=User.Types.CAPTAIN
                )
                logger.info(f"Utilisateur créé avec ID: {user.id}")
            except Exception as e:
                logger.error(f"Erreur lors de la création de l'utilisateur: {str(e)}")
                raise

            # PARTIE 2: Création du profil Captain
            try:
                captain = Captain.objects.create(
                    user=user,
                    experience=experience,
                    license_number=license_number,
                    is_available=True,
                    availability_status='AVAILABLE',
                    rate_per_km=25.00,  # Tarif par défaut
                    rate_per_hour=50.00,  # Tarif par défaut
                    registered_by_establishment=establishment  # Indique que c'est un batelier créé par cet établissement
                )
                logger.info(f"Captain créé avec user_id: {captain.user.id}")
            except Exception as e:
                logger.error(f"Erreur lors de la création du profil Captain: {str(e)}")
                # Supprimer l'utilisateur créé précédemment pour éviter les orphelins
                user.delete()
                raise

            # PARTIE 3: Création du bateau
            try:
                # Valider les types de bateau et de carburant
                from boats.validators import validate_boat_type, validate_fuel_type
                
                # Utiliser le type de bateau fourni ou CLASSIC par défaut
                boat_type = boat_data.get('boat_type', 'CLASSIC')
                validate_boat_type(boat_type)
                
                # Valider le type de carburant
                fuel_type = boat_data['fuel_type']
                validate_fuel_type(fuel_type)
                
                # S'il existe déjà un bateau automatique, on le met à jour ; sinon on le crée
                boat = captain.boats.first()
                if boat:
                    # Mise à jour des champs
                    boat.name = boat_data['name']
                    boat.registration_number = boat_data['registration_number']
                    boat.boat_type = boat_type
                    boat.capacity = int(boat_data['capacity'])
                    boat.fuel_type = fuel_type
                    boat.fuel_consumption = float(boat_data['fuel_consumption'])
                    boat.zone_served = "Zone par défaut"
                    boat.radius = 20
                    boat.establishment = establishment  # ← Assigner l'établissement aussi lors de la mise à jour
                    boat.save()
                else:
                    boat = Boat.objects.create(
                        captain=captain,
                        establishment=establishment,  # ← Assigner le bateau à l'établissement
                        name=boat_data['name'],
                        registration_number=boat_data['registration_number'],
                        boat_type=boat_type,
                        capacity=int(boat_data['capacity']),
                        fuel_type=fuel_type,
                        fuel_consumption=float(boat_data['fuel_consumption']),
                        is_available=True,  # Valeur par défaut
                        zone_served="Zone par défaut",  # Valeur par défaut
                        radius=20  # Valeur par défaut
                    )
                logger.info(f"Bateau créé avec ID: {boat.id}")
            except Exception as e:
                logger.error(f"Erreur lors de la création du bateau: {str(e)}")
                # Supprimer le captain et l'utilisateur créés précédemment
                user.delete()  # Cela supprimera aussi le captain grâce à la cascade
                raise

            # PARTIE 4: Envoi de l'email
            try:
                email_sent = self._send_welcome_email(
                    user=user,
                    temporary_password=temporary_password,
                    establishment=establishment
                )
                logger.info(f"Email envoyé: {email_sent}")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de l'email: {str(e)}")
                email_sent = False
                # On continue même si l'email échoue

            # PARTIE 5: Création de la notification
            try:
                create_notification(
                    user=request.user,
                    title="Nouveau batelier enregistré",
                    message=f"Le batelier {first_name} {last_name} a été enregistré avec succès",
                    notification_type="BOATMAN_REGISTERED"
                )
                logger.info("Notification créée avec succès")
            except Exception as e:
                logger.error(f"Erreur lors de la création de la notification: {str(e)}")
                # On continue même si la notification échoue
            
            # PARTIE 6: Mise à jour des flags utilisateur
            try:
                user.requires_password_change = True
                user.save()
                logger.info("Flag requires_password_change mis à jour")
            except Exception as e:
                logger.error(f"Erreur lors de la mise à jour des flags utilisateur: {str(e)}")
                # On continue même si la mise à jour échoue
            
            # PARTIE 7: Préparation de la réponse
            try:
                # Préparer les données de réponse
                response_data = {
                    'status': 'success',
                    'message': 'Batelier enregistré avec succès',
                    'data': {
                        'captain_id': captain.user.id,  # Captain utilise user comme clé primaire
                        'user_id': user.id,
                        'boat_id': boat.id,
                        'email_sent': email_sent
                    }
                }
                logger.info("Réponse préparée avec succès")
                return Response(response_data)
            except Exception as e:
                logger.error(f"Erreur lors de la préparation de la réponse: {str(e)}")
                # Retourner une réponse simplifiée en cas d'erreur
                return Response({
                    'status': 'success',
                    'message': 'Batelier enregistré avec succès (erreur dans la préparation des détails)'
                })
            

        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement du batelier: {str(e)}")
            return Response({
                'error': f'Erreur lors de l\'enregistrement: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _send_welcome_email(self, user, temporary_password, establishment):
        """Envoyer l'email de bienvenue avec les identifiants"""

        subject = f"Bienvenue chez {establishment.name} - Vos identifiants Commodore"

        # Contexte pour le template
        context = {
            'user': user,
            'establishment': establishment,
            'email': user.email,
            'temporary_password': temporary_password,
            'login_url': f"{settings.FRONTEND_URL}/captain/login",  # URL de connexion capitaine
        }

        # Rendu du template HTML
        html_message = render_to_string('emails/boatman_welcome.html', context)
        plain_message = strip_tags(html_message)

        try:
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message,
                fail_silently=False,
            )
            logger.info(f"Email de bienvenue envoyé à {user.email}")
            return True

        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de l'email à {user.email}: {str(e)}")
            return False


class EstablishmentBoatmenListView(APIView):
    """
    Lister les bateliers enregistrés par l'établissement.

    GET /api/establishments/boatmen/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def get(self, request):
        """Récupérer la liste des bateliers"""

        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)

        establishment = request.user.establishment

        # Récupérer tous les capitaines (pour l'instant, on peut filtrer plus tard)
        captains = Captain.objects.select_related('user').prefetch_related('boats').filter(registered_by_establishment=establishment)

        boatmen_data = []
        for captain in captains:
            # Récupérer le bateau principal
            main_boat = captain.boats.first()

            boatmen_data.append({
                'captain_id': captain.user.id,
                'name': captain.user.get_full_name(),
                'email': captain.user.email,
                'phone': captain.user.phone_number,
                'experience': captain.experience,
                'license_number': captain.license_number,
                'availability_status': captain.availability_status,
                'is_available': True if captain.availability_status == 'AVAILABLE' else False,
                'average_rating': float(captain.average_rating) if captain.average_rating else 0.0,
                'boat': {
                    'id': main_boat.id if main_boat else None,
                    'name': main_boat.name if main_boat else 'Aucun bateau',
                    'capacity': main_boat.capacity if main_boat else 0,
                    'boat_type': main_boat.boat_type if main_boat else 'N/A'
                } if main_boat else None,
                'registered_at': captain.user.date_joined.isoformat()
            })

        return Response({
            'status': 'success',
            'data': {
                'boatmen': boatmen_data,
                'total_count': len(boatmen_data)
            }
        })


class EstablishmentBoatmanDetailView(APIView):
    """
    Voir les détails d'un batelier spécifique.

    GET /api/establishments/boatmen/{captain_id}/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def get(self, request, captain_id):
        """Récupérer les détails d'un batelier"""

        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # Rechercher le capitaine par l'ID de son utilisateur
            captain = Captain.objects.select_related('user').prefetch_related('boats').get(
                user__id=captain_id
            )
        except Captain.DoesNotExist:
            return Response({
                'error': 'Batelier non trouvé'
            }, status=status.HTTP_404_NOT_FOUND)

        # Récupérer les statistiques des courses
        from trips.models import Trip
        total_trips = Trip.objects.filter(captain=captain).count()
        completed_trips = Trip.objects.filter(captain=captain, status='COMPLETED').count()

        # Récupérer les bateaux
        boats_data = []
        for boat in captain.boats.all():
            boats_data.append({
                'id': boat.id,
                'name': boat.name,
                'capacity': boat.capacity,
                'boat_type': boat.boat_type,
                'registration_number': boat.registration_number,
                'is_available': boat.is_available
            })

        return Response({
            'status': 'success',
            'data': {
                'captain_id': captain.user.id,  # Utiliser user.id comme clé primaire
                'name': f"{captain.user.first_name} {captain.user.last_name}",
                'email': captain.user.email,
                'phone': captain.user.phone_number,
                'experience': captain.experience,
                'license_number': captain.license_number,
                'availability_status': captain.availability_status,
                'average_rating': float(captain.average_rating) if captain.average_rating else 0.0,
                'rate_per_km': float(captain.rate_per_km),
                'rate_per_hour': float(captain.rate_per_hour),
                'boats': boats_data,
                'statistics': {
                    'total_trips': total_trips,
                    'completed_trips': completed_trips,
                    'completion_rate': (completed_trips / total_trips * 100) if total_trips > 0 else 0
                },
                'registered_at': captain.user.date_joined.isoformat()
            }
        })
