# API Endpoints - Chat

## 1. Salons de discussion (Chat Rooms)

### 1.1. Liste des salons de discussion
- **Endpoint**: GET /api/chat/rooms/
- **Description**: R<PERSON>cupérer la liste des salons de discussion auxquels l'utilisateur participe
- **Auth Required**: <PERSON>ui (JWT <PERSON>)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "name": "Course #123 - Discussion",
    "type": "TRIP",
    "participants_count": 2,
    "is_active": true,
    "last_message": {
      "sender": "<PERSON>",
      "content": "Je suis arrivé au point de rendez-vous",
      "created_at": "2025-05-25T10:30:45Z"
    },
    "unread_count": 3,
    "updated_at": "2025-05-25T10:30:45Z"
  },
  // ...
]
```

### 1.2. Détails d'un salon de discussion
- **Endpoint**: GET /api/chat/rooms/{id}/
- **Description**: <PERSON><PERSON><PERSON><PERSON><PERSON> les détails d'un salon de discussion spécifique
- **Auth Required**: <PERSON><PERSON> (JWT <PERSON>ken)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "name": "Course #123 - Discussion",
  "type": "TRIP",
  "is_active": true,
  "created_at": "2025-05-24T14:15:30Z",
  "updated_at": "2025-05-25T10:30:45Z",
  "participants": [
    {
      "id": 5,
      "email": "<EMAIL>",
      "first_name": "Jean",
      "last_name": "Dupont",
      "profile_picture": "https://example.com/profiles/jean.jpg"
    },
    {
      "id": 8,
      "email": "<EMAIL>",
      "first_name": "Marie",
      "last_name": "Martin",
      "profile_picture": "https://example.com/profiles/marie.jpg"
    }
  ]
}
```

### 1.3. Marquer un salon comme lu
- **Endpoint**: POST /api/chat/rooms/{id}/mark_as_read/
- **Description**: Marquer tous les messages non lus d'un salon comme lus
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "status": "messages marked as read"
}
```

## 2. Messages

### 2.1. Liste des messages d'un salon
- **Endpoint**: GET /api/chat/rooms/{room_pk}/messages/
- **Description**: Récupérer la liste des messages d'un salon de discussion
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
[
  {
    "id": 45,
    "sender": {
      "id": 5,
      "first_name": "Jean",
      "last_name": "Dupont",
      "profile_picture": "https://example.com/profiles/jean.jpg"
    },
    "type": "TEXT",
    "content": "Je suis arrivé au point de rendez-vous",
    "attachment": null,
    "metadata": {},
    "is_read": true,
    "created_at": "2025-05-25T10:30:45Z"
  },
  {
    "id": 46,
    "sender": {
      "id": 8,
      "first_name": "Marie",
      "last_name": "Martin",
      "profile_picture": "https://example.com/profiles/marie.jpg"
    },
    "type": "IMAGE",
    "content": "Voici une photo de l'emplacement",
    "attachment": "https://example.com/attachments/photo123.jpg",
    "metadata": {
      "width": 1024,
      "height": 768
    },
    "is_read": false,
    "created_at": "2025-05-25T10:32:15Z"
  },
  // ...
]
```

### 2.2. Envoyer un message
- **Endpoint**: POST /api/chat/rooms/{room_pk}/messages/
- **Description**: Envoyer un nouveau message dans un salon de discussion
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "type": "TEXT",
  "content": "Je serai là dans 5 minutes",
  "attachment": null,
  "metadata": {}
}
```
- **Response (201 Created)**:
```json
{
  "id": 47,
  "sender": {
    "id": 8,
    "first_name": "Marie",
    "last_name": "Martin",
    "profile_picture": "https://example.com/profiles/marie.jpg"
  },
  "type": "TEXT",
  "content": "Je serai là dans 5 minutes",
  "attachment": null,
  "metadata": {},
  "is_read": false,
  "created_at": "2025-05-25T10:35:20Z"
}
```

### 2.3. Marquer un message comme lu
- **Endpoint**: POST /api/chat/rooms/{room_pk}/messages/{pk}/mark_as_read/
- **Description**: Marquer un message spécifique comme lu
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "status": "message marked as read"
}
```

### 2.4. Supprimer un message (suppression douce)
- **Endpoint**: POST /api/chat/rooms/{room_pk}/messages/{pk}/soft_delete/
- **Description**: Supprimer un message (suppression douce, le message reste en base mais n'est plus visible)
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "status": "message deleted"
}
```

## 3. API Chatbot d'assistance (Meta-Llama-3)

### 3.1. Envoyer un message au chatbot
- **Endpoint**: POST /api/chat/chatbot/message/
- **Description**: Envoyer un message au chatbot alimenté par Meta-Llama-3 et recevoir une réponse générée
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "message": "Comment réserver un bateau-taxi ?",
  "session_id": "9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d" // Optionnel
}
```
- **Response (200 OK)**:
```json
{
  "session_id": "9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d",
  "message": {
    "id": 128,
    "role": "ASSISTANT",
    "content": "Pour réserver un bateau-taxi sur Commodore, suivez ces étapes simples :\n\n1. Ouvrez l'application et sélectionnez 'Nouvelle réservation'\n2. Indiquez votre point de départ et d'arrivée\n3. Choisissez la date et l'heure souhaitées\n4. Précisez le nombre de passagers\n5. Confirmez et payez votre réservation\n\nVous recevrez ensuite une confirmation par email avec un QR code à présenter au capitaine.",
    "created_at": "2025-05-25T11:45:30Z"
  }
}
```

### 3.2. Récupérer l'historique des conversations avec le chatbot
- **Endpoint**: GET /api/chat/chatbot/history/{session_id}/
- **Description**: Récupérer l'historique des messages d'une session de chatbot
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "session_id": "9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d",
  "messages": [
    {
      "id": 127,
      "role": "USER",
      "content": "Comment réserver un bateau-taxi ?",
      "created_at": "2025-05-25T11:45:15Z"
    },
    {
      "id": 128,
      "role": "ASSISTANT",
      "content": "Pour réserver un bateau-taxi sur Commodore, suivez ces étapes simples :\n\n1. Ouvrez l'application et sélectionnez 'Nouvelle réservation'\n2. Indiquez votre point de départ et d'arrivée\n3. Choisissez la date et l'heure souhaitées\n4. Précisez le nombre de passagers\n5. Confirmez et payez votre réservation\n\nVous recevrez ensuite une confirmation par email avec un QR code à présenter au capitaine.",
      "created_at": "2025-05-25T11:45:30Z"
    }
  ]
}
```

### 3.3. Récupérer la liste des sessions de chatbot
- **Endpoint**: GET /api/chat/chatbot/history/
- **Description**: Récupérer la liste des sessions de chatbot de l'utilisateur
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
[
  {
    "session_id": "9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d",
    "last_interaction": "2025-05-25T11:45:30Z",
    "last_message": "Pour réserver un bateau-taxi sur Commodore, suivez ces étapes simples...",
    "created_at": "2025-05-25T11:45:15Z"
  },
  {
    "session_id": "1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed",
    "last_interaction": "2025-05-24T15:30:45Z",
    "last_message": "Les tarifs pour une course de Saint-Tropez à Pampelonne commencent à partir de...",
    "created_at": "2025-05-24T15:25:30Z"
  }
]
```

### 3.4. Effacer une session de chatbot
- **Endpoint**: DELETE /api/chat/chatbot/clear/{session_id}/
- **Description**: Supprimer une session de chatbot et tous ses messages
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "status": "success",
  "message": "Session supprimée avec succès"
}
```

### 3.5. Soumettre un feedback sur une réponse du chatbot
- **Endpoint**: POST /api/chat/chatbot/feedback/
- **Description**: Soumettre un feedback sur une réponse du chatbot pour amélioration future
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "message_id": 128,
  "rating": 4,
  "feedback": "Réponse claire et précise, mais il manque des infos sur les tarifs"
}
```
- **Response (200 OK)**:
```json
{
  "status": "success",
  "message": "Feedback enregistré avec succès",
  "feedback_id": 45
}
```

## Fonctionnalités implémentées

1. ✅ **Chatbot Meta-Llama-3** : Un chatbot alimenté par Meta-Llama-3 a été implémenté pour fournir une assistance IA de haute qualité aux utilisateurs.

2. ✅ **Gestion des sessions** : Les conversations avec le chatbot sont organisées en sessions pour assurer la continuité des échanges.

3. ✅ **Système de feedback** : Les utilisateurs peuvent évaluer les réponses du chatbot pour améliorer continuellement le service.

4. ✅ **Statistiques d'utilisation** : L'API pour consulter les statistiques de feedback du chatbot est implémentée pour les administrateurs.

## Fonctionnalités à implémenter

1. **WebSockets pour les messages en temps réel** : Implémenter une solution WebSocket pour les messages en temps réel, ce qui améliorerait l'expérience utilisateur dans l'application de chat.

2. **Prise en charge des pièces jointes plus riches** : Enrichir l'API pour gérer différents types de médias dans les conversations (audio, vidéo, documents).

3. **Indicateurs de frappe** : Implémenter un mécanisme pour montrer quand un utilisateur est en train de taper un message.
