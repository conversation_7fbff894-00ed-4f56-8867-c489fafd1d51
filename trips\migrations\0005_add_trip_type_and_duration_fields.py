# Generated by Django 4.2.8 on 2025-06-03 18:08

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("trips", "0004_trip_qr_code"),
    ]

    operations = [
        migrations.AddField(
            model_name="trip",
            name="actual_duration",
            field=models.IntegerField(
                blank=True, null=True, verbose_name="durée réelle (minutes)"
            ),
        ),
        migrations.AddField(
            model_name="trip",
            name="distance_km",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=8,
                null=True,
                verbose_name="distance (km)",
            ),
        ),
        migrations.AddField(
            model_name="trip",
            name="estimated_duration",
            field=models.IntegerField(
                blank=True, null=True, verbose_name="durée estimée (minutes)"
            ),
        ),
        migrations.AddField(
            model_name="trip",
            name="trip_type",
            field=models.CharField(
                choices=[
                    ("COURSE_SIMPLE", "Course simple"),
                    ("MISE_A_DISPOSITION", "Mise à disposition"),
                    ("NAVETTES_GRATUITES", "Navette gratuite"),
                ],
                default="COURSE_SIMPLE",
                max_length=30,
                verbose_name="type de course",
            ),
        ),
    ]
