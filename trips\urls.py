from django.urls import path
from . import views
from . import views_status, views_tracking, views_shuttle, views_qr, views_booking, views_captain
from . import views_trip_payments, views_post_trip_payments

app_name = 'trips'

urlpatterns = [
    # URLs existantes
    path('', views.TripListCreateView.as_view(), name='trip-list-create'),
    path('<int:pk>/', views.TripDetailView.as_view(), name='trip-detail'),
    path('shuttles/', views.ShuttleListCreateView.as_view(), name='shuttle-list-create'),
    path('shuttles/<int:pk>/', views.ShuttleDetailView.as_view(), name='shuttle-detail'),

    # Nouveaux endpoints pour les trois types de courses
    path('requests/simple/', views.SimpleTripRequestView.as_view(), name='simple-trip-request'),
    path('requests/hourly/', views.HourlyTripRequestView.as_view(), name='hourly-trip-request'),
    path('requests/shuttle/', views.ShuttleTripRequestView.as_view(), name='shuttle-trip-request'),
    path('requests/<int:pk>/', views.TripRequestDetailView.as_view(), name='trip-request-detail'),

    # Gestion des devis
    path('quotes/<int:quote_id>/accept/', views.TripQuoteAcceptView.as_view(), name='quote-accept'),

    # Nettoyage automatique
    path('cleanup/expired/', views.ExpiredTripRequestCleanupView.as_view(), name='cleanup-expired'),

    # Gestion des statuts de courses
    path('<int:trip_id>/start/', views_status.TripStartView.as_view(), name='trip-start'),
    path('<int:trip_id>/complete/', views_status.TripCompleteView.as_view(), name='trip-complete'),
    path('<int:trip_id>/cancel/', views_status.TripCancelView.as_view(), name='trip-cancel'),
    path('<int:trip_id>/status/', views_status.TripStatusView.as_view(), name='trip-status'),
    path('<int:trip_id>/problem/', views_status.TripProblemView.as_view(), name='trip-problem'),
    path('<int:trip_id>/delay/', views_status.TripDelayView.as_view(), name='trip-delay'),

    # Suivi en temps réel
    path('<int:trip_id>/location/', views_tracking.TripLocationUpdateView.as_view(), name='trip-location-update'),
    path('<int:trip_id>/tracking/', views_tracking.TripTrackingView.as_view(), name='trip-tracking'),
    path('<int:trip_id>/history/', views_tracking.TripHistoryView.as_view(), name='trip-history'),
    path('<int:trip_id>/notes/', views_tracking.TripNotesView.as_view(), name='trip-notes'),

    # Gestion des navettes par établissements
    path('requests/shuttle/<int:request_id>/accept/', views_shuttle.ShuttleAcceptView.as_view(), name='shuttle-accept'),
    path('requests/shuttle/<int:request_id>/reject/', views_shuttle.ShuttleRejectView.as_view(), name='shuttle-reject'),
    path('shuttle/list/', views_shuttle.ShuttleListView.as_view(), name='shuttle-list'),
    path('shuttle/resources/', views_shuttle.ShuttleAvailableResourcesView.as_view(), name='shuttle-resources'),
    path('shuttle/schedule/', views_shuttle.ShuttleScheduleView.as_view(), name='shuttle-schedule'),

    # Gestion des QR codes
    path('verify-qr/', views_qr.QRCodeVerificationView.as_view(), name='qr-verify'),
    path('<int:trip_id>/generate-qr/', views_qr.QRCodeGenerationView.as_view(), name='qr-generate'),

    # Gestion des réservations et choix de devis
    path('quotes/<int:quote_id>/choose/', views_booking.QuoteChoiceView.as_view(), name='quote-choose'),
    path('<int:trip_id>/accept/', views_booking.TripAcceptView.as_view(), name='trip-accept'),
    path('<int:trip_id>/reject/', views_booking.TripRejectView.as_view(), name='trip-reject'),
    path('pending/', views_booking.CaptainPendingTripsView.as_view(), name='captain-pending-trips'),

    # Espace capitaine
    path('captain/history/', views_captain.CaptainTripsHistoryView.as_view(), name='captain-history'),
    path('captain/dashboard/', views_captain.CaptainDashboardView.as_view(), name='captain-dashboard'),
    path('captain/availability/', views_captain.CaptainAvailabilityView.as_view(), name='captain-availability'),
    path('captain/trips/<int:trip_id>/', views_captain.CaptainTripDetailView.as_view(), name='captain-trip-detail'),

    # Paiements des courses
    path('<int:trip_id>/payment/', views_trip_payments.TripPaymentView.as_view(), name='trip-payment'),
    path('<int:trip_id>/payment/status/', views_trip_payments.TripPaymentStatusView.as_view(), name='trip-payment-status'),
    path('<int:trip_id>/qr-code/', views_trip_payments.TripQRCodeView.as_view(), name='trip-qr-code'),

    # Gestion du portefeuille
    path('wallet/balance/', views_trip_payments.WalletBalanceView.as_view(), name='wallet-balance'),
    path('wallet/recharge/', views_trip_payments.WalletRechargeView.as_view(), name='wallet-recharge'),

    # Paiements post-voyage
    path('<int:trip_id>/carbon-footprint/', views_post_trip_payments.TripCarbonFootprintView.as_view(), name='trip-carbon-footprint'),
    path('<int:trip_id>/carbon-compensation/', views_post_trip_payments.CarbonCompensationPaymentView.as_view(), name='carbon-compensation'),
    path('<int:trip_id>/tip/', views_post_trip_payments.TipPaymentView.as_view(), name='tip-payment'),
]
