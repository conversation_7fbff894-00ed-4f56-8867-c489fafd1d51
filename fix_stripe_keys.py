import os
import sys
import django

# Configurer Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "commodore.settings")
django.setup()

from django.conf import settings
import stripe

# Définir la clé API correcte
STRIPE_API_KEY = settings.STRIPE_SECRET_KEY
STRIPE_API_VERSION = "2023-10-16"

print(f"Correction de la configuration Stripe dans l'application...")
print(f"Clé API: {STRIPE_API_KEY[:10]}...{STRIPE_API_KEY[-4:]}")
print(f"Version API: {STRIPE_API_VERSION}")

# Mettre à jour la configuration globale de Stripe
stripe.api_key = STRIPE_API_KEY
stripe.api_version = STRIPE_API_VERSION
print("✓ Configuration globale de Stripe mise à jour")

# Mettre à jour les modules spécifiques qui pourraient avoir des clés hardcodées
from payments import services, stripe_utils, webhooks, views_extended, views_trip_adapter, views_api

# Mettre à jour les services
services.stripe.api_key = STRIPE_API_KEY
services.stripe.api_version = STRIPE_API_VERSION
print("✓ Module services mis à jour")

# Mettre à jour les utilitaires Stripe
stripe_utils.stripe.api_key = STRIPE_API_KEY
stripe_utils.stripe.api_version = STRIPE_API_VERSION
print("✓ Module stripe_utils mis à jour")

# Mettre à jour les webhooks
webhooks.stripe.api_key = STRIPE_API_KEY
webhooks.stripe.api_version = STRIPE_API_VERSION
print("✓ Module webhooks mis à jour")

# S'assurer que tous les services accèdent aux même configurations
from payments.services import PaymentService
from payments.models import Payment

# Injecter des fonctions de débogage pour tracer les appels à l'API Stripe
original_process_payment = PaymentService.process_payment

def debug_process_payment(*args, **kwargs):
    print(f"DEBUG - Appel à process_payment avec la clé API: {stripe.api_key[:10]}...{stripe.api_key[-4:]}")
    print(f"DEBUG - Version API: {stripe.api_version}")
    try:
        result = original_process_payment(*args, **kwargs)
        print(f"DEBUG - Paiement réussi avec ID: {result.get('id', 'N/A')}")
        return result
    except Exception as e:
        print(f"DEBUG - Erreur de paiement: {str(e)}")
        raise

# Remplacer la méthode par notre version de débogage
PaymentService.process_payment = debug_process_payment

print("\nFonctions de débogage Stripe activées.")
print("Redémarrez le serveur Django pour appliquer les changements.")
print("Exécutez: python manage.py runserver")
