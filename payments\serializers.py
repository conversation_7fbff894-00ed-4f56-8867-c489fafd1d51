"""
Module de sérialisation pour l'application payments.

Ce module contient les sérialiseurs pour les modèles de l'application payments,
permettant la conversion entre les objets Python et les formats de données comme JSON.
Ces sérialiseurs sont utilisés par l'API REST pour traiter les requêtes et réponses.
"""

from rest_framework import serializers
from .models import Payment, Transaction, Wallet
from trips.models import Trip, Shuttle

class PaymentSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour le modèle Payment.

    Ce sérialiseur gère la conversion des objets Payment en JSON et vice-versa.
    Il inclut les détails de paiement Stripe, les informations de remboursement et les métadonnées.

    Attributs:
        id (int): Identifiant unique du paiement (lecture seule)
        ride (int): Identifiant du trajet associé (optionnel)
        ride_details (dict): Détails du trajet associé (lecture seule)
        amount (float): Montant du paiement
        type (str): Type de paiement (TRIP, TIP, CARBON_OFFSET, CREDIT_PURCHASE, ESTABLISHMENT)
        stripe_payment_id (str): Identifiant du paiement Stripe
        stripe_customer_id (str): Identifiant du client Stripe
        stripe_payment_method_id (str): Identifiant de la méthode de paiement Stripe
        stripe_payment_method_type (str): Type de méthode de paiement Stripe
        status (str): Statut du paiement (PENDING, COMPLETED, FAILED, REFUNDED, PARTIALLY_REFUNDED, CANCELED)
        receipt_url (str): URL du reçu de paiement (lecture seule)
        refund_id (str): Identifiant du remboursement (lecture seule)
        refund_amount (float): Montant remboursé (lecture seule)
        refund_reason (str): Raison du remboursement (lecture seule)
        metadata (dict): Métadonnées additionnelles
        created_at (datetime): Date et heure de création (lecture seule)
    """
    trip_details = serializers.SerializerMethodField(read_only=True)
    shuttle_details = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Payment
        fields = [
            'id', 'trip', 'shuttle', 'trip_details', 'shuttle_details', 'amount', 'type',
            'stripe_payment_id', 'stripe_customer_id', 'stripe_payment_method_id',
            'stripe_payment_method_type', 'status', 'receipt_url',
            'refund_id', 'refund_amount', 'refund_reason', 'metadata',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at', 'receipt_url', 'refund_id',
                           'refund_amount', 'refund_reason', 'trip_details', 'shuttle_details']

    def get_trip_details(self, obj):
        """
        Récupère les détails de la course associée au paiement.

        Args:
            obj (Payment): Objet Payment

        Returns:
            dict: Détails de la course ou None si aucune course n'est associée
        """
        if obj.trip:
            return {
                'id': obj.trip.id,
                'start_location': obj.trip.start_location,
                'end_location': obj.trip.end_location,
                'status': obj.trip.status,
                'total_price': float(obj.trip.total_price),
                'client': obj.trip.client.id if obj.trip.client else None,
                'captain': obj.trip.captain.user.id if obj.trip.captain else None,
                'boat': obj.trip.boat.id if obj.trip.boat else None
            }
        return None

    def get_shuttle_details(self, obj):
        """
        Récupère les détails de la navette associée au paiement.

        Args:
            obj (Payment): Objet Payment

        Returns:
            dict: Détails de la navette ou None si aucune navette n'est associée
        """
        if obj.shuttle:
            return {
                'id': obj.shuttle.id,
                'route_name': obj.shuttle.route_name,
                'start_location': obj.shuttle.start_location,
                'end_location': obj.shuttle.end_location,
                'status': obj.shuttle.status,
                'price_per_person': float(obj.shuttle.price_per_person),
                'establishment': obj.shuttle.establishment.id if obj.shuttle.establishment else None,
                'captain': obj.shuttle.captain.user.id if obj.shuttle.captain else None,
                'boat': obj.shuttle.boat.id if obj.shuttle.boat else None
            }
        return None

class WalletSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour le modèle Wallet.

    Ce sérialiseur gère la conversion des objets Wallet en JSON et vice-versa.
    Il inclut les informations sur le solde et l'utilisateur associé.

    Attributs:
        id (int): Identifiant unique du portefeuille (lecture seule)
        user (int): Identifiant de l'utilisateur associé (lecture seule)
        balance (decimal): Solde du portefeuille
        created_at (datetime): Date et heure de création (lecture seule)
        updated_at (datetime): Date et heure de dernière mise à jour (lecture seule)
        user_details (dict): Détails de l'utilisateur associé (lecture seule)
        recent_transactions (list): Liste des transactions récentes (lecture seule)
    """
    user_details = serializers.SerializerMethodField(read_only=True)
    recent_transactions = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Wallet
        fields = [
            'id', 'user', 'balance', 'created_at', 'updated_at',
            'user_details', 'recent_transactions'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at',
                           'user_details', 'recent_transactions']

    def get_user_details(self, obj):
        """
        Récupère les détails de l'utilisateur associé au portefeuille.

        Args:
            obj (Wallet): Objet Wallet

        Returns:
            dict: Détails de l'utilisateur
        """
        if obj.user:
            return {
                'id': obj.user.id,
                'username': obj.user.username,
                'email': obj.user.email,
                'full_name': obj.user.get_full_name()
            }
        return None

    def get_recent_transactions(self, obj):
        """
        Récupère les transactions récentes du portefeuille.

        Args:
            obj (Wallet): Objet Wallet

        Returns:
            list: Liste des 5 transactions les plus récentes ou liste vide en cas d'erreur
        """
        try:
            transactions = obj.transactions.all().order_by('-created_at')[:5]
            return SimpleTransactionSerializer(transactions, many=True).data
        except Exception:
            # En cas d'erreur de sérialisation, retourner une liste vide
            # plutôt que de faire échouer toute la requête
            return []

class SimpleTransactionSerializer(serializers.ModelSerializer):
    """
    Sérialiseur simplifié pour le modèle Transaction, destiné à l'affichage des transactions récentes
    dans le portefeuille sans tous les champs détaillés du TransactionSerializer complet.
    """
    class Meta:
        model = Transaction
        fields = ['id', 'type', 'amount', 'description', 'created_at']

class TransactionSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour le modèle Transaction.

    Ce sérialiseur gère la conversion des objets Transaction en JSON et vice-versa.
    Il inclut les détails du portefeuille et du trajet associés.

    Attributs:
        id (int): Identifiant unique de la transaction (lecture seule)
        wallet (int): Identifiant du portefeuille associé
        payment (int): Identifiant du paiement associé (optionnel)
        type (str): Type de transaction (CREDIT, DEBIT, REFUND, TRANSFER, ADJUSTMENT)
        amount (Decimal): Montant de la transaction
        balance_after (Decimal): Solde après la transaction
        description (str): Description de la transaction
    """
    wallet_details = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Transaction
        fields = [
            'id', 'wallet', 'payment', 'type', 'amount', 'balance_after',
            'description', 'metadata', 'created_at', 'wallet_details'
        ]
        read_only_fields = ['id', 'created_at', 'wallet_details']

    def get_wallet_details(self, obj):
        """
        Récupère les détails du portefeuille associé à la transaction.

        Args:
            obj (Transaction): Objet Transaction

        Returns:
            dict: Détails du portefeuille ou None si aucun portefeuille n'est associé
        """
        if obj.wallet:
            return {
                'id': obj.wallet.id,
                'balance': float(obj.wallet.balance),
                'user': obj.wallet.user.id if obj.wallet.user else None
            }
        return None
