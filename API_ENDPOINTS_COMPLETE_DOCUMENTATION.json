{"api_documentation": {"title": "Commodore API - Documentation Complète des Endpoints", "version": "1.0.0", "description": "Documentation complète de tous les endpoints testés avec exemples de requêtes et réponses", "base_url": "http://127.0.0.1:8000", "authentication": "Token-based authentication", "tested_workflows": ["Authentification et gestion des comptes", "Courses payantes avec devis multiples", "<PERSON><PERSON><PERSON> gratuites avec assignation bateliers", "Paiements Stripe et compensation carbone", "Gestion des établissements et bateliers"]}, "authentication_endpoints": {"login": {"method": "POST", "url": "/api/login/", "description": "Connexion utilisateur avec email et mot de passe", "auth_required": false, "request_body": {"email": "<EMAIL>", "password": "TestClient123!"}, "response_success": {"status_code": 200, "body": {"token": "6721055fcc9724f7bc2b1234567890abcdef", "user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "type": "CLIENT", "is_verified": true}}}, "response_error": {"status_code": 400, "body": {"error": "Invalid credentials"}}}, "register": {"method": "POST", "url": "/api/register/", "description": "Inscription d'un nouvel utilisateur", "auth_required": false, "request_body": {"email": "<EMAIL>", "password": "MotDePasse123!", "first_name": "Nouveau", "last_name": "Utilisa<PERSON>ur", "phone_number": "+33123456789", "user_type": "CLIENT"}, "response_success": {"status_code": 201, "body": {"message": "User created successfully", "user_id": 100, "verification_required": true}}}}, "wallet_endpoints": {"get_wallet": {"method": "GET", "url": "/api/payments/wallet/", "description": "Récupérer les informations du portefeuille utilisateur", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "response_success": {"status_code": 200, "body": {"id": 99, "user": 99, "balance": "500.00", "created_at": "2025-06-03T22:54:38.398384+02:00", "updated_at": "2025-06-03T22:54:38.398384+02:00", "user_details": {"id": 99, "username": null, "email": "<EMAIL>", "full_name": "<PERSON>"}, "recent_transactions": []}}}, "recharge_wallet": {"method": "POST", "url": "/api/payments/wallet/recharge/", "description": "Recharger le portefeuille avec Stripe", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "request_body": {"amount": 100.0, "payment_method_id": "pm_card_visa"}, "response_success": {"status_code": 200, "body": {"status": "success", "amount": 100.0, "new_balance": "600.00", "transaction_id": "txn_1234567890", "stripe_payment_intent": "pi_1234567890"}}}}, "trip_request_endpoints": {"create_simple_trip_request": {"method": "POST", "url": "/api/trips/requests/simple/", "description": "C<PERSON>er une demande de course simple avec génération automatique de devis", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "request_body": {"departure_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}, "timestamp": "2025-06-04T01:31:01.454740"}, "arrival_location": {"city_name": "Îles de Lérins", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}, "timestamp": "2025-06-04T01:31:01.454740"}, "departure_date": "2025-06-04", "departure_time": "01:31:01", "passenger_count": 4, "boat_type": "CLASSIC"}, "response_success": {"status_code": 201, "body": {"trip_request": {"id": 26, "departure_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}, "timestamp": "2025-06-04T01:31:01.454740"}, "arrival_location": {"city_name": "Îles de Lérins", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}, "timestamp": "2025-06-04T01:31:01.454740"}, "client": {"user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "is_active": true}, "wallet_balance": "500.00", "date_of_birth": null, "nationality": "", "preferred_language": "fr", "emergency_contact_name": "", "emergency_contact_phone": ""}, "distance_km": "4.45", "boat_type": "CLASSIC", "trip_type": "SIMPLE", "status": "PENDING", "passenger_count": 4, "created_at": "2025-06-03T23:31:01.546649+02:00", "updated_at": "2025-06-03T23:31:01.554569+02:00", "expires_at": "2025-06-03T23:41:01.546649+02:00", "scheduled_date": null, "scheduled_time": null}, "quotes": [{"id": 58, "captain_details": {"user": {"id": 91, "email": "<EMAIL>", "first_name": "Zeta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "2.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC091", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.20", "rate_per_hour": "25.00", "boat": {"id": 20, "name": "Classic Explorer", "registration_number": null, "boat_type": "CLASSIC", "capacity": 6, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:12.925554Z", "updated_at": "2025-05-31T07:08:12.930519Z"}}, "boat_details": {"id": 20, "name": "Classic Explorer", "registration_number": null, "boat_type": "CLASSIC", "capacity": 6, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 91, "user": {"id": 91, "email": "<EMAIL>", "first_name": "Zeta", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "2.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC091", "years_of_experience": 0, "rate_per_hour": 25.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:12.925554+02:00", "updated_at": "2025-05-31T09:08:12.930519+02:00", "maintenance_records": []}, "base_price": "5.34", "distance_km": "4.45", "rate_used": "1.20", "captain_name": "Zeta Capitaine", "captain_rating": "4.50", "boat_name": "Classic Explorer", "boat_capacity": 6, "created_at": "2025-06-03T23:31:01.582310+02:00", "is_available": true, "trip_request": 26, "captain": 91, "boat": 20}]}}}, "get_trip_quotes": {"method": "GET", "url": "/api/trips/requests/{request_id}/", "description": "Récupérer les devis pour une demande de course", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "path_parameters": {"request_id": "ID de la demande de course"}, "response_success": {"status_code": 200, "body": {"trip_request": {"id": 26, "departure_location": {"city_name": "Port de Cannes", "timestamp": "2025-06-04T01:31:01.454740", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}}, "arrival_location": {"city_name": "Îles de Lérins", "timestamp": "2025-06-04T01:31:01.454740", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}}, "distance_km": "4.45", "boat_type": "CLASSIC", "trip_type": "SIMPLE", "status": "PENDING", "passenger_count": 4, "expires_at": "2025-06-03T23:41:01.546649+02:00"}, "quotes": [{"id": 58, "base_price": "5.34", "distance_km": "4.45", "rate_used": "1.20", "captain_name": "Zeta Capitaine", "captain_rating": "4.50", "boat_name": "Classic Explorer", "boat_capacity": 6, "is_available": true, "captain": 91, "boat": 20}]}}}}, "quote_endpoints": {"accept_quote": {"method": "POST", "url": "/api/trips/quotes/{quote_id}/accept/", "description": "Accepter un devis et créer une course", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "path_parameters": {"quote_id": "ID du devis à accepter"}, "request_body": {}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Quote accepted successfully", "trip": {"id": 5, "departure_location": "Port de Cannes", "arrival_location": "Îles de Lérins", "status": "ACCEPTED", "captain": {"id": 91, "name": "Zeta Capitaine", "phone": "+33123456789"}, "boat": {"id": 20, "name": "Classic Explorer", "capacity": 6}, "payment": {"amount": "5.34", "status": "PENDING", "method": "WALLET"}, "qr_code": "QR_CODE_STRING_HERE", "estimated_duration": "30 minutes", "created_at": "2025-06-03T23:32:16.294410+02:00"}}}, "response_error": {"status_code": 500, "body": {"error": "IntegrityError: qr_code field cannot be null"}}}}, "trip_management_endpoints": {"start_trip": {"method": "POST", "url": "/api/trips/{trip_id}/start/", "description": "<PERSON><PERSON><PERSON><PERSON> une course (capitaine)", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Trip started successfully", "trip": {"id": 5, "status": "IN_PROGRESS", "started_at": "2025-06-03T23:35:00.000000+02:00"}}}}, "complete_trip": {"method": "POST", "url": "/api/trips/{trip_id}/complete/", "description": "Terminer une course (capitaine)", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Trip completed successfully", "trip": {"id": 5, "status": "COMPLETED", "completed_at": "2025-06-03T23:45:00.000000+02:00", "duration_minutes": 10, "final_amount": "5.34"}}}}, "accept_trip": {"method": "POST", "url": "/api/trips/{trip_id}/accept/", "description": "Accepter une course assignée (capitaine/batelier)", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "<PERSON> accepted by captain", "trip": {"id": 5, "status": "ACCEPTED", "captain_confirmed": true}}}}}, "shuttle_endpoints": {"create_shuttle_request": {"method": "POST", "url": "/api/trips/requests/shuttle/", "description": "<PERSON><PERSON><PERSON> une demande de navette gratuite", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "request_body": {"departure_location": {"city_name": "Hôtel Paradise", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}, "timestamp": "2025-06-04T02:00:00.000000"}, "arrival_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}, "timestamp": "2025-06-04T02:00:00.000000"}, "passenger_count": 2, "establishment_id": 100, "notes": "Navette pour clients de l'hôtel"}, "response_success": {"status_code": 201, "body": {"status": "success", "message": "Shuttle request created successfully", "shuttle_request": {"id": 15, "departure_location": {"city_name": "Hôtel Paradise", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}}, "arrival_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}}, "passenger_count": 2, "status": "PENDING", "amount": "0.00", "trip_type": "FREE_SHUTTLE", "establishment": {"id": 100, "name": "Hotel Paradise", "type": "HOTEL"}, "client": {"id": 99, "name": "<PERSON>", "phone": "+33123456789"}, "created_at": "2025-06-03T23:30:00.000000+02:00", "expires_at": "2025-06-03T23:40:00.000000+02:00"}}}}, "get_establishment_shuttle_requests": {"method": "GET", "url": "/api/establishments/shuttle-requests/", "description": "Récupérer les demandes de navettes pour un établissement", "auth_required": true, "headers": {"Authorization": "Token establishment_token_here"}, "response_success": {"status_code": 200, "body": {"status": "success", "data": {"requests": [{"id": 15, "departure_location": {"city_name": "Hôtel Paradise", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}}, "arrival_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}}, "passenger_count": 2, "status": "PENDING", "client": {"name": "<PERSON>", "phone": "+33123456789"}, "created_at": "2025-06-03T23:30:00.000000+02:00", "expires_at": "2025-06-03T23:40:00.000000+02:00"}], "total_count": 1}}}}, "accept_shuttle_request": {"method": "POST", "url": "/api/establishments/shuttle-requests/{request_id}/accept/", "description": "Accepter une demande de navette et assigner un batelier/capitaine", "auth_required": true, "headers": {"Authorization": "Token establishment_token_here"}, "path_parameters": {"request_id": "ID de la demande de navette"}, "request_body": {"captain_id": 91, "boat_id": 20, "estimated_pickup_time": "2025-06-04T02:05:00.000000", "notes": "Assignation au batelier officiel"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Shuttle request accepted successfully", "data": {"trip_id": 6, "shuttle_request_id": 15, "assigned_captain": {"id": 91, "name": "<PERSON>", "phone": "+33111222333"}, "assigned_boat": {"id": 20, "name": "Paradise Shuttle", "capacity": 8}, "estimated_pickup_time": "2025-06-04T02:05:00.000000", "amount": "0.00", "trip_type": "FREE_SHUTTLE"}}}}}}