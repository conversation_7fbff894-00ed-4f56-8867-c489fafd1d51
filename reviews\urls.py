from django.urls import path
from . import views

app_name = 'reviews'

urlpatterns = [
    # Gestion des avis
    path('', views.ReviewListCreateView.as_view(), name='review-list-create'),
    path('<int:pk>/', views.ReviewDetailView.as_view(), name='review-detail'),
    path('user/', views.UserReviewsView.as_view(), name='user-reviews'),
    path('statistics/', views.ReviewStatisticsView.as_view(), name='review-statistics'),
    
    # Réponses aux avis
    path('<int:review_id>/respond/', views.ReviewResponseView.as_view(), name='review-respond'),
    path('responses/<int:pk>/', views.ReviewResponseDetailView.as_view(), name='response-detail'),
    
    # Signalements
    path('<int:review_id>/report/', views.ReviewReportView.as_view(), name='review-report'),
]
