# API Endpoints - Boats

## 1. Gestion des bateaux

### 1.1. Liste des bateaux
- **Endpoint**: GET /api/boats/
- **Description**: Récupérer la liste des bateaux disponibles selon différents filtres
- **Auth Required**: <PERSON><PERSON> (JWT <PERSON>)
- **Query Parameters**:
  - captain_id: Filtrer par capitaine
  - establishment_id: Filtrer par établissement
  - capacity_min: Capacité minimale
  - is_available: Disponibilité (true/false)
  - fuel_type: Type de carburant (DIESEL, GASOLINE, ELECTRIC, HYBRID)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "name": "Blue Wave",
    "registration_number": "FR123456",
    "color": "Bleu",
    "capacity": 8,
    "fuel_type": "DIESEL",
    "fuel_consumption": 12.5,
    "photos": [
      "https://example.com/boats/blue_wave_1.jpg",
      "https://example.com/boats/blue_wave_2.jpg"
    ],
    "is_available": true,
    "captain": {
      "id": 5,
      "user": {
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>"
      }
    },
    "establishment": null,
    "average_rating": 4.8
  },
  {
    "id": 2,
    "name": "Riviera Express",
    "registration_number": "FR789012",
    "color": "Blanc",
    "capacity": 12,
    "fuel_type": "HYBRID",
    "fuel_consumption": 8.0,
    "photos": [
      "https://example.com/boats/riviera_express_1.jpg"
    ],
    "is_available": true,
    "captain": null,
    "establishment": {
      "id": 3,
      "name": "Hôtel Riviera"
    },
    "average_rating": 4.6
  }
]
```

### 1.2. Détails d'un bateau
- **Endpoint**: GET /api/boats/{id}/
- **Description**: Récupérer les détails d'un bateau spécifique
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "name": "Blue Wave",
  "registration_number": "FR123456",
  "color": "Bleu",
  "capacity": 8,
  "fuel_type": "DIESEL",
  "fuel_consumption": 12.5,
  "photos": [
    "https://example.com/boats/blue_wave_1.jpg",
    "https://example.com/boats/blue_wave_2.jpg"
  ],
  "captain": {
    "id": 5,
    "user": {
      "first_name": "Jean",
      "last_name": "Dupont",
      "email": "<EMAIL>",
      "phone_number": "+33687654321"
    },
    "experience": "5 ans d'expérience en navigation"
  },
  "establishment": null,
  "is_available": true,
  "last_maintenance": "2025-04-15",
  "next_maintenance": "2025-07-15",
  "created_at": "2025-01-10T14:30:00Z",
  "updated_at": "2025-05-10T09:15:30Z",
  "average_rating": 4.8,
  "reviews_count": 45,
  "upcoming_trips": [
    {
      "id": 123,
      "start_location": "Port de Saint-Tropez",
      "end_location": "Plage de Pampelonne",
      "scheduled_start_time": "2025-05-30T14:00:00Z",
      "scheduled_end_time": "2025-05-30T15:30:00Z",
      "status": "ACCEPTED"
    }
  ]
}
```

### 1.3. Créer un bateau
- **Endpoint**: POST /api/boats/
- **Description**: Ajouter un nouveau bateau (réservé aux capitaines et établissements)
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "name": "Blue Wave",
  "registration_number": "FR123456",
  "color": "Bleu",
  "capacity": 8,
  "fuel_type": "DIESEL",
  "fuel_consumption": 12.5,
  "photos": [
    "https://example.com/boats/blue_wave_1.jpg",
    "https://example.com/boats/blue_wave_2.jpg"
  ],
  "is_available": true,
  "last_maintenance": "2025-04-15",
  "next_maintenance": "2025-07-15"
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "name": "Blue Wave",
  "registration_number": "FR123456",
  "color": "Bleu",
  "capacity": 8,
  "fuel_type": "DIESEL",
  "fuel_consumption": 12.5,
  "photos": [
    "https://example.com/boats/blue_wave_1.jpg",
    "https://example.com/boats/blue_wave_2.jpg"
  ],
  "is_available": true,
  "last_maintenance": "2025-04-15",
  "next_maintenance": "2025-07-15",
  "created_at": "2025-05-25T13:40:15Z",
  "captain": {
    "id": 5,
    "user": {
      "id": 12,
      "first_name": "Jean",
      "last_name": "Dupont",
      "email": "<EMAIL>",
      "phone_number": "+33687654321"
    }
  }
}
```

### 1.4. Mettre à jour un bateau
- **Endpoint**: PATCH /api/boats/{id}/
- **Description**: Modifier un bateau existant (uniquement par le propriétaire)
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "name": "Blue Wave II",
  "color": "Bleu marine",
  "is_available": false,
  "next_maintenance": "2025-06-30"
}
```
- **Response (200 OK)**:
```json
{
  "id": 1,
  "message": "Bateau mis à jour avec succès"
}
```

### 1.5. Supprimer un bateau
- **Endpoint**: DELETE /api/boats/{id}/
- **Description**: Supprimer un bateau (uniquement par le propriétaire)
- **Auth Required**: Oui (JWT Token)
- **Response (204 No Content)**

### 1.6. Changer la disponibilité d'un bateau
- **Endpoint**: POST /api/boats/{id}/toggle_availability/
- **Description**: Basculer la disponibilité d'un bateau (AVAILABLE <-> UNAVAILABLE)
- **Auth Required**: Oui (JWT Token)
- **Request Body**: Aucun (bascule automatiquement le statut actuel)
- **Response (200 OK)**:
```json
{
  "boat_id": 1,
  "status": "UNAVAILABLE",
  "message": "Statut du bateau modifié en UNAVAILABLE"
}
```

### 1.7. Recherche de bateaux disponibles
- **Endpoint**: GET /api/boats/available/
- **Description**: Rechercher des bateaux disponibles selon des critères spécifiques
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - boat_type: Type de bateau
  - capacity_min: Capacité minimale 
  - location: Emplacement (recherche par texte)
  - date_start: Date de début de disponibilité (YYYY-MM-DD)
  - date_end: Date de fin de disponibilité (YYYY-MM-DD)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "name": "Blue Wave",
    "registration_number": "FR123456",
    "boat_type": "SPEEDBOAT",
    "capacity": 8,
    "status": "AVAILABLE",
    "captain_name": "Jean Dupont",
    "establishment_name": null,
    "location": "Nice, France"
  },
  {
    "id": 2,
    "name": "Riviera Express",
    "registration_number": "FR789012",
    "boat_type": "YACHT",
    "capacity": 12,
    "status": "AVAILABLE",
    "captain_name": null,
    "establishment_name": "Riviera Cruises",
    "location": "Cannes, France"
  }
]
```

## 2. Gestion des maintenances

### 2.1. Liste des maintenances d'un bateau
- **Endpoint**: GET /api/boats/{id}/maintenance/
- **Description**: Récupérer l'historique des maintenances d'un bateau
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "maintenance_type": "ROUTINE",
    "description": "Vidange moteur et remplacement des filtres",
    "cost": 250.00,
    "performed_at": "2025-04-15T10:00:00Z",
    "performed_by": "Marine Service Plus",
    "notes": "Tout est en ordre, prochaine maintenance dans 3 mois",
    "documents": [
      "https://example.com/maintenance/receipt_1234.pdf"
    ]
  },
  {
    "id": 2,
    "maintenance_type": "REPAIR",
    "description": "Réparation du système de direction",
    "cost": 450.00,
    "performed_at": "2025-03-10T14:30:00Z",
    "performed_by": "Marine Service Plus",
    "notes": "Remplacement des câbles de direction",
    "documents": []
  }
]
```

### 2.2. Ajouter une maintenance
- **Endpoint**: POST /api/boats/{id}/maintenance/
- **Description**: Ajouter un nouvel enregistrement de maintenance
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "maintenance_type": "ROUTINE",
  "description": "Vidange moteur et remplacement des filtres",
  "cost": 250.00,
  "performed_at": "2025-04-15T10:00:00Z",
  "performed_by": "Marine Service Plus",
  "notes": "Tout est en ordre, prochaine maintenance dans 3 mois",
  "documents": [
    "https://example.com/maintenance/receipt_1234.pdf"
  ]
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "message": "Maintenance enregistrée avec succès"
}
```

### 2.3. Détails d'une maintenance
- **Endpoint**: GET /api/boats/maintenance/{id}/
- **Description**: Récupérer les détails d'un enregistrement de maintenance
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "boat": 1,
  "maintenance_type": "ROUTINE",
  "description": "Vidange moteur et remplacement des filtres",
  "cost": 250.00,
  "performed_at": "2025-04-15T10:00:00Z",
  "performed_by": "Marine Service Plus",
  "notes": "Tout est en ordre, prochaine maintenance dans 3 mois",
  "documents": [
    "https://example.com/maintenance/receipt_1234.pdf"
  ]
}
```

### 2.4. Mettre à jour une maintenance
- **Endpoint**: PATCH /api/boats/maintenance/{id}/
- **Description**: Modifier un enregistrement de maintenance
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "cost": 275.00,
  "notes": "Tout est en ordre, prochaine maintenance dans 3 mois. Remplacement supplémentaire du filtre à carburant."
}
```
- **Response (200 OK)**:
```json
{
  "id": 1,
  "message": "Maintenance mise à jour avec succès"
}
```

### 2.5. Supprimer une maintenance
- **Endpoint**: DELETE /api/boats/maintenance/{id}/
- **Description**: Supprimer un enregistrement de maintenance
- **Auth Required**: Oui (JWT Token)
- **Response (204 No Content)**

## 3. Disponibilité des bateaux

### 3.1. Vérifier la disponibilité
- **Endpoint**: GET /api/boats/available/
- **Description**: Rechercher les bateaux disponibles pour une période donnée
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - start_time: Date et heure de début (format ISO 8601)
  - end_time: Date et heure de fin (format ISO 8601)
  - capacity_min: Capacité minimale requise
  - location: Localisation approximative (optionnel)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "name": "Blue Wave",
    "registration_number": "FR123456",
    "color": "Bleu",
    "capacity": 8,
    "fuel_type": "DIESEL",
    "photos": [
      "https://example.com/boats/blue_wave_1.jpg"
    ],
    "captain": {
      "id": 5,
      "user": {
        "first_name": "Jean",
        "last_name": "Dupont"
      }
    },
    "average_rating": 4.8
  },
  // ...
]
```

## Fonctionnalités à implémenter

1. **Implémentation des vues** : Créer les vues API basées sur les endpoints documentés ci-dessus.

2. **Autorisations** : Configurer les permissions pour s'assurer que seuls les utilisateurs autorisés peuvent effectuer certaines actions (ex: seuls les propriétaires peuvent modifier leurs bateaux).

3. **Validation** : Vérifier que les données soumises sont valides (ex: numéro d'immatriculation unique).

4. **Gestion des photos** : Implémenter un système de gestion des photos pour les bateaux.

5. **Calcul de disponibilité** : Mettre en place un algorithme pour vérifier la disponibilité des bateaux en fonction des courses déjà programmées.

6. **Intégration avec le système de réservation** : S'assurer que les bateaux réservés sont correctement marqués comme non disponibles pendant les périodes de réservation.

7. **Notifications** : Envoyer des notifications aux propriétaires lorsqu'un bateau est réservé ou lorsqu'une maintenance est prévue.
