# Script de test des endpoints de paiement avec Stripe CLI
# Pour exécuter ce script, utilisez la commande : .\test_payments.ps1

Write-Host "=== TESTS DES ENDPOINTS DE PAIEMENT COMMODORE TAXI BOAT ==="
Write-Host "Ce script utilise Stripe CLI pour tester les différents endpoints de paiement"
Write-Host ""

# Variables de configuration
$API_URL = "http://localhost:8000/api"
$STRIPE_KEY = "sk_test_51RQVnNDvUQ1WBV9hLrYCMsSWn4dUgWb3kbKWfNj70oO3spL17pkkPmwRZC5dzke0wM4jEtXlEr4fyeJV5iKiI20h00wq0jpWYi"

# Utilisez un token JWT valide obtenu manuellement
# Pour obtenir un token:
# 1. Connectez-vous à l'application web avec les identifiants du client
#    Email: <EMAIL>
#    Mot de passe: client123
# 2. Ouvrez les outils de développeur (F12)
# 3. Allez dans l'onglet "Application" > "Local Storage"
# 4. Copiez le token depuis la clé "access_token" ou similaire
#
# IMPORTANT: Vous devez utiliser le token de l'utilisateur CLIENT (ID 2)
# car les trajets créés par create_test_data.py sont associés à cet utilisateur

# Token JWT valide pour l'utilisateur client (ID: 2)
# Généré le 2025-05-26 00:11:34 avec scripts\generate_test_token.py
$TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4Mjk2OTg0LCJpYXQiOjE3NDgyMTA1ODQsImp0aSI6IjJfMTc0ODIxMDU4NC4zNzUyMTMiLCJ1c2VyX2lkIjoyfQ.020cRZGNI8czl5DnOnETaDGlfyV2pl0I-B_dB9C12aA"
# Token pour le test de paiement partagé
# Ce token a été généré par le script create_shared_payment_invite.py
$SHARED_PAYMENT_TOKEN = "6db28244-7bd9-4502-8983-f773834f0575"

# Fonction pour vérifier si un token est renseigné
function Confirm-Token {
    # Décoder le token JWT pour vérifier l'ID utilisateur
    $tokenParts = $TOKEN.Split('.')
    if ($tokenParts.Count -ge 2) {
        # Décoder la partie payload (deuxième partie du token)
        $payloadBase64 = $tokenParts[1].Replace('-', '+').Replace('_', '/')
        # Ajouter padding si nécessaire
        while ($payloadBase64.Length % 4 -ne 0) { $payloadBase64 += '=' }
        
        try {
            $payloadBytes = [System.Convert]::FromBase64String($payloadBase64)
            $payload = [System.Text.Encoding]::UTF8.GetString($payloadBytes) | ConvertFrom-Json
            
            # Vérifier si l'utilisateur est un administrateur (ID 1) au lieu du client (ID 2)
            if ($payload.user_id -eq 1) {
                Write-Host "" 
                Write-Host "ATTENTION: Vous utilisez un token appartenant à l'administrateur (ID 1)." -ForegroundColor Yellow
                Write-Host "Les trajets créés par le script create_test_data.py appartiennent à l'utilisateur client (ID 2)." -ForegroundColor Yellow
                Write-Host "Le test échouera avec l'erreur 'Vous n'êtes pas autorisé à payer cette course'." -ForegroundColor Yellow
                Write-Host "" 
                Write-Host "Veuillez obtenir un token pour l'utilisateur client (<EMAIL>) pour effectuer ces tests." -ForegroundColor Cyan
                Write-Host "" 
                $continue = Read-Host "Voulez-vous continuer avec le token actuel? (o/n)"
                if ($continue -ne "o") {
                    exit
                }
            }
        } catch {
            # Si échec de décodage, continuer avec la vérification standard
        }
    }
    
    # Vérification du token par défaut
    if ($TOKEN -eq "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzE2OTEwNDk2LCJpYXQiOjE3MTY4MjQwOTYsImp0aSI6ImQ5ZjgzZjE5YTk1YzQ1MzliZjkxZWEwZDNmNzIxMTFhIiwidXNlcl9pZCI6MX0.DEMO_TOKEN_SIMULATED") {
        Write-Host "" 
        Write-Host "ATTENTION: Vous utilisez un token par défaut qui est probablement expiré." -ForegroundColor Yellow
        Write-Host "Pour que les tests fonctionnent, vous devez modifier la variable \$TOKEN dans ce script" -ForegroundColor Yellow
        Write-Host "avec un token JWT valide obtenu depuis votre application." -ForegroundColor Yellow
        Write-Host "" 
        Write-Host "Méthode pour obtenir un token valide:" -ForegroundColor Cyan
        Write-Host "1. Connectez-vous à l'application web" -ForegroundColor Cyan
        Write-Host "2. Ouvrez les outils de développeur (F12)" -ForegroundColor Cyan
        Write-Host "3. Allez dans l'onglet 'Application' > 'Local Storage'" -ForegroundColor Cyan
        Write-Host "4. Copiez le token JWT depuis la clé 'access_token' ou similaire" -ForegroundColor Cyan
        Write-Host "5. Collez ce token dans ce script à la ligne 13" -ForegroundColor Cyan
        Write-Host "" 
        $continue = Read-Host "Voulez-vous continuer avec le token par défaut? (o/n)"
        if ($continue -ne "o") {
            exit
        }
    }
}

# Vérifier le token
Confirm-Token

# Créer une méthode de paiement de test avec Stripe CLI
Write-Host "1. Création d'une méthode de paiement de test avec Stripe CLI..."

try {
    $paymentMethod = stripe payment_methods create --type=card -d "card[number]=****************" -d "card[exp_month]=12" -d "card[exp_year]=2030" -d "card[cvc]=123" --api-key $STRIPE_KEY
    
    # Essayer de convertir la sortie JSON en objet PowerShell
    $paymentMethodObj = $paymentMethod | ConvertFrom-Json
    $PAYMENT_METHOD_ID = $paymentMethodObj.id
    
    Write-Host "Méthode de paiement créée avec succès: $PAYMENT_METHOD_ID"
}
catch {
    Write-Host "Erreur lors de la création de la méthode de paiement: $_"
    Write-Host "Utilisation d'un ID de méthode de paiement par défaut pour les tests"
    $PAYMENT_METHOD_ID = "pm_card_visa" # ID de méthode de paiement de test par défaut
}

Write-Host "Méthode de paiement pour les tests: $PAYMENT_METHOD_ID"
Write-Host ""

# Fonction pour recharger le portefeuille
function Add-WalletFunds {
    param (
        [decimal]$amount = 100.00
    )
    
    Write-Host "1.1. Rechargement du portefeuille avec $amount euros..."
    
    try {
        $response = Invoke-RestMethod -Uri "$API_URL/payments/wallet/recharge/" `
            -Method Post `
            -Headers @{
                "Authorization" = "Bearer $TOKEN"
                "Content-Type" = "application/json"
            } `
            -Body (@{
                amount = $amount
                payment_method_id = $PAYMENT_METHOD_ID
                success_url = "http://localhost:8000/success"
                cancel_url = "http://localhost:8000/cancel"
            } | ConvertTo-Json)
        
        if ($response) {
            Write-Host "Portefeuille rechargé avec succès!" -ForegroundColor Green
            Write-Host "Session ID: $($response.session_id)" -ForegroundColor Green
            
            # Simuler le paiement réussi via le webhook
            Write-Host "Simulation du webhook de paiement réussi..."
            
            # Récupérer les détails du portefeuille pour vérifier le solde
            $wallet = Invoke-RestMethod -Uri "$API_URL/payments/wallet/" `
                -Method Get `
                -Headers @{
                    "Authorization" = "Bearer $TOKEN"
                }
            
            Write-Host "Solde actuel du portefeuille: $($wallet.balance) euros" -ForegroundColor Cyan
            return $true
        }
    }
    catch {
        Write-Host "Erreur lors du rechargement du portefeuille: $_" -ForegroundColor Red
        # En cas d'échec, ajouter directement de l'argent dans le portefeuille via l'API d'administration
        Write-Host "Tentative alternative pour ajouter des fonds..." -ForegroundColor Yellow
        
        try {
            # Cette partie nécessite d'être admin ou d'avoir un token admin
            $adminResponse = Invoke-RestMethod -Uri "$API_URL/payments/admin/wallet/add-funds/" `
                -Method Post `
                -Headers @{
                    "Authorization" = "Bearer $TOKEN"
                    "Content-Type" = "application/json"
                } `
                -Body (@{
                    user_id = 2  # ID de l'utilisateur client
                    amount = $amount
                } | ConvertTo-Json) `
                -ErrorAction SilentlyContinue
            
            if ($adminResponse) {
                Write-Host "Fonds ajoutés avec succès via l'API d'administration!" -ForegroundColor Green
                return $true
            }
        }
        catch {
            Write-Host "Échec de l'ajout de fonds via l'API d'administration: $_" -ForegroundColor Red
        }
        
        # Si toutes les méthodes échouent, ajouter directement dans la base de données
        Write-Host "Pour ajouter des fonds manuellement, exécutez cette commande dans un shell Django:" -ForegroundColor Yellow
        Write-Host "from payments.models import Wallet; wallet = Wallet.objects.get(user_id=2); wallet.balance += 100; wallet.save()" -ForegroundColor Yellow
        
        return $false
    }
}

# Test 1: Paiement d'une course individuelle
function Test-TripPayment {
    param (
        [int]$tripId = 1
    )
    
    # Recharger le portefeuille avant le paiement
    Write-Host "Rechargement du portefeuille avant le test de paiement..."
    $rechargementOk = Add-WalletFunds -amount 100.00
    if (-not $rechargementOk) {
        Write-Host "ATTENTION: Le portefeuille n'a pas pu être rechargé. Le test peut échouer pour cause de solde insuffisant." -ForegroundColor Yellow
    }
    
    Write-Host "2. Test du paiement d'une course individuelle (trip_id: $tripId)..."
    
    $response = Invoke-RestMethod -Uri "$API_URL/payments/trip/" `
        -Method Post `
        -Headers @{
            "Authorization" = "Bearer $TOKEN"
            "Content-Type" = "application/json"
        } `
        -Body (@{
            trip_id = $tripId
        } | ConvertTo-Json) `
        -ErrorAction SilentlyContinue
    
    if ($response) {
        Write-Host "Paiement réussi! ID de transaction: $($response.id)"
        Write-Host "Montant: $($response.amount) $($response.currency)"
        return $response.id
    } else {
        Write-Host "Erreur lors du paiement de la course."
        return $null
    }
    Write-Host ""
}

# Test 2: Paiement d'une réservation de navette
function Test-ShuttlePayment {
    param (
        [int]$shuttleId = 5
    )
    
    Write-Host "3. Test du paiement d'une réservation de navette (shuttle_id: $shuttleId)..."
    
    $body = @{
        payment_method_id = $PAYMENT_METHOD_ID
        seats = 2
        passenger_ids = @("p_1", "p_2")
        passenger_names = @("John Doe", "Jane Doe")
        special_requests = "Besoin d'assistance pour les bagages"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "$API_URL/payments/shuttles/$shuttleId/pay/" `
        -Method Post `
        -Headers @{
            "Authorization" = "Bearer $TOKEN"
            "Content-Type" = "application/json"
        } `
        -Body $body `
        -ErrorAction SilentlyContinue
    
    if ($response) {
        Write-Host "Paiement de navette réussi! ID de transaction: $($response.id)"
        Write-Host "Montant: $($response.amount) $($response.currency)"
        Write-Host "Sièges réservés: $($response.seats)"
        Write-Host "Numéros de siège: $($response.seat_numbers -join ', ')"
        return $response.id
    } else {
        Write-Host "Erreur lors du paiement de la navette."
        return $null
    }
    Write-Host ""
}

# Test 3: Paiement d'un service de maintenance
function Test-MaintenancePayment {
    param (
        [int]$maintenanceId = 3
    )
    
    Write-Host "4. Test du paiement d'un service de maintenance (maintenance_id: $maintenanceId)..."
    
    $body = @{
        payment_method_id = $PAYMENT_METHOD_ID
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "$API_URL/payments/maintenance/$maintenanceId/pay/" `
        -Method Post `
        -Headers @{
            "Authorization" = "Bearer $TOKEN"
            "Content-Type" = "application/json"
        } `
        -Body $body `
        -ErrorAction SilentlyContinue
    
    if ($response) {
        Write-Host "Paiement de maintenance réussi! ID de transaction: $($response.id)"
        Write-Host "Montant: $($response.amount) $($response.currency)"
        Write-Host "Description: $($response.maintenance.description)"
        return $response.id
    } else {
        Write-Host "Erreur lors du paiement du service de maintenance."
        return $null
    }
    Write-Host ""
}

# Test 4: Paiement pour une promotion
function Test-PromotionPayment {
    Write-Host "5. Test du paiement d'une promotion..."
    
    $body = @{
        payment_method_id = $PAYMENT_METHOD_ID
        promotion_type = "FEATURED_LISTING"
        duration_days = 30
        target_type = "CAPTAIN"
        target_id = 5
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "$API_URL/payments/promotions/" `
        -Method Post `
        -Headers @{
            "Authorization" = "Bearer $TOKEN"
            "Content-Type" = "application/json"
        } `
        -Body $body `
        -ErrorAction SilentlyContinue
    
    if ($response) {
        Write-Host "Paiement de promotion réussi! ID de transaction: $($response.id)"
        Write-Host "Montant: $($response.amount) $($response.currency)"
        Write-Host "Type de promotion: $($response.promotion.type)"
        Write-Host "Durée: Du $($response.promotion.start_date) au $($response.promotion.end_date)"
        return $response.id
    } else {
        Write-Host "Erreur lors du paiement de la promotion."
        return $null
    }
    Write-Host ""
}

# Test 5: Paiement partagé
function Test-SharedTripPayment {
    param (
        [int]$tripId = 1
    )
    
    # Recharger le portefeuille avant le paiement partagé pour éviter l'erreur de solde insuffisant
    Write-Host "Rechargement du portefeuille avant le test de paiement partagé..." -ForegroundColor Cyan
    $rechargementOk = Add-WalletFunds -amount 100.00
    if (-not $rechargementOk) {
        Write-Host "ATTENTION: Le portefeuille n'a pas pu être rechargé. Le test peut échouer pour cause de solde insuffisant." -ForegroundColor Yellow
    }
    
    Write-Host "Génération d'une nouvelle invitation de paiement partagé..."
    $inviteResult = python ./scripts/create_shared_payment_invite.py
    if ($LASTEXITCODE -ne 0 -or $inviteResult -like 'ERROR*') {
        Write-Host "Erreur lors de la génération de l'invitation de paiement partagé: $inviteResult" -ForegroundColor Red
        return $null
    }
    $invitationToken = $inviteResult.Trim()
    Write-Host "Token d'invitation généré: $invitationToken"
    
    Write-Host "6. Test du paiement partagé d'une course (trip_id: $tripId)..."
    
    $body = @{
        payment_method_id = $PAYMENT_METHOD_ID
        token = $invitationToken  # Utiliser 'token' au lieu de 'invitation_token' pour correspondre à l'API
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "$API_URL/payments/trips/$tripId/shared_pay/" `
        -Method Post `
        -Headers @{
            "Authorization" = "Bearer $TOKEN"
            "Content-Type" = "application/json"
        } `
        -Body $body `
        -ErrorAction SilentlyContinue
    
    if ($response) {
        Write-Host "Paiement partagé réussi! ID de transaction: $($response.id)"
        Write-Host "Montant: $($response.amount) $($response.currency)"
        Write-Host "Statut du paiement: $($response.ride.payment_status)"
        Write-Host "Participants: $($response.ride.participants.Count)"
        return $response.id
    } else {
        Write-Host "Erreur lors du paiement partagé."
        return $null
    }
    Write-Host ""
}

# Test 6: Ajout de crédits au portefeuille
function Test-AddWalletCredits {
    Write-Host "7. Test d'ajout de crédits au portefeuille..."
    
    $body = @{
        amount = 50.0
        payment_method_id = $PAYMENT_METHOD_ID
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "$API_URL/payments/wallet/add_credits/" `
        -Method Post `
        -Headers @{
            "Authorization" = "Bearer $TOKEN"
            "Content-Type" = "application/json"
        } `
        -Body $body `
        -ErrorAction SilentlyContinue
    
    if ($response) {
        Write-Host "Ajout de crédits réussi! ID de transaction: $($response.transaction_id)"
        Write-Host "Nouveau solde: $($response.new_balance)"
        return $response.transaction_id
    } else {
        Write-Host "Erreur lors de l'ajout de crédits au portefeuille."
        return $null
    }
    Write-Host ""
}

# Test 7: Remboursement d'une transaction
function Test-RefundPayment {
    param (
        [string]$transactionId
    )
    
    if (-not $transactionId) {
        Write-Host "Impossible de tester le remboursement sans ID de transaction."
        return
    }
    
    Write-Host "8. Test de remboursement d'une transaction (transaction_id: $transactionId)..."
    
    $body = @{
        amount = 25.0
        reason = "Test de remboursement"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "$API_URL/payments/transactions/$transactionId/refund/" `
        -Method Post `
        -Headers @{
            "Authorization" = "Bearer $TOKEN"
            "Content-Type" = "application/json"
        } `
        -Body $body `
        -ErrorAction SilentlyContinue
    
    if ($response) {
        Write-Host "Remboursement réussi! ID de remboursement: $($response.id)"
        Write-Host "Montant remboursé: $($response.amount)"
        Write-Host "Statut: $($response.status)"
    } else {
        Write-Host "Erreur lors du remboursement."
    }
    Write-Host ""
}

# Menu interactif
function Show-Menu {
    Clear-Host
    Write-Host "=== MENU DE TEST DES PAIEMENTS ==="
    Write-Host "1: Paiement d'un trip individuel"
    Write-Host "2: Paiement d'une navette"
    Write-Host "3: Paiement d'un service de maintenance"
    Write-Host "4: Paiement d'une promotion"
    Write-Host "5: Paiement partagé"
    Write-Host "6: Ajout de crédits au portefeuille"
    Write-Host "7: Remboursement d'une transaction"
    Write-Host "8: Exécuter tous les tests"
    Write-Host "0: Quitter"
    Write-Host ""
    
    $option = Read-Host "Choisissez une option"
    
    switch ($option) {
        "1" { 
            # Recharger d'abord le portefeuille
            $rechargeSuccess = Add-WalletFunds -amount 200.00
            if ($rechargeSuccess) {
                # Puis essayer de payer la course
                Test-TripPayment
            } else {
                Write-Host "Impossible de continuer sans fonds dans le portefeuille." -ForegroundColor Red
            }
            break
        }
        "2" { $transactionId = Test-ShuttlePayment; pause }
        "3" { $transactionId = Test-MaintenancePayment; pause }
        "4" { $transactionId = Test-PromotionPayment; pause }
        "5" { $transactionId = Test-SharedTripPayment; pause }
        "6" { $transactionId = Add-WalletFunds -amount 200.00; pause }
        "7" { 
            $tid = Read-Host "Entrez l'ID de transaction à rembourser"
            Test-RefundPayment -transactionId $tid
            pause 
        }
        "8" { 
            $tid1 = Test-TripPayment
            $tid2 = Test-ShuttlePayment
            $tid3 = Test-MaintenancePayment
            $tid4 = Test-PromotionPayment
            $tid5 = Test-SharedTripPayment
            $tid6 = Add-WalletFunds -amount 200.00
            
            # Tester un remboursement sur la première transaction
            if ($tid1) {
                Test-RefundPayment -transactionId $tid1
            }
            
            Write-Host "Tous les tests ont été exécutés!"
            pause
        }
        "0" { return }
        default { Write-Host "Option invalide!"; pause }
    }
    
    Show-Menu
}

# Démarrer le menu interactif
Show-Menu
