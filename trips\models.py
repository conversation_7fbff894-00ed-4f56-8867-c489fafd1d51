from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from django.conf import settings
from accounts.models import Client, Captain, Establishment
from boats.models import Boat
from django.utils import timezone
from datetime import timedelta
import json

class Trip(models.Model):
    class Status(models.TextChoices):
        PENDING = 'PENDING', _('En attente')
        ACCEPTED = 'ACCEPTED', _('Acceptée')
        REJECTED = 'REJECTED', _('Refusée')
        IN_PROGRESS = 'IN_PROGRESS', _('En cours')
        COMPLETED = 'COMPLETED', _('Terminée')
        CANCELLED = 'CANCELLED', _('Annulée')
        CANCELLED_BY_CLIENT = 'CANCELLED_BY_CLIENT', _('Annulée par le client')
        CANCELLED_BY_CAPTAIN = 'CANCELLED_BY_CAPTAIN', _('Annulée par le capitaine')
        DELAYED = 'DELAYED', _('Retardée')
        PROBLEM = 'PROBLEM', _('Problème technique')

    class TripType(models.TextChoices):
        COURSE_SIMPLE = 'COURSE_SIMPLE', _('Course simple')
        MISE_A_DISPOSITION = 'MISE_A_DISPOSITION', _('Mise à disposition')
        NAVETTES_GRATUITES = 'NAVETTES_GRATUITES', _('Navette gratuite')

    # Informations de base
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='trips')
    captain = models.ForeignKey(Captain, on_delete=models.CASCADE, related_name='trips', null=True, blank=True)
    boat = models.ForeignKey(Boat, on_delete=models.CASCADE, related_name='trips', null=True, blank=True)
    establishment = models.ForeignKey(Establishment, on_delete=models.CASCADE, related_name='trips', null=True, blank=True)
    shuttle = models.ForeignKey('Shuttle', on_delete=models.SET_NULL, null=True, blank=True, related_name='trips', verbose_name=_('navette associée'))

    # Type de course
    trip_type = models.CharField(_('type de course'), max_length=30, choices=TripType.choices, default=TripType.COURSE_SIMPLE)

    # Détails du trajet
    # MIGRATION : Passage de CharField à JSONField pour stocker adresse + coordonnées
    start_location = models.JSONField(_('lieu de départ'), default=dict)
    end_location = models.JSONField(_('lieu d\'arrivée'), default=dict)
    scheduled_start_time = models.DateTimeField(_('heure de départ prévue'))
    scheduled_end_time = models.DateTimeField(_('heure d\'arrivée prévue'))
    actual_start_time = models.DateTimeField(_('heure de départ réelle'), null=True, blank=True)
    actual_end_time = models.DateTimeField(_('heure d\'arrivée réelle'), null=True, blank=True)
    # Ajout distance_km stockée
    distance_km = models.DecimalField(_('distance (km)'), max_digits=8, decimal_places=2, null=True, blank=True)

    # Durées
    estimated_duration = models.IntegerField(_('durée estimée (minutes)'), null=True, blank=True)
    actual_duration = models.IntegerField(_('durée réelle (minutes)'), null=True, blank=True)

    # Distance
    distance_km = models.DecimalField(_('distance (km)'), max_digits=8, decimal_places=2, null=True, blank=True)

    # Détails des passagers
    passenger_count = models.IntegerField(_('nombre de passagers'), validators=[MinValueValidator(1)])
    passenger_names = models.JSONField(_('noms des passagers'), default=list)
    special_requests = models.TextField(_('demandes spéciales'), blank=True)

    # Statut et suivi
    status = models.CharField(_('statut'), max_length=20, choices=Status.choices, default=Status.PENDING)
    current_location = models.CharField(_('position actuelle'), max_length=255, blank=True)
    tracking_data = models.JSONField(_('données de suivi'), default=list)

    # Paiement
    base_price = models.DecimalField(_('prix de base'), max_digits=10, decimal_places=2)
    additional_charges = models.DecimalField(_('frais supplémentaires'), max_digits=10, decimal_places=2, default=0)
    tip = models.DecimalField(_('pourboire'), max_digits=10, decimal_places=2, default=0)
    total_price = models.DecimalField(_('prix total'), max_digits=10, decimal_places=2)
    payment_status = models.CharField(_('statut du paiement'), max_length=20, default='PENDING')
    payment_method = models.CharField(_('méthode de paiement'), max_length=50, blank=True)

    # Métadonnées
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    cancellation_reason = models.TextField(_('raison d\'annulation'), blank=True)
    notes = models.TextField(_('notes'), blank=True)

    # QR Code pour le ticket
    qr_code = models.TextField(_('QR code'), blank=True, help_text=_('QR code encodé en base64'))

    # Nouveaux champs pour gestion avancée
    cancelled_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='cancelled_trips')
    delay_minutes = models.IntegerField(_('retard en minutes'), default=0)
    problem_description = models.TextField(_('description du problème'), blank=True)
    captain_notes = models.TextField(_('notes du capitaine'), blank=True)
    client_notes = models.TextField(_('notes du client'), blank=True)
    estimated_arrival_time = models.DateTimeField(_('heure d\'arrivée estimée'), null=True, blank=True)

    class Meta:
        verbose_name = _('course')
        verbose_name_plural = _('courses')
        ordering = ['-created_at']

    def __str__(self):
        return f'Course {self.id} - {self.start_location} → {self.end_location}'

    def calculate_total_price(self):
        self.total_price = self.base_price + self.additional_charges + self.tip
        self.save()

    def can_start(self):
        """Vérifie si la course peut être démarrée"""
        return self.status == self.Status.ACCEPTED

    def can_complete(self):
        """Vérifie si la course peut être terminée"""
        # Une course peut être terminée lorsqu'elle est en cours normalement (IN_PROGRESS)
        # ou lorsque le capitaine a signalé un retard ou un problème technique.
        return self.status in [
            self.Status.IN_PROGRESS,
            self.Status.DELAYED,
            self.Status.PROBLEM,
        ]

    def can_cancel(self):
        """Vérifie si la course peut être annulée"""
        return self.status in [self.Status.PENDING, self.Status.ACCEPTED]

    def start_trip(self, user=None):
        """Démarre la course"""
        if self.can_start():
            self.status = self.Status.IN_PROGRESS
            self.actual_start_time = timezone.now()
            if user:
                self.captain_notes = f"Course démarrée par {user.get_full_name()}"
            self.save()
            return True
        return False

    def complete_trip(self, user=None):
        """Termine la course"""
        if self.can_complete():
            self.status = self.Status.COMPLETED
            self.actual_end_time = timezone.now()
            if user:
                self.captain_notes += f"\nCourse terminée par {user.get_full_name()}"
            self.save()
            
            # Mise à jour du solde du capitaine (80% du prix total)
            captain = self.captain
            from decimal import Decimal
            captain_share = self.total_price * Decimal('0.8')
            captain.wallet_balance += captain_share
            
            # Incrémentation du nombre total de courses
            captain.total_trips += 1
            
            # Sauvegarde des modifications
            captain.save(update_fields=['wallet_balance', 'total_trips'])
            
            return True
        return False

    def cancel_trip(self, user=None, reason=""):
        """Annule la course"""
        if self.can_cancel():
            if hasattr(user, 'client'):
                self.status = self.Status.CANCELLED_BY_CLIENT
            elif hasattr(user, 'captain'):
                self.status = self.Status.CANCELLED_BY_CAPTAIN
            else:
                self.status = self.Status.CANCELLED

            self.cancelled_by = user
            self.cancellation_reason = reason
            self.save()
            return True
        return False

    def calculate_duration(self):
        """Calcule la durée réelle de la course"""
        if self.actual_start_time and self.actual_end_time:
            duration = self.actual_end_time - self.actual_start_time
            duration_minutes = int(duration.total_seconds() / 60)
            self.actual_duration = duration_minutes
            self.save(update_fields=['actual_duration'])
            return duration_minutes
        return None

    def generate_qr_code(self):
        """Génère le QR code pour cette course"""
        from .qr_service import generate_trip_qr_code
        self.qr_code = generate_trip_qr_code(self)
        return self.qr_code

    def save(self, *args, **kwargs):
        """Override save pour générer automatiquement le QR code, vérifier les transitions de statut et calculer le total_price."""

        # Calcul automatique du total_price si non renseigné ou incohérent
        if self.base_price is None:
            self.base_price = 0
        if self.additional_charges is None:
            self.additional_charges = 0
        if self.tip is None:
            self.tip = 0
        calc_total = self.base_price + self.additional_charges + self.tip
        if self.total_price is None or float(self.total_price) != float(calc_total):
            self.total_price = calc_total

        # Vérifier les transitions de statut autorisées
        if self.pk:  # Si l'objet existe déjà
            old_instance = Trip.objects.get(pk=self.pk)

            # Vérifier que la course ne peut pas passer à ACCEPTED sans paiement
            if (self.status == self.Status.ACCEPTED and
                old_instance.status == self.Status.PENDING and
                self.payment_status != 'PAID'):
                # Permettre l'acceptation, mais le paiement sera requis avant confirmation
                pass

            # Empêcher le passage à IN_PROGRESS sans paiement
            if (self.status == self.Status.IN_PROGRESS and
                self.payment_status != 'PAID'):
                raise ValueError("Impossible de démarrer une course non payée")

        super().save(*args, **kwargs)

        # Générer le QR code après la première sauvegarde (pour avoir un ID)
        if not self.qr_code and self.id:
            self.generate_qr_code()
            # Sauvegarder à nouveau avec le QR code (sans déclencher une boucle infinie)
            super().save(update_fields=['qr_code'])

    def can_be_paid(self):
        """Vérifie si la course peut être payée"""
        return self.status == self.Status.ACCEPTED and self.payment_status == 'PENDING'

    def mark_as_paid(self):
        """Marque la course comme payée et confirme la réservation"""
        if self.can_be_paid():
            self.payment_status = 'PAID'
            # La course reste ACCEPTED jusqu'à ce que le capitaine la démarre
            self.save()
            return True
        return False

class Shuttle(models.Model):
    class Status(models.TextChoices):
        SCHEDULED = 'SCHEDULED', _('Programmée')
        IN_PROGRESS = 'IN_PROGRESS', _('En cours')
        COMPLETED = 'COMPLETED', _('Terminée')
        CANCELLED = 'CANCELLED', _('Annulée')

    establishment = models.ForeignKey(Establishment, on_delete=models.CASCADE, related_name='shuttles')
    boat = models.ForeignKey(Boat, on_delete=models.CASCADE, related_name='shuttles', null=True, blank=True)
    captain = models.ForeignKey(Captain, on_delete=models.CASCADE, related_name='shuttles', null=True, blank=True)

    # Nom concaténé (déprécié, maintenu pour compatibilité)
    route_name = models.CharField(_('nom de la route'), max_length=200, blank=True)

    # Noms simples des lieux
    departure_name = models.CharField(_('nom lieu départ'), max_length=100, null=True, blank=True, default='')
    arrival_name = models.CharField(_('nom lieu arrivée'), max_length=100, null=True, blank=True, default='')

    # Coordonnées détaillées (JSON)
    start_location = models.JSONField(_('lieu de départ'), default=dict)
    end_location = models.JSONField(_('lieu d\'arrivée'), default=dict)
    stops = models.JSONField(_('arrêts'), default=list)

    departure_time = models.DateTimeField(_('heure de départ'))
    arrival_time = models.DateTimeField(_('heure d\'arrivée'))
    frequency = models.CharField(_('fréquence'), max_length=50, blank=True)
    days_of_week = models.JSONField(_('jours de la semaine'), default=list)

    max_capacity = models.IntegerField(_('capacité maximale'))
    current_bookings = models.IntegerField(_('réservations actuelles'), default=0)
    price_per_person = models.DecimalField(_('prix par personne'), max_digits=10, decimal_places=2)

    status = models.CharField(_('statut'), max_length=20, choices=Status.choices, default=Status.SCHEDULED)
    is_recurring = models.BooleanField(_('récurrent'), default=False)
    cancellation_policy = models.TextField(_('politique d\'annulation'), blank=True)

    # Champ distance_km ajouté
    distance_km = models.DecimalField(_('distance (km)'), max_digits=8, decimal_places=2, null=True, blank=True)

    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)

    def calculate_distance(self):
        try:
            dep_coords = self.start_location.get('coordinates', {}) if isinstance(self.start_location, dict) else {}
            arr_coords = self.end_location.get('coordinates', {}) if isinstance(self.end_location, dict) else {}
            dep_lat = dep_coords.get('latitude')
            dep_lon = dep_coords.get('longitude')
            arr_lat = arr_coords.get('latitude')
            arr_lon = arr_coords.get('longitude')
            if all([dep_lat, dep_lon, arr_lat, arr_lon]):
                from math import radians, cos, sin, asin, sqrt
                dep_lat, dep_lon, arr_lat, arr_lon = map(radians, [float(dep_lat), float(dep_lon), float(arr_lat), float(arr_lon)])
                dlat = arr_lat - dep_lat
                dlon = arr_lon - dep_lon
                a = sin(dlat/2)**2 + cos(dep_lat) * cos(arr_lat) * sin(dlon/2)**2
                c = 2 * asin(sqrt(a))
                r = 6371
                self.distance_km = round(c * r, 2)
                self.save(update_fields=['distance_km'])
                return self.distance_km
        except Exception as e:
            print(f"Erreur calcul distance (Shuttle): {e}")
        return None

    def save(self, *args, **kwargs):
        # Met à jour automatiquement route_name à partir des champs simples
        if self.departure_name and self.arrival_name:
            self.route_name = f"{self.departure_name} -> {self.arrival_name}"
        super().save(*args, **kwargs)
        # Calcul automatique de la distance après la sauvegarde (pour avoir les coordonnées)
        self.calculate_distance()

    class Meta:
        verbose_name = _('navette')
        verbose_name_plural = _('navettes')
        ordering = ['departure_time']

    def __str__(self):
        return f'Navette {self.route_name} - {self.departure_time.date()}'

class Location(models.Model):
    trip = models.ForeignKey(Trip, on_delete=models.CASCADE, related_name='locations')
    latitude = models.DecimalField(_('latitude'), max_digits=9, decimal_places=6)
    longitude = models.DecimalField(_('longitude'), max_digits=9, decimal_places=6)
    accuracy = models.FloatField(_('précision'), null=True, blank=True)
    speed = models.FloatField(_('vitesse'), null=True, blank=True)
    heading = models.FloatField(_('cap'), null=True, blank=True)
    altitude = models.FloatField(_('altitude'), null=True, blank=True)
    timestamp = models.DateTimeField(_('horodatage'), auto_now_add=True)

    class Meta:
        verbose_name = _('position')
        verbose_name_plural = _('positions')
        ordering = ['-timestamp']

    def __str__(self):
        return f'Position de {self.trip} à {self.timestamp}'


# Nouveaux modèles pour les trois types de courses

class TripRequest(models.Model):
    """Modèle de base pour toutes les demandes de courses"""

    class TripType(models.TextChoices):
        SIMPLE = 'SIMPLE', _('Course simple')
        HOURLY = 'HOURLY', _('Mise à disposition')
        SHUTTLE = 'SHUTTLE', _('Navette gratuite')

    class Status(models.TextChoices):
        PENDING = 'PENDING', _('En attente')
        ACCEPTED = 'ACCEPTED', _('Acceptée')
        REJECTED = 'REJECTED', _('Refusée')
        IN_PROGRESS = 'IN_PROGRESS', _('En cours')
        COMPLETED = 'COMPLETED', _('Terminée')
        CANCELLED = 'CANCELLED', _('Annulée')
        EXPIRED = 'EXPIRED', _('Expirée')

    # Champs communs
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='trip_requests')
    trip_type = models.CharField(_('type de course'), max_length=20, choices=TripType.choices)
    status = models.CharField(_('statut'), max_length=20, choices=Status.choices, default=Status.PENDING)

    # Localisation avec structure JSON complète
    departure_location = models.JSONField(_('lieu de départ'), help_text=_('Structure complète avec coordonnées'))
    arrival_location = models.JSONField(_('lieu d\'arrivée'), help_text=_('Structure complète avec coordonnées'))

    # Informations passagers
    passenger_count = models.IntegerField(_('nombre de passagers'), validators=[MinValueValidator(1)])

    # Métadonnées
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    expires_at = models.DateTimeField(_('expire le'), null=True, blank=True)

    # Calculs
    distance_km = models.DecimalField(_('distance (km)'), max_digits=8, decimal_places=2, null=True, blank=True)

    class Meta:
        verbose_name = _('demande de course')
        verbose_name_plural = _('demandes de courses')
        ordering = ['-created_at']

    def __str__(self):
        return f'Demande {self.trip_type} #{self.id} - {self.status}'

    def save(self, *args, **kwargs):
        # Définir l'expiration automatique à 10 minutes
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(minutes=10)
        super().save(*args, **kwargs)

    def is_expired(self):
        """Vérifie si la demande a expiré"""
        return timezone.now() > self.expires_at

    def calculate_distance(self):
        """Calcule la distance entre les points de départ et d'arrivée"""
        try:
            # Extraction des coordonnées
            dep_coords = self.departure_location.get('coordinates', {})
            arr_coords = self.arrival_location.get('coordinates', {})

            dep_lat = dep_coords.get('latitude')
            dep_lon = dep_coords.get('longitude')
            arr_lat = arr_coords.get('latitude')
            arr_lon = arr_coords.get('longitude')

            if all([dep_lat, dep_lon, arr_lat, arr_lon]):
                # Utilisation de la formule de Haversine pour calculer la distance
                from math import radians, cos, sin, asin, sqrt

                # Convertir en radians
                dep_lat, dep_lon, arr_lat, arr_lon = map(radians, [dep_lat, dep_lon, arr_lat, arr_lon])

                # Formule de Haversine
                dlat = arr_lat - dep_lat
                dlon = arr_lon - dep_lon
                a = sin(dlat/2)**2 + cos(dep_lat) * cos(arr_lat) * sin(dlon/2)**2
                c = 2 * asin(sqrt(a))
                r = 6371  # Rayon de la Terre en kilomètres

                self.distance_km = round(c * r, 2)
                self.save()
                return self.distance_km
        except Exception as e:
            print(f"Erreur calcul distance: {e}")
        return None


class SimpleTripRequest(TripRequest):
    """Course simple - trajet immédiat ou programmé"""

    # Référence au type de bateau souhaité
    boat_type = models.CharField(_('type de bateau'), max_length=20, choices=Boat.BoatTypes.choices)

    # Programmation optionnelle
    scheduled_date = models.DateField(_('date programmée'), null=True, blank=True)
    scheduled_time = models.TimeField(_('heure programmée'), null=True, blank=True)
    # Ajout distance_km stockée

    class Meta:
        verbose_name = _('course simple')
        verbose_name_plural = _('courses simples')

    def calculate_distance(self):
        try:
            dep_coords = self.departure_location.get('coordinates', {}) if isinstance(self.departure_location, dict) else {}
            arr_coords = self.arrival_location.get('coordinates', {}) if isinstance(self.arrival_location, dict) else {}
            dep_lat = dep_coords.get('latitude')
            dep_lon = dep_coords.get('longitude')
            arr_lat = arr_coords.get('latitude')
            arr_lon = arr_coords.get('longitude')
            if all([dep_lat, dep_lon, arr_lat, arr_lon]):
                from math import radians, cos, sin, asin, sqrt
                dep_lat, dep_lon, arr_lat, arr_lon = map(radians, [float(dep_lat), float(dep_lon), float(arr_lat), float(arr_lon)])
                dlat = arr_lat - dep_lat
                dlon = arr_lon - dep_lon
                a = sin(dlat/2)**2 + cos(dep_lat) * cos(arr_lat) * sin(dlon/2)**2
                c = 2 * asin(sqrt(a))
                r = 6371
                self.distance_km = round(c * r, 2)
                self.save(update_fields=['distance_km'])
                return self.distance_km
        except Exception as e:
            print(f"Erreur calcul distance (SimpleTripRequest): {e}")
        return None

    def save(self, *args, **kwargs):
        self.trip_type = TripRequest.TripType.SIMPLE
        super().save(*args, **kwargs)
        self.calculate_distance()


class HourlyTripRequest(TripRequest):
    """Mise à disposition - réservation par heures"""

    # Référence au type de bateau souhaité
    boat_type = models.CharField(_('type de bateau'), max_length=20, choices=Boat.BoatTypes.choices)

    # Détails de la réservation
    start_date = models.DateField(_('date de début'))
    duration_hours = models.IntegerField(_('durée en heures'), validators=[MinValueValidator(1)])
    # Ajout distance_km stockée
    

    class Meta:
        verbose_name = _('mise à disposition')
        verbose_name_plural = _('mises à disposition')

    def calculate_distance(self):
        try:
            dep_coords = self.departure_location.get('coordinates', {}) if isinstance(self.departure_location, dict) else {}
            arr_coords = self.arrival_location.get('coordinates', {}) if isinstance(self.arrival_location, dict) else {}
            dep_lat = dep_coords.get('latitude')
            dep_lon = dep_coords.get('longitude')
            arr_lat = arr_coords.get('latitude')
            arr_lon = arr_coords.get('longitude')
            if all([dep_lat, dep_lon, arr_lat, arr_lon]):
                from math import radians, cos, sin, asin, sqrt
                dep_lat, dep_lon, arr_lat, arr_lon = map(radians, [float(dep_lat), float(dep_lon), float(arr_lat), float(arr_lon)])
                dlat = arr_lat - dep_lat
                dlon = arr_lon - dep_lon
                a = sin(dlat/2)**2 + cos(dep_lat) * cos(arr_lat) * sin(dlon/2)**2
                c = 2 * asin(sqrt(a))
                r = 6371
                self.distance_km = round(c * r, 2)
                self.save(update_fields=['distance_km'])
                return self.distance_km
        except Exception as e:
            print(f"Erreur calcul distance (HourlyTripRequest): {e}")
        return None

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.calculate_distance()

    def save(self, *args, **kwargs):
        self.trip_type = TripRequest.TripType.HOURLY
        super().save(*args, **kwargs)


class ShuttleTripRequest(TripRequest):
    """Navette gratuite - offerte par les partenaires"""

    # Référence à l'établissement partenaire
    establishment = models.ForeignKey(Establishment, on_delete=models.CASCADE, related_name='shuttle_requests')

    # Détails du départ
    departure_date = models.DateField(_('date de départ'))
    departure_time = models.TimeField(_('heure de départ'))

    # Message optionnel
    message = models.TextField(_('message'), blank=True)

    class Meta:
        verbose_name = _('navette gratuite')
        verbose_name_plural = _('navettes gratuites')

    def save(self, *args, **kwargs):
        self.trip_type = TripRequest.TripType.SHUTTLE
        super().save(*args, **kwargs)

    def calculate_distance(self):
        """Calcule la distance entre le point de départ et l'établissement"""
        try:
            # Extraction des coordonnées de départ
            dep_coords = self.departure_location.get('coordinates', {})
            dep_lat = dep_coords.get('latitude')
            dep_lon = dep_coords.get('longitude')

            # Utiliser les coordonnées de l'établissement comme destination
            if self.establishment and self.establishment.longitude and self.establishment.latitude:
                arr_lat = float(self.establishment.latitude)
                arr_lon = float(self.establishment.longitude)

                if all([dep_lat, dep_lon, arr_lat, arr_lon]):
                    # Utilisation de la formule de Haversine pour calculer la distance
                    from math import radians, cos, sin, asin, sqrt

                    # Convertir en radians
                    dep_lat, dep_lon, arr_lat, arr_lon = map(radians, [float(dep_lat), float(dep_lon), arr_lat, arr_lon])

                    # Formule de Haversine
                    dlat = arr_lat - dep_lat
                    dlon = arr_lon - dep_lon
                    a = sin(dlat/2)**2 + cos(dep_lat) * cos(arr_lat) * sin(dlon/2)**2
                    c = 2 * asin(sqrt(a))
                    r = 6371  # Rayon de la Terre en kilomètres

                    self.distance_km = round(c * r, 2)
                    self.save()
                    return self.distance_km
        except Exception as e:
            print(f"Erreur calcul distance navette: {e}")
        return None


class TripQuote(models.Model):
    """Devis pour une demande de course"""

    trip_request = models.ForeignKey(TripRequest, on_delete=models.CASCADE, related_name='quotes')
    captain = models.ForeignKey(Captain, on_delete=models.CASCADE, related_name='trip_quotes')
    boat = models.ForeignKey(Boat, on_delete=models.CASCADE, related_name='trip_quotes')

    # Calculs de prix
    base_price = models.DecimalField(_('prix de base'), max_digits=10, decimal_places=2)
    distance_km = models.DecimalField(_('distance (km)'), max_digits=8, decimal_places=2)
    rate_used = models.DecimalField(_('tarif utilisé'), max_digits=7, decimal_places=2)

    # Informations du capitaine
    captain_name = models.CharField(_('nom du capitaine'), max_length=200)
    captain_rating = models.DecimalField(_('note du capitaine'), max_digits=3, decimal_places=2, default=0)

    # Informations du bateau
    boat_name = models.CharField(_('nom du bateau'), max_length=100)
    boat_capacity = models.IntegerField(_('capacité du bateau'))

    # Informations sur la tarification
    PRICING_METHOD_CHOICES = [
        ("PER_KM", "Prix au kilomètre"),
        ("PER_HOUR", "Prix à l'heure"),
    ]
    pricing_method = models.CharField(_('méthode de tarification'), max_length=20, choices=PRICING_METHOD_CHOICES, default="PER_KM")
    unit = models.CharField(_('unité'), max_length=10, default="km")

    # Métadonnées
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    is_available = models.BooleanField(_('disponible'), default=True)

    class Meta:
        verbose_name = _('devis de course')
        verbose_name_plural = _('devis de courses')
        ordering = ['base_price']  # Trier par prix croissant
        unique_together = ['trip_request', 'captain']  # Un seul devis par capitaine par demande

    def __str__(self):
        return f'Devis {self.captain_name} - {self.base_price}€'
