"""
Vues de gestion du portefeuille pour l'espace batelier.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Sum, Q
from django.core.paginator import Paginator
from decimal import Decimal
from datetime import timedelta

from accounts.models import Captain
from payments.models import Payment, Wallet
from accounts.permissions import IsBoatman


class BoatmanWalletView(APIView):
    """
    Consultation du portefeuille du batelier.

    GET /api/boatman/wallet/
    """
    permission_classes = [IsAuthenticated, <PERSON><PERSON><PERSON><PERSON>]

    def get(self, request):
        """Récupérer les informations du portefeuille"""

        # La permission IsBoatman s'en charge déjà
        captain = request.user.captain
        user = request.user

        # Récupérer ou créer le portefeuille
        wallet, created = Wallet.objects.get_or_create(user=user)

        # Paramètres de pagination pour les transactions
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))

        # Récupérer les transactions récentes
        transactions = Payment.objects.filter(
            user=user
        ).order_by('-created_at')

        # Pagination
        paginator = Paginator(transactions, limit)
        page_obj = paginator.get_page(page)

        # Sérialiser les transactions
        transactions_data = []
        for payment in page_obj:
            transaction_type = self._get_transaction_type(payment)
            amount_display = float(payment.amount)

            # Les retraits sont négatifs
            if payment.type == Payment.PaymentType.WITHDRAWAL:
                amount_display = -amount_display

            transactions_data.append({
                'transaction_id': str(payment.id),
                'type': transaction_type,
                'amount': amount_display,
                'date': payment.created_at.isoformat(),
                'status': payment.status,
                'description': payment.description or self._get_default_description(payment),
                'details': self._get_transaction_details(payment),
                'reference': payment.reference or f"TXN_{payment.id}"
            })

        # Statistiques financières
        stats = self._get_financial_stats(user)

        return Response({
            'status': 'success',
            'data': {
                'wallet': {
                    'available_balance': float(wallet.balance),
                    'currency': 'EUR',
                    'total_earned': float(wallet.total_earned) if hasattr(wallet, 'total_earned') else 0.0,
                    'total_withdrawn': float(wallet.total_spent) if hasattr(wallet, 'total_spent') else 0.0,
                    'pending_amount': float(stats['pending_earnings'])
                },
                'transactions': transactions_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': paginator.count,
                    'total_pages': paginator.num_pages,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                },
                'statistics': {
                    'earnings_this_month': float(stats['earnings_this_month']),
                    'earnings_this_week': float(stats['earnings_this_week']),
                    'total_tips': float(stats['total_tips']),
                    'average_trip_earning': float(stats['average_trip_earning']),
                    'total_trips_paid': stats['total_trips_paid']
                },
                'quick_actions': [
                    {
                        'action': 'withdraw',
                        'label': 'Retirer des fonds',
                        'url': '/api/boatman/wallet/withdraw/',
                        'enabled': wallet.balance > 0
                    },
                    {
                        'action': 'payment_methods',
                        'label': 'Gérer les moyens de paiement',
                        'url': '/api/boatman/payment-methods/',
                        'enabled': True
                    }
                ]
            },
            'message': 'Portefeuille récupéré avec succès',
            'timestamp': timezone.now().isoformat()
        })

    def _get_transaction_type(self, payment):
        """Obtenir le type de transaction lisible"""
        type_mapping = {
            Payment.PaymentType.TRIP: 'Paiement course',
            Payment.PaymentType.TIP: 'Pourboire',
            Payment.PaymentType.WITHDRAWAL: 'Retrait',
            Payment.PaymentType.REFUND: 'Remboursement',
            Payment.PaymentType.BONUS: 'Bonus'
        }
        return type_mapping.get(payment.type, 'Autre')

    def _get_default_description(self, payment):
        """Obtenir une description par défaut"""
        if payment.type == Payment.PaymentType.TRIP:
            return f"Paiement course #{payment.trip.id if payment.trip else 'N/A'}"
        elif payment.type == Payment.PaymentType.TIP:
            return f"Pourboire course #{payment.trip.id if payment.trip else 'N/A'}"
        elif payment.type == Payment.PaymentType.WITHDRAWAL:
            return f"Retrait vers compte bancaire"
        return "Transaction"

    def _get_transaction_details(self, payment):
        """Obtenir les détails de la transaction"""
        details = {}

        if payment.trip:
            details['trip_id'] = str(payment.trip.id)
            details['destination'] = payment.trip.end_location
            details['passengers'] = payment.trip.passenger_count
            details['client'] = payment.trip.client.user.get_full_name() if payment.trip.client else 'Client inconnu'

        if payment.type == Payment.PaymentType.WITHDRAWAL:
            details['withdrawal_method'] = payment.payment_method
            details['processing_time'] = '1-3 jours ouvrés'

        return details

    def _get_financial_stats(self, user):
        """Calculer les statistiques financières"""
        now = timezone.now()
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        week_start = now - timedelta(days=7)

        # Gains ce mois-ci
        earnings_this_month = Payment.objects.filter(
            user=user,
            type__in=[Payment.PaymentType.TRIP, Payment.PaymentType.TIP],
            status=Payment.Status.COMPLETED,
            created_at__gte=month_start
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # Gains cette semaine
        earnings_this_week = Payment.objects.filter(
            user=user,
            type__in=[Payment.PaymentType.TRIP, Payment.PaymentType.TIP],
            status=Payment.Status.COMPLETED,
            created_at__gte=week_start
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # Total des pourboires
        total_tips = Payment.objects.filter(
            user=user,
            type=Payment.PaymentType.TIP,
            status=Payment.Status.COMPLETED
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # Gains en attente
        pending_earnings = Payment.objects.filter(
            user=user,
            type__in=[Payment.PaymentType.TRIP, Payment.PaymentType.TIP],
            status=Payment.Status.PENDING
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # Statistiques des courses
        trip_payments = Payment.objects.filter(
            user=user,
            type=Payment.PaymentType.TRIP,
            status=Payment.Status.COMPLETED
        )

        total_trips_paid = trip_payments.count()
        total_trip_earnings = trip_payments.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        average_trip_earning = total_trip_earnings / total_trips_paid if total_trips_paid > 0 else Decimal('0.00')

        return {
            'earnings_this_month': earnings_this_month,
            'earnings_this_week': earnings_this_week,
            'total_tips': total_tips,
            'pending_earnings': pending_earnings,
            'total_trips_paid': total_trips_paid,
            'average_trip_earning': average_trip_earning
        }


class BoatmanWithdrawView(APIView):
    """
    Retrait de fonds du portefeuille.

    POST /api/boatman/wallet/withdraw/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def post(self, request):
        """Effectuer un retrait"""

        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)

        user = request.user
        amount = request.data.get('amount')

        if not amount:
            return Response({
                'status': 'error',
                'error': 'Montant requis',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            amount = Decimal(str(amount))
        except:
            return Response({
                'status': 'error',
                'error': 'Montant invalide',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)

        if amount <= 0:
            return Response({
                'status': 'error',
                'error': 'Le montant doit être positif',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)

        # Vérifier le solde
        wallet = getattr(user, 'wallet', None)
        if not wallet:
            return Response({
                'status': 'error',
                'error': 'Portefeuille non trouvé',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)

        if wallet.balance < amount:
            return Response({
                'status': 'error',
                'error': 'Solde insuffisant',
                'error_code': 400,
                'data': {
                    'available_balance': float(wallet.balance),
                    'requested_amount': float(amount)
                }
            }, status=status.HTTP_400_BAD_REQUEST)

        # Pour l'instant, on accepte les retraits sans vérification de méthode de paiement
        # En production, il faudrait vérifier qu'une méthode de paiement est configurée

        # Créer la transaction de retrait
        withdrawal = Payment.objects.create(
            user=user,
            type=Payment.PaymentType.WITHDRAWAL,
            amount=amount,
            status=Payment.Status.PENDING,
            payment_method='BANK_TRANSFER',
            description="Retrait vers compte bancaire",
            reference=f"WD_{timezone.now().strftime('%Y%m%d%H%M%S')}"
        )

        # Débiter le portefeuille
        wallet.balance -= amount
        wallet.save()

        return Response({
            'status': 'success',
            'data': {
                'withdrawal_id': str(withdrawal.id),
                'amount': float(amount),
                'new_balance': float(wallet.balance),
                'processing_time': '1-3 jours ouvrés',
                'reference': withdrawal.reference,
                'payment_method': {
                    'type': 'Compte bancaire',
                    'last_four': '****'
                }
            },
            'message': 'Retrait effectué, fonds en cours de transfert',
            'timestamp': timezone.now().isoformat()
        })


class BoatmanPaymentMethodsView(APIView):
    """
    Gestion des méthodes de paiement.

    GET/POST /api/boatman/payment-methods/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def get(self, request):
        """Récupérer les méthodes de paiement"""

        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)

        # Pour l'instant, retourner une liste vide
        # En production, il faudrait implémenter un vrai système de méthodes de paiement
        return Response({
            'status': 'success',
            'data': {
                'payment_methods': [],
                'total_methods': 0,
                'has_default': False,
                'message': 'Fonctionnalité en cours de développement'
            },
            'message': 'Méthodes de paiement récupérées',
            'timestamp': timezone.now().isoformat()
        })

    def post(self, request):
        """Ajouter/Mettre à jour une méthode de paiement"""

        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)

        # Pour l'instant, retourner un message indiquant que la fonctionnalité est en développement
        return Response({
            'status': 'success',
            'data': {
                'message': 'Fonctionnalité en cours de développement',
                'note': 'Les méthodes de paiement seront bientôt disponibles'
            },
            'message': 'Méthode de paiement enregistrée (simulation)',
            'timestamp': timezone.now().isoformat()
        })
