# Script de setup pour nouveaux développeurs
# Ce script configure automatiquement l'environnement de développement

Write-Host "=== SETUP DU PROJET COMMODORE ===" -ForegroundColor Green
Write-Host "Ce script va configurer votre environnement de développement" -ForegroundColor Yellow

# Vérifier si on est dans le bon répertoire
if (-not (Test-Path "manage.py")) {
    Write-Host "ERREUR: Ce script doit être exécuté depuis la racine du projet (où se trouve manage.py)" -ForegroundColor Red
    exit 1
}

# Vérifier si Python est installé
try {
    $pythonVersion = python --version
    Write-Host "✅ Python détecté: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python n'est pas installé ou pas dans le PATH" -ForegroundColor Red
    exit 1
}

# Vérifier si PostgreSQL est installé
try {
    $pgVersion = psql --version
    Write-Host "✅ PostgreSQL détecté: $pgVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ PostgreSQL n'est pas installé ou pas dans le PATH" -ForegroundColor Red
    Write-Host "Veuillez installer PostgreSQL avant de continuer" -ForegroundColor Yellow
    exit 1
}

# Créer l'environnement virtuel s'il n'existe pas
if (-not (Test-Path "venv")) {
    Write-Host "Création de l'environnement virtuel..." -ForegroundColor Yellow
    python -m venv venv
    Write-Host "✅ Environnement virtuel créé" -ForegroundColor Green
} else {
    Write-Host "✅ Environnement virtuel déjà existant" -ForegroundColor Green
}

# Activer l'environnement virtuel
Write-Host "Activation de l'environnement virtuel..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"

# Installer les dépendances
Write-Host "Installation des dépendances..." -ForegroundColor Yellow
pip install -r requirements.txt

# Vérifier si le fichier .env existe
if (-not (Test-Path ".env")) {
    Write-Host "Création du fichier .env..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "✅ Fichier .env créé depuis .env.example" -ForegroundColor Green
    Write-Host "⚠️  Veuillez modifier le fichier .env avec vos paramètres" -ForegroundColor Yellow
} else {
    Write-Host "✅ Fichier .env déjà existant" -ForegroundColor Green
}

# Créer la base de données
Write-Host "Configuration de la base de données..." -ForegroundColor Yellow
$env:PGPASSWORD = "admin"
$createDbCommand = "CREATE DATABASE IF NOT EXISTS commodore;"
try {
    psql -U postgres -c "$createDbCommand"
    Write-Host "✅ Base de données 'commodore' créée/vérifiée" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors de la création de la base de données" -ForegroundColor Red
    Write-Host "Vérifiez que PostgreSQL fonctionne et que l'utilisateur 'postgres' existe" -ForegroundColor Yellow
}

# Exécuter les migrations
Write-Host "Application des migrations..." -ForegroundColor Yellow
python manage.py migrate

Write-Host "=== SETUP TERMINÉ ===" -ForegroundColor Green
Write-Host "Prochaines étapes:" -ForegroundColor Yellow
Write-Host "1. Modifiez le fichier .env si nécessaire" -ForegroundColor Cyan
Write-Host "2. Créez un superuser: python manage.py createsuperuser" -ForegroundColor Cyan
Write-Host "3. Lancez le serveur: python manage.py runserver" -ForegroundColor Cyan
