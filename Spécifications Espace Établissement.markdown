# Spécifications pour l'Espace Établissement

Ce document détaille les spécifications backend pour l'espace **Établissement** d'une application de gestion de navettes maritimes, basé sur le mockup fourni (Etablissement1.pdf). Il inclut les champs, les payloads JSON pour les échanges frontend-backend, les actions, et les user stories. L'authentification et le profil sont déjà gé<PERSON>, donc l'accent est mis sur les autres fonctionnalités, y compris l'enregistrement des bateliers avec envoi d'email et mot de passe temporaire.

## 1. Tableau de Bord (Page 1)

### Champs
- **Nom de l'établissement** : `string` (ex. "Nobu")
- **Type** : `string` (Restaurant, Hôtel, Plage privée)
- **Solde disponible** : `number` (ex. 25.00)
- **Navettes réalisées** : `integer` (ex. 12)
- **Demandes en attente** : `integer` (ex. 2)
- **Disponibilité** : `boolean` (disponible/indisponible)
- **Liste des courses** :
  - `shuttle_id` : `string`
  - `date` : `string` (ISO 8601, ex. "2024-05-23T11:00:00Z")
  - `status` : `string` (En attente, À venir, En cours, Terminé, Annulé)
  - `departure` : `string` (ex. "Port de Beaulieu")
  - `destination` : `string` (ex. "Plage de la Petite Afrique")
  - `client` : `string` (ex. "WaveRiders")
  - `amount` : `number` (ex. 33.00)

### Actions et Payloads JSON

#### 1. Récupérer les données du tableau de bord
**Requête :** `GET /dashboard`
```json
{
  "establishment_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "establishment_name": "string",
    "establishment_type": "string",
    "available_balance": "number",
    "total_shuttles": "integer",
    "pending_requests": "integer",
    "availability": "boolean",
    "shuttles": [
      {
        "shuttle_id": "string",
        "date": "string",
        "status": "string",
        "departure": "string",
        "destination": "string",
        "client": "string",
        "amount": "number"
      }
    ]
  }
}
```

#### 2. Basculer la disponibilité
**Requête :** `POST /toggle-availability`
```json
{
  "establishment_id": "string",
  "availability": "boolean"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Disponibilité mise à jour"
}
```

#### 3. Voir toutes les courses
**Requête :** `GET /shuttles`
```json
{
  "establishment_id": "string",
  "filter": "string (optionnel, ex. En attente)",
  "page": "integer",
  "limit": "integer"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "shuttles": [
      {
        "shuttle_id": "string",
        "date": "string",
        "status": "string",
        "departure": "string",
        "destination": "string",
        "client": "string",
        "amount": "number"
      }
    ],
    "pagination": {
      "page": "integer",
      "limit": "integer",
      "total": "integer"
    }
  }
}
```

## 2. Détails de la Course (Pages 2, 12-19)

### Champs
- **Localisation** : `string` (ex. "Clinton", non interactive)
- **Statut** : `string` (À venir, En attente, En cours, Terminé)
- **Date et heure** : `string` (ex. "2024-05-17T16:30:00Z")
- **Départ** : `string` (ex. "Port de Cannes")
- **Arrivée** : `string` (ex. "Nobu, St Tropez var France")
- **Client** : `string` (ex. "Jacob28")
- **Passagers** : `integer` (ex. 5)
- **Remarque** : `string` (ex. "Merci de prévoir un bateau couvert...")
- **Capitaine** : `string` (ex. "Cameron Williamson")
- **Bateau** : `string` (ex. "Beneteau Flyer 7.7 SUNdeck - Blue")
- **Total de la course** : `number` (ex. 40.00)

### Actions et Payloads JSON

#### 1. Récupérer les détails de la course
**Requête :** `GET /shuttle/:shuttle_id`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "shuttle_id": "string",
    "status": "string",
    "date": "string",
    "departure": "string",
    "destination": "string",
    "passengers": "integer",
    "remarks": "string",
    "client": {
      "client_id": "string",
      "name": "string"
    },
    "captain": {
      "captain_id": "string",
      "name": "string"
    },
    "boat": {
      "boat_id": "string",
      "name": "string",
      "category": "string"
    },
    "amount": "number"
  }
}
```

#### 2. Annuler la course
**Requête :** `POST /shuttle/:shuttle_id/cancel`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Course annulée"
}
```

#### 3. Démarrer la course
**Requête :** `POST /shuttle/:shuttle_id/start`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Course démarrée"
}
```

#### 4. Payer la course
**Requête :** `POST /shuttle/:shuttle_id/pay`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string",
  "payment_method": "string (credit, card)",
  "card_details": {
    "cardholder": "string",
    "card_number": "string",
    "expiry_date": "string (MM/YY)",
    "cvv": "string"
  }
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Paiement effectué",
  "transaction_id": "string"
}
```

#### 5. Accepter/Rejeter la course
**Requête :** `POST /shuttle/:shuttle_id/accept` ou `/reject`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Course acceptée/rejetée"
}
```

#### 6. Suivre la course
**Requête :** `GET /shuttle/:shuttle_id/track`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "shuttle_id": "string",
    "status": "string",
    "current_location": "string",
    "departure": "string",
    "destination": "string",
    "client": "string",
    "captain": "string",
    "boat": "string"
  }
}
```

## 3. Changer le Mot de Passe (Pages 3-4)

### Champs
- **Mot de passe** : `string` (min. 8 chars, maj, min, spécial)
- **Confirmer le mot de passe** : `string`

### Actions et Payloads JSON

#### 1. Changer le mot de passe
**Requête :** `POST /change-password`
```json
{
  "establishment_id": "string",
  "new_password": "string",
  "confirm_password": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Mot de passe mis à jour"
}
```

## 4. Informations de Paiement (Pages 5-6, 40)

### Champs
- **Propriétaire** : `string` (ex. "Jacob Jones")
- **Numéro de carte** : `string` (ex. "****************")
- **Date d'expiration** : `string` (ex. "11/26")
- **CVV** : `string` (ex. "231")
- **Total à payer** : `number` (ex. 25.00)
- **Options de paiement** : `string` (credit, card)

### Actions et Payloads JSON

#### 1. Ajouter/Mettre à jour la méthode de paiement
**Requête :** `POST /payment-method`
```json
{
  "establishment_id": "string",
  "cardholder": "string",
  "card_number": "string",
  "expiry_date": "string (MM/YY)",
  "cvv": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Méthode de paiement mise à jour"
}
```

#### 2. Effectuer un paiement
**Requête :** `POST /process-payment`
```json
{
  "establishment_id": "string",
  "amount": "number",
  "payment_method": "string (credit, card)",
  "card_details": {
    "cardholder": "string",
    "card_number": "string",
    "expiry_date": "string (MM/YY)",
    "cvv": "string"
  }
}
```
**Réponse :**
```json
{
 MUNSCI{
  "status": "success",
  "message": "Paiement réussi",
  "transaction_id": "string"
}
```

## 5. Code de Vérification (Pages 7-8)

### Champs
- **Code de vérification** : `string` (4 chiffres, ex. "1572")
- **Contact** : `string` (ex. email "ja**<EMAIL>")
- **Conditions** : `boolean` (acceptation des termes)

### Actions et Payloads JSON

#### 1. Envoyer le code de vérification
**Requête :** `POST /send-verification`
```json
{
  "establishment_id": "string",
  "contact_method": "string (email, phone)",
  "contact_value": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Code de vérification envoyé"
}
```

#### 2. Vérifier le code
**Requête :** `POST /verify-code`
```json
{
  "establishment_id": "string",
  "code": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Code vérifié"
}
```

## 6. Connexion (Page 9)

### Champs
- **Email** : `string` (ex. "<EMAIL>")
- **Mot de passe** : `string`
- **Options de connexion** : `string` (Google, Facebook)

### Actions et Payloads JSON

#### 1. Connexion
**Requête :** `POST /login`
```json
{
  "email": "string",
  "password": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "establishment_id": "string",
    "token": "string",
    "establishment_name": "string"
  }
}
```

#### 2. Connexion via Google/Facebook
**Requête :** `POST /login/social`
```json
{
  "provider": "string (google, facebook)",
  "access_token": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "establishment_id": "string",
    "token": "string",
    "establishment_name": "string"
  }
}
```

#### 3. Mot de passe oublié
**Requête :** `POST /forgot-password`
```json
{
  "email": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Instructions de réinitialisation envoyées"
}
```

## 7. Détails de la Course - Informations Complémentaires (Page 10)

### Champs
- **Date et heure** : `string` (ex. "2024-05-20T14:00:00Z")
- **Durée** : `string` (ex. "3h")
- **Passagers** : `integer` (ex. 3)

### Actions : 
Inclus dans `GET /shuttle/:id` avec les champs supplémentaires : `duration`, `passengers`.

## 8. Gestion des Courses (Pages 20-23, 43)

### Champs
- **Filtres** : `string` (En attente, À venir, En cours, Terminé, Annulé)
- **Client** : `string` (ex. "Jacob28")
- **Date et heure** : `string` (ex. "2024-05-17T16:30:00Z")
- **Départ** : `string` (ex. "Port de la Cannes")
- **Passagers** : `integer` (ex. 4)
- **Capitaine** : `string` (ex. "Cameron Williamson")
- **Bateau** : `string` (ex. "Beneteau Flyer 7.7 SUNdeck - Blue")
- **Montant** : `number` (ex. 28.00)

### Actions : 
Identique à `GET /shuttles`.

## 9. Sélection du Capitaine (Page 24)

### Champs
- **Date et heure** : `string` (ex. "2024-05-18T14:00:00Z")
- **Client** : `string` (ex. "Jacob28")
- **Départ** : `string` (ex. "Port de Cannes")
- **Passagers** : `integer` (ex. 4)
- **Capitaines disponibles** :
  - `boat` : `string` (ex. "Sea Ray SLX 400")
  - `registration` : `string` (ex. "ST-67890-A")
  - `distance` : `number` (ex. 0.3)
  - `captain` : `string` (ex. "Edouard SMITH")
  - `rating` : `number` (ex. 5.2)
  - `amount` : `number` (ex. 35.00)

### Actions et Payloads JSON

#### 1. Récupérer les capitaines disponibles
**Requête :** `GET /available-captains`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "shuttle_id": "string",
    "date": "string",
    "client": "string",
    "departure": "string",
    "passengers": "integer",
    "captains": [
      {
        "captain_id": "string",
        "name": "string",
        "boat": {
          "boat_id": "string",
          "name": "string",
          "registration": "string"
        },
        "distance": "number",
        "rating": "number",
        "amount": "number"
      }
    ]
  }
}
```

#### 2. Assigner un capitaine
**Requête :** `POST /shuttle/:shuttle_id/assign-captain`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string",
  "captain_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Capitaine assigné"
}
```

## 10. Profil de l'Établissement (Pages 25-28)

### Champs
- **Nom du capitaine** : `string` (ex. "Jules Lefevre")
- **Note** : `number` (ex. 1.5)
- **Avis** : `integer` (ex. 127)
- **Bio** : `string` (ex. "Marin depuis plus de 12 ans...")
- **Bateau** : `string` (ex. "Beneteau Flyer 7.7 SUNdeck")
- **Type** : `string` (ex. "Blue")
- **Matricule** : `string` (ex. "MA-345-CM")
- **Capacité** : `string` (ex. "5 places")
- **Capitaine** : `string` (ex. "Cameron Williamson")
- **Date d'expiration** : `string` (ex. "2025-12-15")
- **Note moyenne** : `number` (ex. 4.8)
- **Type d'établissement** : `string` (Restaurant, Hôtel, Plage privée)
- **Nom** : `string` (ex. "Nobu")
- **Adresse** : `string` (ex. "Route de l'Escalet, 83350 Ramatuelle")
- **Description** : `string` (ex. "Niché au cœur...")

### Actions et Payloads JSON

#### 1. Mettre à jour les détails de l'établissement
**Requête :** `POST /establishment/update`
```json
{
  "establishment_id": "string",
  "type": "string",
  "name": "string",
  "address": "string",
  "description": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Détails de l'établissement mis à jour"
}
```

#### 2. Envoyer une demande à un capitaine
**Requête :** `POST /shuttle-request`
```json
{
  "establishment_id": "string",
  "captain_id": "string",
  "shuttle_details": {
    "departure": "string",
    "destination": "string",
    "date": "string (ISO 8601)",
    "passengers": "integer",
    "remarks": "string"
  }
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Demande envoyée",
  "shuttle_id": "string"
}
```

## 11. Ajouter des Photos de l'Établissement (Pages 29-30)

### Champs
- **Photo principale** : `string` (base64, JPG/PNG, max 5MB)
- **Photos secondaires** : `array of string` (base64, JPG/PNG, max 5MB)

### Actions et Payloads JSON

#### 1. Ajouter des photos
**Requête :** `POST /establishment/photos`
```json
{
  "establishment_id": "string",
  "main_photo": "string",
  "secondary_photos": ["string"]
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Photos ajoutées"
}
```

## 12. Enregistrer un Batelier (Pages 31-32)

### Champs
- **Nom du capitaine** : `string` (ex. "John Smith")
- **Email** : `string` (ex. "<EMAIL>")
- **Nom du bateau** : `string` (ex. "Beneteau Flyer 7.7 SUNdeck")
- **Matricule** : `string` (ex. "MA-345-CM")
- **Capacité** : `integer` (1-25, ex. 25)
- **Type de carburant** : `string` (essence, diesel, électrique, hybride)
- **Consommation de carburant** : `number` (ex. 12.0 L/h)

### Logique d'Enregistrement
- Lors de l'enregistrement, le système génère un **mot de passe temporaire** (8 caractères alphanumériques, ex. `X7k4mP9v`).
- Un **email** est envoyé à l'email du batelier avec :
  - Nom d'utilisateur : l'adresse email
  - Mot de passe temporaire
  - Lien vers l'espace batelier (ex. `https://app.example.com/boatman/login`)
- Le batelier peut se connecter à l'espace batelier et changer son mot de passe après la première connexion.

### Actions et Payloads JSON

#### 1. Enregistrer un batelier
**Requête :** `POST /boatman/register`
```json
{
  "establishment_id": "string",
  "captain": {
    "name": "string",
    "email": "string"
  },
  "boat": {
    "name": "string",
    "registration": "string",
    "capacity": "integer",
    "fuel_type": "string",
    "fuel_consumption": "number"
  }
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Batelier enregistré, email avec mot de passe envoyé",
  "captain_id": "string"
}
```

#### 2. Connexion du batelier
**Requête :** `POST /boatman/login`
```json
{
  "email": "string",
  "password": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "captain_id": "string",
    "token": "string",
    "name": "string"
  }
}
```

## 13. Inscription (Pages 33-34)

### Champs
- **Email** : `string` (ex. "<EMAIL>")
- **Mot de passe** : `string`
- **Options** : `string` (Google, Facebook)

### Actions et Payloads JSON

#### 1. Inscription
**Requête :** `POST /signup`
```json
{
  "email": "string",
  "password": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "establishment_id": "string",
    "token": "string"
  }
}
```

#### 2. Inscription via Google/Facebook
**Requête :** `POST /signup/social`
```json
{
  "provider": "string (google, facebook)",
  "access_token": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "establishment_id": "string",
    "token": "string",
    "establishment_name": "string"
  }
}
```

## 14. Mot de Passe Oublié (Page 35)

### Champs
- **Email** : `string` (ex. "<EMAIL>")
- **Téléphone** : `string` (ex. "*******254")

### Actions : 
Identique à `POST /send-verification`.

## 15. Assignation d'un Capitaine Interne/Indépendant (Page 36)

### Champs
- **Batelier interne** : `string` (ex. "John Smith")
- **Bateau** : `string` (ex. "Beneteau Flyer 7.7 SUNdeck")

### Actions et Payloads JSON

#### 1. Assigner un batelier interne
**Requête :** `POST /shuttle/:shuttle_id/assign-internal`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string",
  "captain_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Batelier interne assigné"
}
```

#### 2. Rechercher un capitaine indépendant
**Requête :** `GET /independent-captains`
```json
{
  "establishment_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": [
    {
      "captain_id": "string",
      "name": "string",
      "boat": {
        "boat_id": "string",
        "name": "string",
        "registration": "string"
      },
      "distance": "number",
      "rating": "number",
      "amount": "number"
    }
  ]
}
```

## 16. Confirmation de Paiement (Pages 37-39)

### Champs
- **Message** : `string` (ex. "Paiement réussi ! Votre portefeuille a été rechargé de 25.00 €")
- **Utilisation** : `string` (ex. "Vous pouvez l'utiliser pour vos prochaines courses...")

### Actions : 
Inclus dans `POST /process-payment`.

## 17. Créditer le Compte (Pages 41-42)

### Champs
- **Montant à ajouter** : `number` (ex. 25, 50, 75, 100, 125, 150, 175, 200)
- **Montant personnalisé** : `number` (ex. 25)

### Actions et Payloads JSON

#### 1. Créditer le compte
**Requête :** `POST /credit-account`
```json
{
  "establishment_id": "string",
  "amount": "number"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Compte crédité",
  "new_balance": "number"
}
```

## User Stories

1. **Vue d'ensemble du tableau de bord**
   - **Description** : En tant que gestionnaire d'établissement, je veux voir mon tableau de bord pour consulter le solde disponible, le nombre de navettes réalisées, les demandes en attente, et un résumé des courses récentes.
   - **Critères d'acceptation** :
     - Afficher le nom et le type de l'établissement, le solde, le nombre de navettes réalisées, et les demandes en attente.
     - Montrer une liste des courses récentes avec date, statut, départ, destination, client, et montant.
     - Permettre de basculer la disponibilité de l'établissement.
     - Fournir une option pour voir toutes les courses.

2. **Basculer la disponibilité**
   - **Description** : En tant que gestionnaire d'établissement, je veux activer ou désactiver la disponibilité de mon établissement pour recevoir des demandes de navettes.
   - **Critères d'acceptation** :
     - Permettre de basculer entre disponible et indisponible.
     - Afficher un message de confirmation après la mise à jour.

3. **Gérer les courses**
   - **Description** : En tant que gestionnaire d'établissement, je veux gérer mes courses pour voir, accepter, rejeter, démarrer, annuler, payer ou suivre une course.
   - **Critères d'acceptation** :
     - Afficher une liste des courses avec client, date, départ, passagers, capitaine, bateau, montant, et statut.
     - Permettre le filtrage par statut (En attente, À venir, En cours, Terminé, Annulé).
     - Fournir des options pour accepter, rejeter, démarrer, annuler, payer ou suivre une course.
     - Afficher les détails de la course, y compris les remarques et le nombre de passagers.
     - Afficher des messages de confirmation pour chaque action.

4. **Suivre une course**
   - **Description** : En tant que gestionnaire d'établissement, je veux suivre une course en cours pour surveiller sa progression.
   - **Critères d'acceptation** :
     - Afficher l'emplacement actuel, le départ, la destination, le client, le capitaine, et le bateau.
     - Mettre à jour le statut en temps réel.

5. **Changer le mot de passe**
   - **Description** : En tant que gestionnaire d'établissement, je veux changer mon mot de passe pour sécuriser mon compte.
   - **Critères d'acceptation** :
     - Exiger un mot de passe d'au moins 8 caractères avec majuscule, minuscule, et caractère spécial.
     - Confirmer que les mots de passe correspondent.
     - Afficher un message de succès après la mise à jour.

6. **Gérer les informations de paiement**
   - **Description** : En tant que gestionnaire d'établissement, je veux ajouter ou mettre à jour ma méthode de paiement pour effectuer des paiements.
   - **Critères d'acceptation** :
     - Accepter le nom du titulaire, le numéro de carte, la date d'expiration, et le CVV.
     - Permettre le choix entre utiliser le crédit du portefeuille ou payer par carte.
     - Afficher un message de succès après la mise à jour ou le paiement.

7. **Vérifier l'identité**
   - **Description** : En tant que gestionnaire d'établissement, je veux vérifier mon identité avec un code envoyé à mon email ou téléphone pour sécuriser mon compte.
   - **Critères d'acceptation** :
     - Envoyer un code de vérification à la méthode de contact choisie.
     - Permettre le renvoi du code.
     - Valider le code saisi (4 chiffres).
     - Afficher un message de succès après vérification.
     - Exiger l'acceptation des termes et conditions lors de l'inscription.

8. **Connexion**
   - **Description** : En tant que gestionnaire d'établissement, je veux me connecter à mon compte pour accéder à l'espace établissement.
   - **Critères d'acceptation** :
     - Accepter l'email et le mot de passe.
     - Fournir une option "Mot de passe oublié".
     - Supporter la connexion via Google ou Facebook.
     - Rediriger vers le tableau de bord après une connexion réussie.

9. **Inscription**
   - **Description** : En tant que nouveau gestionnaire d'établissement, je veux m'inscrire pour créer un compte et gérer les navettes.
   - **Critères d'acceptation** :
     - Accepter l'email et le mot de passe.
     - Supporter l'inscription via Google ou Facebook.
     - Rediriger vers la configuration de l'établissement après l'inscription.

10. **Mot de passe oublié**
    - **Description** : En tant que gestionnaire d'établissement, je veux réinitialiser mon mot de passe si je l'oublie pour récupérer l'accès à mon compte.
    - **Critères d'acceptation** :
      - Permettre la sélection de l'email ou du téléphone pour recevoir un code de vérification.
      - Envoyer un code de vérification à la méthode choisie.
      - Rediriger vers la page de vérification du code.

11. **Sélectionner un capitaine**
    - **Description** : En tant que gestionnaire d'établissement, je veux sélectionner un capitaine pour une course en consultant les capitaines disponibles.
    - **Critères d'acceptation** :
      - Afficher la liste des capitaines avec bateau, matricule, distance, note, et montant.
      - Permettre de sélectionner un capitaine pour une course spécifique.
      - Afficher un message de confirmation après l'assignation.

12. **Mettre à jour les détails de l'établissement**
    - **Description** : En tant que gestionnaire d'établissement, je veux mettre à jour les détails de mon établissement pour refléter les informations actuelles.
    - **Critères d'acceptation** :
      - Permettre de sélectionner le type (Restaurant, Hôtel, Plage privée).
      - Saisir le nom, l'adresse, et une description (2-3 phrases).
      - Afficher un message de succès après la mise à jour.

13. **Ajouter des photos de l'établissement**
    - **Description** : En tant que gestionnaire d'établissement, je veux ajouter des photos pour présenter mon établissement.
    - **Critères d'acceptation** :
      - Permettre de télécharger une photo principale et jusqu'à 3 photos secondaires (JPG/PNG, max 5MB).
      - Afficher un message de succès après l'ajout.

14. **Enregistrer un batelier**
    - **Description** : En tant que gestionnaire d'établissement, je veux enregistrer un batelier pour qu'il puisse gérer les navettes de mon établissement.
    - **Critères d'acceptation** :
      - Saisir le nom, l'email, le nom du bateau, le matricule, la capacité (1-25 personnes), le type de carburant, et la consommation.
      - Générer un mot de passe temporaire et envoyer un email au batelier avec ses informations de connexion.
      - Permettre au batelier de se connecter à l'espace batelier avec l'email et le mot de passe temporaire.
      - Afficher un message de succès après l'enregistrement.

15. **Assigner un capitaine interne ou indépendant**
    - **Description** : En tant que gestionnaire d'établissement, je veux assigner un batelier interne ou rechercher un capitaine indépendant pour une course.
    - **Critères d'acceptation** :
      - Afficher le batelier interne disponible avec son bateau.
      - Permettre de rechercher des capitaines indépendants à proximité.
      - Permettre d'assigner un capitaine interne ou indépendant à une course.
      - Afficher un message de confirmation après l'assignation.

16. **Créditer le compte**
    - **Description** : En tant que gestionnaire d'établissement, je veux créditer mon compte pour disposer de fonds pour les paiements.
    - **Critères d'acceptation** :
      - Proposer des montants prédéfinis (25 €, 50 €, 75 €, 100 €, 125 €, 150 €, 175 €, 200 €) ou un montant personnalisé.
      - Afficher un message de succès avec le nouveau solde après le crédit.

17. **Confirmer le paiement**
    - **Description** : En tant que gestionnaire d'établissement, je veux recevoir une confirmation après un paiement réussi.
    - **Critères d'acceptation** :
      - Afficher le montant crédité et les options d'utilisation (courses, cause écoresponsable).
      - Permettre de continuer vers une autre action.