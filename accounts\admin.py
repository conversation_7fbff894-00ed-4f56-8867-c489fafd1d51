from django.contrib import admin
from .models import User, C<PERSON>, Captain, Establishment
from .favorites import FavoriteLocation, FavoriteCaptain

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('id', 'email', 'display_type', 'is_active', 'email_verified', 'phone_number', 'date_joined')
    search_fields = ('email', 'phone_number')
    list_filter = ('type', 'is_active', 'email_verified')
    ordering = ('-date_joined',)
    
    def display_type(self, obj):
        """Affiche le type d'utilisateur avec distinction pour les bateliers"""
        if obj.type == 'CAPTAIN' and hasattr(obj, 'captain') and obj.captain.registered_by_establishment:
            return "BATELIER"
        elif obj.type == 'CAPTAIN':
            return "Capitaine"
        elif obj.type == 'ESTABLISHMENT':
            return "Établissement"
        elif obj.type == 'CLIENT':
            return "Client"
        else:
            return obj.get_type_display()
    
    display_type.short_description = "Type d'utilisateur"

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('pk', 'user', 'wallet_balance', 'date_of_birth', 'nationality', 'preferred_language')
    search_fields = ('user__email', 'nationality')
    list_filter = ('preferred_language',)

@admin.register(Captain)
class CaptainAdmin(admin.ModelAdmin):
    list_display = ('pk', 'user', 'user_email', 'captain_type', 'registered_by', 'experience', 'average_rating', 'total_trips', 'is_available', 'boat_types')
    search_fields = ('user__email', 'user__first_name', 'user__last_name')
    list_filter = ('registered_by_establishment', 'is_available', 'average_rating')
    
    def user_email(self, obj):
        return obj.user.email if obj.user else "-"
    user_email.short_description = "Email"
    
    def captain_type(self, obj):
        """Distingue clairement les bateliers des capitaines"""
        if obj.registered_by_establishment:
            return f"BATELIER ✓"
        return "Capitaine"
    captain_type.short_description = "Type"
    
    def registered_by(self, obj):
        if obj.registered_by_establishment:
            return f"{obj.registered_by_establishment.name}"
        return "Auto-inscription"
    registered_by.short_description = "Enregistré par"

    def boat_types(self, obj):
        types = obj.boats.values_list('boat_type', flat=True).distinct()
        type_list = [t if t else '-' for t in types]
        return ", ".join(type_list) if type_list else "-"
    boat_types.short_description = "Types de bateaux"

# Version unique et officielle de l'admin Establishment pour tout le projet Commodore
@admin.register(Establishment)
class EstablishmentAdmin(admin.ModelAdmin):
    list_display = (
        'pk', 'user', 'name', 'type', 'business_name', 'address', 'wallet_balance',
        'average_rating', 'user_date_joined'
    )
    search_fields = ('user__email', 'name', 'business_name', 'address')
    list_filter = ('type', 'business_type', 'average_rating')
    readonly_fields = ('user', 'wallet_balance', 'average_rating')

    def user_date_joined(self, obj):
        return obj.user.date_joined if obj.user else None
    user_date_joined.short_description = "Date d'inscription utilisateur"


@admin.register(FavoriteLocation)
class FavoriteLocationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'name', 'address', 'coordinates', 'created_at')
    search_fields = ('user__email', 'name', 'address')
    list_filter = ('created_at',)

@admin.register(FavoriteCaptain)
class FavoriteCaptainAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'captain', 'created_at')
    search_fields = ('user__email', 'captain__user__email')
    list_filter = ('created_at',)
