# 🔧 PAYLOADS COMPLETS POUR LES ENDPOINTS PATCH

## 📋 ENDPOINT PRINCIPAL: PATCH /api/profile/

**URL:** `/api/profile/`  
**Méthode:** `PATCH`  
**Authentification:** Token requis  
**Content-Type:** `application/json`

---

## 👤 PAYLOAD COMPLET POUR CLIENT

```json
{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "phone_number": "+***********",
  "client_profile": {
    "user": {
      "email": "<EMAIL>",
      "password": "NouveauMotDePasse123!",
      "password2": "NouveauMotDePasse123!",
      "first_name": "<PERSON>",
      "last_name": "<PERSON><PERSON>",
      "phone_number": "+***********",
      "type": "CLIENT",
      "is_active": true
    },
    "wallet_balance": "150.75",
    "date_of_birth": "1990-05-15",
    "nationality": "<PERSON>an<PERSON>",
    "preferred_language": "fr",
    "emergency_contact_name": "<PERSON>",
    "emergency_contact_phone": "+33687654321"
  }
}
```

### 📝 Champs modifiables pour CLIENT:

**Niveau racine:**
- `first_name` (string) - Prénom
- `last_name` (string) - Nom de famille  
- `phone_number` (string) - Numéro de téléphone

**Dans client_profile.user:**
- `email` (string) - Adresse email
- `password` (string) - Nouveau mot de passe
- `password2` (string) - Confirmation mot de passe
- `first_name` (string) - Prénom
- `last_name` (string) - Nom de famille
- `phone_number` (string) - Téléphone
- `type` (string) - Type d'utilisateur
- `is_active` (boolean) - Statut actif

**Dans client_profile:**
- `wallet_balance` (decimal) - Solde du portefeuille
- `date_of_birth` (date) - Date de naissance
- `nationality` (string) - Nationalité
- `preferred_language` (string) - Langue préférée (fr/en/es/it)
- `emergency_contact_name` (string) - Contact d'urgence
- `emergency_contact_phone` (string) - Téléphone d'urgence

---

## ⛵ PAYLOAD COMPLET POUR CAPITAINE

```json
{
  "first_name": "Pierre",
  "last_name": "Martin",
  "phone_number": "+***********",
  "captain_profile": {
    "user": {
      "email": "<EMAIL>",
      "password": "NouveauMotDePasse123!",
      "password2": "NouveauMotDePasse123!",
      "first_name": "Pierre",
      "last_name": "Martin",
      "phone_number": "+***********",
      "type": "CAPTAIN",
      "is_active": true
    },
    "experience": "10 ans d'expérience maritime professionnelle",
    "average_rating": "4.8",
    "total_trips": 245,
    "wallet_balance": "1250.50",
    "is_available": true,
    "current_location": "Port de Cannes",
    "license_number": "CAP123456",
    "license_expiry_date": "2025-12-31",
    "years_of_experience": 10,
    "certifications": {
      "permis_cotier": "Permis côtier",
      "permis_hauturier": "Permis hauturier",
      "certificat_securite": "Certificat de sécurité"
    },
    "specializations": {
      "navigation_cotiere": "Navigation côtière",
      "peche_mer": "Pêche en mer",
      "transport_passagers": "Transport de passagers"
    },
    "availability_status": "AVAILABLE",
    "boat_photos": {
      "main": "https://example.com/boat1.jpg",
      "interior": "https://example.com/boat1_interior.jpg",
      "exterior": "https://example.com/boat1_exterior.jpg"
    },
    "rate_per_km": "2.50",
    "rate_per_hour": "45.00",
    "boat": "boat_reference_123"
  }
}
```

### 📝 Champs modifiables pour CAPITAINE:

**Niveau racine:**
- `first_name` (string) - Prénom
- `last_name` (string) - Nom de famille
- `phone_number` (string) - Numéro de téléphone

**Dans captain_profile.user:**
- `email` (string) - Adresse email
- `password` (string) - Nouveau mot de passe
- `password2` (string) - Confirmation mot de passe
- `first_name` (string) - Prénom
- `last_name` (string) - Nom de famille
- `phone_number` (string) - Téléphone
- `type` (string) - Type d'utilisateur
- `is_active` (boolean) - Statut actif

**Dans captain_profile:**
- `experience` (string) - Description de l'expérience
- `average_rating` (decimal) - Note moyenne
- `total_trips` (integer) - Nombre total de courses
- `wallet_balance` (decimal) - Solde du portefeuille
- `is_available` (boolean) - Disponibilité
- `current_location` (string) - Localisation actuelle
- `license_number` (string) - Numéro de licence
- `license_expiry_date` (date) - Date d'expiration licence
- `years_of_experience` (integer) - Années d'expérience
- `certifications` (object) - Certifications (JSON)
- `specializations` (object) - Spécialisations (JSON)
- `availability_status` (string) - Statut (AVAILABLE/BUSY/OFFLINE)
- `boat_photos` (object) - Photos du bateau (JSON)
- `rate_per_km` (decimal) - Tarif par kilomètre
- `rate_per_hour` (decimal) - Tarif horaire
- `boat` (string) - Référence du bateau

---

## 🏢 PAYLOAD COMPLET POUR ÉTABLISSEMENT

```json
{
  "first_name": "Hôtel",
  "last_name": "Riviera",
  "phone_number": "+33493123456",
  "establishment_profile": {
    "user": {
      "email": "<EMAIL>",
      "password": "NouveauMotDePasse123!",
      "password2": "NouveauMotDePasse123!",
      "first_name": "Hôtel",
      "last_name": "Riviera",
      "phone_number": "+33493123456",
      "type": "ESTABLISHMENT",
      "is_active": true
    },
    "name": "Hôtel Riviera",
    "type": "HOTEL",
    "address": "123 Avenue de la Mer, 06400 Cannes",
    "description": "Hôtel de luxe avec vue sur mer et services nautiques",
    "main_photo": "https://example.com/hotel_main.jpg",
    "secondary_photos": {
      "facade": "https://example.com/hotel_facade.jpg",
      "lobby": "https://example.com/hotel_lobby.jpg",
      "restaurant": "https://example.com/hotel_restaurant.jpg"
    },
    "wallet_balance": "5000.00",
    "business_name": "Riviera Hospitality SAS",
    "business_type": "Hôtellerie",
    "registration_number": "RCS123456789",
    "tax_id": "FR12345678901",
    "opening_hours": {
      "monday": "24h/24",
      "tuesday": "24h/24",
      "wednesday": "24h/24",
      "thursday": "24h/24",
      "friday": "24h/24",
      "saturday": "24h/24",
      "sunday": "24h/24"
    },
    "services_offered": {
      "hebergement": "Hébergement",
      "restaurant": "Restaurant",
      "spa": "Spa",
      "navettes": "Navettes maritimes",
      "concierge": "Service de conciergerie"
    },
    "average_rating": "4.5",
    "location_coordinates": "43.5528,7.0174",
    "website": "https://hotel-riviera.com",
    "social_media": {
      "facebook": "https://facebook.com/hotel-riviera",
      "instagram": "https://instagram.com/hotel_riviera",
      "twitter": "https://twitter.com/hotel_riviera"
    }
  }
}
```

### 📝 Champs modifiables pour ÉTABLISSEMENT:

**Niveau racine:**
- `first_name` (string) - Prénom/Nom
- `last_name` (string) - Nom de famille/Entité
- `phone_number` (string) - Numéro de téléphone

**Dans establishment_profile.user:**
- `email` (string) - Adresse email
- `password` (string) - Nouveau mot de passe
- `password2` (string) - Confirmation mot de passe
- `first_name` (string) - Prénom
- `last_name` (string) - Nom de famille
- `phone_number` (string) - Téléphone
- `type` (string) - Type d'utilisateur
- `is_active` (boolean) - Statut actif

**Dans establishment_profile:**
- `name` (string) - Nom de l'établissement
- `type` (string) - Type (HOTEL/RESTAURANT/BAR/CLUB/MARINA/OTHER)
- `address` (string) - Adresse complète
- `description` (string) - Description
- `main_photo` (url) - Photo principale
- `secondary_photos` (object) - Photos secondaires (JSON)
- `wallet_balance` (decimal) - Solde du portefeuille
- `business_name` (string) - Nom commercial
- `business_type` (string) - Type d'entreprise
- `registration_number` (string) - Numéro d'enregistrement
- `tax_id` (string) - Numéro fiscal
- `opening_hours` (object) - Horaires d'ouverture (JSON)
- `services_offered` (object) - Services proposés (JSON)
- `average_rating` (decimal) - Note moyenne
- `location_coordinates` (string) - Coordonnées GPS
- `website` (url) - Site web
- `social_media` (object) - Réseaux sociaux (JSON)

---

## 🚤 ENDPOINT BATELIER: PATCH /api/boatman/profile/

**URL:** `/api/boatman/profile/`  
**Méthode:** `PATCH`  
**Authentification:** Token requis (type CAPTAIN)  
**Content-Type:** `application/json`

### 📝 PAYLOAD COMPLET POUR BATELIER

```json
{
  "first_name": "Jacques",
  "last_name": "Marin",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "experience": "8 ans d'expérience en navettes côtières et transport de passagers",
  "contact_preferences": {
    "email_notifications": true,
    "sms_notifications": false,
    "push_notifications": true
  }
}
```

### 📝 Champs modifiables pour BATELIER:

- `first_name` (string) - Prénom
- `last_name` (string) - Nom de famille
- `phone_number` (string) - Numéro de téléphone
- `email` (string) - Adresse email
- `experience` (string) - Description de l'expérience
- `contact_preferences` (object) - Préférences de contact
  - `email_notifications` (boolean) - Notifications email
  - `sms_notifications` (boolean) - Notifications SMS
  - `push_notifications` (boolean) - Notifications push

---

## ⚠️ LOGIQUE SPÉCIALE DE L'ENDPOINT /api/profile/

L'endpoint `/api/profile/` a une logique de traitement spéciale :

1. **Si `phone_number` présent** → Traitement séparé du téléphone uniquement
2. **Si `email` présent** → Traitement séparé de l'email uniquement  
3. **Si `new_password` présent** → Traitement séparé du mot de passe uniquement
4. **Sinon** → Traitement standard du profil complet

### 🔧 Exemples de requêtes spéciales:

**Mise à jour téléphone uniquement:**
```json
{
  "phone_number": "+***********"
}
```

**Changement de mot de passe uniquement:**
```json
{
  "new_password": "NouveauMotDePasse123!",
  "confirm_password": "NouveauMotDePasse123!"
}
```

**Mise à jour email uniquement:**
```json
{
  "email": "<EMAIL>"
}
```

---

## 🔐 AUTORISATION

- **Utilisateur** peut modifier uniquement son propre profil
- **Staff** peut modifier tous les profils
- **Validation** des données selon le type d'utilisateur
- **Token d'authentification** obligatoire dans l'en-tête

---

*Documentation générée automatiquement - Version 2.0.0*
