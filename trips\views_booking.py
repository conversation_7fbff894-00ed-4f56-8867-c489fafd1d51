"""
Module de gestion des réservations et choix de devis.

Ce module contient les vues pour permettre aux clients de choisir
un devis et aux capitaines d'accepter/rejeter les courses.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from .models import Trip, TripQuote, TripRequest
from .serializers import TripSerializer, TripQuoteSerializer
from accounts.models import Client, Captain
from notifications.models import Notification
from accounts.permissions import IsClient, IsCaptain


class QuoteChoiceView(APIView):
    """
    Endpoint pour qu'un client **ou un établissement** choisisse un devis et envoie la demande au capitaine.
    - Client : Courses simples / horaires
    - Établissement : Navettes gratuites option B (capitaine indépendant)
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, quote_id):
        """
        Permet au client de choisir un devis et d'envoyer la demande au capitaine.
        
        URL: POST /api/trips/quotes/{quote_id}/choose/
        
        Body: {
            "message": "Message optionnel pour le capitaine"
        }
        """
        
        try:
            # Identifier le contexte
            acting_client = getattr(request.user, 'client', None)
            acting_establishment = getattr(request.user, 'establishment', None)

            if not acting_client and not acting_establishment:
                return Response({'error': 'Accès réservé aux clients ou établissements'}, status=status.HTTP_403_FORBIDDEN)

            quote = get_object_or_404(TripQuote, id=quote_id)

            # Vérifier l'appartenance du devis
            if acting_client:
                if quote.trip_request.client != acting_client:
                    return Response({'error': 'Ce devis ne vous appartient pas'}, status=status.HTTP_403_FORBIDDEN)
            else:  # établissement
                # Correction robuste : gérer le cas ShuttleTripRequest (navette gratuite), où establishment est sur la sous-classe
                if hasattr(quote.trip_request, 'shuttletriprequest'):
                    trip_establishment = quote.trip_request.shuttletriprequest.establishment
                else:
                    trip_establishment = getattr(quote.trip_request, 'establishment', None)
                if trip_establishment != acting_establishment:
                    return Response({'error': 'Ce devis ne concerne pas votre établissement'}, status=status.HTTP_403_FORBIDDEN)
            
            # Vérifier que le devis est encore disponible
            if not quote.is_available:
                return Response({
                    'error': 'Ce devis n\'est plus disponible'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Vérifier que la demande n'a pas expiré
            if quote.trip_request.is_expired():
                return Response({
                    'error': 'Cette demande a expiré'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Déterminer le client final (dans le cas établissement le client est celui de la demande)
            final_client = quote.trip_request.client

            # Créer la course officielle
            trip = Trip.objects.create(
                client=final_client,
                establishment=acting_establishment if acting_establishment else None,
                captain=quote.captain,
                boat=quote.boat,
                
                # Détails du trajet
                start_location=quote.trip_request.departure_location.get('city_name', ''),
                end_location=quote.trip_request.arrival_location.get('city_name', ''),
                scheduled_start_time=timezone.now() + timezone.timedelta(hours=1),  # Par défaut dans 1h
                scheduled_end_time=timezone.now() + timezone.timedelta(hours=2),    # Par défaut dans 2h
                
                # Passagers
                passenger_count=quote.trip_request.passenger_count,
                passenger_names=[],
                special_requests=request.data.get('message', ''),
                
                # Prix
                base_price=quote.base_price,
                total_price=quote.base_price,
                
                # Statut initial
                status=Trip.Status.PENDING,
                payment_status='PENDING'
            )
            
            # Marquer les autres devis comme non disponibles
            TripQuote.objects.filter(
                trip_request=quote.trip_request
            ).exclude(id=quote_id).update(is_available=False)
            
            # Créer notification pour le capitaine
            Notification.objects.create(
                user=quote.captain.user,
                title='Nouvelle demande de course',
                message=(
                    f'Vous avez reçu une demande de course de {final_client.user.get_full_name()}.'
                    f"\nDépart: {trip.start_location}"
                    f"\nArrivée: {trip.end_location}"
                    f"\nPrix: {float(trip.total_price)}€"
                ),
                type='TRIP_REQUEST'
            )
            
            # Retourner les détails de la course créée
            serializer = TripSerializer(trip)
            
            return Response({
                'success': True,
                'message': 'Demande envoyée au capitaine',
                'trip': serializer.data,
                'next_step': 'Attendez la réponse du capitaine (délai: 10 minutes)'
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response({
                'error': f'Erreur lors du choix du devis: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TripAcceptView(APIView):
    """
    Endpoint pour que le capitaine accepte une course.
    """
    permission_classes = [IsAuthenticated, IsCaptain]

    def post(self, request, trip_id):
        """
        Permet au capitaine d'accepter une course.
        
        URL: POST /api/trips/{trip_id}/accept/
        
        Body: {
            "estimated_pickup_time": "2024-01-15T16:30:00Z",
            "captain_notes": "Message pour le client"
        }
        """
        
        try:
            # Vérifier que l'utilisateur est un capitaine
            if not hasattr(request.user, 'captain'):
                return Response({
                    'error': 'Seuls les capitaines peuvent accepter une course'
                }, status=status.HTTP_403_FORBIDDEN)
            
            captain = request.user.captain
            trip = get_object_or_404(Trip, id=trip_id)
            
            # Vérifier que la course appartient au capitaine
            if trip.captain != captain:
                return Response({
                    'error': 'Cette course ne vous est pas assignée'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Vérifier que la course est en attente
            if trip.status != Trip.Status.PENDING:
                return Response({
                    'error': f'Cette course ne peut plus être acceptée (statut: {trip.status})'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Mettre à jour la course
            trip.status = Trip.Status.ACCEPTED
            trip.captain_notes = request.data.get('captain_notes', '')
            
            # Mettre à jour l'heure de pickup si fournie
            estimated_pickup = request.data.get('estimated_pickup_time')
            if estimated_pickup:
                from django.utils.dateparse import parse_datetime
                pickup_time = parse_datetime(estimated_pickup)
                if pickup_time:
                    trip.scheduled_start_time = pickup_time
            
            trip.save()
            
            # Créer notification pour le client
            Notification.objects.create(
                user=trip.client.user,
                title='Course acceptée !',
                message=f'Le capitaine {captain.user.get_full_name()} a accepté votre course',
                type='TRIP_ACCEPTED'
            )
            
            # Retourner les détails mis à jour
            serializer = TripSerializer(trip)
            
            return Response({
                'success': True,
                'message': 'Course acceptée avec succès',
                'trip': serializer.data,
                'next_step': 'Le client peut maintenant procéder au paiement'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Erreur lors de l\'acceptation: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TripRejectView(APIView):
    """
    Endpoint pour que le capitaine refuse une course.
    """
    permission_classes = [IsAuthenticated, IsCaptain]

    def post(self, request, trip_id):
        """
        Permet au capitaine de refuser une course.
        
        URL: POST /api/trips/{trip_id}/reject/
        
        Body: {
            "reason": "Raison du refus"
        }
        """
        
        try:
            # Vérifier que l'utilisateur est un capitaine
            if not hasattr(request.user, 'captain'):
                return Response({
                    'error': 'Seuls les capitaines peuvent refuser une course'
                }, status=status.HTTP_403_FORBIDDEN)
            
            captain = request.user.captain
            trip = get_object_or_404(Trip, id=trip_id)
            
            # Vérifier que la course appartient au capitaine
            if trip.captain != captain:
                return Response({
                    'error': 'Cette course ne vous est pas assignée'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Vérifier que la course est en attente
            if trip.status != Trip.Status.PENDING:
                return Response({
                    'error': f'Cette course ne peut plus être refusée (statut: {trip.status})'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Mettre à jour la course
            trip.status = Trip.Status.REJECTED
            trip.cancellation_reason = request.data.get('reason', 'Refusée par le capitaine')
            trip.cancelled_by = request.user
            trip.save()
            
            # Créer notification pour le client
            Notification.objects.create(
                user=trip.client.user,
                title='Course refusée',
                message=f'Le capitaine {captain.user.get_full_name()} a refusé votre course',
                type='TRIP_REJECTED'
            )
            
            # Remettre les autres devis disponibles pour cette demande
            # Trouver la demande originale via le quote choisi
            original_quotes = TripQuote.objects.filter(
                captain=captain,
                trip_request__client=trip.client,
                created_at__date=trip.created_at.date()
            )
            
            if original_quotes.exists():
                trip_request = original_quotes.first().trip_request
                # Remettre tous les devis disponibles sauf celui du capitaine qui a refusé
                TripQuote.objects.filter(
                    trip_request=trip_request
                ).exclude(captain=captain).update(is_available=True)
            
            return Response({
                'success': True,
                'message': 'Course refusée',
                'trip_id': trip.id,
                'status': trip.status
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Erreur lors du refus: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CaptainPendingTripsView(APIView):
    """
    Endpoint pour voir les demandes de courses en attente pour un capitaine.
    """
    permission_classes = [IsAuthenticated, IsCaptain]

    def get(self, request):
        """
        Retourne les courses en attente d'acceptation pour le capitaine connecté.
        
        URL: GET /api/trips/pending/
        """
        
        try:
            # Vérifier que l'utilisateur est un capitaine
            if not hasattr(request.user, 'captain'):
                return Response({
                    'error': 'Seuls les capitaines peuvent voir les demandes'
                }, status=status.HTTP_403_FORBIDDEN)
            
            captain = request.user.captain
            
            # Récupérer les courses en attente pour ce capitaine
            pending_trips = Trip.objects.filter(
                captain=captain,
                status=Trip.Status.PENDING
            ).order_by('-created_at')
            
            # Sérialiser les données
            serializer = TripSerializer(pending_trips, many=True)
            
            return Response({
                'success': True,
                'count': pending_trips.count(),
                'pending_trips': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Erreur lors de la récupération: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
