import pytest
from unittest.mock import patch, MagicMock
from payments.stripe_utils import (
    create_payment_intent, create_checkout_session, create_connect_account,
    create_account_link, create_transfer, handle_webhook_event,
    create_customer, create_payment_method, attach_payment_method,
    detach_payment_method, list_payment_methods, create_refund
)

pytestmark = pytest.mark.unit

@pytest.fixture
def mock_stripe_payment_intent():
    """Fixture pour simuler un payment intent Stripe"""
    return MagicMock(
        id='pi_test123',
        client_secret='pi_test123_secret',
        amount=1000,
        currency='eur',
        status='requires_payment_method',
        metadata={}
    )

@pytest.fixture
def mock_stripe_checkout_session():
    """Fixture pour simuler une session checkout Stripe"""
    return MagicMock(
        id='cs_test123',
        url='https://checkout.stripe.com/test',
        payment_intent='pi_test123',
        amount_total=1000,
        currency='eur',
        status='open',
        metadata={}
    )

@pytest.fixture
def mock_stripe_account():
    """Fixture pour simuler un compte Stripe Connect"""
    return MagicMock(
        id='acct_test123',
        email='<EMAIL>',
        country='FR',
        type='express',
        capabilities={
            'card_payments': {'requested': True},
            'transfers': {'requested': True}
        }
    )

@pytest.fixture
def mock_stripe_account_link():
    """Fixture pour simuler un lien de compte Stripe"""
    return MagicMock(
        url='https://connect.stripe.com/test',
        created=**********,
        expires_at=**********
    )

@pytest.fixture
def mock_stripe_transfer():
    """Fixture pour simuler un transfert Stripe"""
    return MagicMock(
        id='tr_test123',
        amount=800,
        currency='eur',
        destination='acct_test123',
        metadata={}
    )

@pytest.fixture
def mock_stripe_customer():
    """Fixture pour simuler un client Stripe"""
    return MagicMock(
        id='cus_test123',
        email='<EMAIL>',
        name='Test User',
        metadata={}
    )

@pytest.fixture
def mock_stripe_payment_method():
    """Fixture pour simuler une méthode de paiement Stripe"""
    return MagicMock(
        id='pm_test123',
        type='card',
        card=MagicMock(
            brand='visa',
            last4='4242',
            exp_month=12,
            exp_year=2025
        )
    )

@pytest.fixture
def mock_stripe_refund():
    """Fixture pour simuler un remboursement Stripe"""
    return MagicMock(
        id='re_test123',
        amount=1000,
        currency='eur',
        status='succeeded',
        payment_intent='pi_test123',
        metadata={}
    )

class TestStripeUtils:
    """Tests pour les fonctions utilitaires Stripe"""

    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent(self, mock_create, mock_stripe_payment_intent):
        """Test de la création d'un payment intent"""
        mock_create.return_value = mock_stripe_payment_intent
        
        # Test avec les paramètres de base
        result = create_payment_intent(amount=1000)
        
        mock_create.assert_called_once()
        assert result.id == 'pi_test123'
        assert result.client_secret == 'pi_test123_secret'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_payment_intent(
            amount=1000,
            currency='eur',
            payment_method_types=['card'],
            customer='cus_test123',
            setup_future_usage='off_session',
            metadata={'test': 'value'},
            description='Test payment'
        )
        
        mock_create.assert_called_once()
        assert result.id == 'pi_test123'
    
    @patch('stripe.checkout.Session.create')
    def test_create_checkout_session(self, mock_create, mock_stripe_checkout_session):
        """Test de la création d'une session checkout"""
        mock_create.return_value = mock_stripe_checkout_session
        
        # Test avec les paramètres de base
        result = create_checkout_session(amount=1000)
        
        mock_create.assert_called_once()
        assert result.id == 'cs_test123'
        assert result.url == 'https://checkout.stripe.com/test'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_checkout_session(
            amount=1000,
            currency='eur',
            product_name='Test Product',
            product_description='Test Description',
            customer='cus_test123',
            payment_method_types=['card'],
            success_url='https://example.com/success',
            cancel_url='https://example.com/cancel',
            metadata={'test': 'value'},
            locale='fr'
        )
        
        mock_create.assert_called_once()
        assert result.id == 'cs_test123'
    
    @patch('stripe.Account.create')
    def test_create_connect_account(self, mock_create, mock_stripe_account):
        """Test de la création d'un compte Connect"""
        mock_create.return_value = mock_stripe_account
        
        # Test avec les paramètres de base
        result = create_connect_account(email='<EMAIL>')
        
        mock_create.assert_called_once()
        assert result.id == 'acct_test123'
        assert result.email == '<EMAIL>'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_connect_account(
            email='<EMAIL>',
            country='FR',
            business_type='individual',
            business_profile={
                'name': 'Test Business',
                'url': 'https://example.com'
            }
        )
        
        mock_create.assert_called_once()
        assert result.id == 'acct_test123'
    
    @patch('stripe.AccountLink.create')
    def test_create_account_link(self, mock_create, mock_stripe_account_link):
        """Test de la création d'un lien de compte"""
        mock_create.return_value = mock_stripe_account_link
        
        # Test avec les paramètres de base
        result = create_account_link(
            account_id='acct_test123',
            refresh_url='https://example.com/refresh',
            return_url='https://example.com/return'
        )
        
        mock_create.assert_called_once()
        assert result.url == 'https://connect.stripe.com/test'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_account_link(
            account_id='acct_test123',
            refresh_url='https://example.com/refresh',
            return_url='https://example.com/return',
            type='account_update'
        )
        
        mock_create.assert_called_once()
        assert result.url == 'https://connect.stripe.com/test'
    
    @patch('stripe.Transfer.create')
    def test_create_transfer(self, mock_create, mock_stripe_transfer):
        """Test de la création d'un transfert"""
        mock_create.return_value = mock_stripe_transfer
        
        # Test avec les paramètres de base
        result = create_transfer(
            amount=1000,
            destination='acct_test123'
        )
        
        mock_create.assert_called_once()
        assert result.id == 'tr_test123'
        assert result.destination == 'acct_test123'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_transfer(
            amount=1000,
            destination='acct_test123',
            currency='eur',
            source_transaction='pi_test123',
            transfer_group='group_test',
            metadata={'test': 'value'},
            description='Test transfer'
        )
        
        mock_create.assert_called_once()
        assert result.id == 'tr_test123'
    
    @patch('stripe.Customer.create')
    def test_create_customer(self, mock_create, mock_stripe_customer):
        """Test de la création d'un client"""
        mock_create.return_value = mock_stripe_customer
        
        # Test avec les paramètres de base
        result = create_customer(email='<EMAIL>')
        
        mock_create.assert_called_once()
        assert result.id == 'cus_test123'
        assert result.email == '<EMAIL>'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_customer(
            email='<EMAIL>',
            name='Test User',
            phone='+33612345678',
            metadata={'test': 'value'},
            description='Test customer'
        )
        
        mock_create.assert_called_once()
        assert result.id == 'cus_test123'
    
    @patch('stripe.PaymentMethod.create')
    def test_create_payment_method(self, mock_create, mock_stripe_payment_method):
        """Test de la création d'une méthode de paiement"""
        mock_create.return_value = mock_stripe_payment_method
        
        # Test avec les paramètres de base
        result = create_payment_method(type='card')
        
        mock_create.assert_called_once()
        assert result.id == 'pm_test123'
        assert result.type == 'card'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_payment_method(
            type='card',
            card={
                'number': '****************',
                'exp_month': 12,
                'exp_year': 2025,
                'cvc': '123'
            },
            billing_details={
                'name': 'Test User',
                'email': '<EMAIL>'
            }
        )
        
        mock_create.assert_called_once()
        assert result.id == 'pm_test123'
    
    @patch('stripe.PaymentMethod.attach')
    def test_attach_payment_method(self, mock_attach, mock_stripe_payment_method):
        """Test de l'attachement d'une méthode de paiement"""
        mock_attach.return_value = mock_stripe_payment_method
        
        result = attach_payment_method(
            payment_method_id='pm_test123',
            customer_id='cus_test123'
        )
        
        mock_attach.assert_called_once_with(
            'pm_test123',
            customer='cus_test123'
        )
        assert result.id == 'pm_test123'
    
    @patch('stripe.PaymentMethod.detach')
    def test_detach_payment_method(self, mock_detach, mock_stripe_payment_method):
        """Test du détachement d'une méthode de paiement"""
        mock_detach.return_value = mock_stripe_payment_method
        
        result = detach_payment_method(payment_method_id='pm_test123')
        
        mock_detach.assert_called_once_with('pm_test123')
        assert result.id == 'pm_test123'
    
    @patch('stripe.PaymentMethod.list')
    def test_list_payment_methods(self, mock_list, mock_stripe_payment_method):
        """Test de la liste des méthodes de paiement"""
        mock_list.return_value = MagicMock(
            data=[mock_stripe_payment_method]
        )
        
        result = list_payment_methods(customer_id='cus_test123')
        
        mock_list.assert_called_once_with(
            customer='cus_test123',
            type='card'
        )
        assert result.data[0].id == 'pm_test123'
    
    @patch('stripe.Refund.create')
    def test_create_refund(self, mock_create, mock_stripe_refund):
        """Test de la création d'un remboursement"""
        mock_create.return_value = mock_stripe_refund
        
        # Test avec payment_intent_id
        result = create_refund(payment_intent_id='pi_test123')
        
        mock_create.assert_called_once()
        assert result.id == 're_test123'
        assert result.payment_intent == 'pi_test123'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec charge_id
        result = create_refund(charge_id='ch_test123')
        
        mock_create.assert_called_once()
        assert result.id == 're_test123'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_refund(
            payment_intent_id='pi_test123',
            amount=500,
            reason='requested_by_customer',
            metadata={'test': 'value'}
        )
        
        mock_create.assert_called_once()
        assert result.id == 're_test123'
    
    @patch('stripe.Webhook.construct_event')
    def test_handle_webhook_event(self, mock_construct_event):
        """Test de la gestion des événements webhook"""
        mock_event = MagicMock(
            id='evt_test123',
            type='payment_intent.succeeded',
            data=MagicMock(
                object=MagicMock(
                    id='pi_test123'
                )
            )
        )
        mock_construct_event.return_value = mock_event
        
        result = handle_webhook_event(
            payload=b'{"id": "evt_test123"}',
            sig_header='t=123,v1=abc'
        )
        
        mock_construct_event.assert_called_once()
        assert result.id == 'evt_test123'
        assert result.type == 'payment_intent.succeeded'
