# Documentation du Service RAG Commodore avec Meta-Llama-3

## Vue d'ensemble

Le service RAG (Retrieval Augmented Generation) de Commodore utilise Meta-Llama-3, un modèle de langage avancé fourni par DeepInfra, pour générer des réponses contextuelles aux questions des utilisateurs. Le système enrichit les réponses du modèle avec des informations pertinentes extraites de la base de connaissances de Commodore.

## Composants principaux

1. **Meta-Llama-3 via DeepInfra** : Modèle de langage principal pour générer des réponses
2. **HuggingFace Embeddings** : Génération d'embeddings vectoriels pour les documents
3. **FAISS Vectorstore** : Stockage et recherche efficace des chunks de documents
4. **Système de chunking** : Découpage intelligent des documents pour optimiser la récupération

## Configuration

Le service utilise les variables d'environnement suivantes :

```
DEEPINFRA_API_KEY=votre_clé_api_deepinfra
LLM_MODEL=meta-llama/Meta-Llama-3-8B-Instruct
```

## Formats de Prompt

Le système utilise un format de prompt spécifique pour Meta-Llama-3 :

```
<|im_start|>system
[Instructions système et contexte]
<|im_end|>
<|im_start|>user
[Question de l'utilisateur]
<|im_end|>
<|im_start|>assistant
```

## Fonctionnalités

- **Chunking intelligent** : Découpage des documents en morceaux significatifs avec chevauchement
- **Recherche sémantique** : Recherche basée sur la similarité vectorielle des embeddings
- **Recherche de secours** : Mécanisme alternatif quand la recherche principale ne donne pas de résultats
- **Détection d'intention** : Analyse des questions pour mieux adapter les réponses
- **Post-traitement** : Amélioration automatique de la qualité des réponses
- **Cache intelligent** : Stockage des réponses fréquentes pour une performance optimale

## Utilisation

```python
# Exemple d'utilisation
from rag.rag_service import rag_service

# Générer une réponse
response = rag_service.generate_response(
    session=chat_session,
    user_message="Comment réserver un bateau-taxi ?",
    user_profile="Client"
)

# Afficher la réponse
print(response)
```

## Profils utilisateurs supportés

- **Client** : Utilisateurs recherchant des informations sur les réservations, paiements, etc.
- **Établissement** : Partenaires commerciaux avec des besoins spécifiques
- **Capitaine** : Conducteurs de bateaux-taxis avec des questions techniques

## Maintenance

Pour mettre à jour la base de connaissances :

1. Ajouter un nouveau document à la base de données
2. Utiliser `rag_service.process_document(document)` pour traiter le document
3. Les embeddings seront automatiquement générés et indexés

## Performance et limitations

- Le modèle Meta-Llama-3-8B-Instruct offre un bon équilibre entre qualité des réponses et vitesse
- Les réponses sont générées en français par défaut
- Temps de réponse typique : 1-3 secondes selon la complexité de la question
