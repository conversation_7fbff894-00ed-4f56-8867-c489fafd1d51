# Documentation des Endpoints Payments



---

## 1. <PERSON><PERSON> (Portefeuille)

### 1.1. GET `/api/payments/wallet/`
**But** : R<PERSON><PERSON><PERSON>rer les informations du portefeuille de l’utilisateur connecté.

- **Headers** : `Authorization: Bearer <token>`
- **Réponse (200)** :
```json
{
  "user": 12,
  "balance": "984.50",
  "currency": "eur"
}
```

---

### 1.2. POST `/api/payments/wallet/recharge/`
**But** : Recharger le portefeuille via Stripe.

- **Headers** : `Authorization: Bearer <token>`
- **Body** :
```json
{
  "amount": 100,
  "payment_method_id": "pm_xxx"
}
```
- **Réponse (200)** :
```json
{
  "id": "pi_xxx",
  "status": "succeeded",
  "amount": 100,
  "balance": "1084.50"
}
```
- **Pré-requis** : Avoir un `payment_method_id` Stripe valide.

---

# Endpoints Paiement – Commodore

## Paiement d’une course individuelle
- **POST /api/payments/rides/{ride_id}/pay/**
- Body :
```json
{
  "payment_method_id": "pm_xxx",
  "use_wallet": false
}
```

## Paiement d’une navette
- **POST /api/payments/shuttles/{shuttle_id}/pay/**
- Body :
```json
{
  "payment_method_id": "pm_xxx",
  "seats": 2,
  "passenger_ids": ["p_1", "p_2"],
  "passenger_names": ["John Doe", "Jane Doe"],
  "special_requests": "Besoin d'assistance pour les bagages"
}
```

## Paiement d’un service de maintenance
- **POST /api/payments/maintenance/{maintenance_id}/pay/**
- Body :
```json
{
  "payment_method_id": "pm_xxx"
}
```

## Paiement d’une promotion
- **POST /api/payments/promotions/**
- Body :
```json
{
  "payment_method_id": "pm_xxx",
  "promotion_type": "FEATURED_LISTING",
  "duration_days": 30,
  "target_type": "CAPTAIN",
  "target_id": 5
}
```

## Paiement partagé d’une course
- **POST /api/payments/rides/{ride_id}/shared_pay/**
- Body :
```json
{
  "payment_method_id": "pm_xxx",
  "amount": 50
}
```

## Paiement vers le wallet
- **POST /api/payments/wallet/topup/**
- Body :
```json
{
  "payment_method_id": "pm_xxx",
  "amount": 100
}
```

---

> Tous les endpoints nécessitent un header :
> `Authorization: Bearer <token>`

> Pour les réponses, voir les exemples dans les tests cURL du fichier scripts/curl_examples.sh


---

### 1.3. POST `/api/payments/wallet/add_credits/` (alias recharge)
**But** : Même usage que `/wallet/recharge/`.

---

### 1.4. POST `/api/payments/admin/wallet/add-funds/`
**But** : Ajouter des fonds à un portefeuille (admin uniquement).

- **Headers** : `Authorization: Bearer <admin_token>`
- **Body** :
```json
{
  "user_id": 42,
  "amount": 500
}
```
- **Réponse (200)** :
```json
{
  "message": "Fonds ajoutés avec succès.",
  "balance": "1500.00"
}
```

---

## 2. Paiement de courses (Trips)

### 2.1. POST `/api/payments/trips/<trip_id>/pay/`
**But** : Payer un trip individuel.

- **Headers** : `Authorization: Bearer <token>`
- **Body** :
```json
{
  "payment_method_id": "pm_xxx"
}
```
- **Réponse (200)** :
```json
{
  "id": "pi_xxx",
  "amount": 150.00,
  "currency": "eur",
  "status": "succeeded",
  "trip": {
    "id": 1,
    "payment_status": "PAID"
  }
}
```
- **Pré-requis** : Le trip doit être assigné à l’utilisateur.

---

### 2.2. POST `/api/payments/trips/<trip_id>/shared_pay/`
**But** : Payer une invitation de paiement partagé pour un trip.

- **Headers** : `Authorization: Bearer <token>`
- **Body** :
```json
{
  "invitation_token": "abc123",
  "payment_method_id": "pm_xxx"
}
```
- **Réponse (200)** :
```json
{
  "id": "pi_xxx",
  "amount": 50.00,
  "status": "succeeded",
  "ride": {
    "id": 1,
    "payment_status": "PARTIALLY_PAID"
  }
}
```
- **Pré-requis** :  
  - **Avoir un token d’invitation** :  
    - Obtenu via la création d’invitation (voir endpoint suivant).
    - Une invitation ne peut être utilisée qu’une fois.

---

### 2.3. POST `/api/trips/<trip_id>/shared_invite/` (à adapter selon ton routage)
**But** : Créer une invitation de paiement partagé.

- **Headers** : `Authorization: Bearer <token>`
- **Body** :
```json
{
  "invitee_email": "<EMAIL>",
  "amount": 50.00,
  "message": "Partageons la course !"
}
```
- **Réponse (201)** :
```json
{
  "id": 11,
  "trip": 1,
  "inviter": 12,
  "invitee_email": "<EMAIL>",
  "amount": "50.00",
  "token": "abc123",
  "status": "PENDING",
  "expires_at": "2025-05-29T12:00:00Z"
}
```
- **Si une invitation existe déjà** :
```json
{
  "message": "Une invitation active existe déjà pour ce trajet et cet invité.",
  "invitation": { ... }
}
```
- **À savoir** :  
  - Le token d’invitation (`token`) est à utiliser pour le paiement partagé.
  - Une invitation n’est créée que si aucune invitation active n’existe déjà pour ce trip/email.

---

## 3. Paiement navette

### 3.1. POST `/api/payments/shuttles/<shuttle_id>/pay/`
**But** : Payer une réservation de navette.

- **Headers** : `Authorization: Bearer <token>`
- **Body** :
```json
{
  "payment_method_id": "pm_xxx",
  "seats": 2
}
```
- **Réponse (200)** :
```json
{
  "id": "pi_xxx",
  "amount": 100.00,
  "currency": "eur",
  "status": "succeeded",
  "shuttle": {
    "id": 5,
    "route_name": "Port A - Port B"
  }
}
```
- **Pré-requis** :  
  - Le shuttle doit être disponible.
  - L’utilisateur doit pouvoir réserver.

---

## 4. Paiement maintenance

### 4.1. POST `/api/payments/maintenance/<maintenance_id>/pay/`
**But** : Payer une opération de maintenance sur un bateau.

- **Headers** : `Authorization: Bearer <token>`
- **Body** :
```json
{
  "payment_method_id": "pm_xxx"
}
```
- **Réponse (200)** :
```json
{
  "id": "pi_xxx",
  "amount": 250.00,
  "currency": "eur",
  "status": "succeeded",
  "maintenance": {
    "id": 3,
    "boat_id": 7,
    "boat_name": "Le Neptune",
    "maintenance_type": "REPAIR",
    "description": "Changement moteur",
    "performed_at": "2025-05-20T10:00:00Z",
    "status": "COMPLETED"
  }
}
```
- **Pré-requis** :  
  - L’utilisateur doit être autorisé à payer la maintenance (propriétaire, capitaine, ou établissement lié au bateau).

---

## 5. Paiement promotion

### 5.1. POST `/api/payments/promotions/`
**But** : Payer pour promouvoir un service.

- **Headers** : `Authorization: Bearer <token>`
- **Body** :
```json
{
  "promotion_type": "FEATURED_LISTING",
  "duration_days": 30,
  "payment_method_id": "pm_xxx"
}
```
- **Réponse (200)** :
```json
{
  "id": "pi_xxx",
  "amount": 99.00,
  "currency": "eur",
  "status": "succeeded",
  "promotion": {
    "type": "FEATURED_LISTING",
    "start_date": "2025-05-26T10:00:00Z",
    "end_date": "2025-06-25T10:00:00Z"
  }
}
```

---

## 6. Transactions

### 6.1. GET `/api/payments/transactions/`
**But** : Lister les transactions de l’utilisateur.

- **Headers** : `Authorization: Bearer <token>`
- **Réponse (200)** :
```json
[
  {
    "id": "pi_xxx",
    "amount": 150.00,
    "type": "TRIP_PAYMENT",
    "status": "succeeded",
    "created_at": "2025-05-26T10:00:00Z"
  },
  ...
]
```

---

### 6.2. GET `/api/payments/transactions/<id>/`
**But** : Détail d’une transaction.

- **Headers** : `Authorization: Bearer <token>`
- **Réponse (200)** :
```json
{
  "id": "pi_xxx",
  "amount": 150.00,
  "type": "TRIP_PAYMENT",
  "status": "succeeded",
  "details": { ... }
}
```

---

### 6.3. POST `/api/payments/transactions/<id>/refund/`
**But** : Demander le remboursement d’une transaction.

- **Headers** : `Authorization: Bearer <token>`
- **Body** :
```json
{
  "reason": "Annulation du service"
}
```
- **Réponse (200)** :
```json
{
  "id": "re_xxx",
  "amount": 150.00,
  "status": "refunded"
}
```
- **Pré-requis** :  
  - La transaction doit être remboursable.

---

## 7. Offsets carbone

### 7.1. POST `/api/payments/carbon-offset/`
**But** : Compenser l’empreinte carbone d’un trajet ou d’un service.

- **Headers** : `Authorization: Bearer <token>`
- **Body** :
```json
{
  "amount": 10.00,
  "target_type": "trip",
  "target_id": 1
}
```
- **Réponse (200)** :
```json
{
  "id": "co_xxx",
  "amount": 10.00,
  "status": "succeeded"
}
```

---

## 8. Webhooks Stripe

### 8.1. POST `/api/payments/webhook/` ou `/api/payments/webhooks/stripe/`
**But** : Point d’entrée pour les notifications Stripe (pour usage Stripe uniquement).

---

# 📌 Notes importantes pour les tests

- **Token d’invitation** :  
  - À obtenir via la création d’invitation (voir 2.3).
  - Une invitation ne peut être utilisée qu’une fois : il faut en créer une nouvelle ou utiliser un autre trip/email pour tester plusieurs paiements partagés.
- **payment_method_id** :  
  - À générer via Stripe côté frontend (ex : Stripe Elements ou Checkout).
- **Authorization** :  
  - Tous les endpoints (sauf webhooks) nécessitent un token JWT valide dans le header.

GET /api/payments/methods/
Headers:
  Authorization: Bearer {token}

Réponse 200 OK:
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "pm_1234567890",
      "type": "card",
      "card": {
        "brand": "visa",
        "last4": "4242",
        "exp_month": 12,
        "exp_year": 2025
      },
      "is_default": true
    }
  ]
}

2. Ajout d'une méthode de paiement
--------------------------------
POST /api/payments/methods/
Headers:
  Authorization: Bearer {token}
  Content-Type: application/json

Body:
{
  "payment_method_id": "pm_1234567890"
}

Réponse 201 Created:
{
  "id": "pm_1234567890",
  "type": "card",
  "card": {
    "brand": "visa",
    "last4": "4242",
    "exp_month": 12,
    "exp_year": 2025
  },
  "is_default": false
}

3. Suppression d'une méthode de paiement
-------------------------------------
DELETE /api/payments/methods/{id}/
Headers:
  Authorization: Bearer {token}

Réponse 204 No Content

4. Définir une méthode de paiement par défaut
------------------------------------------
POST /api/payments/methods/{id}/set_default/
Headers:
  Authorization: Bearer {token}

Réponse 200 OK:
{
  "id": "pm_1234567890",
  "is_default": true
}

5. Paiement d'un trajet
---------------------
POST /api/payments/rides/{ride_id}/pay/
Headers:
  Authorization: Bearer {token}
  Content-Type: application/json

Body:
{
  "payment_method_id": "pm_1234567890"
}

Réponse 200 OK:
{
  "id": "pi_1234567890",
  "amount": 50.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T13:45:30Z",
  "payment_method": {
    "id": "pm_1234567890",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242",
      "exp_month": 12,
      "exp_year": 2025
    }
  },
  "receipt_url": "https://api.commodore.com/api/payments/receipts/pi_1234567890/",
  "ride": {
    "id": 1,
    "status": "PAID",
    "start_location": "Port de Saint-Tropez",
    "end_location": "Plage de Pampelonne",
    "scheduled_start_time": "2025-06-15T14:00:00Z",
    "total_price": 50.0
  },
  "wallet": {
    "previous_balance": 100.0,
    "current_balance": 50.0
  }
}

6. Liste des transactions
----------------------
GET /api/payments/transactions/
Headers:
  Authorization: Bearer {token}

Paramètres de requête :
  - status: État du paiement (succeeded, pending, failed)
  - start_date: Date de début
  - end_date: Date de fin

Réponse 200 OK:
{
  "count": 10,
  "next": "http://api.example.com/payments/transactions/?page=2",
  "previous": null,
  "results": [
    {
      "id": "pi_1234567890",
      "amount": 50.0,
      "currency": "eur",
      "status": "succeeded",
      "created_at": "2025-05-24T00:00:00Z",
      "ride": {
        "id": 1,
        "start_location": "Vieux Port",
        "end_location": "Beach Club"
      }
    }
  ]
}

7. Détails d'une transaction
-------------------------
GET /api/payments/transactions/{id}/
Headers:
  Authorization: Bearer {token}

Réponse 200 OK:
{
  "id": "pi_1234567890",
  "amount": 50.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-24T00:00:00Z",
  "payment_method": {
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242"
    }
  },
  "ride": {
    "id": 1,
    "start_location": "Vieux Port",
    "end_location": "Beach Club",
    "date": "2025-05-24T00:00:00Z"
  }
}

8. Remboursement d'une transaction
-------------------------------
POST /api/payments/transactions/{id}/refund/
Headers:
  Authorization: Bearer {token}
  Content-Type: application/json

Body:
{
  "amount": 50.0,
  "reason": "Annulation du trajet"
}

Réponse 200 OK:
{
  "id": "re_1234567890",
  "amount": 50.0,
  "status": "succeeded",
  "created_at": "2025-05-24T00:00:00Z"
}

9. Portefeuille de crédits
------------------------
GET /api/payments/wallet/
Headers:
  Authorization: Bearer {token}

Réponse 200 OK:
{
  "balance": 100.0,
  "currency": "eur",
  "transactions": [
    {
      "id": 1,
      "type": "credit",
      "amount": 50.0,
      "description": "Remboursement trajet #123",
      "created_at": "2025-05-24T00:00:00Z"
    }
  ]
}

10. Ajout de crédits au portefeuille
---------------------------------
POST /api/payments/wallet/add_credits/
Headers:
  Authorization: Bearer {token}
  Content-Type: application/json

Body:
{
  "amount": 50.0,
  "payment_method_id": "pm_1234567890"
}

Réponse 200 OK:
{
  "transaction_id": "pi_1234567890",
  "new_balance": 150.0
}

11. Paiement de réservation de navette
------------------------------------
POST /api/payments/shuttles/{shuttle_id}/pay/
Headers:
  Authorization: Bearer {token}
  Content-Type: application/json

Body:
{
  "payment_method_id": "pm_1234567890",
  "seats": 2,
  "passenger_ids": ["p_1", "p_2"]
}

Réponse 200 OK:
{
  "id": "pi_1234567890",
  "amount": 75.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:00:30Z",
  "payment_method": {
    "id": "pm_1234567890",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242",
      "exp_month": 12,
      "exp_year": 2025
    }
  },
  "receipt_url": "https://api.commodore.com/api/payments/receipts/pi_1234567890/",
  "shuttle": {
    "id": 5,
    "name": "Saint-Tropez - Pampelonne Express",
    "departure": "Port de Saint-Tropez",
    "arrival": "Plage de Pampelonne",
    "departure_time": "2025-06-01T10:00:00Z",
    "price_per_seat": 37.5,
    "total_seats": 12,
    "available_seats": 8
  },
  "seats": 2,
  "seat_numbers": ["A3", "A4"],
  "wallet": {
    "previous_balance": 100.0,
    "current_balance": 25.0
  }
}

12. Paiement partagé d'une course
---------------------------------
POST /api/payments/rides/{ride_id}/shared_pay/
Headers:
  Authorization: Bearer {token}
  Content-Type: application/json

Body:
{
  "payment_method_id": "pm_1234567890",
  "amount": 25.0,
  "invitation_token": "inv_abcdefg"
}

Réponse 200 OK:
{
  "id": "pi_1234567890",
  "amount": 25.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:05:45Z",
  "ride": {
    "id": 1,
    "total_amount": 100.0,
    "amount_paid": 75.0,
    "amount_remaining": 25.0,
    "payment_status": "PARTIALLY_PAID",
    "participants": [
      {
        "user_id": 8,
        "name": "Marie Martin",
        "amount_paid": 50.0,
        "payment_status": "PAID"
      },
      {
        "user_id": 12,
        "name": "Pierre Dupont",
        "amount_paid": 25.0,
        "payment_status": "PAID"
      },
      {
        "invitation_email": "<EMAIL>",
        "amount_paid": 0.0,
        "payment_status": "PENDING",
        "invitation_status": "SENT"
      }
    ]
  }
}

13. Paiement de services de maintenance
-------------------------------------
POST /api/payments/maintenance/{maintenance_id}/pay/
Headers:
  Authorization: Bearer {token}
  Content-Type: application/json

Body:
{
  "payment_method_id": "pm_1234567890"
}

Réponse 200 OK:
{
  "id": "pi_1234567890",
  "amount": 250.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:10:15Z",
  "payment_method": {
    "id": "pm_1234567890",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242",
      "exp_month": 12,
      "exp_year": 2025
    }
  },
  "maintenance": {
    "id": 3,
    "boat_id": 1,
    "boat_name": "Blue Wave",
    "type": "ROUTINE",
    "description": "Entretien moteur et coque",
    "date": "2025-05-25T09:00:00Z",
    "status": "COMPLETED"
  },
  "invoice_url": "https://api.commodore.com/api/payments/invoices/pi_1234567890/"
}

14. Paiement pour la promotion de services
----------------------------------------
POST /api/payments/promotions/
Headers:
  Authorization: Bearer {token}
  Content-Type: application/json

Body:
{
  "payment_method_id": "pm_1234567890",
  "promotion_type": "FEATURED_LISTING",
  "duration_days": 30,
  "target_type": "CAPTAIN",
  "target_id": 5
}

Réponse 200 OK:
{
  "id": "pi_1234567890",
  "amount": 99.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:15:30Z",
  "promotion": {
    "id": 2,
    "type": "FEATURED_LISTING",
    "start_date": "2025-05-25T14:15:30Z",
    "end_date": "2025-06-24T14:15:30Z",
    "status": "ACTIVE",
    "target": {
      "type": "CAPTAIN",
      "id": 5,
      "name": "Jean Dupont"
    }
  },
  "features": [
    "Mise en avant dans les résultats de recherche",
    "Badge 'Recommandé'",
    "Visibilité dans la section 'Top capitaines'"
  ]
}

Fonctionnalités implémentées
====================================

1. ✅ **Gestion des méthodes de paiement** : Ajout, suppression et mise à jour des méthodes de paiement des utilisateurs.

2. ✅ **Transactions et portefeuille** : Gestion du portefeuille utilisateur avec ajout et retrait de crédits.

3. ✅ **Historique des transactions** : Suivi et consultation de l'historique des transactions pour les utilisateurs.

4. ✅ **Paiements de courses** : Système de paiement des courses et d'application des frais de service.

5. ✅ **Facturation** : Génération et consultation des factures associées aux transactions.

Fonctionnalités à implémenter
====================================

1. **Remboursements automatisés** : Mécanisme de remboursement automatisé en cas d'annulation de course.

2. **Paiements échelonnés** : Permettre le paiement de grosses sommes en plusieurs versements.

3. **Système de récompenses** : Implémenter un système de points de fidélité ou de réductions pour les utilisateurs réguliers.

4. **Gestion des devises multiples** : Support pour différentes devises et conversion automatique.

5. **Intégration avec d'autres  méthodes de paiement** : Support pour Apple Pay, Google Pay, et autres méthodes de paiement populaires.
