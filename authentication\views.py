import random
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.contrib.auth import get_user_model
from django.conf import settings
from django.utils import timezone
from django.utils.html import strip_tags
from django.template.loader import render_to_string
from django.core.mail import send_mail
from rest_framework_simplejwt.tokens import RefreshToken
from dj_rest_auth.registration.views import SocialLoginView
# from allauth.socialaccount.providers.facebook.views import FacebookOAuth2Adapter
# from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
# from allauth.socialaccount.providers.apple.views import AppleOAuth2Adapter
# from allauth.socialaccount.providers.oauth2.client import OAuth2Client
from .models import VerificationCode, UserSocialAccount
from .serializers import (
    RegisterSerializer,
    VerificationCodeSerializer, SocialLoginSerializer,
    PasswordResetRequestSerializer, PasswordResetVerifySerializer,
    ResendVerificationCodeSerializer
)
from drf_spectacular.utils import extend_schema, OpenApiParameter

from .social_auth_utils import validate_social_token, SocialAuthError
from accounts.models import Client, Captain, Establishment

User = get_user_model()

class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response({"success": "Déconnecté"}, status=status.HTTP_205_RESET_CONTENT)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response({"success": "Déconnecté"}, status=status.HTTP_205_RESET_CONTENT)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@extend_schema(tags=["Authentication"], request=RegisterSerializer, responses=None)
class RegisterView(APIView):
    permission_classes = [AllowAny]

    @extend_schema(tags=["Authentication"], request=RegisterSerializer, responses=None)
    def post(self, request):
        # Vérifier que le type d'utilisateur est présent
        if 'user_type' not in request.data or not request.data.get('user_type'):
            return Response(
                {'error': 'Le type d\'utilisateur est obligatoire', 'field': 'user_type'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        user_type = request.data.get('user_type', '').upper()
        if user_type not in User.Types.values:
            return Response(
                {'error': 'Type d\'utilisateur invalide', 'field': 'user_type', 'valid_types': User.Types.values},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()

            # Générer un code de vérification
            code = ''.join(random.choices('0123456789', k=settings.VERIFICATION_CODE_LENGTH))
            verification = VerificationCode.objects.create(
                user=user,
                code=code,
                action=VerificationCode.Actions.EMAIL_VERIFICATION
            )

            # Envoyer l'email de vérification
            context = {
                'user': user,
                'code': code,
                'action': 'vérifier votre compte',
                'expiry_minutes': settings.VERIFICATION_CODE_EXPIRY_MINUTES
            }
            html_message = render_to_string('email/verification_code.html', context)
            send_mail(
                'Vérifiez votre compte Commodore',
                f'Votre code de vérification est : {code}',
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
                html_message=html_message,
                fail_silently=False
            )

            return Response({
                'user_id': user.id,
                'email': user.email,
                'message': 'Vérifiez votre email pour activer le compte'
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@extend_schema(tags=["Authentication"], request=VerificationCodeSerializer, responses=None)
class VerifyEmailView(APIView):
    permission_classes = [AllowAny]

    @extend_schema(tags=["Authentication"], request=VerificationCodeSerializer, responses=None)
    def post(self, request):
        serializer = VerificationCodeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email = request.data.get('email')
        code = serializer.validated_data['code']

        try:
            user = User.objects.get(email=email)
            if user.email_verified:
                return Response(
                    {'error': 'Cet email est déjà vérifié'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            verification = VerificationCode.objects.filter(
                user=user,
                code=code,
                action=VerificationCode.Actions.EMAIL_VERIFICATION,
                is_used=False
            ).first()

            if not verification:
                return Response(
                    {'error': 'Code de vérification invalide'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not verification.is_valid():
                return Response(
                    {'error': 'Code de vérification expiré'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            verification.is_used = True
            verification.save()

            user.email_verified = True
            user.is_active = True  # Active le compte après vérification de l'email
            user.save()

            # Générer les tokens JWT
            refresh = RefreshToken.for_user(user)
            return Response({
                'access': str(refresh.access_token),
                'refresh': str(refresh),
                'user_id': user.id,
                'email': user.email
            })

        except User.DoesNotExist:
            return Response(
                {'error': 'Utilisateur non trouvé'},
                status=status.HTTP_404_NOT_FOUND
            )


    def get_response(self):
        response = super().get_response()
        if self.user:
            # Vérifier si l'utilisateur a un profil client/captain/establishment
            self.create_or_update_user_profile()
        return response

    def create_or_update_user_profile(self):
        user = self.user
        social_account = user.socialaccount_set.first()

        if social_account:
            extra_data = social_account.extra_data
            user_type = extra_data.get('user_type', 'client')

            if user_type == 'client' and not hasattr(user, 'client'):
                Client.objects.create(user=user)
            elif user_type == 'captain' and not hasattr(user, 'captain'):
                Captain.objects.create(user=user)
            elif user_type == 'establishment' and not hasattr(user, 'establishment'):
                Establishment.objects.create(
                    user=user,
                    name=extra_data.get('business_name', ''),
                    type=extra_data.get('business_type', 'RESTAURANT')
                )



@extend_schema(tags=["Authentication"])
class EmailVerificationView(APIView):
    permission_classes = [AllowAny]

    @extend_schema(tags=["Authentication"], responses=None)
    def get(self, request, uidb64, token):
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            user = None

        if user is not None and default_token_generator.check_token(user, token):
            user.is_active = True
            user.save()
            return Response({'detail': 'Email vérifié avec succès.'}, status=status.HTTP_200_OK)
        else:
            return Response({'error': 'Lien de vérification invalide.'}, status=status.HTTP_400_BAD_REQUEST)

def send_verification_email(user):
    token = default_token_generator.make_token(user)
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    verification_url = f"{settings.FRONTEND_URL}/verify-email/{uid}/{token}/"

    context = {
        'user': user,
        'verification_url': verification_url
    }

    html_message = render_to_string('email/verification.html', context)
    plain_message = strip_tags(html_message)

    send_mail(
        'Vérifiez votre adresse email - Commodore',
        plain_message,
        settings.DEFAULT_FROM_EMAIL,
        [user.email],
        html_message=html_message,
        fail_silently=False,
    )

@extend_schema(tags=["Authentication"], request=SocialLoginSerializer, responses=None)
class SocialLoginView(APIView):
    permission_classes = [AllowAny]

    @extend_schema(tags=["Authentication"], request=SocialLoginSerializer, responses=None)
    def post(self, request):
        serializer = SocialLoginSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )

        provider = serializer.validated_data['provider']
        access_token = serializer.validated_data['access_token']
        user_type = serializer.validated_data.get('user_type')

        try:
            # Valider le token avec le provider et récupérer les infos utilisateur
            user_info = validate_social_token(provider.lower(), access_token)

            # Vérifier si l'utilisateur existe déjà
            try:
                user = User.objects.get(email=user_info['email'])

                # Mettre à jour ou créer le compte social
                social_account, created = UserSocialAccount.objects.get_or_create(
                    user=user,
                    provider=provider,
                    defaults={
                        'provider_user_id': user_info['provider_user_id'],
                        'access_token': access_token,
                    }
                )

                if not created:
                    # Mettre à jour le token
                    social_account.access_token = access_token
                    social_account.save()

            except User.DoesNotExist:
                # Créer un nouvel utilisateur
                if not user_type:
                    return Response(
                        {'error': 'Type d\'utilisateur requis pour la création de compte'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Créer l'utilisateur avec les informations du provider
                user = User.objects.create_user(
                    email=user_info['email'],
                    first_name=user_info.get('first_name', ''),
                    last_name=user_info.get('last_name', ''),
                    type=user_type,
                    is_active=True,
                    email_verified=True
                )

                # Créer le profil spécifique selon le type
                if user_type == User.Types.CLIENT:
                    Client.objects.create(user=user)
                elif user_type == User.Types.CAPTAIN:
                    Captain.objects.create(user=user)
                elif user_type == User.Types.ESTABLISHMENT:
                    name = serializer.validated_data.get('name', user_info.get('name', ''))
                    Establishment.objects.create(user=user, name=name)

                # Créer le compte social
                UserSocialAccount.objects.create(
                    user=user,
                    provider=provider,
                    provider_user_id=user_info['provider_user_id'],
                    access_token=access_token,
                )

            # Générer les tokens JWT
            refresh = RefreshToken.for_user(user)

            # Récupérer les données utilisateur complètes
            from accounts.serializers import UserProfileSerializer
            user_data = UserProfileSerializer(user).data

            return Response({
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh),
                'user': user_data
            })

        except SocialAuthError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': 'Erreur lors de l\'authentification sociale'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

@extend_schema(tags=["Authentication"], request=None, responses=None)
class LoginView(APIView):
    permission_classes = [AllowAny]

    @extend_schema(tags=["Authentication"], request=None, responses=None)
    def post(self, request):
        email = request.data.get('email')
        password = request.data.get('password')
        requested_user_type = request.data.get('user_type')  # Type d'utilisateur demandé dans la requête (optionnel)

        try:
            user = User.objects.get(email=email)
            if not user.check_password(password):
                return Response(
                    {'error': 'Identifiants incorrects'},
                    status=status.HTTP_401_UNAUTHORIZED
                )

            if not user.is_active:
                return Response(
                    {'error': 'Ce compte n\'est pas activé'},
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Si un type d'utilisateur est spécifié dans la requête, vérifier qu'il correspond au type réel
            if requested_user_type and user.type != requested_user_type:
                return Response({
                    'error': f'Ce compte est de type {user.type.lower()}, mais vous essayez de vous connecter en tant que {requested_user_type.lower()}.',
                    'details': 'Veuillez utiliser l\'application ou l\'interface correspondant à votre type de compte.',
                    'account_type': user.type  # Renvoie le vrai type pour débogage
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Vérification spécifique selon le type d'utilisateur
            if user.type == 'CLIENT' and not hasattr(user, 'client'):
                return Response({
                    'error': 'Ce compte n\'a pas de profil client associé.',
                    'details': 'Veuillez contacter le support.'
                }, status=status.HTTP_403_FORBIDDEN)
                
            elif user.type == 'CAPTAIN' and not hasattr(user, 'captain'):
                return Response({
                    'error': 'Ce compte n\'a pas de profil capitaine associé.',
                    'details': 'Veuillez contacter le support.'
                }, status=status.HTTP_403_FORBIDDEN)
                
            elif user.type == 'ESTABLISHMENT' and not hasattr(user, 'establishment'):
                return Response({
                    'error': 'Ce compte n\'a pas de profil établissement associé.',
                    'details': 'Veuillez contacter le support.'
                }, status=status.HTTP_403_FORBIDDEN)

            from accounts.serializers import UserProfileSerializer
            refresh = RefreshToken.for_user(user)
            user_data = UserProfileSerializer(user).data
            return Response({
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh),
                'user': user_data
            })

        except User.DoesNotExist:
            return Response(
                {'error': 'Identifiants incorrects'},
                status=status.HTTP_401_UNAUTHORIZED
            )

@extend_schema(tags=["Authentication"], request=PasswordResetRequestSerializer, responses=None)
class PasswordResetRequestView(APIView):
    permission_classes = [AllowAny]

    @extend_schema(tags=["Authentication"], request=PasswordResetRequestSerializer, responses=None)
    def post(self, request):
        email = request.data.get('email')
        phone_number = request.data.get('phone_number')

        if not email and not phone_number:
            return Response(
                {'error': 'Email ou numéro de téléphone requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if email:
                user = User.objects.get(email=email)
                send_by = 'email'
            else:
                user = User.objects.get(phone_number=phone_number)
                send_by = 'SMS'

            # Générer un code de vérification
            code = ''.join(random.choices('0123456789', k=settings.VERIFICATION_CODE_LENGTH))
            verification = VerificationCode.objects.create(
                user=user,
                code=code,
                action=VerificationCode.Actions.PASSWORD_RESET
            )

            if send_by == 'email':
                # Envoyer l'email de réinitialisation
                context = {
                    'user': user,
                    'code': code,
                    'action': 'réinitialiser votre mot de passe',
                    'expiry_minutes': settings.VERIFICATION_CODE_EXPIRY_MINUTES
                }
                html_message = render_to_string('email/verification_code.html', context)
                send_mail(
                    'Réinitialisation de votre mot de passe Commodore',
                    f'Votre code de vérification est : {code}',
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    html_message=html_message,
                    fail_silently=False
                )
            else:
                # TODO: Implémenter l'envoi de SMS via Twilio
                pass

            return Response({
                'message': f'Code de vérification envoyé par {send_by}',
                'email': user.email if send_by == 'email' else None,
                'phone_number': user.phone_number if send_by == 'SMS' else None
            })

        except User.DoesNotExist:
            return Response(
                {'error': 'Utilisateur non trouvé'},
                status=status.HTTP_404_NOT_FOUND
            )

@extend_schema(tags=["Authentication"], request=PasswordResetVerifySerializer, responses=None)
class PasswordResetVerifyView(APIView):
    permission_classes = [AllowAny]

    @extend_schema(tags=["Authentication"], request=PasswordResetVerifySerializer, responses=None)
    def post(self, request):
        serializer = PasswordResetVerifySerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.validated_data.get('email')
        phone_number = serializer.validated_data.get('phone_number')
        code = serializer.validated_data['code']
        new_password = serializer.validated_data['new_password']

        try:
            if email:
                user = User.objects.get(email=email)
            else:
                user = User.objects.get(phone_number=phone_number)

            verification = VerificationCode.objects.filter(
                user=user,
                code=code,
                action=VerificationCode.Actions.PASSWORD_RESET,
                is_used=False
            ).first()

            if not verification:
                return Response(
                    {'error': 'Code de réinitialisation invalide'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not verification.is_valid():
                return Response(
                    {'error': 'Code de réinitialisation expiré'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            verification.is_used = True
            verification.save()

            # Mettre à jour le mot de passe
            user.set_password(new_password)
            user.save()

            # Générer un nouveau token JWT pour l'utilisateur
            from rest_framework_simplejwt.tokens import RefreshToken
            refresh = RefreshToken.for_user(user)
            return Response({
                'message': 'Mot de passe réinitialisé avec succès',
                'access': str(refresh.access_token),
                'refresh': str(refresh)
            })

        except User.DoesNotExist:
            return Response(
                {'error': 'Utilisateur non trouvé'},
                status=status.HTTP_404_NOT_FOUND
            )

@extend_schema(tags=["Authentication"], request=ResendVerificationCodeSerializer, responses=None)
class ResendVerificationCodeView(APIView):
    """Vue pour permettre aux utilisateurs de demander un nouveau code de vérification
    lorsqu'ils n'ont pas reçu le premier email ou si leur code a expiré."""
    permission_classes = [AllowAny]

    @extend_schema(tags=["Authentication"], request=ResendVerificationCodeSerializer, responses=None)
    def post(self, request):
        serializer = ResendVerificationCodeSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']

            try:
                user = User.objects.get(email=email)

                # Supprimer les anciens codes de vérification
                VerificationCode.objects.filter(user=user).delete()

                # Générer un nouveau code
                code = ''.join(random.choices('0123456789', k=settings.VERIFICATION_CODE_LENGTH))
                expires_at = timezone.now() + timezone.timedelta(minutes=settings.VERIFICATION_CODE_EXPIRY_MINUTES)
                VerificationCode.objects.create(user=user, code=code, action=VerificationCode.Actions.EMAIL_VERIFICATION, expires_at=expires_at)

                # Envoyer l'email avec le nouveau code
                context = {
                    'user': user,
                    'code': code,  # Le code sera utilisé pour l'affichage chiffre par chiffre
                    'verification_code': code,  # Pour compatibilité avec l'ancien template
                    'action': 'vérification de votre email',
                    'expiry_minutes': settings.VERIFICATION_CODE_EXPIRY_MINUTES,
                    'expiration_minutes': settings.VERIFICATION_CODE_EXPIRY_MINUTES  # Pour compatibilité
                }
                html_message = render_to_string('email/verification_code.html', context)
                plain_message = strip_tags(html_message)

                send_mail(
                    'Votre nouveau code de vérification - Commodore',
                    plain_message,
                    settings.DEFAULT_FROM_EMAIL,
                    [email],
                    html_message=html_message,
                    fail_silently=False,
                )

                return Response({
                    'message': 'Un nouveau code de vérification a été envoyé à votre adresse email.'
                })

            except User.DoesNotExist:
                return Response(
                    {"error": "Aucun utilisateur trouvé avec cette adresse email."},
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
