import requests
import random
import string
import time

BASE_URL = "http://127.0.0.1:8000/api/auth/"
PROFILE_URL = "http://127.0.0.1:8000/api/accounts/profile/"
PASSWORD_RESET_REQUEST_URL = BASE_URL + "password-reset/request/"
PASSWORD_RESET_VERIFY_URL = BASE_URL + "password-reset/verify/"

BASE_URL = "http://127.0.0.1:8000/api/"
PROFILE_URL = "http://127.0.0.1:8000/api/accounts/profile/"
PASSWORD_RESET_REQUEST_URL = BASE_URL + "password-reset/request/"
PASSWORD_RESET_VERIFY_URL = BASE_URL + "password-reset/verify/"

# Un email unique pour chaque type d'utilisateur
USER_EMAILS = {
    "CLIENT": "<EMAIL>",
    "CAPTAIN": "<EMAIL>",
    "ESTABLISHMENT": "arm<PERSON><EMAIL>"
}
user_types = ["CLIENT", "CAPTAIN", "ESTABLISHMENT"]

# Génère un mot de passe fort aléatoire

def gen_password():
    return ''.join(random.choices(string.ascii_letters + string.digits, k=12))

# Génère un nom aléatoire

def gen_name(user_type):
    base = {
        "CLIENT": "TestClient",
        "CAPTAIN": "TestCaptain",
        "ESTABLISHMENT": "TestEtab"
    }[user_type]
    return f"{base}{random.randint(100,999)}"

# PATCH data exemples
PATCH_DATA = {
    "CLIENT": {
        "client_profile": {
            "date_of_birth": "1992-04-15",
            "preferred_language": "fr",
            "nationality": "Bénin"
        }
    },
    "CAPTAIN": {
        "captain_profile": {
            "experience": "8 ans",
            "current_location": "Porto-Novo",
            "boat": {
                "name": "Cascade Boat PATCH",
                "registration_number": "REGPATCH1234",
                "color": "Rouge",
                "capacity": 8,
                "fuel_type": "ELECTRIC",
                "fuel_consumption": 7.2,
                "photos": [
                    "https://bucket.s3.amazonaws.com/photo1.jpg",
                    "https://bucket.s3.amazonaws.com/photo2.jpg",
                    "https://bucket.s3.amazonaws.com/photo3.jpg",
                    "https://bucket.s3.amazonaws.com/photo4.jpg"
                ],
                "zone_served": "Cotonou, Ouidah, Grand-Popo",
                "radius": 42
            }
        }
    },
    "ESTABLISHMENT": {
        "establishment_profile": {
            "name": "Etablissement PATCH",
            "address": "456 Avenue du Port",
            "website": "https://patch-website.com"
        }
    }
}

def register_user(email, user_type, password):
    payload = {
        "name": gen_name(user_type),
        "email": email,
        "password": password,
        "user_type": user_type
    }
    print(f"\n[INSCRIPTION] {email} as {user_type}")
    r = requests.post(BASE_URL + "register/", json=payload)
    print("Status:", r.status_code, r.text)
    return r.status_code == 201

def verify_email(email):
    print(f"[ACTION REQUISE] Entre le code reçu par email pour {email} : ", end="")
    code = input().strip()
    payload = {"email": email, "code": code}
    r = requests.post(BASE_URL + "verify-email/", json=payload)
    print("[Vérification Email] Status:", r.status_code, r.text)
    return r.status_code == 200

def login_user(email, password):
    payload = {"email": email, "password": password}
    r = requests.post(BASE_URL + "login/", json=payload)
    print("[LOGIN] Status:", r.status_code, r.text)
    if r.status_code == 200:
        return r.json()["access"]
    return None

def patch_profile(token, user_type):
    headers = {"Authorization": f"Bearer {token}"}
    data = PATCH_DATA[user_type]
    r = requests.patch(PROFILE_URL, json=data, headers=headers)
    print(f"[PATCH {user_type}] Status:", r.status_code, r.text)
    return r.status_code == 200

def password_reset_request(email):
    payload = {"email": email}
    r = requests.post(PASSWORD_RESET_REQUEST_URL, json=payload)
    print("[Password Reset Request] Status:", r.status_code, r.text)
    return r.status_code == 200

def password_reset_verify(email, new_password):
    print(f"[ACTION REQUISE] Entre le code reçu par email pour reset {email} : ", end="")
    code = input().strip()
    payload = {"email": email, "code": code, "new_password": new_password}
    r = requests.post(PASSWORD_RESET_VERIFY_URL, json=payload)
    print("[Password Reset Verify] Status:", r.status_code, r.text)
    return r.status_code == 200

def main():
    credentials = []
    for user_type in user_types:
        email = USER_EMAILS[user_type]
        password = gen_password()
        cred = {"email": email, "user_type": user_type, "initial_password": password, "reset_password": None}
        print(f"\n====== Test {email} as {user_type} ======")
        # 1. Inscription
        if not register_user(email, user_type, password):
            credentials.append(cred)
            continue
        # 2. Vérification Email
        if not verify_email(email):
            credentials.append(cred)
            continue
        # 3. Connexion
        token = login_user(email, password)
        if not token:
            credentials.append(cred)
            continue
        # 4. PATCH Profil
        patch_profile(token, user_type)
        # 5. Password Reset
        new_password = gen_password()
        if password_reset_request(email):
            password_reset_verify(email, new_password)
            cred["reset_password"] = new_password
        credentials.append(cred)
        print(f"[FIN DU TEST POUR {email} - {user_type}]\n")
        time.sleep(1)
    # Écrire les credentials dans un fichier à la fin
    with open("credentials_test.txt", "w", encoding="utf-8") as f:
        f.write("email | user_type | initial_password | reset_password\n")
        for cred in credentials:
            f.write(f"{cred['email']} | {cred['user_type']} | {cred['initial_password']} | {cred.get('reset_password','')}\n")
    print("\nLes identifiants de test ont été sauvegardés dans credentials_test.txt (avec mot de passe initial et après reset si applicable)")

if __name__ == "__main__":
    main()
