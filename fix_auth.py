# Script pour corriger la vérification d'autorisation dans views_api.py
import re

# <PERSON>u<PERSON><PERSON><PERSON> le fichier
with open('payments/views_api.py', 'r', encoding='utf-8') as file:
    content = file.read()

# Remplacer la vérification d'autorisation
old_line = "                if trip.client != request.user:"
new_line = "                if not hasattr(request.user, 'client') or trip.client != request.user.client:"

# Effectuer le remplacement
modified_content = content.replace(old_line, new_line)

# Sauvegarder le fichier modifié
with open('payments/views_api.py', 'w', encoding='utf-8') as file:
    file.write(modified_content)

print("Correction appliquée avec succès!")
