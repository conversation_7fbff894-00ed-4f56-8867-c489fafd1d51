"""
Permissions personnalisées pour l'application establishments.
"""

from rest_framework import permissions


class IsEstablishment(permissions.BasePermission):
    """
    Permission personnalisée pour vérifier que l'utilisateur est un établissement.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur est authentifié et est un établissement"""
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'establishment')
        )


class IsEstablishmentOwner(permissions.BasePermission):
    """
    Permission pour vérifier que l'utilisateur est propriétaire de l'établissement.
    """
    
    def has_object_permission(self, request, view, obj):
        """Vérifier que l'utilisateur est propriétaire de l'objet"""
        if hasattr(obj, 'establishment'):
            return obj.establishment.user == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        return False


class CanManageBoatmen(permissions.BasePermission):
    """
    Permission pour gérer les bateliers.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur peut gérer les bateliers"""
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'establishment')
        )


class CanManageShuttles(permissions.BasePermission):
    """
    Permission pour gérer les navettes.
    """
    
    def has_permission(self, request, view):
        """Vérifier que l'utilisateur peut gérer les navettes"""
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'establishment')
        )
    
    def has_object_permission(self, request, view, obj):
        """Vérifier que l'utilisateur peut gérer cette navette spécifique"""
        if hasattr(obj, 'establishment'):
            return obj.establishment.user == request.user
        return False
