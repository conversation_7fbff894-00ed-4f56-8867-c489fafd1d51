"""
Script pour exécuter l'importation des documents depuis django.setup()
"""

import os
import sys
import django

# Configurer l'environnement Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

# Maintenant, importer le script d'importation des documents
with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'import_docs.py')) as f:
    exec(f.read())
