import os
import sys
import django
import importlib
import inspect

# Configurer Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "commodore.settings")
django.setup()

from django.conf import settings
import stripe

def reset_stripe_api_key():
    """Réinitialise la clé API Stripe dans tous les modules qui l'utilisent"""
    # Afficher la clé configurée dans settings
    print(f"Clé API configurée dans settings: {settings.STRIPE_SECRET_KEY[:10]}...{settings.STRIPE_SECRET_KEY[-4:]}")
    
    # Réinitialiser la clé dans le module stripe global
    stripe.api_key = settings.STRIPE_SECRET_KEY
    stripe.api_version = "2023-10-16"
    print(f"Clé API réinitialisée dans le module stripe global")
    print(f"Version de l'API configurée: {stripe.api_version}")
    
    # Chercher tous les modules Python dans le projet
    modules_to_check = []
    for root, dirs, files in os.walk(os.path.dirname(os.path.abspath(__file__))):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                module_path = os.path.join(root, file).replace(os.path.dirname(os.path.abspath(__file__)) + os.sep, '')
                module_name = module_path.replace(os.sep, '.').replace('.py', '')
                modules_to_check.append(module_name)
    
    # Importer chaque module et vérifier s'il utilise stripe
    stripe_using_modules = []
    for module_name in modules_to_check:
        try:
            module = importlib.import_module(module_name)
            if 'stripe' in dir(module):
                stripe_using_modules.append(module_name)
                # Réinitialiser la clé API dans ce module
                module.stripe.api_key = settings.STRIPE_SECRET_KEY
                module.stripe.api_version = "2023-10-16"
                print(f"Clé API réinitialisée dans le module {module_name}")
        except (ImportError, AttributeError, ModuleNotFoundError):
            continue
    
    return stripe_using_modules

if __name__ == "__main__":
    print("Réinitialisation de la configuration Stripe...")
    modules = reset_stripe_api_key()
    print(f"\nModules utilisant Stripe ({len(modules)}):")
    for module in modules:
        print(f"- {module}")
    
    print("\nPour appliquer ces changements, redémarrez le serveur Django.")
    print("Exécutez: python manage.py runserver")
