from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal

from .models import Boat


class MaintenanceProvider(models.Model):
    """
    Modèle pour les prestataires de services de maintenance.
    """
    name = models.CharField(_('nom'), max_length=100)
    user = models.OneToOneField('auth.User', on_delete=models.CASCADE, related_name='maintenance_provider', null=True, blank=True)
    email = models.EmailField(_('email'), unique=True)
    phone = models.CharField(_('téléphone'), max_length=20)
    address = models.TextField(_('adresse'), blank=True)
    specialties = models.JSONField(_('spécialités'), default=list)
    rating = models.DecimalField(_('note'), max_digits=3, decimal_places=1, default=Decimal('0.0'))
    is_verified = models.BooleanField(_('vérifié'), default=False)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    
    class Meta:
        verbose_name = _('prestataire de maintenance')
        verbose_name_plural = _('prestataires de maintenance')
        ordering = ['-rating', 'name']
    
    def __str__(self):
        return self.name


class Maintenance(models.Model):
    """
    Modèle pour les services de maintenance des bateaux.
    """
    TYPE_CHOICES = (
        ('ROUTINE', _('Routine')),
        ('REPAIR', _('Réparation')),
        ('EMERGENCY', _('Urgence')),
        ('INSPECTION', _('Inspection')),
        ('UPGRADE', _('Amélioration')),
    )
    
    STATUS_CHOICES = (
        ('SCHEDULED', _('Programmée')),
        ('IN_PROGRESS', _('En cours')),
        ('COMPLETED', _('Terminée')),
        ('CANCELLED', _('Annulée')),
    )
    
    PAYMENT_STATUS_CHOICES = (
        ('PENDING', _('En attente')),
        ('PAID', _('Payée')),
        ('REFUNDED', _('Remboursée')),
        ('FAILED', _('Échouée')),
    )
    
    boat = models.ForeignKey(Boat, on_delete=models.CASCADE, related_name='maintenances')
    service_provider = models.ForeignKey(MaintenanceProvider, on_delete=models.SET_NULL, null=True, blank=True, related_name='maintenances')
    type = models.CharField(_('type'), max_length=20, choices=TYPE_CHOICES)
    description = models.TextField(_('description'))
    date = models.DateTimeField(_('date'))
    completion_date = models.DateTimeField(_('date de fin'), null=True, blank=True)
    status = models.CharField(_('statut'), max_length=20, choices=STATUS_CHOICES, default='SCHEDULED')
    notes = models.TextField(_('notes'), blank=True)
    cost = models.DecimalField(_('coût'), max_digits=10, decimal_places=2)
    payment_status = models.CharField(_('statut de paiement'), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='PENDING')
    payment_date = models.DateTimeField(_('date de paiement'), null=True, blank=True)
    transaction_id = models.CharField(_('ID de transaction'), max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    
    class Meta:
        verbose_name = _('maintenance')
        verbose_name_plural = _('maintenances')
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.get_type_display()} - {self.boat.name}"
    
    def is_overdue(self):
        """
        Vérifie si la maintenance est en retard.
        """
        return self.status == 'SCHEDULED' and self.date < timezone.now()
    
    def mark_as_completed(self, completion_date=None):
        """
        Marque la maintenance comme terminée.
        """
        self.status = 'COMPLETED'
        self.completion_date = completion_date or timezone.now()
        self.save()
    
    def cancel(self):
        """
        Annule la maintenance.
        """
        self.status = 'CANCELLED'
        self.save()
        
        # Si la maintenance a été payée, initier un remboursement
        if self.payment_status == 'PAID' and self.transaction_id:
            from payments.services import PaymentService
            try:
                PaymentService.auto_refund_on_cancellation(
                    booking_id=self.id,
                    booking_type='maintenance',
                    reason=f"Annulation de la maintenance {self.id}"
                )
                self.payment_status = 'REFUNDED'
                self.save()
            except Exception as e:
                # Logger l'erreur
                print(f"Erreur lors du remboursement automatique: {str(e)}")


class MaintenanceReport(models.Model):
    """
    Modèle pour les rapports de maintenance.
    """
    maintenance = models.OneToOneField(Maintenance, on_delete=models.CASCADE, related_name='report')
    findings = models.TextField(_('constatations'))
    work_done = models.TextField(_('travaux effectués'))
    parts_replaced = models.JSONField(_('pièces remplacées'), default=list)
    recommendations = models.TextField(_('recommandations'), blank=True)
    images = models.JSONField(_('images'), default=list)
    created_by = models.ForeignKey(MaintenanceProvider, on_delete=models.SET_NULL, null=True, related_name='reports')
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    
    class Meta:
        verbose_name = _('rapport de maintenance')
        verbose_name_plural = _('rapports de maintenance')
    
    def __str__(self):
        return f"Rapport - {self.maintenance}"
