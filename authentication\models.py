import uuid
from django.db import models
from django.conf import settings
from django.utils import timezone
from datetime import timedelta


class VerificationCode(models.Model):
    class Actions(models.TextChoices):
        EMAIL_VERIFICATION = 'EMAIL_VERIFICATION', 'Vérification email'
        PASSWORD_RESET = 'PASSWORD_RESET', 'Réinitialisation mot de passe'
        PHONE_VERIFICATION = 'PHONE_VERIFICATION', 'Vérification téléphone'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    code = models.CharField(max_length=6)
    action = models.CharField(max_length=20, choices=Actions.choices)
    is_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()

    def save(self, *args, **kwargs):
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(
                minutes=settings.VERIFICATION_CODE_EXPIRY_MINUTES
            )
        super().save(*args, **kwargs)

    def is_valid(self):
        return not self.is_used and timezone.now() <= self.expires_at

    def __str__(self):
        return f'{self.action} code for {self.user.email}'

class UserSocialAccount(models.Model):
    class Providers(models.TextChoices):
        FACEBOOK = 'FACEBOOK', 'Facebook'
        GOOGLE = 'GOOGLE', 'Google'
        APPLE = 'APPLE', 'Apple'

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    provider = models.CharField(max_length=20, choices=Providers.choices)
    provider_user_id = models.CharField(max_length=255)
    access_token = models.TextField()
    refresh_token = models.TextField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('provider', 'provider_user_id')

    def __str__(self):
        return f'{self.provider} account for {self.user.email}'
