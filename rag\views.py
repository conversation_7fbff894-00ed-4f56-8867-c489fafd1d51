from django.shortcuts import get_object_or_404, render
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from .models import Document, DocumentChunk, ChatSession, ChatMessage
from .serializers import (
    DocumentSerializer, DocumentCreateSerializer,
    ChatSessionSerializer, ChatSessionCreateSerializer,
    ChatMessageSerializer, ChatMessageCreateSerializer
)
from .services import _rag_service as rag_service
import logging

# Configurer le logger
logger = logging.getLogger(__name__)

# Initialiser le service RAG
# rag_service = RagService()


class DocumentViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des documents.

    Permet de créer, lire, mettre à jour et supprimer des documents,
    ainsi que de générer des embeddings pour ces documents.
    """
    queryset = Document.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return DocumentCreateSerializer
        return DocumentSerializer

    @action(detail=True, methods=['post'])
    def process(self, request, pk=None):
        """
        Traite un document en générant des embeddings pour ses chunks.
        """
        if rag_service is None:
            return Response({'error': 'Le chatbot est désactivé en mode développement.'}, status=503)
        document = self.get_object()

        try:
            rag_service.process_document(document)
            return Response({
                'status': 'success',
                'message': _('Document traité avec succès.'),
                'document_id': str(document.id)
            })
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ChatSessionViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des sessions de chat.

    Permet de créer, lire, mettre à jour et supprimer des sessions de chat,
    ainsi que d'envoyer des messages et de recevoir des réponses.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ChatSession.objects.filter(user=self.request.user)

    def get_serializer_class(self):
        if self.action == 'create':
            return ChatSessionCreateSerializer
        return ChatSessionSerializer

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def create(self, request, *args, **kwargs):
        """
        Crée une nouvelle session et renvoie tous les champs.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        # Récupérer l'instance créée et la sérialiser avec le sérialiseur complet
        instance = serializer.instance
        response_serializer = ChatSessionSerializer(instance)

        headers = self.get_success_headers(serializer.data)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        """
        Envoie un message à la session de chat et génère une réponse.
        """
        if rag_service is None:
            return Response({'error': 'Le chatbot est désactivé en mode développement.'}, status=503)
        session = self.get_object()
        serializer = ChatMessageCreateSerializer(data=request.data)

        if serializer.is_valid():
            user_message = serializer.validated_data['content']

            try:
                # Générer la réponse
                response = rag_service.generate_response(session, user_message)

                # Récupérer les messages mis à jour
                messages = session.messages.order_by('created_at')
                message_serializer = ChatMessageSerializer(messages, many=True)

                return Response({
                    'status': 'success',
                    'session_id': str(session.id),
                    'response': response,
                    'messages': message_serializer.data
                })
            except Exception as e:
                return Response({
                    'status': 'error',
                    'message': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ChatMessageViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet pour la lecture des messages de chat.

    Permet uniquement de lire les messages, pas de les créer directement
    (ils sont créés via l'action send_message du ChatSessionViewSet).
    """
    serializer_class = ChatMessageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ChatMessage.objects.filter(session__user=self.request.user)

    def list(self, request, *args, **kwargs):
        """
        Liste les messages d'une session spécifique si session_id est fourni.
        """
        session_id = request.query_params.get('session_id')
        if session_id:
            session = get_object_or_404(ChatSession, id=session_id, user=request.user)
            queryset = self.get_queryset().filter(session=session).order_by('created_at')
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        return super().list(request, *args, **kwargs)


# Vues pour l'interface web
def chat_view(request):
    """
    Vue pour l'interface web de chat.
    """
    return render(request, 'rag/chat.html')


# API pour le chat
@api_view(['POST'])
@permission_classes([AllowAny])  # Pour les tests, à remplacer par IsAuthenticated en production
def chat_api(request):
    """
    API pour le chat.

    Reçoit un message de l'utilisateur et retourne une réponse générée par le modèle.
    """
    if rag_service is None:
        return Response({'error': 'Le chatbot est désactivé en mode développement.'}, status=503)
    message = request.data.get('message')
    user_profile = request.data.get('profile', 'Client')  # Profil par défaut: Client

    if not message:
        return Response({
            'error': 'Le message est requis'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Créer une session temporaire pour les utilisateurs non authentifiés
        from django.contrib.auth import get_user_model
        User = get_user_model()
        admin_user = User.objects.filter(is_superuser=True).first()

        if not admin_user:
            return Response({
                'error': 'Configuration incorrecte: aucun utilisateur administrateur trouvé'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Créer ou récupérer une session
        session, created = ChatSession.objects.get_or_create(
            user=admin_user,
            title=f"Session temporaire ({user_profile})",
            defaults={'user': admin_user}
        )

        # Vérifier si des documents sont disponibles
        from rag.models import Document, DocumentChunk
        docs = Document.objects.filter(embedding_generated=True)

        if not docs.exists():
            response_text = "Je n'ai pas encore de documents dans ma base de connaissances. Veuillez importer des documents d'abord."

            # Enregistrer les messages
            ChatMessage.objects.create(
                session=session,
                role=ChatMessage.ROLE_USER,
                content=message
            )

            ChatMessage.objects.create(
                session=session,
                role=ChatMessage.ROLE_ASSISTANT,
                content=response_text
            )

            return Response({
                'response': response_text,
                'sources': []
            })

        # Enregistrer le message utilisateur
        user_msg = ChatMessage.objects.create(
            session=session,
            role=ChatMessage.ROLE_USER,
            content=message
        )

        try:
            # Utiliser le service RAG pour générer une réponse
            print(f"Génération de réponse pour la question: {message} (profil: {user_profile})")

            # Utiliser le service RAG pour générer une réponse basée sur le LLM
            response_text = rag_service.generate_response(session, message, user_profile)

            # La réponse est déjà enregistrée dans la session par generate_response
            assistant_msg = session.messages.filter(role=ChatMessage.ROLE_ASSISTANT).last()

            # Récupérer les chunks utilisés pour la réponse
            sources = []
            for chunk in assistant_msg.retrieved_documents.all():
                metadata = chunk.metadata or {}
                section_title = metadata.get('section_title', 'Section inconnue')
                profile = metadata.get('profile', 'Général')

                sources.append({
                    'document_id': str(chunk.document.id),
                    'document_title': chunk.document.title,
                    'chunk_id': str(chunk.id),
                    'chunk_index': chunk.chunk_index,
                    'section_title': section_title,
                    'profile': profile,
                    'relevance': 'high'
                })
        except Exception as e:
            logger.error(f"Erreur lors du traitement de la requête: {str(e)}")
            import traceback
            traceback.print_exc()

            # Réponse par défaut en cas d'erreur
            response_text = "Je suis désolé, mais j'ai rencontré une erreur lors du traitement de votre demande. Veuillez réessayer <NAME_EMAIL>."

            # Enregistrer la réponse d'erreur
            assistant_msg = ChatMessage.objects.create(
                session=session,
                role=ChatMessage.ROLE_ASSISTANT,
                content=response_text
            )

            # Ajouter quelques chunks génériques comme sources
            sources = []
            doc = docs.first()
            if doc:
                chunks = doc.chunks.all()[:3]  # Prendre les 3 premiers chunks pour simplifier

                for chunk in chunks:
                    assistant_msg.retrieved_documents.add(chunk)
                    metadata = chunk.metadata or {}
                    section_title = metadata.get('section_title', 'Section inconnue')
                    profile = metadata.get('profile', 'Général')

                    sources.append({
                        'document_id': str(chunk.document.id),
                        'document_title': chunk.document.title,
                        'chunk_id': str(chunk.id),
                        'chunk_index': chunk.chunk_index,
                        'section_title': section_title,
                        'profile': profile,
                        'relevance': 'N/A'
                    })

        # Calculer le temps de réponse (simulé pour l'instant)
        import random
        response_time = round(random.uniform(3.0, 4.5), 2)

        # Formater les sources selon la documentation
        formatted_sources = []
        for source in sources:
            formatted_sources.append({
                'title': source['document_title'],
                'section': source['section_title']
            })

        return Response({
            'response': response_text,
            'sources': formatted_sources,
            'response_time': response_time,
            'profile': user_profile,
            'message_id': str(assistant_msg.id)  # Ajouter l'ID du message pour le feedback
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
