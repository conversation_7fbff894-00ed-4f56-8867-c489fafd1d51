"""
Module de tests pour les vues API de l'application notifications.

Ce module contient les tests unitaires pour les vues API de l'application notifications.
"""

import pytest
import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from notifications.models import Notification, Device
from unittest.mock import patch, MagicMock

User = get_user_model()

@pytest.fixture
def api_client():
    """Fixture pour créer un client API."""
    return APIClient()

@pytest.fixture
def user():
    """Fixture pour créer un utilisateur."""
    import uuid
    unique_username = f'testuser_{uuid.uuid4().hex[:8]}'
    return User.objects.create_user(
        username=unique_username,
        email=f'{unique_username}@example.com',
        password='testpassword'
    )

@pytest.fixture
def admin_user():
    """Fixture pour créer un utilisateur administrateur."""
    import uuid
    unique_username = f'adminuser_{uuid.uuid4().hex[:8]}'
    return User.objects.create_user(
        username=unique_username,
        email=f'{unique_username}@example.com',
        password='adminpassword',
        is_staff=True,
        is_superuser=True
    )

@pytest.fixture
def authenticated_client(api_client, user):
    """Fixture pour créer un client API authentifié."""
    api_client.force_authenticate(user=user)
    return api_client

@pytest.fixture
def admin_client(api_client, admin_user):
    """Fixture pour créer un client API authentifié en tant qu'administrateur."""
    api_client.force_authenticate(user=admin_user)
    return api_client

@pytest.fixture
def notification(user):
    """Fixture pour créer une notification."""
    return Notification.objects.create(
        user=user,
        type='system',
        title='Test Notification',
        message='This is a test notification',
        is_read=False,
        data={'test_key': 'test_value'}
    )

@pytest.fixture
def device(user):
    """Fixture pour créer un appareil."""
    return Device.objects.create(
        user=user,
        token='test_token_123456789',
        device_type='android',
        device_name='Test Device'
    )

@pytest.mark.django_db
class TestNotificationListView:
    """Tests pour la vue NotificationListView."""

    def test_get_notifications(self, authenticated_client, notification):
        """Teste la récupération des notifications."""
        url = reverse('notification-list')
        response = authenticated_client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['id'] == notification.id
        assert response.data[0]['title'] == notification.title

    def test_get_notifications_filter_is_read(self, authenticated_client, notification):
        """Teste le filtrage des notifications par statut de lecture."""
        # Créer une notification lue
        read_notification = Notification.objects.create(
            user=notification.user,
            type='system',
            title='Read Notification',
            message='This is a read notification',
            is_read=True
        )

        # Tester le filtre is_read=false
        url = reverse('notification-list')
        response = authenticated_client.get(f"{url}?is_read=false")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['id'] == notification.id

        # Tester le filtre is_read=true
        response = authenticated_client.get(f"{url}?is_read=true")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['id'] == read_notification.id

    def test_get_notifications_filter_type(self, authenticated_client, notification):
        """Teste le filtrage des notifications par type."""
        # Créer une notification d'un autre type
        other_notification = Notification.objects.create(
            user=notification.user,
            type='payment_success',
            title='Payment Success',
            message='Your payment was successful',
            is_read=False
        )

        # Tester le filtre type=system
        url = reverse('notification-list')
        response = authenticated_client.get(f"{url}?type=system")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['id'] == notification.id

        # Tester le filtre type=payment_success
        response = authenticated_client.get(f"{url}?type=payment_success")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['id'] == other_notification.id

    def test_get_notifications_limit(self, authenticated_client, notification):
        """Teste la limitation du nombre de notifications."""
        # Créer plusieurs notifications supplémentaires
        for i in range(5):
            Notification.objects.create(
                user=notification.user,
                type='system',
                title=f'Notification {i}',
                message=f'This is notification {i}',
                is_read=False
            )

        # Tester la limite par défaut
        url = reverse('notification-list')
        response = authenticated_client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 6  # 5 nouvelles + 1 existante

        # Tester une limite personnalisée
        response = authenticated_client.get(f"{url}?limit=3")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 3

    def test_get_notifications_unauthenticated(self, api_client):
        """Teste la récupération des notifications sans authentification."""
        url = reverse('notification-list')
        response = api_client.get(url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

@pytest.mark.django_db
class TestNotificationDetailView:
    """Tests pour la vue NotificationDetailView."""

    def test_get_notification(self, authenticated_client, notification):
        """Teste la récupération des détails d'une notification."""
        url = reverse('notification-detail', args=[notification.id])
        response = authenticated_client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == notification.id
        assert response.data['title'] == notification.title

    def test_mark_notification_as_read(self, authenticated_client, notification):
        """Teste le marquage d'une notification comme lue."""
        url = reverse('notification-detail', args=[notification.id])
        response = authenticated_client.put(url, {'is_read': True}, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert response.data['is_read'] is True

        # Vérifier que la notification a bien été mise à jour en base
        notification.refresh_from_db()
        assert notification.is_read is True

    def test_mark_notification_as_unread(self, authenticated_client, notification):
        """Teste le marquage d'une notification comme non lue."""
        # D'abord marquer comme lue
        notification.is_read = True
        notification.save()

        url = reverse('notification-detail', args=[notification.id])
        response = authenticated_client.put(url, {'is_read': False}, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert response.data['is_read'] is False

        # Vérifier que la notification a bien été mise à jour en base
        notification.refresh_from_db()
        assert notification.is_read is False

    def test_delete_notification(self, authenticated_client, notification):
        """Teste la suppression d'une notification."""
        url = reverse('notification-detail', args=[notification.id])
        response = authenticated_client.delete(url)
        assert response.status_code == status.HTTP_204_NO_CONTENT

        # Vérifier que la notification a bien été supprimée
        assert not Notification.objects.filter(id=notification.id).exists()

    def test_get_notification_not_found(self, authenticated_client):
        """Teste la récupération d'une notification inexistante."""
        url = reverse('notification-detail', args=[999])
        response = authenticated_client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_notification_unauthorized(self, authenticated_client, admin_user):
        """Teste la récupération d'une notification appartenant à un autre utilisateur."""
        # Créer une notification pour l'administrateur
        admin_notification = Notification.objects.create(
            user=admin_user,
            type='system',
            title='Admin Notification',
            message='This is an admin notification',
            is_read=False
        )

        url = reverse('notification-detail', args=[admin_notification.id])
        response = authenticated_client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND

@pytest.mark.django_db
class TestNotificationBulkActionView:
    """Tests pour la vue NotificationBulkActionView."""

    def test_mark_all_read(self, authenticated_client, user):
        """Teste le marquage de toutes les notifications comme lues."""
        # Créer plusieurs notifications
        for i in range(5):
            Notification.objects.create(
                user=user,
                type='system',
                title=f'Notification {i}',
                message=f'This is notification {i}',
                is_read=False
            )

        url = reverse('notification-bulk-action')
        response = authenticated_client.post(url, {'action': 'mark_all_read'}, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert 'message' in response.data
        assert '5 notifications' in response.data['message']

        # Vérifier que toutes les notifications ont été marquées comme lues
        assert Notification.objects.filter(user=user, is_read=True).count() == 5

    def test_delete_all(self, authenticated_client, user):
        """Teste la suppression de toutes les notifications."""
        # Créer plusieurs notifications
        for i in range(5):
            Notification.objects.create(
                user=user,
                type='system',
                title=f'Notification {i}',
                message=f'This is notification {i}',
                is_read=False
            )

        url = reverse('notification-bulk-action')
        response = authenticated_client.post(url, {'action': 'delete_all'}, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert 'message' in response.data
        assert '5 notifications' in response.data['message']

        # Vérifier que toutes les notifications ont été supprimées
        assert Notification.objects.filter(user=user).count() == 0

    def test_mark_all_read_by_type(self, authenticated_client, user):
        """Teste le marquage de toutes les notifications d'un certain type comme lues."""
        # Créer des notifications de différents types
        for i in range(3):
            Notification.objects.create(
                user=user,
                type='system',
                title=f'System Notification {i}',
                message=f'This is a system notification {i}',
                is_read=False
            )

        for i in range(2):
            Notification.objects.create(
                user=user,
                type='payment_success',
                title=f'Payment Success {i}',
                message=f'Your payment was successful {i}',
                is_read=False
            )

        url = reverse('notification-bulk-action')
        response = authenticated_client.post(url, {'action': 'mark_all_read', 'type': 'system'}, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert 'message' in response.data
        assert '3 notifications' in response.data['message']

        # Vérifier que seules les notifications de type 'system' ont été marquées comme lues
        assert Notification.objects.filter(user=user, type='system', is_read=True).count() == 3
        assert Notification.objects.filter(user=user, type='payment_success', is_read=False).count() == 2

    def test_mark_specific_notifications_read(self, authenticated_client, user):
        """Teste le marquage de notifications spécifiques comme lues."""
        # Créer plusieurs notifications
        notifications = []
        for i in range(5):
            notification = Notification.objects.create(
                user=user,
                type='system',
                title=f'Notification {i}',
                message=f'This is notification {i}',
                is_read=False
            )
            notifications.append(notification)

        # Sélectionner quelques notifications à marquer comme lues
        selected_ids = [notifications[0].id, notifications[2].id, notifications[4].id]

        url = reverse('notification-bulk-action')
        response = authenticated_client.post(url, {
            'action': 'mark_all_read',
            'notification_ids': selected_ids
        }, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert 'message' in response.data
        assert '3 notifications' in response.data['message']

        # Vérifier que seules les notifications sélectionnées ont été marquées comme lues
        for i, notification in enumerate(notifications):
            notification.refresh_from_db()
            if notification.id in selected_ids:
                assert notification.is_read is True
            else:
                assert notification.is_read is False

    def test_invalid_action(self, authenticated_client):
        """Teste une action invalide."""
        url = reverse('notification-bulk-action')
        response = authenticated_client.post(url, {'action': 'invalid_action'}, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

@pytest.mark.django_db
class TestPushNotificationView:
    """Tests pour la vue PushNotificationView."""

    @patch('notifications.views.send_push_notification')
    def test_send_push_notification(self, mock_send_push, admin_client, user):
        """Teste l'envoi d'une notification push."""
        # Configurer le mock
        mock_send_push.return_value = {'success': True, 'response': {'name': 'test_message_id'}}

        url = reverse('notification-push')
        data = {
            'user_ids': [user.id],
            'title': 'Test Push',
            'message': 'This is a test push notification',
            'data': {'test_key': 'test_value'}
        }
        response = admin_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert 'message' in response.data
        assert 'succès' in response.data['message'].lower()

        # Vérifier que la fonction send_push_notification a été appelée avec les bons arguments
        mock_send_push.assert_called_once_with(
            [user.id],
            'Test Push',
            'This is a test push notification',
            {'test_key': 'test_value'}
        )

    def test_send_push_notification_non_admin(self, authenticated_client, user):
        """Teste l'envoi d'une notification push par un utilisateur non administrateur."""
        url = reverse('notification-push')
        data = {
            'user_ids': [user.id],
            'title': 'Test Push',
            'message': 'This is a test push notification'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_403_FORBIDDEN

    @patch('notifications.views.send_push_notification')
    def test_send_push_notification_missing_fields(self, mock_send_push, admin_client, user):
        """Teste l'envoi d'une notification push avec des champs manquants."""
        url = reverse('notification-push')

        # Test sans titre
        data = {
            'user_ids': [user.id],
            'message': 'This is a test push notification'
        }
        response = admin_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

        # Test sans message
        data = {
            'user_ids': [user.id],
            'title': 'Test Push'
        }
        response = admin_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

        # Test sans user_ids
        data = {
            'title': 'Test Push',
            'message': 'This is a test push notification'
        }
        response = admin_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

        # Vérifier que la fonction send_push_notification n'a pas été appelée
        mock_send_push.assert_not_called()

@pytest.mark.django_db
class TestDeviceRegistrationView:
    """Tests pour la vue DeviceRegistrationView."""

    @patch('notifications.views.register_device')
    def test_register_device(self, mock_register_device, authenticated_client, user):
        """Teste l'enregistrement d'un appareil."""
        # Configurer le mock
        mock_register_device.return_value = {'success': True, 'created': True, 'device_id': 1}

        url = reverse('device-registration')
        data = {
            'token': 'fcm_token_123456789',
            'device_type': 'android',
            'device_name': 'Test Android Device'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        assert 'message' in response.data
        assert 'succès' in response.data['message'].lower()

        # Vérifier que la fonction register_device a été appelée avec les bons arguments
        mock_register_device.assert_called_once_with(
            user,
            'fcm_token_123456789',
            'android'
        )

    @patch('notifications.views.register_device')
    def test_register_device_invalid_data(self, mock_register_device, authenticated_client):
        """Teste l'enregistrement d'un appareil avec des données invalides."""
        url = reverse('device-registration')

        # Test sans token
        data = {
            'device_type': 'android',
            'device_name': 'Test Android Device'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'token' in response.data

        # Test avec un type d'appareil invalide
        data = {
            'token': 'fcm_token_123456789',
            'device_type': 'invalid_type',
            'device_name': 'Test Device'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'device_type' in response.data

        # Vérifier que la fonction register_device n'a pas été appelée
        mock_register_device.assert_not_called()

    @patch('notifications.views.unregister_device')
    def test_unregister_device(self, mock_unregister_device, authenticated_client, device):
        """Teste la suppression d'un appareil."""
        # Configurer le mock
        mock_unregister_device.return_value = {'success': True, 'deleted': True}

        url = reverse('device-registration')
        data = {
            'token': device.token
        }
        response = authenticated_client.delete(url, data, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert 'message' in response.data
        assert 'succès' in response.data['message'].lower()

        # Vérifier que la fonction unregister_device a été appelée avec les bons arguments
        mock_unregister_device.assert_called_once_with(device.user, device.token)

    @patch('notifications.views.unregister_device')
    def test_unregister_device_missing_token(self, mock_unregister_device, authenticated_client):
        """Teste la suppression d'un appareil sans token."""
        url = reverse('device-registration')
        response = authenticated_client.delete(url, {}, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

        # Vérifier que la fonction unregister_device n'a pas été appelée
        mock_unregister_device.assert_not_called()
