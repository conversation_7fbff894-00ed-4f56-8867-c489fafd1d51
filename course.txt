# Exemples complets de workflows de réservation de bateau (SIMPLE, HOURLY, SHUTTLE)

Ce document présente des exemples précis de payloads JSON, réponses et commandes pour tester les trois types de demandes de course dans l'API, en se basant sur les vrais modèles et endpoints du codebase.

---

## 1. Demande de course SIMPLE (SimpleTripRequest)

### Création d'une demande
**Endpoint :** POST `/trips/requests/simple/`

```json
{
  "boat_type": "YACHT",
  "departure_location": {"city_name": "Marseille", "latitude": 43.2965, "longitude": 5.3698},
  "arrival_location": {"city_name": "Nice", "latitude": 43.7102, "longitude": 7.2620},
  "passenger_count": 4,
  "scheduled_date": "2025-06-05",
  "scheduled_time": "14:00:00",
  "message": "Anniversaire surprise."
}
```

**Exemple cURL :**
```sh
curl -X POST http://localhost:8000/trips/requests/simple/ \
  -H "Authorization: Token <client_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "boat_type": "YACHT",
    "departure_location": {"city_name": "Marseille", "latitude": 43.2965, "longitude": 5.3698},
    "arrival_location": {"city_name": "Nice", "latitude": 43.7102, "longitude": 7.2620},
    "passenger_count": 4,
    "scheduled_date": "2025-06-05",
    "scheduled_time": "14:00:00",
    "message": "Anniversaire surprise."
  }'
```

### Réponse attendue
```json
{
  "id": 123,
  "boat_type": "YACHT",
  "departure_location": {"city_name": "Marseille", "latitude": 43.2965, "longitude": 5.3698},
  "arrival_location": {"city_name": "Nice", "latitude": 43.7102, "longitude": 7.2620},
  "passenger_count": 4,
  "scheduled_date": "2025-06-05",
  "scheduled_time": "14:00:00",
  "message": "Anniversaire surprise.",
  "client": 7,
  "status": "PENDING",
  "distance_km": 180.5,
  "created_at": "2025-05-01T10:00:00Z",
  "trip_type": "SIMPLE"
}
```

---

## 2. Demande de course à l'heure (HOURLY / Mise à disposition)

### Création d'une demande
**Endpoint :** POST `/trips/requests/hourly/`

```json
{
  "boat_type": "YACHT",
  "departure_location": {"city_name": "Cannes", "latitude": 43.5528, "longitude": 7.0174},
  "passenger_count": 6,
  "start_date": "2025-07-10",
  "duration_hours": 5,
  "message": "Tour privé pour événement entreprise."
}
```

**Exemple cURL :**
```sh
curl -X POST http://localhost:8000/trips/requests/hourly/ \
  -H "Authorization: Token <client_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "boat_type": "YACHT",
    "departure_location": {"city_name": "Cannes", "latitude": 43.5528, "longitude": 7.0174},
    "passenger_count": 6,
    "start_date": "2025-07-10",
    "duration_hours": 5,
    "message": "Tour privé pour événement entreprise."
  }'
```

### Réponse attendue
```json
{
  "id": 456,
  "boat_type": "YACHT",
  "departure_location": {"city_name": "Cannes", "latitude": 43.5528, "longitude": 7.0174},
  "passenger_count": 6,
  "start_date": "2025-07-10",
  "duration_hours": 5,
  "message": "Tour privé pour événement entreprise.",
  "client": 7,
  "status": "PENDING",
  "distance_km": 0.0,
  "created_at": "2025-05-01T11:00:00Z",
  "trip_type": "HOURLY"
}
```

---

## 3. Demande de navette gratuite (SHUTTLE)

### Création d'une demande
**Endpoint :** POST `/trips/requests/shuttle/`

```json
{
  "establishment": 3,
  "departure_location": {"city_name": "Antibes", "latitude": 43.5804, "longitude": 7.1251},
  "passenger_count": 2,
  "departure_date": "2025-08-20",
  "departure_time": "09:30:00",
  "message": "Navette pour l'hôtel."
}
```

**Exemple cURL :**
```sh
curl -X POST http://localhost:8000/trips/requests/shuttle/ \
  -H "Authorization: Token <client_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "establishment": 3,
    "departure_location": {"city_name": "Antibes", "latitude": 43.5804, "longitude": 7.1251},
    "passenger_count": 2,
    "departure_date": "2025-08-20",
    "departure_time": "09:30:00",
    "message": "Navette pour l'hôtel."
  }'
```

### Réponse attendue
```json
{
  "id": 789,
  "establishment": 3,
  "departure_location": {"city_name": "Antibes", "latitude": 43.5804, "longitude": 7.1251},
  "passenger_count": 2,
  "departure_date": "2025-08-20",
  "departure_time": "09:30:00",
  "message": "Navette pour l'hôtel.",
  "client": 7,
  "status": "PENDING",
  "distance_km": 2.8,
  "created_at": "2025-05-01T12:00:00Z",
  "trip_type": "SHUTTLE"
}
```

---

## 4. Génération et consultation des devis (SIMPLE & HOURLY)

**Endpoint :** GET `/trips/requests/<trip_request_id>/quotes/`

**Réponse :**
```json
[
  {
    "id": 1,
    "trip_request": 123,
    "captain": 21,
    "boat": 5,
    "base_price": 250.0,
    "is_available": true
  },
  {
    "id": 2,
    "trip_request": 123,
    "captain": 22,
    "boat": 6,
    "base_price": 300.0,
    "is_available": true
  }
]
```

**Exemple cURL :**
```sh
curl -X GET http://localhost:8000/trips/requests/123/quotes/ \
  -H "Authorization: Token <client_token>"
```

---

## 5. Le client accepte un devis (quote)
**Endpoint :** PATCH `/trips/quotes/<quote_id>/accept/`

```json
{
  "status": "ACCEPTED"
}
```

**Exemple cURL :**
```sh
curl -X PATCH http://localhost:8000/trips/quotes/1/accept/ \
  -H "Authorization: Token <client_token>" \
  -H "Content-Type: application/json" \
  -d '{"status": "ACCEPTED"}'
```

---

## 6. Le capitaine accepte ou refuse une demande qui lui est assignée
**Endpoint :** PATCH `/trips/requests/<trip_request_id>/`

```json
{
  "status": "ACCEPTED"
}
```
_ou_
```json
{
  "status": "REJECTED"
}
```

**Exemple cURL :**
```sh
curl -X PATCH http://localhost:8000/trips/requests/123/ \
  -H "Authorization: Token <captain_token>" \
  -H "Content-Type: application/json" \
  -d '{"status": "ACCEPTED"}'
```

---

## 7. Notification de changement de statut

```json
{
  "type": "TRIP_REQUEST_STATUS_UPDATE",
  "trip_request": 123,
  "status": "ACCEPTED",
  "message": "Votre demande a été acceptée par le capitaine."
}
```

---

# Notes
- Vérifiez les IDs, tokens, et types de bateau selon votre base de données.
- Les champs `created_at`, `distance_km`, etc., sont générés automatiquement.
- Pour les navettes, l'établissement doit exister et être référencé par son ID.
- Les endpoints et structures sont alignés avec les serializers et vues du codebase.
