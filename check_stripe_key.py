import os
import sys
import stripe
import django

# Configurer Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "commodore.settings")
django.setup()

from django.conf import settings

print("Vérification de la clé API Stripe...")
print(f"Clé configurée : {settings.STRIPE_SECRET_KEY[:10]}...{settings.STRIPE_SECRET_KEY[-4:]}")

# Vérifier si la clé fonctionne
stripe.api_key = settings.STRIPE_SECRET_KEY
# Utiliser la version standard de l'API sans spécifier de version spécifique
# stripe.api_version = "2025-04-30.basil"  # Commenté pour utiliser la version par défaut

try:
    # Essayer une opération simple qui devrait fonctionner avec n'importe quelle clé valide
    balance = stripe.Balance.retrieve()
    print("✅ La clé API est valide ! Voici le solde disponible :")
    print(balance)
except Exception as e:
    print(f"❌ Erreur lors de la vérification de la clé API : {str(e)}")
    print("Conseil : Vérifiez que la clé est correcte et qu'elle ne contient pas d'espaces ou de caractères indésirables.")
