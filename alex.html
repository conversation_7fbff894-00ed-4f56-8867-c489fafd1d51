<!DOCTYPE html>
<html>
<head>
  <title>Test Paiement Stripe</title>
  <script src="https://js.stripe.com/v3/"></script>
  <style>
    body { font-family: Arial; margin: 40px; }
    #card-element { border: 1px solid #ccc; padding: 10px; border-radius: 4px; }
    #result { margin-top: 20px; }
  </style>
</head>
<body>
  <h2>Paiement Test Stripe</h2>
  <form id="payment-form">
    <div id="card-element"></div>
    <button type="submit">Payer</button>
  </form>
  <div id="result"></div>

  <script>
    // Mets ici ta clé publique Stripe (pk_test_xxx)
    const stripe = Stripe('pk_test_51RQVnNDvUQ1WBV9hRdEnUswDzleJdgmRj7XAFAKwmTJT5iQ6mZoujYgeXXzwqJCcBroMEFcz4ExF7LQzH74vavpm00UjTJPGld');
    const elements = stripe.elements();
    const card = elements.create('card');
    card.mount('#card-element');

    // Mets ici ton client_secret reçu de ton API Commodore
    const clientSecret = 'pi_3RcgbWDvUQ1WBV9h2F9dxObp_secret_LjV6Gr1ozKdVtHM37cvf3EmAL';

    const form = document.getElementById('payment-form');
    const resultDiv = document.getElementById('result');

    form.addEventListener('submit', async (event) => {
      event.preventDefault();
      const {paymentIntent, error} = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: card,
          billing_details: { name: 'Test User' }
        }
      });
      if (error) {
        resultDiv.textContent = 'Erreur : ' + error.message;
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        resultDiv.textContent = 'Paiement réussi ! 🎉';
      } else {
        resultDiv.textContent = 'Statut : ' + paymentIntent.status;
      }
    });
  </script>
</body>
</html>