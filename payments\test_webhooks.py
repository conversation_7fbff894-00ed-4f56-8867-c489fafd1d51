from django.test import TestCase, RequestFactory
from django.urls import reverse
from unittest.mock import patch, MagicMock
from django.contrib.auth import get_user_model
from rest_framework.test import force_authenticate
from .views import PaymentViewSet
from .models import Payment
# Utiliser notre adaptateur pour le modèle Booking
from .models_rides_adapter import Booking, Ride
from accounts.models import Client as Passenger, Captain
# Adaptations pour les modèles qui n'existent pas dans la structure actuelle
class ImpactStatistics:
    pass

class AvailabilityCalendar:
    pass

User = get_user_model()

class WebhookTestCase(TestCase):
    """Tests pour les webhooks Stripe"""

    def setUp(self):
        """Configuration initiale pour les tests"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='SecurePassword123!'
        )
        self.impact_statistics = ImpactStatistics.objects.create()
        self.passenger = Passenger.objects.create(
            user=self.user,
            impact_statistics=self.impact_statistics
        )
        self.booking = Booking.objects.create(
            passenger=self.passenger,
            status='PENDING'
        )
        self.payment = Payment.objects.create(
            booking=self.booking,
            amount=100.0,
            type='TRIP',
            status='PENDING',
            stripe_payment_id='pi_test123'
        )
        
        # Créer un capitaine
        self.captain_user = User.objects.create_user(
            username='captainuser',
            email='<EMAIL>',
            password='SecurePassword123!'
        )
        self.availability_calendar = AvailabilityCalendar.objects.create()
        self.captain = Captain.objects.create(
            user=self.captain_user,
            availability=self.availability_calendar,
            stripe_connect_id='acct_test123'
        )
    
    @patch('payments.views.handle_webhook_event')
    def test_webhook_payment_intent_succeeded(self, mock_handle_webhook_event):
        """Test du webhook payment_intent.succeeded"""
        # Simuler l'événement webhook
        mock_event = MagicMock(
            id='evt_test123',
            type='payment_intent.succeeded',
            data=MagicMock(
                object=MagicMock(
                    id='pi_test123',
                    amount=10000,
                    metadata={
                        'payment_id': str(self.payment.id)
                    },
                    charges=MagicMock(
                        data=[
                            MagicMock(
                                receipt_url='https://receipt.url'
                            )
                        ]
                    ),
                    payment_method_types=['card']
                )
            )
        )
        mock_handle_webhook_event.return_value = mock_event
        
        # Créer la requête
        url = reverse('stripe-webhook')
        request = self.factory.post(
            url,
            data='{"id": "evt_test123"}',
            content_type='application/json',
            HTTP_STRIPE_SIGNATURE='t=123,v1=abc'
        )
        
        # Appeler la vue
        view = PaymentViewSet.as_view({'post': 'webhook'})
        with patch.object(PaymentViewSet, '_handle_payment_intent_succeeded') as mock_handle:
            response = view(request)
            mock_handle.assert_called_once_with(mock_event.data.object)
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 200)
    
    @patch('payments.views.handle_webhook_event')
    def test_webhook_payment_intent_failed(self, mock_handle_webhook_event):
        """Test du webhook payment_intent.payment_failed"""
        # Simuler l'événement webhook
        mock_event = MagicMock(
            id='evt_test123',
            type='payment_intent.payment_failed',
            data=MagicMock(
                object=MagicMock(
                    id='pi_test123',
                    metadata={
                        'payment_id': str(self.payment.id)
                    },
                    last_payment_error=MagicMock(
                        message='Your card was declined.',
                        code='card_declined'
                    )
                )
            )
        )
        mock_handle_webhook_event.return_value = mock_event
        
        # Créer la requête
        url = reverse('stripe-webhook')
        request = self.factory.post(
            url,
            data='{"id": "evt_test123"}',
            content_type='application/json',
            HTTP_STRIPE_SIGNATURE='t=123,v1=abc'
        )
        
        # Appeler la vue
        view = PaymentViewSet.as_view({'post': 'webhook'})
        with patch.object(PaymentViewSet, '_handle_payment_intent_failed') as mock_handle:
            response = view(request)
            mock_handle.assert_called_once_with(mock_event.data.object)
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 200)
    
    @patch('payments.views.handle_webhook_event')
    def test_webhook_checkout_session_completed(self, mock_handle_webhook_event):
        """Test du webhook checkout.session.completed"""
        # Simuler l'événement webhook
        mock_event = MagicMock(
            id='evt_test123',
            type='checkout.session.completed',
            data=MagicMock(
                object=MagicMock(
                    id='cs_test123',
                    payment_intent='pi_test123',
                    amount_total=10000,
                    metadata={
                        'payment_id': str(self.payment.id),
                        'booking_id': str(self.booking.id)
                    },
                    customer='cus_test123'
                )
            )
        )
        mock_handle_webhook_event.return_value = mock_event
        
        # Créer la requête
        url = reverse('stripe-webhook')
        request = self.factory.post(
            url,
            data='{"id": "evt_test123"}',
            content_type='application/json',
            HTTP_STRIPE_SIGNATURE='t=123,v1=abc'
        )
        
        # Appeler la vue
        view = PaymentViewSet.as_view({'post': 'webhook'})
        with patch.object(PaymentViewSet, '_handle_checkout_session_completed') as mock_handle:
            response = view(request)
            mock_handle.assert_called_once_with(mock_event.data.object)
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 200)
    
    @patch('payments.views.handle_webhook_event')
    def test_webhook_charge_refunded(self, mock_handle_webhook_event):
        """Test du webhook charge.refunded"""
        # Simuler l'événement webhook
        mock_event = MagicMock(
            id='evt_test123',
            type='charge.refunded',
            data=MagicMock(
                object=MagicMock(
                    id='ch_test123',
                    payment_intent='pi_test123',
                    amount=10000,
                    amount_refunded=10000,
                    refunds=MagicMock(
                        data=[
                            MagicMock(
                                id='re_test123'
                            )
                        ]
                    )
                )
            )
        )
        mock_handle_webhook_event.return_value = mock_event
        
        # Créer la requête
        url = reverse('stripe-webhook')
        request = self.factory.post(
            url,
            data='{"id": "evt_test123"}',
            content_type='application/json',
            HTTP_STRIPE_SIGNATURE='t=123,v1=abc'
        )
        
        # Appeler la vue
        view = PaymentViewSet.as_view({'post': 'webhook'})
        with patch.object(PaymentViewSet, '_handle_charge_refunded') as mock_handle:
            response = view(request)
            mock_handle.assert_called_once_with(mock_event.data.object)
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 200)
    
    @patch('payments.views.handle_webhook_event')
    def test_webhook_invalid_signature(self, mock_handle_webhook_event):
        """Test du webhook avec une signature invalide"""
        # Simuler une erreur de signature
        mock_handle_webhook_event.return_value = {'error': 'Signature invalide'}
        
        # Créer la requête
        url = reverse('stripe-webhook')
        request = self.factory.post(
            url,
            data='{"id": "evt_test123"}',
            content_type='application/json',
            HTTP_STRIPE_SIGNATURE='t=123,v1=abc'
        )
        
        # Appeler la vue
        view = PaymentViewSet.as_view({'post': 'webhook'})
        response = view(request)
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'error': 'Signature invalide'})
    
    @patch('payments.views.handle_webhook_event')
    def test_webhook_no_signature(self, mock_handle_webhook_event):
        """Test du webhook sans signature"""
        # Créer la requête
        url = reverse('stripe-webhook')
        request = self.factory.post(
            url,
            data='{"id": "evt_test123"}',
            content_type='application/json'
        )
        
        # Appeler la vue
        view = PaymentViewSet.as_view({'post': 'webhook'})
        response = view(request)
        
        # Vérifier la réponse
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'error': "Pas d'en-tête de signature Stripe"})
