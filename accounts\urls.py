from django.urls import path
from . import views
from .favorites_views import (
    FavoriteLocationListCreateView, FavoriteLocationDetailView,
    FavoriteCaptainListCreateView, FavoriteCaptainDetailView
)

app_name = 'accounts'

urlpatterns = [
    # PATCH séparés profils
    path('client/profile/', views.ClientProfilePatchView.as_view(), name='client-profile-patch'),
    path('captain/profile/', views.CaptainProfilePatchView.as_view(), name='captain-profile-patch'),
    path('establishment/profile/', views.EstablishmentProfilePatchView.as_view(), name='establishment-profile-patch'),
    # Profil utilisateur
    path('profile/', views.UserProfileView.as_view(), name='user-profile'),
    
    # Listes d'utilisateurs par type
    path('clients/', views.ClientListView.as_view(), name='client-list'),
    path('captains/', views.CaptainListView.as_view(), name='captain-list'),
    path('establishments/', views.EstablishmentListView.as_view(), name='establishment-list'),
    
    # Détails des utilisateurs par type
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user-detail'),
    path('captains/<int:pk>/', views.CaptainDetailView.as_view(), name='captain-detail'),
    path('establishments/<int:pk>/', views.EstablishmentDetailView.as_view(), name='establishment-detail'),
    
    # Gestion des favoris - Emplacements
    path('favorites/locations/', FavoriteLocationListCreateView.as_view(), name='favorite-locations'),
    path('favorites/locations/<int:pk>/', FavoriteLocationDetailView.as_view(), name='favorite-location-detail'),
    
    # Gestion des favoris - Capitaines
    path('favorites/captains/', FavoriteCaptainListCreateView.as_view(), name='favorite-captains'),
    path('favorites/captains/<int:pk>/', FavoriteCaptainDetailView.as_view(), name='favorite-captain-detail'),
]
