from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.conf import settings
import stripe
import json
import logging
from .models import Transaction, Wallet
# Importé localement dans les fonctions qui en ont besoin
# from notifications.services import create_payment_success_notification, create_payment_failed_notification
from trips.shared_payments import SharedPaymentInvitation, ShuttleBooking
from trips.models import Trip

logger = logging.getLogger(__name__)

# Configuration de Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY
webhook_secret = settings.STRIPE_WEBHOOK_SECRET

@csrf_exempt
@require_POST
def stripe_webhook(request):
    """
    Endpoint pour recevoir les webhooks de Stripe.
    Gère les événements asynchrones comme les paiements confirmés, échoués, remboursés, etc.
    """
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    
    try:
        logger.info(f"Received Stripe webhook event: {sig_header}")
        # Vérifier la signature du webhook
        event = stripe.Webhook.construct_event(
            payload, sig_header, webhook_secret
        )
    except ValueError as e:
        # Payload invalide
        logger.error(f"Webhook Error: {str(e)}")
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError as e:
        # Signature invalide
        logger.error(f"Webhook Signature Error: {str(e)}")
        return HttpResponse(status=400)
    
    # Traiter l'événement selon son type
    event_handler = WebhookEventHandler()
    response = event_handler.handle_event(event)
    
    return HttpResponse(status=200)

class WebhookEventHandler:
    """
    Classe pour gérer les différents types d'événements webhooks.
    """
    
    def handle_event(self, event):
        """
        Aiguille l'événement vers le handler approprié selon son type.
        """
        event_type = event['type']
        event_object = event['data']['object']
        
        handlers = {
            'payment_intent.succeeded': self.handle_payment_succeeded,
            'payment_intent.payment_failed': self.handle_payment_failed,
            'charge.refunded': self.handle_refund_succeeded,
            'checkout.session.completed': self.handle_checkout_completed,
        }
        
        handler = handlers.get(event_type, self.handle_default)
        return handler(event_object)
    
    def handle_payment_succeeded(self, payment_intent):
        """
        Gère les paiements réussis.
        """

        logger.info(f"Payment Intent information: {payment_intent}")

        metadata = payment_intent.get('metadata', {})
        transaction_id = metadata.get('transaction_id')

        logger.info(f"Processing payment succeeded for transaction_id: {transaction_id}")

        if not transaction_id:
            logger.error("No transaction_id found in payment intent metadata.")
            return False
        
        try:

            # Mettre à jour la transaction si elle existe déjà
            transaction = Transaction.objects.get(id=transaction_id)
            transaction.status = 'succeeded'

            logger.info(f"Transaction found:  {transaction}")
            
            if isinstance(transaction.metadata, dict):
                metadata = transaction.metadata
            elif isinstance(transaction.metadata, str):
                try:
                    metadata = json.loads(transaction.metadata)
                except json.JSONDecodeError:
                    metadata = {}
            else:
                metadata = {}

            wallet = transaction.wallet
            from django.db.models import F
            synchronisation_effectuee = False

            # Si c'est une recharge Stripe
            if metadata.get('transaction_type') == 'recharge':
                # Créer une transaction de crédit dans le portefeuille
                wallet_transaction = Transaction.objects.create(
                    wallet=wallet,
                    type=Transaction.TransactionType.CREDIT,
                    amount=transaction.amount,
                    description='Recharge du portefeuille via Stripe',
                    metadata={'stripe_transaction_id': transaction_id}
                )

                # Incrémenter le solde de façon atomique
                Wallet.objects.filter(pk=wallet.pk).update(balance=F('balance') + transaction.amount)
                wallet.refresh_from_db()
                synchronisation_effectuee = True
                logger.info(f"Recharge Stripe détectée pour Wallet {wallet.id}, synchronisation du solde...")
            else:
                # Si ce n'est pas une recharge, s'assurer que le solde est synchronisé
                wallet.refresh_from_db()
                logger.info(f"Synchronisation du solde Wallet/Profil pour Wallet {wallet.id} (hors recharge Stripe)...")
                synchronisation_effectuee = True

            # Synchroniser le champ wallet_balance des profils dans tous les cas
            try:
                wallet.update_profile_balance()
                logger.info(f"wallet_balance synchronisé pour Wallet {wallet.id} (solde: {wallet.balance}€)")
            except Exception as sync_err:
                logger.error(f"Erreur lors de la synchronisation wallet_balance: {sync_err}")

            # Mettre à jour le solde après transaction si c'était une recharge
            if metadata.get('transaction_type') == 'recharge':
                wallet_transaction.balance_after = wallet.balance
                wallet_transaction.save(update_fields=["balance_after"])

                logger.info(
                    f"Wallet {wallet.id} credited by {transaction.amount}€. New balance: {wallet.balance}€"
                )
            
            transaction.save()
            
            # Récupérer les métadonnées pour déterminer le type de paiement
            if isinstance(transaction.metadata, dict):
                metadata = transaction.metadata
            elif isinstance(transaction.metadata, str):
                try:
                    metadata = json.loads(transaction.metadata)
                except json.JSONDecodeError:
                    metadata = {}
            else:
                metadata = {}
            
            # Gérer les différents types de paiements
            if 'trip_id' in metadata:
                self._handle_trip_payment_success(transaction, metadata)
            elif 'shuttle_booking_id' in metadata:
                self._handle_shuttle_payment_success(transaction, metadata)
            elif 'shared_payment_invitation_id' in metadata:
                self._handle_shared_payment_success(transaction, metadata)
            elif 'maintenance_id' in metadata:
                self._handle_maintenance_payment_success(transaction, metadata)
            elif 'promotion_id' in metadata:
                self._handle_promotion_payment_success(transaction, metadata)
            
            # Notifier l'utilisateur
            # NotificationService.send_notification(
            #     user=transaction.user,
            #     title="Paiement confirmé",
            #     body=f"Votre paiement de {transaction.amount} € a été confirmé.",
            #     data={
            #         "type": "payment_success",
            #         "transaction_id": transaction_id
            #     }
            # )
            
        except Transaction.DoesNotExist:
            # La transaction n'existe pas encore, elle sera créée lors du retour du client
            pass
        
        return True
    
    def handle_payment_failed(self, payment_intent):
        """
        Gère les paiements échoués.
        """
        transaction_id = payment_intent.get('id')
        
        try:
            # Mettre à jour la transaction si elle existe déjà
            transaction = Transaction.objects.get(transaction_id=transaction_id)
            transaction.status = 'failed'
            transaction.save()
            
            # Récupérer les métadonnées pour déterminer le type de paiement
            metadata = json.loads(transaction.metadata) if transaction.metadata else {}
            
            # Notifier l'utilisateur
            NotificationService.send_notification(
                user=transaction.user,
                title="Paiement échoué",
                body=f"Votre paiement de {transaction.amount} € a échoué. Veuillez réessayer.",
                data={
                    "type": "payment_failed",
                    "transaction_id": transaction_id
                }
            )
            
        except Transaction.DoesNotExist:
            # La transaction n'existe pas encore
            pass
        
        return True
    
    def handle_refund_succeeded(self, charge):
        """
        Gère les remboursements réussis.
        """
        payment_intent_id = charge.get('payment_intent')
        
        if not payment_intent_id:
            return True
        
        try:
            # Trouver la transaction associée
            transaction = Transaction.objects.get(transaction_id=payment_intent_id)
            
            # Notifier l'utilisateur
            NotificationService.send_notification(
                user=transaction.user,
                title="Remboursement effectué",
                body=f"Un remboursement de {charge.get('amount_refunded') / 100} € a été effectué sur votre compte.",
                data={
                    "type": "refund_success",
                    "transaction_id": payment_intent_id
                }
            )
            
        except Transaction.DoesNotExist:
            # La transaction n'existe pas
            pass
        
        return True
    
    def handle_checkout_completed(self, session):
        """
        Gère les sessions de paiement Checkout complétées.
        """
        # Utiliser pour des paiements via Checkout plutôt que via PaymentIntent
        return True
    
    def handle_default(self, event_object):
        """
        Handler par défaut pour les événements non gérés spécifiquement.
        """
        return True
    
    def _handle_trip_payment_success(self, transaction, metadata):
        """
        Gère le succès d'un paiement de trajet individuel.
        """
        trip_id = metadata.get('trip_id')
        
        try:
            trip = Trip.objects.get(id=trip_id)
            
            # Mettre à jour le statut de paiement du trajet
            trip.payment_status = 'PAID'
            trip.amount_paid = transaction.amount
            trip.save()
            
            # Notifier le capitaine
            if trip.captain and trip.captain.user:
                NotificationService.send_notification(
                    user=trip.captain.user,
                    title="Nouvelle course payée",
                    body=f"Un client a payé une course de {transaction.amount} €.",
                    data={
                        "type": "trip_payment",
                        "trip_id": trip_id
                    }
                )
            
        except Trip.DoesNotExist:
            logger.error(f"Trip not found: {trip_id}")
    
    def _handle_shuttle_payment_success(self, transaction, metadata):
        """
        Gère le succès d'un paiement de réservation de navette.
        """
        booking_id = metadata.get('shuttle_booking_id')
        
        try:
            booking = ShuttleBooking.objects.get(id=booking_id)
            
            # Mettre à jour le statut de la réservation
            booking.status = 'PAID'
            booking.amount_paid = transaction.amount
            booking.transaction_id = transaction.transaction_id
            booking.save()
            
            # Notifier le capitaine de la navette
            if booking.shuttle.captain and booking.shuttle.captain.user:
                NotificationService.send_notification(
                    user=booking.shuttle.captain.user,
                    title="Nouvelle réservation de navette",
                    body=f"Un client a réservé {booking.number_of_seats} place(s) sur votre navette {booking.shuttle.route_name}.",
                    data={
                        "type": "shuttle_booking",
                        "booking_id": booking_id,
                        "shuttle_id": booking.shuttle.id
                    }
                )
            
        except ShuttleBooking.DoesNotExist:
            logger.error(f"Shuttle booking not found: {booking_id}")
    
    def _handle_shared_payment_success(self, transaction, metadata):
        """
        Gère le succès d'un paiement partagé.
        """
        invitation_id = metadata.get('shared_payment_invitation_id')
        
        try:
            invitation = SharedPaymentInvitation.objects.get(id=invitation_id)
            
            # Mettre à jour le statut de l'invitation
            invitation.status = 'PAID'
            invitation.save()
            
            # Mettre à jour le statut de paiement du trajet
            trip = invitation.trip
            trip.update_payment_status()
            
            # Notifier le créateur de l'invitation
            NotificationService.send_notification(
                user=invitation.inviter,
                title="Paiement partagé reçu",
                body=f"{invitation.invitee_email} a payé sa part ({transaction.amount} €) pour le trajet.",
                data={
                    "type": "shared_payment_success",
                    "invitation_id": invitation_id,
                    "trip_id": trip.id
                }
            )
            
        except SharedPaymentInvitation.DoesNotExist:
            logger.error(f"Shared payment invitation not found: {invitation_id}")
    
    def _handle_maintenance_payment_success(self, transaction, metadata):
        """
        Gère le succès d'un paiement de maintenance.
        """
        maintenance_id = metadata.get('maintenance_id')
        
        # Mettre à jour le statut de la maintenance dans l'application boats
        # Cette implémentation dépendra de la structure de l'application boats
        pass
    
    def _handle_promotion_payment_success(self, transaction, metadata):
        """
        Gère le succès d'un paiement pour une promotion.
        """
        promotion_id = metadata.get('promotion_id')
        
        # Mettre à jour le statut de la promotion
        # Cette implémentation dépendra de la structure du système de promotion
        pass
