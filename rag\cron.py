from django_cron import CronJobBase, Schedule
from datetime import timedelta
from django.utils import timezone
from rag.models import ChatMessage

class DeleteOldMessagesJob(CronJobBase):
    RUN_EVERY_MINS = 60 * 24  # tous les jours

    schedule = Schedule(run_every_mins=RUN_EVERY_MINS)
    code = 'rag.delete_old_messages'

    def do(self):
        limit_date = timezone.now() - timedelta(days=365)
        deleted, _ = ChatMessage.objects.filter(created_at__lt=limit_date).delete()
        print(f"{deleted} anciens messages supprimés")
