import os
import sys
import django
import random
from datetime import datetime, timedelta, timezone
from decimal import Decimal

# Configurer l'environnement Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

# Importer les modèles après avoir configuré Django
from django.contrib.auth import get_user_model
from django.db import transaction
from accounts.models import Client, Captain, Establishment
from trips.models import Trip, Location, Shuttle
from boats.models import Boat
from payments.models import Payment, Wallet, Transaction

User = get_user_model()

# Couleurs pour les logs
class Colors:
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.ENDC}")

def print_info(message):
    print(f"{Colors.BLUE}ℹ️ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️ {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.ENDC}")

@transaction.atomic
def seed_database():
    print_info("Début du seeding de la base de données...")
    
    # Supprimer les données existantes si elles existent
    print_info("Suppression des données existantes...")
    Payment.objects.all().delete()
    Transaction.objects.all().delete()
    Wallet.objects.all().delete()
    Trip.objects.all().delete()
    Shuttle.objects.all().delete()
    Boat.objects.all().delete()
    Location.objects.all().delete()
    Captain.objects.all().delete()
    Client.objects.all().delete()
    Establishment.objects.all().delete()
    User.objects.filter(is_superuser=False).delete()
    
    # Créer des utilisateurs
    print_info("Création des utilisateurs...")
    admin_user = User.objects.filter(is_superuser=True).first()
    
    if not admin_user:
        admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='Admin123!',
            is_active=True,
            email_verified=True
        )
        print_success("Utilisateur administrateur créé")
    
    # Créer des clients
    clients_data = [
        {'email': '<EMAIL>', 'first_name': 'Jean', 'last_name': 'Dupont'},
        {'email': '<EMAIL>', 'first_name': 'Marie', 'last_name': 'Martin'},
        {'email': '<EMAIL>', 'first_name': 'Pierre', 'last_name': 'Durand'},
    ]
    
    clients = []
    for data in clients_data:
        user = User.objects.create_user(
            email=data['email'],
            password='Password123!',
            first_name=data['first_name'],
            last_name=data['last_name'],
            type='CLIENT',
            is_active=True,
            email_verified=True
        )
        client = Client.objects.create(user=user)
        clients.append(client)
        print_success(f"Client créé: {data['first_name']} {data['last_name']}")
        
        # Créer un portefeuille pour chaque client s'il n'en a pas déjà un
        wallet, created = Wallet.objects.get_or_create(
            user=user,
            defaults={
                'balance': Decimal(str(random.randint(50, 200))),
                'currency': 'EUR',
                'loyalty_points': random.randint(0, 1000)
            }
        )
        if created:
            print_success(f"Portefeuille créé pour {data['first_name']} {data['last_name']} avec {wallet.balance} EUR")
        else:
            print_info(f"Portefeuille existant pour {data['first_name']} {data['last_name']} avec {wallet.balance} EUR")
    
    # Créer des capitaines
    captains_data = [
        {'email': '<EMAIL>', 'first_name': 'Michel', 'last_name': 'Leroy', 'experience': '10 ans en mer'},
        {'email': '<EMAIL>', 'first_name': 'Sophie', 'last_name': 'Bernard', 'experience': '7 ans de navigation'},
        {'email': '<EMAIL>', 'first_name': 'Antoine', 'last_name': 'Moreau', 'experience': '15 ans de taxi boat'},
    ]
    
    captains = []
    for data in captains_data:
        user = User.objects.create_user(
            email=data['email'],
            password='Password123!',
            first_name=data['first_name'],
            last_name=data['last_name'],
            type='CAPTAIN',
            is_active=True,
            email_verified=True
        )
        captain = Captain.objects.create(user=user, experience=data['experience'])
        captains.append(captain)
        print_success(f"Capitaine créé: {data['first_name']} {data['last_name']}")
        
        # Créer un portefeuille pour chaque capitaine s'il n'en a pas déjà un
        wallet, created = Wallet.objects.get_or_create(
            user=user,
            defaults={
                'balance': Decimal(str(random.randint(200, 500))),
                'currency': 'EUR',
                'loyalty_points': random.randint(0, 500)
            }
        )
        if created:
            print_success(f"Portefeuille créé pour {data['first_name']} {data['last_name']} avec {wallet.balance} EUR")
        else:
            print_info(f"Portefeuille existant pour {data['first_name']} {data['last_name']} avec {wallet.balance} EUR")
    
    # Créer des établissements
    establishments_data = [
        {'email': '<EMAIL>', 'name': 'Hôtel Riviera', 'address': '123 Promenade des Anglais, Nice', 'type': 'HOTEL'},
        {'email': '<EMAIL>', 'name': 'Restaurant La Plage', 'address': '45 Boulevard Jean Jaurès, Cannes', 'type': 'RESTAURANT'},
    ]
    
    establishments = []
    for data in establishments_data:
        user = User.objects.create_user(
            email=data['email'],
            password='Password123!',
            type='ESTABLISHMENT',
            is_active=True,
            email_verified=True
        )
        establishment = Establishment.objects.create(
            user=user,
            name=data['name'],
            address=data['address'],
            type=data['type']
        )
        establishments.append(establishment)
        print_success(f"Établissement créé: {data['name']}")
    
    # Créer des bateaux
    boats_data = [
        {'name': 'Blue Wave', 'registration_number': 'FR12345', 'capacity': 8, 'captain': captains[0]},
        {'name': 'Sea Adventure', 'registration_number': 'FR23456', 'capacity': 6, 'captain': captains[1]},
        {'name': 'Méditerranée', 'registration_number': 'FR34567', 'capacity': 10, 'captain': captains[2]},
    ]
    
    boats = []
    for data in boats_data:
        boat = Boat.objects.create(
            name=data['name'],
            registration_number=data['registration_number'],
            capacity=data['capacity'],
            captain=data['captain']
        )
        boats.append(boat)
        print_success(f"Bateau créé: {data['name']}")
    
    # Créer des localisations
    locations_data = [
        {'name': 'Port de Nice', 'address': 'Quai des Deux Emmanuel, 06300 Nice', 'coordinates': '43.6959,7.2741'},
        {'name': 'Port de Cannes', 'address': 'Jetée Albert Edouard, 06400 Cannes', 'coordinates': '43.5513,7.0128'},
        {'name': 'Port de Saint-Tropez', 'address': 'Quai de l\'Épi, 83990 Saint-Tropez', 'coordinates': '43.2721,6.6388'},
        {'name': 'Plage de Pampelonne', 'address': 'Route de Tahiti, 83350 Ramatuelle', 'coordinates': '43.2275,6.6608'},
    ]
    
    locations = []
    for data in locations_data:
        location = Location.objects.create(
            name=data['name'],
            address=data['address'],
            coordinates=data['coordinates']
        )
        locations.append(location)
        print_success(f"Localisation créée: {data['name']}")
    
    # Créer des trajets
    now = datetime.now(timezone.utc)
    
    trips_data = [
        {
            'client': clients[0],
            'captain': captains[0],
            'boat': boats[0],
            'pickup_location': locations[0],
            'dropoff_location': locations[2],
            'status': 'PENDING',
            'price': Decimal('150.00'),
            'scheduled_at': now + timedelta(hours=2),
        },
        {
            'client': clients[1],
            'captain': captains[1],
            'boat': boats[1],
            'pickup_location': locations[1],
            'dropoff_location': locations[3],
            'status': 'CONFIRMED',
            'price': Decimal('200.00'),
            'scheduled_at': now + timedelta(hours=5),
        },
        {
            'client': clients[2],
            'captain': captains[2],
            'boat': boats[2],
            'pickup_location': locations[2],
            'dropoff_location': locations[0],
            'status': 'PENDING',
            'price': Decimal('180.00'),
            'scheduled_at': now + timedelta(hours=8),
        },
    ]
    
    trips = []
    for data in trips_data:
        trip = Trip.objects.create(
            client=data['client'],
            captain=data['captain'],
            boat=data['boat'],
            pickup_location=data['pickup_location'],
            dropoff_location=data['dropoff_location'],
            status=data['status'],
            price=data['price'],
            scheduled_at=data['scheduled_at']
        )
        trips.append(trip)
        print_success(f"Trajet créé: {data['pickup_location'].name} → {data['dropoff_location'].name} pour {data['price']} EUR")
    
    # Créer des navettes
    shuttles_data = [
        {
            'name': 'Navette Journalière Nice-Saint-Tropez',
            'captain': captains[0],
            'boat': boats[0],
            'departure_location': locations[0],
            'arrival_location': locations[2],
            'price': Decimal('75.00'),
            'capacity': 8,
            'scheduled_at': now + timedelta(days=1),
        },
        {
            'name': 'Navette Express Cannes-Pampelonne',
            'captain': captains[1],
            'boat': boats[1],
            'departure_location': locations[1],
            'arrival_location': locations[3],
            'price': Decimal('60.00'),
            'capacity': 6,
            'scheduled_at': now + timedelta(days=2),
        },
    ]
    
    shuttles = []
    for data in shuttles_data:
        shuttle = Shuttle.objects.create(
            name=data['name'],
            captain=data['captain'],
            boat=data['boat'],
            departure_location=data['departure_location'],
            arrival_location=data['arrival_location'],
            price=data['price'],
            capacity=data['capacity'],
            scheduled_at=data['scheduled_at']
        )
        shuttles.append(shuttle)
        print_success(f"Navette créée: {data['name']} pour {data['price']} EUR")
    
    # Créer des paiements
    payments_data = [
        {
            'user': clients[0].user,
            'trip': trips[0],
            'amount': Decimal('150.00'),
            'status': 'PENDING',
            'type': 'TRIP',
        },
        {
            'user': clients[1].user,
            'trip': trips[1],
            'amount': Decimal('200.00'),
            'status': 'COMPLETED',
            'type': 'TRIP',
        },
    ]
    
    payments = []
    for data in payments_data:
        payment = Payment.objects.create(
            user=data['user'],
            trip=data['trip'],
            amount=data['amount'],
            status=data['status'],
            type=data['type'],
        )
        payments.append(payment)
        print_success(f"Paiement créé: {data['amount']} EUR pour {data['trip'].pickup_location.name} → {data['trip'].dropoff_location.name}")
    
    print_info("Fin du seeding. Base de données prête pour les tests!")
    print_info("\nInformations de connexion:")
    print_info("Admin: email=<EMAIL>, password=Admin123!")
    print_info("Client: email=<EMAIL>, password=Password123!")
    print_info("Capitaine: email=<EMAIL>, password=Password123!")
    
    # Afficher les IDs des trajets pour les tests de paiement
    print_info("\nIDs des trajets pour les tests de paiement:")
    for i, trip in enumerate(trips):
        print_info(f"Trajet {i+1}: ID={trip.id}, Statut={trip.status}, Prix={trip.price} EUR")
    
    print_info("\nIDs des navettes pour les tests de paiement:")
    for i, shuttle in enumerate(shuttles):
        print_info(f"Navette {i+1}: ID={shuttle.id}, Prix={shuttle.price} EUR")

if __name__ == "__main__":
    seed_database()
