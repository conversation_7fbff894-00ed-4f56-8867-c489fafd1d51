"""
Tests pour l'application boatman.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from decimal import Decimal
from unittest.mock import patch
from django.utils import timezone

from accounts.models import Captain, Client
from boats.models import Boat
from trips.models import Trip
from payments.models import Wallet, Payment

User = get_user_model()


class BoatmanAuthTestCase(APITestCase):
    """Tests pour l'authentification des bateliers"""
    
    def setUp(self):
        """Configuration initiale pour les tests"""
        # Créer un utilisateur capitaine
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Captain',
            last_name='Jack',
            phone_number='+***********',
            is_captain=True
        )
        
        # Créer le profil capitaine
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans d\'expérience',
            license_number='LIC001',
            is_available=True,
            availability_status='AVAILABLE'
        )
        
        # Créer un bateau
        self.boat = Boat.objects.create(
            captain=self.captain,
            name='Test Boat',
            boat_type='classic',
            capacity=8
        )
        
        self.client = APIClient()

    def test_captain_login_success(self):
        """Test de connexion réussie d'un capitaine"""
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = self.client.post('/api/boatman/login/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('token', response.data['data'])
        self.assertEqual(response.data['data']['captain_id'], str(self.captain.user.id))  # Captain utilise user comme clé primaire

    def test_captain_login_invalid_credentials(self):
        """Test de connexion avec identifiants invalides"""
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post('/api/boatman/login/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn('Identifiants invalides', response.data['error'])

    def test_non_captain_login_denied(self):
        """Test de connexion refusée pour un non-capitaine"""
        # Créer un utilisateur client
        client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = self.client.post('/api/boatman/login/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('Compte batelier requis', response.data['error'])

    @patch('boatman.views_auth.send_mail')
    def test_forgot_password_email(self, mock_send_mail):
        """Test de mot de passe oublié par email"""
        mock_send_mail.return_value = True
        
        data = {
            'contact': '<EMAIL>'
        }
        
        response = self.client.post('/api/boatman/forgot-password/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['contact_method'], 'email')
        mock_send_mail.assert_called_once()

    def test_forgot_password_phone(self):
        """Test de mot de passe oublié par téléphone"""
        data = {
            'contact': '+***********'
        }
        
        response = self.client.post('/api/boatman/forgot-password/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['contact_method'], 'sms')


class BoatmanDashboardTestCase(APITestCase):
    """Tests pour le tableau de bord des bateliers"""
    
    def setUp(self):
        """Configuration initiale"""
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Captain',
            last_name='Jack',
            is_captain=True
        )
        
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans',
            license_number='LIC001',
            is_available=True,
            availability_status='AVAILABLE'
        )
        
        # Créer un portefeuille
        self.wallet = Wallet.objects.create(
            user=self.captain_user,
            balance=Decimal('150.00')
        )
        
        self.token = Token.objects.create(user=self.captain_user)
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

    def test_dashboard_access(self):
        """Test d'accès au tableau de bord"""
        response = self.client.get('/api/boatman/dashboard/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('captain', response.data['data'])
        self.assertIn('financial_summary', response.data['data'])
        self.assertIn('trip_statistics', response.data['data'])

    def test_dashboard_unauthorized(self):
        """Test d'accès non autorisé au tableau de bord"""
        # Créer un utilisateur client
        client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        client_token = Token.objects.create(user=client_user)
        
        # Essayer d'accéder avec un token client
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + client_token.key)
        response = self.client.get('/api/boatman/dashboard/')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_availability_update(self):
        """Test de mise à jour de la disponibilité"""
        data = {
            'availability_status': 'BUSY'
        }
        
        response = self.client.post('/api/boatman/availability/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['availability_status'], 'BUSY')
        
        # Vérifier que le capitaine a été mis à jour
        self.captain.refresh_from_db()
        self.assertEqual(self.captain.availability_status, 'BUSY')


class BoatmanShuttlesTestCase(APITestCase):
    """Tests pour la gestion des courses"""
    
    def setUp(self):
        """Configuration initiale"""
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_captain=True
        )
        
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans',
            license_number='LIC001'
        )
        
        self.boat = Boat.objects.create(
            captain=self.captain,
            name='Test Boat',
            capacity=8
        )
        
        # Créer un client
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.client_profile = Client.objects.create(user=self.client_user)
        
        # Créer une course
        self.trip = Trip.objects.create(
            client=self.client_profile,
            captain=self.captain,
            boat=self.boat,
            trip_type='NAVETTES_GRATUITES',
            start_location='Aéroport',
            end_location='Hotel Paradise',
            scheduled_start_time=timezone.now() + timezone.timedelta(hours=1),
            passenger_count=2,
            status='ACCEPTED'
        )
        
        self.token = Token.objects.create(user=self.captain_user)
        self.api_client = APIClient()
        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

    def test_shuttles_list(self):
        """Test de récupération de la liste des courses"""
        response = self.api_client.get('/api/boatman/shuttles/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('shuttles', response.data['data'])
        self.assertEqual(len(response.data['data']['shuttles']), 1)

    def test_shuttle_detail(self):
        """Test de récupération des détails d'une course"""
        response = self.api_client.get(f'/api/boatman/shuttle/{self.trip.id}/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['shuttle_id'], str(self.trip.id))

    def test_start_shuttle(self):
        """Test de démarrage d'une course"""
        # Mettre la course dans le futur proche pour pouvoir la démarrer
        self.trip.scheduled_start_time = timezone.now() + timezone.timedelta(minutes=15)
        self.trip.save()
        
        response = self.api_client.post(f'/api/boatman/shuttle/{self.trip.id}/start/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['status'], 'IN_PROGRESS')
        
        # Vérifier que la course a été mise à jour
        self.trip.refresh_from_db()
        self.assertEqual(self.trip.status, 'IN_PROGRESS')

    def test_complete_shuttle(self):
        """Test de fin d'une course"""
        # Mettre la course en cours
        self.trip.status = 'IN_PROGRESS'
        self.trip.actual_start_time = timezone.now()
        self.trip.save()
        
        response = self.api_client.post(f'/api/boatman/shuttle/{self.trip.id}/end/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['status'], 'COMPLETED')
        
        # Vérifier que la course a été mise à jour
        self.trip.refresh_from_db()
        self.assertEqual(self.trip.status, 'COMPLETED')


class BoatmanWalletTestCase(APITestCase):
    """Tests pour le portefeuille des bateliers"""
    
    def setUp(self):
        """Configuration initiale"""
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_captain=True
        )
        
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans',
            license_number='LIC001'
        )
        
        self.wallet = Wallet.objects.create(
            user=self.captain_user,
            balance=Decimal('250.00')
        )
        
        self.token = Token.objects.create(user=self.captain_user)
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

    def test_wallet_view(self):
        """Test de consultation du portefeuille"""
        response = self.client.get('/api/boatman/wallet/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('wallet', response.data['data'])
        self.assertEqual(response.data['data']['wallet']['available_balance'], 250.0)

    def test_withdraw_funds_success(self):
        """Test de retrait de fonds réussi"""
        # Créer une méthode de paiement
        from payments.models import PaymentMethod
        PaymentMethod.objects.create(
            user=self.captain_user,
            type='CARD',
            cardholder_name='Captain Jack',
            card_last_four='1234',
            is_active=True
        )
        
        data = {
            'amount': 100.00
        }
        
        response = self.client.post('/api/boatman/wallet/withdraw/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['amount'], 100.0)
        self.assertEqual(response.data['data']['new_balance'], 150.0)

    def test_withdraw_insufficient_funds(self):
        """Test de retrait avec fonds insuffisants"""
        data = {
            'amount': 500.00  # Plus que le solde disponible
        }
        
        response = self.client.post('/api/boatman/wallet/withdraw/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Solde insuffisant', response.data['error'])

    def test_withdraw_no_payment_method(self):
        """Test de retrait sans méthode de paiement"""
        data = {
            'amount': 50.00
        }
        
        response = self.client.post('/api/boatman/wallet/withdraw/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('méthode de paiement', response.data['error'])


class BoatmanProfileTestCase(APITestCase):
    """Tests pour la gestion du profil"""
    
    def setUp(self):
        """Configuration initiale"""
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Captain',
            last_name='Jack',
            phone_number='+***********',
            is_captain=True
        )
        
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans d\'expérience',
            license_number='LIC001'
        )
        
        self.token = Token.objects.create(user=self.captain_user)
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

    def test_profile_view(self):
        """Test de consultation du profil"""
        response = self.client.get('/api/boatman/profile/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('personal_info', response.data['data'])
        self.assertIn('professional_info', response.data['data'])

    def test_profile_update(self):
        """Test de mise à jour du profil"""
        data = {
            'first_name': 'NewName',
            'experience': 'Nouvelle expérience'
        }
        
        response = self.client.patch('/api/boatman/profile/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('first_name', response.data['data']['updated_fields'])
        
        # Vérifier que les données ont été mises à jour
        self.captain_user.refresh_from_db()
        self.captain.refresh_from_db()
        self.assertEqual(self.captain_user.first_name, 'NewName')
        self.assertEqual(self.captain.experience, 'Nouvelle expérience')
