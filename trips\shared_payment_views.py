from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timedelta
from django.db import transaction
from django.conf import settings
import requests
import uuid

from .models import Trip, Shuttle
from .shared_payments import SharedPaymentInvitation, ShuttleBooking
from .shared_payments_serializers import SharedPaymentInvitationSerializer, ShuttleBookingSerializer
from accounts.models import Client
from notifications.services import NotificationService
from payments.services import PaymentService

class SharedPaymentInvitationView(APIView):
    """
    Vue pour créer et gérer les invitations de paiement partagé.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, trip_id):
        """Crée une nouvelle invitation de paiement partagé (production: invitation unique par trip/invité tant qu'elle n'est pas expirée ou payée)."""
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur a le droit de partager ce trajet
        if trip.client.user != request.user:
            return Response(
                {"error": "Vous n'êtes pas autorisé à partager ce trajet."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        invitee_email = request.data.get("invitee_email")
        amount = request.data.get("amount")
        # Recherche d'une invitation active (ni expirée ni payée)
        existing_invitation = SharedPaymentInvitation.objects.filter(
            trip=trip,
            invitee_email=invitee_email,
            status__in=["PENDING", "ACCEPTED"]
        ).order_by('-created_at').first()
        if existing_invitation:
            return Response(
                {
                    "message": "Une invitation active existe déjà pour ce trajet et cet invité.",
                    "invitation": SharedPaymentInvitationSerializer(existing_invitation).data
                },
                status=status.HTTP_200_OK
            )
        
        # Préparer les données pour l'invitation
        expires_at = timezone.now() + timedelta(days=3)  # L'invitation expire dans 3 jours
        invitation_data = {
            "trip": trip.id,
            "inviter": request.user.id,
            "invitee_email": invitee_email,
            "amount": amount,
            "message": request.data.get("message", ""),
            "expires_at": expires_at
        }
        
        serializer = SharedPaymentInvitationSerializer(data=invitation_data)
        if serializer.is_valid():
            invitation = serializer.save()
            
            # Envoyer une notification à l'invité s'il est inscrit
            try:
                client = Client.objects.get(user__email=invitation.invitee_email)
                invitation.invitee = client.user
                invitation.save()
                
                # Envoyer une notification in-app
                NotificationService.send_notification(
                    user=client.user,
                    title="Invitation à partager un paiement",
                    body=f"{request.user.get_full_name()} vous invite à partager le paiement d'un trajet",
                    data={
                        "type": "shared_payment_invitation",
                        "invitation_id": invitation.id,
                        "trip_id": trip.id,
                        "token": invitation.token
                    }
                )
            except Client.DoesNotExist:
                # Envoyer un email d'invitation
                # Utilisation d'un service d'email à implémenter
                pass
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def get(self, request, token=None):
        """Récupère les détails d'une invitation par son token."""
        if token:
            invitation = get_object_or_404(SharedPaymentInvitation, token=token)
            serializer = SharedPaymentInvitationSerializer(invitation)
            return Response(serializer.data)
        
        # Si pas de token, renvoyer toutes les invitations reçues par l'utilisateur
        invitations = SharedPaymentInvitation.objects.filter(
            invitee=request.user,
            status__in=['PENDING', 'ACCEPTED']
        )
        serializer = SharedPaymentInvitationSerializer(invitations, many=True)
        return Response(serializer.data)

class SharedPaymentProcessView(APIView):
    """
    Vue pour traiter un paiement partagé.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, trip_id):
        """Traite un paiement partagé pour un trajet."""
        # Vérifier que l'invitation existe et est valide
        invitation_token = request.data.get("invitation_token")
        if not invitation_token:
            return Response(
                {"error": "Token d'invitation requis."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        invitation = get_object_or_404(
            SharedPaymentInvitation, 
            token=invitation_token,
            trip_id=trip_id,
            status__in=['PENDING', 'ACCEPTED']
        )
        
        # Vérifier que l'invitation n'a pas expiré
        if invitation.expires_at < timezone.now():
            invitation.status = 'EXPIRED'
            invitation.save()
            return Response(
                {"error": "Cette invitation a expiré."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Procéder au paiement via le service de paiement
        payment_method_id = request.data.get("payment_method_id")
        
        try:
            with transaction.atomic():
                # Effectuer le paiement
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=invitation.amount,
                    payment_method_id=payment_method_id,
                    description=f"Paiement partagé pour le trajet #{invitation.trip.id}"
                )
                
                # Mettre à jour le statut de l'invitation
                invitation.status = 'PAID'
                invitation.save()
                
                # Mettre à jour le statut de paiement du trajet
                trip = invitation.trip
                trip.update_payment_status()
                
                # Construire la réponse détaillée
                response_data = {
                    "id": payment_result.get("id"),
                    "amount": invitation.amount,
                    "currency": "eur",
                    "status": "succeeded",
                    "created_at": timezone.now().isoformat(),
                    "ride": {
                        "id": trip.id,
                        "total_amount": trip.total_price,
                        "amount_paid": trip.amount_paid,
                        "amount_remaining": trip.total_price - trip.amount_paid,
                        "payment_status": trip.payment_status,
                        "participants": trip.get_payment_participants()
                    }
                }
                
                # Notifier le créateur du trajet
                NotificationService.send_notification(
                    user=invitation.inviter,
                    title="Paiement partagé reçu",
                    body=f"{request.user.get_full_name()} a payé sa part ({invitation.amount}€) pour le trajet",
                    data={
                        "type": "shared_payment_completed",
                        "trip_id": trip.id,
                        "amount": str(invitation.amount),
                        "invitation_id": invitation.id
                    }
                )
                
                return Response(response_data, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class ShuttleBookingView(APIView):
    """
    Vue pour gérer les réservations de navettes.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, shuttle_id):
        """Crée une nouvelle réservation de navette."""
        shuttle = get_object_or_404(Shuttle, id=shuttle_id)
        
        # Vérifier que l'utilisateur a un profil client
        try:
            client = request.user.client
        except:
            return Response(
                {"error": "Seuls les clients peuvent réserver des places sur une navette."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Préparer les données pour la réservation
        booking_data = {
            "shuttle": shuttle.id,
            "client": client.id,
            "number_of_seats": request.data.get("seats", 1),
            "passenger_names": request.data.get("passenger_names", []),
            "special_requests": request.data.get("special_requests", "")
        }
        
        serializer = ShuttleBookingSerializer(data=booking_data)
        if serializer.is_valid():
            # Créer la réservation
            booking = serializer.save()
            
            # Mettre à jour le nombre de places disponibles sur la navette
            shuttle.current_bookings += booking.number_of_seats
            shuttle.save()
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def get(self, request, booking_reference=None):
        """Récupère les détails d'une réservation."""
        if booking_reference:
            booking = get_object_or_404(ShuttleBooking, booking_reference=booking_reference)
            
            # Vérifier que l'utilisateur a le droit de voir cette réservation
            if booking.client.user != request.user:
                return Response(
                    {"error": "Vous n'êtes pas autorisé à voir cette réservation."},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            serializer = ShuttleBookingSerializer(booking)
            return Response(serializer.data)
        
        # Si pas de référence, renvoyer toutes les réservations de l'utilisateur
        bookings = ShuttleBooking.objects.filter(client__user=request.user)
        serializer = ShuttleBookingSerializer(bookings, many=True)
        return Response(serializer.data)

class ShuttlePaymentView(APIView):
    """
    Vue pour traiter le paiement d'une réservation de navette.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, shuttle_id):
        """Traite le paiement d'une réservation de navette."""
        shuttle = get_object_or_404(Shuttle, id=shuttle_id)
        
        # Vérifier que l'utilisateur a un profil client
        try:
            client = request.user.client
        except:
            return Response(
                {"error": "Seuls les clients peuvent réserver des places sur une navette."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Récupérer les informations de paiement
        payment_method_id = request.data.get("payment_method_id")
        seats = request.data.get("seats", 1)
        passenger_ids = request.data.get("passenger_ids", [])
        
        # Vérifier la disponibilité des places
        available_seats = shuttle.max_capacity - shuttle.current_bookings
        if seats > available_seats:
            return Response(
                {"error": f"Il n'y a que {available_seats} places disponibles sur cette navette."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Calculer le montant du paiement
        amount = shuttle.price_per_person * seats
        
        try:
            with transaction.atomic():
                # Créer la réservation
                booking = ShuttleBooking.objects.create(
                    shuttle=shuttle,
                    client=client,
                    number_of_seats=seats,
                    passenger_names=request.data.get("passenger_names", []),
                    special_requests=request.data.get("special_requests", ""),
                    status="RESERVED"
                )
                
                # Effectuer le paiement
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=amount,
                    payment_method_id=payment_method_id,
                    description=f"Réservation de {seats} place(s) sur la navette {shuttle.route_name}"
                )
                
                # Mettre à jour la réservation
                booking.status = "PAID"
                booking.amount_paid = amount
                booking.transaction_id = payment_result.get("id")
                booking.payment_method = "card"  # ou wallet selon le cas
                booking.save()
                
                # Mettre à jour le nombre de places sur la navette
                shuttle.current_bookings += seats
                shuttle.save()
                
                # Assigner des numéros de siège
                seat_numbers = shuttle.assign_seats(seats)
                booking.seat_numbers = seat_numbers
                booking.save()
                
                # Obtenir le solde du portefeuille
                wallet_balance = PaymentService.get_wallet_balance(request.user)
                
                # Construire la réponse détaillée
                response_data = {
                    "id": payment_result.get("id"),
                    "amount": amount,
                    "currency": "eur",
                    "status": "succeeded",
                    "created_at": timezone.now().isoformat(),
                    "payment_method": {
                        "id": payment_method_id,
                        "type": "card",
                        "card": payment_result.get("card", {})
                    },
                    "receipt_url": f"https://api.commodore.com/api/payments/receipts/{payment_result.get('id')}/",
                    "shuttle": {
                        "id": shuttle.id,
                        "name": shuttle.route_name,
                        "departure": shuttle.start_location,
                        "arrival": shuttle.end_location,
                        "departure_time": shuttle.departure_time.isoformat(),
                        "price_per_seat": shuttle.price_per_person,
                        "total_seats": shuttle.max_capacity,
                        "available_seats": shuttle.max_capacity - shuttle.current_bookings
                    },
                    "seats": seats,
                    "seat_numbers": seat_numbers,
                    "wallet": {
                        "previous_balance": wallet_balance + amount,
                        "current_balance": wallet_balance
                    }
                }
                
                # Envoyer un email de confirmation
                # Utilisation d'un service d'email à implémenter
                
                return Response(response_data, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
