from django.shortcuts import render
from django.conf import settings
from django.contrib.auth.decorators import login_required

@login_required
def test_payment_view(request):
    """
    Vue pour tester les paiements Stripe.

    Cette vue affiche une page HTML permettant de tester les paiements Stripe
    et de mettre à jour manuellement le statut des paiements.
    """
    context = {
        'stripe_public_key': settings.STRIPE_PUBLISHABLE_KEY,
    }
    return render(request, 'payments/test_payment.html', context)
