"""
Module contenant des adaptateurs pour faire fonctionner le code existant avec la structure de modèles du projet Commodore.
"""

from accounts.models import User, Client, Captain
from trips.models import Trip, Shuttle
from django.db import models

class CreditWallet:
    """
    Adaptateur pour la gestion des portefeuilles d'utilisateurs.
    Dans ce projet, les utilisateurs ont directement un champ wallet_balance.
    """
    objects = None  # Pour simuler un gestionnaire d'objets
    
    def __init__(self, user_instance):
        self.user = user_instance
        self.id = f"wallet_{user_instance.id}"
        
    @property
    def balance(self):
        """Retourne le solde du portefeuille de l'utilisateur"""
        if hasattr(self.user, 'client') and self.user.client:
            return self.user.client.wallet_balance
        elif hasattr(self.user, 'captain') and self.user.captain:
            return self.user.captain.wallet_balance
        elif hasattr(self.user, 'establishment') and self.user.establishment:
            return self.user.establishment.wallet_balance
        return 0
        
    @staticmethod
    def get(id=None):
        """
        Méthode statique pour récupérer un portefeuille par son ID.
        
        Args:
            id: L'ID du portefeuille à récupérer
            
        Returns:
            Une instance de CreditWallet
        """
        if id and isinstance(id, str) and id.startswith('wallet_'):
            user_id = id.split('_')[1]
        else:
            user_id = id
            
        try:
            user = User.objects.get(id=user_id)
            return CreditWallet(user)
        except User.DoesNotExist:
            raise Exception(f"Utilisateur {user_id} non trouvé")

# Attacher la méthode get au gestionnaire d'objets simulé
CreditWallet.objects = type('CreditWalletManager', (), {'get': CreditWallet.get})


def get_trip_from_booking_id(booking_id):
    """
    Récupère un Trip à partir d'un ID de booking.
    
    Args:
        booking_id: L'ID du booking à récupérer
        
    Returns:
        Une instance de Trip
    """
    try:
        return Trip.objects.get(id=booking_id)
    except Trip.DoesNotExist:
        raise Trip.DoesNotExist(f"Trip {booking_id} non trouvé")
