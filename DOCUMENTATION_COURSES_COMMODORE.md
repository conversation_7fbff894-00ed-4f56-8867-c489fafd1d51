# Guide Complet des Types de Courses dans Commodore

## 1. Course Simple (COURSE_SIMPLE / SIMPLE)

### Description

Une course simple est un trajet direct d'un point A à un point B, avec un départ immédiat ou programmé à une date/heure précise.

### Payload complet pour créer une course simple

```json
{
  "departure_location": {
    "city_name": "Port de Cannes",
    "coordinates": {
      "latitude": 43.5528,
      "longitude": 7.0174
    },
    "timestamp": "2025-06-11T06:38:10+02:00"
  },
  "arrival_location": {
    "city_name": "Îles de Lérins",
    "coordinates": {
      "latitude": 43.5184,
      "longitude": 7.0457
    },
    "timestamp": "2025-06-11T06:38:10+02:00"
  },
  "passenger_count": 4,
  "boat_type": "CLASSIC",
  "scheduled_date": "2025-06-15", // Optionnel
  "scheduled_time": "14:30:00" // Optionnel
}
```

### Contraintes techniques

- La structure de `departure_location` et `arrival_location` doit contenir `city_name`, `coordinates` (avec `latitude` et `longitude`) et `timestamp`
- `passenger_count` doit être entre 1 et 50
- `boat_type` doit être l'une des valeurs disponibles dans les types de bateaux (`CLASSIC`, `LUXU', etc..)
- Si `scheduled_date` est spécifié, il doit être dans le futur
- En absence de `scheduled_date` et `scheduled_time`, la course est considérée comme immédiate

### Workflow complet détaillé

#### 1. Création de la demande

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/requests/simple/`
- **Payload** : Voir ci-dessus
- **Réponse** :
  ```json
  {
    "trip_request": {
      "id": 123,
      "client": {
        "id": 45,
        "user": {
          "id": 67,
          "first_name": "Jean",
          "last_name": "Dupont",
          "email": "<EMAIL>"
        }
      },
      "departure_location": {
        "city_name": "Port de Cannes",
        "coordinates": {
          "latitude": 43.5528,
          "longitude": 7.0174
        },
        "timestamp": "2025-06-11T06:38:10+02:00"
      },
      "arrival_location": {
        "city_name": "Îles de Lérins",
        "coordinates": {
          "latitude": 43.5184,
          "longitude": 7.0457
        },
        "timestamp": "2025-06-11T06:38:10+02:00"
      },
      "passenger_count": 4,
      "boat_type": "CLASSIC",
      "scheduled_date": "2025-06-15",
      "scheduled_time": "14:30:00",
      "created_at": "2025-06-11T06:38:10+02:00",
      "updated_at": "2025-06-11T06:38:10+02:00",
      "expires_at": "2025-06-11T06:48:10+02:00",
      "distance_km": 3.42,
      "status": "PENDING",
      "trip_type": "SIMPLE"
    },
    "quotes": [
      {
        "id": 234,
        "captain": {
          "id": 56,
          "user": {
            "id": 78,
            "first_name": "Pierre",
            "last_name": "Martin"
          }
        },
        "boat": {
          "id": 89,
          "name": "Ocean Breeze",
          "boat_type": "CLASSIC",
          "capacity": 8
        },
        "base_price": 34.2,
        "distance_km": 3.42,
        "rate_used": 10.0,
        "captain_name": "Pierre Martin",
        "captain_rating": 4.5,
        "boat_name": "Ocean Breeze",
        "boat_capacity": 8,
        "is_available": true,
        "created_at": "2025-06-11T06:38:10+02:00"
      }
    ]
  }
  ```
- **Code de statut** : 201 Created

#### 2. Génération automatique de devis

- **Acteur** : Système
- **Processus** :
  - Calcule la distance entre les points de départ et d'arrivée avec la formule de Haversine
  - Identifie les capitaines disponibles avec un bateau du type demandé et une capacité suffisante
  - Calcule le prix pour chaque capitaine en fonction de son tarif par km (`base_price = distance_km * rate_per_km`)
  - Génère des devis pour chaque capitaine disponible

#### 3. Notification des capitaines

- **Acteur** : Système
- **Processus** : Une notification est créée pour chaque capitaine ayant reçu un devis
- **Contenu** : Les capitaines reçoivent une notification avec les détails de la demande, y compris lieux de départ/arrivée et prix proposé

#### 4. Choix du devis par le client

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/quotes/{quote_id}/choose/`
- **Payload** :
  ```json
  {
    "message": "Message optionnel pour le capitaine"
  }
  ```
- **Réponse** :
  ```json
  {
    "success": true,
    "message": "Demande envoyée au capitaine",
    "trip": {
      "id": 345,
      "client": {
        "id": 45,
        "user": {
          "id": 67,
          "first_name": "Jean",
          "last_name": "Dupont"
        }
      },
      "captain": {
        "id": 56,
        "user": {
          "id": 78,
          "first_name": "Pierre",
          "last_name": "Martin"
        }
      },
      "boat": {
        "id": 89,
        "name": "Ocean Breeze",
        "boat_type": "CLASSIC"
      },
      "start_location": "Port de Cannes",
      "end_location": "Îles de Lérins",
      "scheduled_start_time": "2025-06-15T14:30:00+02:00",
      "scheduled_end_time": "2025-06-15T15:30:00+02:00",
      "passenger_count": 4,
      "base_price": 34.2,
      "total_price": 34.2,
      "status": "PENDING",
      "payment_status": "PENDING"
    },
    "next_step": "Attendez la réponse du capitaine (délai: 10 minutes)"
  }
  ```
- **Code de statut** : 201 Created
- **Effet secondaire** : Tous les autres devis pour cette demande sont marqués comme non disponibles

#### 5. Acceptation par le capitaine

- **Acteur** : Capitaine
- **Endpoint** : `POST /api/trips/{trip_id}/accept/`
- **Réponse** :
  ```json
  {
    "success": true,
    "message": "Course acceptée",
    "trip": {
      "id": 345,
      "status": "ACCEPTED",
      "scheduled_start_time": "2025-06-15T14:30:00+02:00",
      "scheduled_end_time": "2025-06-15T15:30:00+02:00"
    },
    "next_step": "Le client doit effectuer le paiement"
  }
  ```
- **Code de statut** : 200 OK
- **Effet secondaire** : Une notification est envoyée au client

#### 6. Paiement par le client

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/{trip_id}/payment/`
- **Payload** :
  ```json
  {
    "payment_method": "card", // Options: "card" ou "wallet"
    "payment_method_id": "pm_123" // ID de la méthode de paiement Stripe ou null pour wallet
  }
  ```
- **Réponse** :
  ```json
  {
    "message": "Paiement effectué avec succès",
    "payment_id": "pi_3RZ5T5D6Ags5eCCM1Ior82LR",
    "trip_id": 30,
    "amount_paid": 18366.3,
    "payment_method": "card",
    "trip_status": "ACCEPTED",
    "payment_status": "PAID",
    "qr_code": "COMMODORE_TRIP_30_b138fd5165cb3f61",
    "qr_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAXIAAAFyAQAAAADAX2ykAAAC0ElEQVR4nO1by43jMAwlaQNztIEtYEpROltsSdNBXEoKGMA+BlDABSnJcbyA5BySlcd8h8Qx34EA8fiRGGR4BgM9RQcwfh5UsK9h/DyoYF/D+HlQwb6G8fOggn0N4x+BjxEt4Glqw0+ACRFP8qE4vdGf7aAnuAfmOxaM8th54HPHjCdopPlq1MR1+78ZdDB+G7+nHsB9AfDQf7fsQqSvyDABIEDj3+QPGf8l8U1A6ORr6jXSgBLzmv0n4z/F53PKyrHgFvglkPGr0G8nQZ2kEl8wpGZwX9HIFftPxt/CH7RF7gFCV+UuH0HEAHAL7fMjfzPI+BXolx9fMsAVYfi8YhT2+/wh479i/j1NiCpiPus43M+EqQUY+pvNv/vkw2K4dewB3KhZWQ1eJuE4GYUp+Fyb/2T8PDjErdPzjYZVv5KV09HG+MirzX8y/hb9uhC+qOT4bozjkio5UCy+u9YvpIAG1TYS+Bhai+8u+ZC0KqlZa62cP8dIC7o5+hbfPevXR+lKVDWgKt1Qk1XOpt/d87sr4m/pn90lnWsNn1dE/LT7o33rl1WhOhpJktYnp+**************************/V9eLvmB5RAL6/SfjL91PgIdeGMlnovwOEvc9LtDPqTISS/Fq9bZz9eFlp9/wPx7DqrVA0mtxJy6qtBOW3z3x4dF8p1PrdKBho9Pd57Fd+f7kzD0860RaqclHVi6ZKjV/+2go+9POlGtXAgGJd/Hpbf6sxm0nXpIPiyzcjikTKkZFvVXuy/Lz/vno+y3a5IOTXQQ9uUjrdvV7n8JdHS+GzU16/7kDfmPZOpQk/+PPwVQiXBwfrvan+ShbzwM/ShLWLewDo3QfSPX6T8ZPw9e/PlEL3z1wii0W81iicPq74/Yn2SAeRbqfDLclyhr85+MnwXlzf/A+HlQwb6G8fOggn0N4+dBBfsaxs/j1f3VX9sy+nZcCPhMAAAAAElFTkSuQmCC",
    "booking_confirmed": true
  }
  ```
  Brunel a ce niveau tu reçois Tu reçois déjà : • qr*code (string unique ex. COMMODORE_TRIP_30*…)
  • qr_image (base 64 PNG prêt à afficher)
  • trip_status = ACCEPTED et payment_status = PAID
  donc déjà tu peux Afficher l’écran « Votre QR Code d’embarquement en utilisant qr_image. (Stocker en cache le qr_code et l’id de la course).

#### 7. Récupération du QR code

- **Acteur** : Client
- **Endpoint** : `GET /api/trips/{trip_id}/qr-code/`
- **Réponse** :
  ```json
  {
    "trip_id": 30,
    "qr_code": "COMMODORE_TRIP_30_b138fd5165cb3f61",
    "qr_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAXIAAAFyAQAAAADAX2ykAAAC0ElEQVR4nO1by43jMAwlaQNztIEtYEpROltsSdNBXEoKGMA+BlDABSnJcbyA5BySlcd8h8Qx34EA8fiRGGR4BgM9RQcwfh5UsK9h/DyoYF/D+HlQwb6G8fOggn0N4x+BjxEt4Glqw0+ACRFP8qE4vdGf7aAnuAfmOxaM8th54HPHjCdopPlq1MR1+78ZdDB+G7+nHsB9AfDQf7fsQqSvyDABIEDj3+QPGf8l8U1A6ORr6jXSgBLzmv0n4z/F53PKyrHgFvglkPGr0G8nQZ2kEl8wpGZwX9HIFftPxt/CH7RF7gFCV+UuH0HEAHAL7fMjfzPI+BXolx9fMsAVYfi8YhT2+/wh479i/j1NiCpiPus43M+EqQUY+pvNv/vkw2K4dewB3KhZWQ1eJuE4GYUp+Fyb/2T8PDjErdPzjYZVv5KV09HG+MirzX8y/hb9uhC+qOT4bozjkio5UCy+u9YvpIAG1TYS+Bhai+8u+ZC0KqlZa62cP8dIC7o5+hbfPevXR+lKVDWgKt1Qk1XOpt/d87sr4m/pn90lnWsNn1dE/LT7o33rl1WhOhpJktYnp+**************************/V9eLvmB5RAL6/SfjL91PgIdeGMlnovwOEvc9LtDPqTISS/Fq9bZz9eFlp9/wPx7DqrVA0mtxJy6qtBOW3z3x4dF8p1PrdKBho9Pd57Fd+f7kzD0860RaqclHVi6ZKjV/+2go+9POlGtXAgGJd/Hpbf6sxm0nXpIPiyzcjikTKkZFvVXuy/Lz/vno+y3a5IOTXQQ9uUjrdvV7n8JdHS+GzU16/7kDfmPZOpQk/+PPwVQiXBwfrvan+ShbzwM/ShLWLewDo3QfSPX6T8ZPw9e/PlEL3z1wii0W81iicPq74/Yn2SAeRbqfDLclyhr85+MnwXlzf/A+HlQwb6G8fOggn0N4+dBBfsaxs/j1f3VX9sy+nZcCPhMAAAAAElFTkSuQmCC",
    "trip_details": {
      "departure": "Port de Cannes",
      "arrival": "Îles de Lérins",
      "scheduled_time": "2025-06-12T07:43:16.110844Z",
      "captain_name": "Dossou DAHOUI",
      "boat_name": "Alpha One",
      "passenger_count": 8
    }
  }
  ```

Détails course « à venir :(Pour le client)
Endpoint : GET /api/trips/{trip_id}/status/
Réponse :

```json
{
  "trip": {
    "id": 30,
    "client": {
      "user": {
        "id": 161,
        "email": "<EMAIL>",
        "first_name": "Marie",
        "last_name": "Dubois",
        "phone_number": "+33123456789",
        "type": "CLIENT",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": "1990-05-14",
      "nationality": "FR",
      "preferred_language": "fr",
      "emergency_contact_name": "Jean Dubois Armel",
      "emergency_contact_phone": "+33698765432"
    },
    "captain": {
      "user": {
        "id": 209,
        "email": "<EMAIL>",
        "first_name": "Dossou",
        "last_name": "DAHOUI",
        "phone_number": "+33600000000",
        "type": "CAPTAIN",
        "profile_picture": "",
        "is_active": true
      },
      "experience": "8 ans d'expérience maritime",
      "average_rating": "4.80",
      "total_trips": 120,
      "wallet_balance": "0.00",
      "is_available": true,
      "current_location": "",
      "license_number": "LIC0014",
      "license_expiry_date": null,
      "years_of_experience": 7,
      "certifications": ["Permis Bateau", "Secourisme"],
      "specializations": [],
      "availability_status": "AVAILABLE",
      "boat_photos": [],
      "rate_per_km": "5.00",
      "rate_per_hour": "44.06"
    },
    "boat": {
      "id": 80,
      "name": "Alpha One",
      "registration_number": "BN000114",
      "boat_type": "CLASSIC",
      "capacity": 8,
      "color": "Rouge",
      "fuel_type": "GASOLINE",
      "fuel_consumption": "10.89",
      "photos": [
        "https://cdn.commodore.com/boats/alpha1.jpg",
        "https://cdn.commodore.com/boats/alpha2.jpg"
      ],
      "zone_served": "Cannes, Antibes",
      "radius": 20,
      "captain": {
        "id": 209,
        "user": {
          "id": 209,
          "email": "<EMAIL>",
          "first_name": "Dossou",
          "last_name": "DAHOUI",
          "phone_number": "+33600000000",
          "profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg"
        },
        "experience": "8 ans d'expérience maritime",
        "average_rating": 4.8,
        "total_trips": 120,
        "is_available": true,
        "license_number": "LIC0014",
        "years_of_experience": 7,
        "rate_per_hour": 44.06
      },
      "establishment": null,
      "is_available": true,
      "last_maintenance": null,
      "next_maintenance": null,
      "created_at": "2025-06-12T02:21:57.684296+02:00",
      "updated_at": "2025-06-12T08:36:59.623797+02:00",
      "maintenance_records": []
    },
    "establishment": null,
    "captain_profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg",
    "captain_full_name": "Dossou DAHOUI",
    "boat_photos": [
      "https://cdn.commodore.com/boats/alpha1.jpg",
      "https://cdn.commodore.com/boats/alpha2.jpg"
    ],
    "qr_code": "COMMODORE_TRIP_30_b138fd5165cb3f61",
    "trip_type": "COURSE_SIMPLE",
    "start_location": "Port de Cannes",
    "end_location": "Îles de Lérins",
    "scheduled_start_time": "2025-06-12T09:43:16.110844+02:00",
    "scheduled_end_time": "2025-06-12T10:43:16.110844+02:00",
    "actual_start_time": "2025-06-12T12:31:13.250338+02:00",
    "actual_end_time": null,
    "estimated_duration": null,
    "actual_duration": null,
    "distance_km": null,
    "passenger_count": 8,
    "passenger_names": [],
    "special_requests": "Message optionnel pour le capitaine",
    "status": "IN_PROGRESS",
    "current_location": "",
    "tracking_data": [],
    "base_price": "18366.30",
    "additional_charges": "0.00",
    "tip": "0.00",
    "total_price": "18366.30",
    "payment_status": "PAID",
    "payment_method": "",
    "created_at": "2025-06-12T08:43:16.110844+02:00",
    "updated_at": "2025-06-12T12:31:13.251700+02:00",
    "cancellation_reason": "",
    "notes": "",
    "delay_minutes": 0,
    "problem_description": "",
    "captain_notes": "Course démarrée par Dossou DAHOUI",
    "client_notes": "",
    "estimated_arrival_time": null,
    "cancelled_by": null
  },
  "status_info": {
    "can_start": false,
    "can_complete": true,
    "can_cancel": false,
    "duration_minutes": null,
    "is_delayed": false,
    "has_problems": false
  }
}
```

avec ca tu as tout les champs nécessaires pour l'écran récapitulatif:statut de la course, le prix total, la compensation carbone, le total à payer, le qr code et l'adresse de la course etc...

````
  - **Code de statut** : 200 OK
  - **Contrainte** : La course doit être payée (`payment_status`: `PAID`)

Le capitaine appelle aussi GET /api/trips/{trip_id}/status/.
et obtient comme réponse :
```json
{
	"trip": {
		"id": 30,
		"client": {
			"user": {
				"id": 161,
				"email": "<EMAIL>",
				"first_name": "Marie",
				"last_name": "Dubois",
				"phone_number": "+33123456789",
				"type": "CLIENT",
				"profile_picture": "",
				"is_active": true
			},
			"wallet_balance": "0.00",
			"date_of_birth": "1990-05-14",
			"nationality": "FR",
			"preferred_language": "fr",
			"emergency_contact_name": "Jean Dubois Armel",
			"emergency_contact_phone": "+33698765432"
		},
		"captain": {
			"user": {
				"id": 209,
				"email": "<EMAIL>",
				"first_name": "Dossou",
				"last_name": "DAHOUI",
				"phone_number": "+33600000000",
				"type": "CAPTAIN",
				"profile_picture": "",
				"is_active": true
			},
			"experience": "8 ans d'expérience maritime",
			"average_rating": "4.80",
			"total_trips": 120,
			"wallet_balance": "0.00",
			"is_available": true,
			"current_location": "",
			"license_number": "LIC0014",
			"license_expiry_date": null,
			"years_of_experience": 7,
			"certifications": [
				"Permis Bateau",
				"Secourisme"
			],
			"specializations": [],
			"availability_status": "AVAILABLE",
			"boat_photos": [],
			"rate_per_km": "5.00",
			"rate_per_hour": "44.06"
		},
		"boat": {
			"id": 80,
			"name": "Alpha One",
			"registration_number": "BN000114",
			"boat_type": "CLASSIC",
			"capacity": 8,
			"color": "Rouge",
			"fuel_type": "GASOLINE",
			"fuel_consumption": "10.89",
			"photos": [
				"https://cdn.commodore.com/boats/alpha1.jpg",
				"https://cdn.commodore.com/boats/alpha2.jpg"
			],
			"zone_served": "Cannes, Antibes",
			"radius": 20,
			"captain": {
				"id": 209,
				"user": {
					"id": 209,
					"email": "<EMAIL>",
					"first_name": "Dossou",
					"last_name": "DAHOUI",
					"phone_number": "+33600000000",
					"profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg"
				},
				"experience": "8 ans d'expérience maritime",
				"average_rating": 4.8,
				"total_trips": 120,
				"is_available": true,
				"license_number": "LIC0014",
				"years_of_experience": 7,
				"rate_per_hour": 44.06
			},
			"establishment": null,
			"is_available": true,
			"last_maintenance": null,
			"next_maintenance": null,
			"created_at": "2025-06-12T02:21:57.684296+02:00",
			"updated_at": "2025-06-12T08:36:59.623797+02:00",
			"maintenance_records": []
		},
		"establishment": null,
		"captain_profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg",
		"captain_full_name": "Dossou DAHOUI",
		"boat_photos": [
			"https://cdn.commodore.com/boats/alpha1.jpg",
			"https://cdn.commodore.com/boats/alpha2.jpg"
		],
		"qr_code": "COMMODORE_TRIP_30_b138fd5165cb3f61",
		"trip_type": "COURSE_SIMPLE",
		"start_location": "Port de Cannes",
		"end_location": "Îles de Lérins",
		"scheduled_start_time": "2025-06-12T09:43:16.110844+02:00",
		"scheduled_end_time": "2025-06-12T10:43:16.110844+02:00",
		"actual_start_time": null,
		"actual_end_time": null,
		"estimated_duration": null,
		"actual_duration": null,
		"distance_km": null,
		"passenger_count": 8,
		"passenger_names": [],
		"special_requests": "Message optionnel pour le capitaine",
		"status": "ACCEPTED",
		"current_location": "",
		"tracking_data": [],
		"base_price": "18366.30",
		"additional_charges": "0.00",
		"tip": "0.00",
		"total_price": "18366.30",
		"payment_status": "PAID",
		"payment_method": "",
		"created_at": "2025-06-12T08:43:16.110844+02:00",
		"updated_at": "2025-06-12T09:22:36.273422+02:00",
		"cancellation_reason": "",
		"notes": "",
		"delay_minutes": 0,
		"problem_description": "",
		"captain_notes": "",
		"client_notes": "",
		"estimated_arrival_time": null,
		"cancelled_by": null
	},
	"status_info": {
		"can_start": true,
		"can_complete": false,
		"can_cancel": true,
		"duration_minutes": null,
		"is_delayed": false,
		"has_problems": false
	}
}
````

• S’il est bien assigné : status_info.can_start = true.
• Le bouton “Démarrer la course” doit déclencher le scan QR.

-Scan du QR par le capitaine:
a- L’app ouvre un scanner
b- Le QR contient la valeur qr_code
une fois scanné tu faire un patch vers /api/trip/ {trip_id}/start/ {} // aucun payload nécessaire
(TripStartView vérifie que le capitaine est bien celui de la course et que le QR est valide.)pour mettre le statut de la course à STARTED

Réponse:

```json
{
  "message": "Course démarrée avec succès",
  "trip": {
    "id": 30,
    "client": {
      "user": {
        "id": 161,
        "email": "<EMAIL>",
        "first_name": "Marie",
        "last_name": "Dubois",
        "phone_number": "+33123456789",
        "type": "CLIENT",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": "1990-05-14",
      "nationality": "FR",
      "preferred_language": "fr",
      "emergency_contact_name": "Jean Dubois Armel",
      "emergency_contact_phone": "+33698765432"
    },
    "captain": {
      "user": {
        "id": 209,
        "email": "<EMAIL>",
        "first_name": "Dossou",
        "last_name": "DAHOUI",
        "phone_number": "+33600000000",
        "type": "CAPTAIN",
        "profile_picture": "",
        "is_active": true
      },
      "experience": "8 ans d'expérience maritime",
      "average_rating": "4.80",
      "total_trips": 120,
      "wallet_balance": "0.00",
      "is_available": true,
      "current_location": "",
      "license_number": "LIC0014",
      "license_expiry_date": null,
      "years_of_experience": 7,
      "certifications": ["Permis Bateau", "Secourisme"],
      "specializations": [],
      "availability_status": "AVAILABLE",
      "boat_photos": [],
      "rate_per_km": "5.00",
      "rate_per_hour": "44.06"
    },
    "boat": {
      "id": 80,
      "name": "Alpha One",
      "registration_number": "BN000114",
      "boat_type": "CLASSIC",
      "capacity": 8,
      "color": "Rouge",
      "fuel_type": "GASOLINE",
      "fuel_consumption": "10.89",
      "photos": [
        "https://cdn.commodore.com/boats/alpha1.jpg",
        "https://cdn.commodore.com/boats/alpha2.jpg"
      ],
      "zone_served": "Cannes, Antibes",
      "radius": 20,
      "captain": {
        "id": 209,
        "user": {
          "id": 209,
          "email": "<EMAIL>",
          "first_name": "Dossou",
          "last_name": "DAHOUI",
          "phone_number": "+33600000000",
          "profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg"
        },
        "experience": "8 ans d'expérience maritime",
        "average_rating": 4.8,
        "total_trips": 120,
        "is_available": true,
        "license_number": "LIC0014",
        "years_of_experience": 7,
        "rate_per_hour": 44.06
      },
      "establishment": null,
      "is_available": true,
      "last_maintenance": null,
      "next_maintenance": null,
      "created_at": "2025-06-12T02:21:57.684296+02:00",
      "updated_at": "2025-06-12T08:36:59.623797+02:00",
      "maintenance_records": []
    },
    "establishment": null,
    "captain_profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg",
    "captain_full_name": "Dossou DAHOUI",
    "boat_photos": [
      "https://cdn.commodore.com/boats/alpha1.jpg",
      "https://cdn.commodore.com/boats/alpha2.jpg"
    ],
    "qr_code": "COMMODORE_TRIP_30_b138fd5165cb3f61",
    "trip_type": "COURSE_SIMPLE",
    "start_location": "Port de Cannes",
    "end_location": "Îles de Lérins",
    "scheduled_start_time": "2025-06-12T09:43:16.110844+02:00",
    "scheduled_end_time": "2025-06-12T10:43:16.110844+02:00",
    "actual_start_time": "2025-06-12T12:31:13.250338+02:00",
    "actual_end_time": null,
    "estimated_duration": null,
    "actual_duration": null,
    "distance_km": null,
    "passenger_count": 8,
    "passenger_names": [],
    "special_requests": "Message optionnel pour le capitaine",
    "status": "IN_PROGRESS",
    "current_location": "",
    "tracking_data": [],
    "base_price": "18366.30",
    "additional_charges": "0.00",
    "tip": "0.00",
    "total_price": "18366.30",
    "payment_status": "PAID",
    "payment_method": "",
    "created_at": "2025-06-12T08:43:16.110844+02:00",
    "updated_at": "2025-06-12T12:31:13.251700+02:00",
    "cancellation_reason": "",
    "notes": "",
    "delay_minutes": 0,
    "problem_description": "",
    "captain_notes": "Course démarrée par Dossou DAHOUI",
    "client_notes": "",
    "estimated_arrival_time": null,
    "cancelled_by": null
  }
}
```

• Le capitaine peut suivre la progression et, au besoin, appeler PATCH /api/trips/{trip_id}/problem/ pour signaler un problème ou PATCH /api/trips/{trip_id}/delay/ pour un retard.

1. Signaler un problème
   • URL : PATCH /api/trips/{trip_id}/problem/
   • Vue :
   TripProblemView
   (dans
   trips/views_status.py
   )
   • Qui peut l’appeler ? Le capitaine assigné à la course.
   • Payload minimal :
   json
   {
   "problem_description": "Moteur en panne",
   "delay_minutes": 30 // optionnel
   }
   Réponse:

```json
{
  "message": "Problème signalé avec succès",
  "trip": {
    "id": 30,
    "client": {
      "user": {
        "id": 161,
        "email": "<EMAIL>",
        "first_name": "Marie",
        "last_name": "Dubois",
        "phone_number": "+33123456789",
        "type": "CLIENT",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": "1990-05-14",
      "nationality": "FR",
      "preferred_language": "fr",
      "emergency_contact_name": "Jean Dubois Armel",
      "emergency_contact_phone": "+33698765432"
    },
    "captain": {
      "user": {
        "id": 209,
        "email": "<EMAIL>",
        "first_name": "Dossou",
        "last_name": "DAHOUI",
        "phone_number": "+33600000000",
        "type": "CAPTAIN",
        "profile_picture": "",
        "is_active": true
      },
      "experience": "8 ans d'expérience maritime",
      "average_rating": "4.80",
      "total_trips": 120,
      "wallet_balance": "0.00",
      "is_available": true,
      "current_location": "",
      "license_number": "LIC0014",
      "license_expiry_date": null,
      "years_of_experience": 7,
      "certifications": ["Permis Bateau", "Secourisme"],
      "specializations": [],
      "availability_status": "AVAILABLE",
      "boat_photos": [],
      "rate_per_km": "5.00",
      "rate_per_hour": "44.06"
    },
    "boat": {
      "id": 80,
      "name": "Alpha One",
      "registration_number": "BN000114",
      "boat_type": "CLASSIC",
      "capacity": 8,
      "color": "Rouge",
      "fuel_type": "GASOLINE",
      "fuel_consumption": "10.89",
      "photos": [
        "https://cdn.commodore.com/boats/alpha1.jpg",
        "https://cdn.commodore.com/boats/alpha2.jpg"
      ],
      "zone_served": "Cannes, Antibes",
      "radius": 20,
      "captain": {
        "id": 209,
        "user": {
          "id": 209,
          "email": "<EMAIL>",
          "first_name": "Dossou",
          "last_name": "DAHOUI",
          "phone_number": "+33600000000",
          "profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg"
        },
        "experience": "8 ans d'expérience maritime",
        "average_rating": 4.8,
        "total_trips": 120,
        "is_available": true,
        "license_number": "LIC0014",
        "years_of_experience": 7,
        "rate_per_hour": 44.06
      },
      "establishment": null,
      "is_available": true,
      "last_maintenance": null,
      "next_maintenance": null,
      "created_at": "2025-06-12T02:21:57.684296+02:00",
      "updated_at": "2025-06-12T08:36:59.623797+02:00",
      "maintenance_records": []
    },
    "establishment": null,
    "captain_profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg",
    "captain_full_name": "Dossou DAHOUI",
    "boat_photos": [
      "https://cdn.commodore.com/boats/alpha1.jpg",
      "https://cdn.commodore.com/boats/alpha2.jpg"
    ],
    "qr_code": "COMMODORE_TRIP_30_b138fd5165cb3f61",
    "trip_type": "COURSE_SIMPLE",
    "start_location": "Port de Cannes",
    "end_location": "Îles de Lérins",
    "scheduled_start_time": "2025-06-12T09:43:16.110844+02:00",
    "scheduled_end_time": "2025-06-12T10:43:16.110844+02:00",
    "actual_start_time": "2025-06-12T12:31:13.250338+02:00",
    "actual_end_time": null,
    "estimated_duration": null,
    "actual_duration": null,
    "distance_km": null,
    "passenger_count": 8,
    "passenger_names": [],
    "special_requests": "Message optionnel pour le capitaine",
    "status": "PROBLEM",
    "current_location": "",
    "tracking_data": [],
    "base_price": "18366.30",
    "additional_charges": "0.00",
    "tip": "0.00",
    "total_price": "18366.30",
    "payment_status": "PAID",
    "payment_method": "",
    "created_at": "2025-06-12T08:43:16.110844+02:00",
    "updated_at": "2025-06-12T12:59:36.695038+02:00",
    "cancellation_reason": "",
    "notes": "",
    "delay_minutes": 30,
    "problem_description": "Moteur en panne",
    "captain_notes": "Course démarrée par Dossou DAHOUI",
    "client_notes": "",
    "estimated_arrival_time": null,
    "cancelled_by": null
  }
}
```

- 2 Signaler un retard
  • URL : PATCH /api/trips/{trip_id}/delay/
  • Vue :
  TripDelayView
  (dans
  trips/views_status.py
  )
  • Qui peut l’appeler ? Le capitaine assigné.
  • Payload minimal :
  json
  {
  "delay_minutes": 15,
  "reason": "Trafic portuaire dense" // selon ta logique interne, optionnel
  }
  • Réponse :

```json
{
  "message": "Retard de 15 minutes signalé",
  "trip": {
    "id": 30,
    "client": {
      "user": {
        "id": 161,
        "email": "<EMAIL>",
        "first_name": "Marie",
        "last_name": "Dubois",
        "phone_number": "+33123456789",
        "type": "CLIENT",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": "1990-05-14",
      "nationality": "FR",
      "preferred_language": "fr",
      "emergency_contact_name": "Jean Dubois Armel",
      "emergency_contact_phone": "+33698765432"
    },
    "captain": {
      "user": {
        "id": 209,
        "email": "<EMAIL>",
        "first_name": "Dossou",
        "last_name": "DAHOUI",
        "phone_number": "+33600000000",
        "type": "CAPTAIN",
        "profile_picture": "",
        "is_active": true
      },
      "experience": "8 ans d'expérience maritime",
      "average_rating": "4.80",
      "total_trips": 120,
      "wallet_balance": "0.00",
      "is_available": true,
      "current_location": "",
      "license_number": "LIC0014",
      "license_expiry_date": null,
      "years_of_experience": 7,
      "certifications": ["Permis Bateau", "Secourisme"],
      "specializations": [],
      "availability_status": "AVAILABLE",
      "boat_photos": [],
      "rate_per_km": "5.00",
      "rate_per_hour": "44.06"
    },
    "boat": {
      "id": 80,
      "name": "Alpha One",
      "registration_number": "BN000114",
      "boat_type": "CLASSIC",
      "capacity": 8,
      "color": "Rouge",
      "fuel_type": "GASOLINE",
      "fuel_consumption": "10.89",
      "photos": [
        "https://cdn.commodore.com/boats/alpha1.jpg",
        "https://cdn.commodore.com/boats/alpha2.jpg"
      ],
      "zone_served": "Cannes, Antibes",
      "radius": 20,
      "captain": {
        "id": 209,
        "user": {
          "id": 209,
          "email": "<EMAIL>",
          "first_name": "Dossou",
          "last_name": "DAHOUI",
          "phone_number": "+33600000000",
          "profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg"
        },
        "experience": "8 ans d'expérience maritime",
        "average_rating": 4.8,
        "total_trips": 120,
        "is_available": true,
        "license_number": "LIC0014",
        "years_of_experience": 7,
        "rate_per_hour": 44.06
      },
      "establishment": null,
      "is_available": true,
      "last_maintenance": null,
      "next_maintenance": null,
      "created_at": "2025-06-12T02:21:57.684296+02:00",
      "updated_at": "2025-06-12T08:36:59.623797+02:00",
      "maintenance_records": []
    },
    "establishment": null,
    "captain_profile_picture": "https://cdn.commodore.com/profiles/alpha.jpg",
    "captain_full_name": "Dossou DAHOUI",
    "boat_photos": [
      "https://cdn.commodore.com/boats/alpha1.jpg",
      "https://cdn.commodore.com/boats/alpha2.jpg"
    ],
    "qr_code": "COMMODORE_TRIP_30_b138fd5165cb3f61",
    "trip_type": "COURSE_SIMPLE",
    "start_location": "Port de Cannes",
    "end_location": "Îles de Lérins",
    "scheduled_start_time": "2025-06-12T09:43:16.110844+02:00",
    "scheduled_end_time": "2025-06-12T10:43:16.110844+02:00",
    "actual_start_time": "2025-06-12T12:31:13.250338+02:00",
    "actual_end_time": null,
    "estimated_duration": null,
    "actual_duration": null,
    "distance_km": null,
    "passenger_count": 8,
    "passenger_names": [],
    "special_requests": "Message optionnel pour le capitaine",
    "status": "DELAYED",
    "current_location": "",
    "tracking_data": [],
    "base_price": "18366.30",
    "additional_charges": "0.00",
    "tip": "0.00",
    "total_price": "18366.30",
    "payment_status": "PAID",
    "payment_method": "",
    "created_at": "2025-06-12T08:43:16.110844+02:00",
    "updated_at": "2025-06-12T13:06:07.728336+02:00",
    "cancellation_reason": "",
    "notes": "",
    "delay_minutes": 15,
    "problem_description": "Moteur en panne",
    "captain_notes": "Course démarrée par Dossou DAHOUI\nRetard de 15 minutes: Trafic portuaire dense",
    "client_notes": "",
    "estimated_arrival_time": null,
    "cancelled_by": null
  }
}
```

6. Fin de course

Quand la course est terminée, le capitaine envoie :

PATCH /api/boatman/shuttle/{shuttle_id}/end/

{
"captain_notes": "Merci pour la course !"
}

- **Code de statut** : 200 OK
- **Effet secondaire** : Un QR code est automatiquement généré pour la course

#### 7. Ajout d'un pourboire (optionnel)

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/{trip_id}/tip/`
- **Payload** :
  ```json
  {
    "amount": 5.0,
    "payment_method": "card",
    "payment_method_id": "pm_123"
  }
  ```
- **Réponse** :
  ```json
  {
    "message": "Pourboire payé avec succès",
    "payment_id": "pi_3RZ9OmD6Ags5eCCM13bZKIu8",
    "amount": 5.0,
    "captain_name": "Dossou DAHOUI",
    "tip_qr": "COMMODORE_TIP_30_07a968a383fe2bc4",
    "payment_method": "card",
    "total_trip_tip": 5.0
  }
  ```
- **Code de statut** : 200 OK

#### 8. Compensation carbone (optionnel)

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/{trip_id}/carbon-compensation/`
- **Payload** :
  ```json
  {
    "payment_method": "card", // Options: "card" ou "wallet"
    "payment_method_id": "pm_card_visa" // ID de la méthode de paiement Stripe ou null pour wallet
  }
  ```
- **Réponse** :
  ```json
  {
    "message": "Compensation carbone payée avec succès",
    "payment_id": "pi_3RZ9RnD6Ags5eCCM0hGblqhW",
    "amount": 1.66,
    "co2_compensated_kg": 20.75,
    "compensation_qr": "COMMODORE_CARBON_30_4e3cc6c1aed9da91",
    "payment_method": "card"
  }
  ```
- **Code de statut** : 200 OK
- **Contraintes** :
  - La course doit être terminée (`status`: `COMPLETED`)
  - Une compensation ne peut être payée qu'une seule fois par course

pour avoir le content_type :
GET /api/content-types/?app_label=trips&model=trip
Réponse :

{
"id": 26,
"app_label": "trips",
"model": "trip",
"name": "trips | course"
}

#### 10. Évaluation après course terminée

- **Acteur** : Client
- **Endpoint** : `POST /api/reviews/`
- **Payload** :

  ```json
  {
    "rating": 4.5,
    "content": "Excellent service, capitaine très professionnel!",
    "aspects": {
      "punctuality": 5,
      "cleanliness": 4,
      "communication": 5,
      "value": 4
    },
    "content_type": 26, // ID du content type Trip
    "object_id": 30, // ID de la course évaluée
    "is_public": true // Si l'avis peut être affiché publiquement
  }
  ```

- **Réponse** :
  ```json
  {
    "id": 1,
    "author": {
      "id": 161,
      "email": "<EMAIL>",
      "first_name": "Marie",
      "last_name": "Dubois",
      "phone_number": "+33123456789",
      "type": "CLIENT",
      "profile_picture": "",
      "is_active": true
    },
    "trip": null,
    "responses": [],
    "average_rating": 4.6,
    "reviewed_object_info": {
      "type": "Trip",
      "id": 30,
      "name": "Course 30 - Port de Cannes → Îles de Lérins"
    },
    "type": "TRIP",
    "rating": 5,
    "title": "Super course !",
    "comment": "Excellent service, capitaine très professionnel.",
    "pros": "",
    "cons": "",
    "cleanliness_rating": 4,
    "communication_rating": 5,
    "punctuality_rating": 5,
    "value_rating": 4,
    "is_verified": false,
    "is_public": true,
    "created_at": "2025-06-12T14:06:32.034987+02:00",
    "updated_at": "2025-06-12T14:06:32.035988+02:00",
    "reported_count": 0
  }
  ```
- **Code de statut** : 201 Created
- **Contraintes** :
  - La course doit être terminée (`status`: `COMPLETED`)
  - L'utilisateur ne peut poster qu'un seul avis par course

#### 12. Suivi en temps réel

- **Acteur** : Client/Capitaine
- **Endpoint** : `GET /api/boatman/shuttle/{shuttle_id}/track/`
- **Réponse** : Positions actuelles et historiques

#### 14. Résumé du workflow complet pour une Course Simple

### Acteurs impliqués

- **Client** : Crée la demande, choisit un devis, paie, peut ajouter une compensation carbone, télécharge le QR code, peut laisser un pourboire et évaluer
- **Capitaine** : Reçoit la demande, accepte/refuse la course, démarre/termine la course
- **Système** : Génère les devis, calcule les prix, gère les notifications, génère le QR code

## 2. Mise à Disposition (COURSE_HORAIRE / HOURLY)

### Description

Location d'un bateau avec capitaine à l'heure. Le client définit une durée de réservation. Le prix est calculé en fonction de la durée et du taux horaire du capitaine.

### Workflow et API pour une Mise à Disposition

#### 1. Création d'une demande de Mise à Disposition

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/requests/hourly/`
- **Payload** :
  ```json
  {
    "departure_location": {
      "city_name": "Port de Cannes",
      "coordinates": {
        "latitude": 43.5528,
        "longitude": 7.0174
      },
      "timestamp": "2025-06-11T06:38:10+02:00"
    },
    "arrival_location": {
      "city_name": "Port de Cannes",
      "coordinates": {
        "latitude": 43.5528,
        "longitude": 7.0174
      },
      "timestamp": "2025-06-11T06:38:10+02:00"
    },
    "passenger_count": 6,
    "boat_type": "LUXURY",
    "start_date": "2025-06-20",
    "duration_hours": 4
  }
  ```
- **Réponse** :
  ```json
  {
    "trip_request": {
      "id": 456,
      "client": {
        "id": 45,
        "user": {
          "id": 78,
          "first_name": "Jean",
          "last_name": "Dupont"
        }
      },
      "start_location": {
        "city_name": "Port de Cannes",
        "coordinates": {
          "latitude": 43.5528,
          "longitude": 7.0174
        },
        "timestamp": "2025-06-11T06:38:10+02:00"
      },
      "end_location": {
        "city_name": "Port de Cannes",
        "coordinates": {
          "latitude": 43.5528,
          "longitude": 7.0174
        },
        "timestamp": "2025-06-11T06:38:10+02:00"
      },
      "passenger_count": 6,
      "boat_type": "LUXURY",
      "start_date": "2025-06-20",
      "duration_hours": 4,
      "created_at": "2025-06-11T06:38:10+02:00",
      "updated_at": "2025-06-11T06:38:10+02:00",
      "expires_at": "2025-06-11T06:48:10+02:00",
      "distance_km": 0.0,
      "status": "PENDING",
      "trip_type": "HOURLY"
    },
    "quotes": [
      {
        "id": 235,
        "captain": {
          "id": 57,
          "user": {
            "id": 79,
            "first_name": "Sophie",
            "last_name": "Dubois"
          }
        },
        "boat": {
          "id": 90,
          "name": "La Belle Vie",
          "boat_type": "LUXURY",
          "capacity": 8
        },
        "base_price": 600.0,
        "distance_km": 0.0,
        "rate_used": 150.0,
        "captain_name": "Sophie Dubois",
        "captain_rating": 4.5,
        "boat_name": "La Belle Vie",
        "boat_capacity": 8,
        "is_available": true,
        "created_at": "2025-06-11T06:38:10+02:00"
      }
    ]
  }
  ```
- **Code de statut** : 201 Created
- **Contraintes** :
  - Le passenger_count doit être compris entre 1 et 50
  - Le boat_type doit être l'un des types prédéfinis (CLASSIC, LUXURY, SPORT)
  - La date de début (start_date) doit être dans le futur
  - La durée (duration_hours) doit être comprise entre 1 et 24 heures

#### 2. Génération automatique de devis

- **Acteur** : Système
- **Processus** :
  - Identifie les capitaines disponibles avec un bateau du type demandé et une capacité suffisante
  - Calcule le prix pour chaque capitaine en fonction de son tarif horaire (`base_price = duration_hours * rate_per_hour`)
  - Génère des devis pour chaque capitaine disponible

#### 3. Notification des capitaines

- **Acteur** : Système
- **Processus** : Une notification est créée pour chaque capitaine ayant reçu un devis

#### 4. Choix du devis par le client

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/quotes/{quote_id}/choose/`
- **Payload** :
  ```json
  {
    "message": "Message optionnel pour le capitaine"
  }
  ```
- **Réponse** :
  ```json
  {
    "success": true,
    "message": "Demande envoyée au capitaine",
    "trip": {
      "id": 346,
      "client": {
        "id": 45,
        "user": {
          "id": 78,
          "first_name": "Jean",
          "last_name": "Dupont"
        }
      },
      "captain": {
        "id": 57,
        "user": {
          "id": 79,
          "first_name": "Sophie",
          "last_name": "Dubois"
        }
      },
      "boat": {
        "id": 90,
        "name": "La Belle Vie",
        "type": "LUXURY",
        "capacity": 8
      },
      "status": "PENDING",
      "start_location": "Port de Cannes",
      "end_location": "Port de Cannes",
      "total_price": 600.0,
      "payment_status": "PENDING",
      "scheduled_start_time": "2025-06-20T09:00:00+02:00",
      "scheduled_end_time": "2025-06-20T13:00:00+02:00"
    },
    "next_step": "Attendez la réponse du capitaine"
  }
  ```
- **Code de statut** : 201 Created
- **Effet secondaire** : Les autres devis sont marqués comme indisponibles

#### 5. Acceptation par le capitaine

- **Acteur** : Capitaine
- **Endpoint** : `POST /api/trips/{trip_id}/accept/`
- **Payload** :
  ```json
  {}
  ```
- **Réponse** :
  ```json
  {
    "success": true,
    "message": "Course acceptée avec succès",
    "trip": {
      "id": 346,
      "status": "ACCEPTED",
      "scheduled_start_time": "2025-06-20T09:00:00+02:00",
      "scheduled_end_time": "2025-06-20T13:00:00+02:00"
    },
    "next_step": "Le client doit effectuer le paiement"
  }
  ```
- **Code de statut** : 200 OK
- **Effet secondaire** : Une notification est envoyée au client

#### 6. Paiement par le client

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/{trip_id}/payment/`
- **Payload** :
  ```json
  {
    "payment_method": "card", // Options: "card" ou "wallet"
    "payment_method_id": "pm_123" // ID de la méthode de paiement Stripe ou null pour wallet
  }
  ```
- **Réponse** :
  ```json
  {
    "message": "Paiement effectué avec succès",
    "payment_id": 457,
    "trip_id": 346,
    "amount_paid": 600.0,
    "payment_method": "card",
    "trip_status": "ACCEPTED",
    "payment_status": "PAID",
    "qr_code": "data:text/plain;base64,BASE64_ENCODED_QR_DATA",
    "qr_image": "data:image/png;base64,BASE64_ENCODED_QR_IMAGE",
    "booking_confirmed": true
  }
  ```
- **Code de statut** : 200 OK

#### 7. Récupération du QR code

- **Acteur** : Client
- **Endpoint** : `GET /api/trips/{trip_id}/qr-code/`
- **Réponse** :
  ```json
  {
    "trip_id": 346,
    "qr_code": "data:text/plain;base64,BASE64_ENCODED_QR_DATA",
    "qr_image": "data:image/png;base64,BASE64_ENCODED_QR_IMAGE",
    "trip_details": {
      "departure": "Port de Cannes",
      "arrival": "Port de Cannes",
      "scheduled_time": "2025-06-20T09:00:00+02:00",
      "captain_name": "Sophie Dubois",
      "boat_name": "La Belle Vie",
      "passenger_count": 6
    }
  }
  ```
- **Code de statut** : 200 OK

#### 8. Démarrage de la course

- **Acteur** : Capitaine
- **Endpoint** : `POST /api/boatman/shuttle/{shuttle_id}/start/`
- **Payload** :
  ```json
  {
    "actual_start_time": "2025-06-20T09:05:00+02:00"
  }
  ```
- **Réponse** :
  ```json
  {
    "success": true,
    "message": "Course démarrée avec succès",
    "trip": {
      "id": 346,
      "status": "STARTED",
      "actual_start_time": "2025-06-20T09:05:00+02:00"
    }
  }
  ```
- **Code de statut** : 200 OK

#### 9. Fin de la course

- **Acteur** : Capitaine
- **Endpoint** : `POST /api/boatman/shuttle/{shuttle_id}/end/`
- **Payload** :
  ```json
  {
    "actual_end_time": "2025-06-20T13:10:00+02:00"
  }
  ```
- **Réponse** :
  ```json
  {
    "success": true,
    "message": "Course terminée avec succès",
    "trip": {
      "id": 346,
      "status": "COMPLETED",
      "actual_start_time": "2025-06-20T09:05:00+02:00",
      "actual_end_time": "2025-06-20T13:10:00+02:00",
      "total_duration": "4h 5min"
    }
  }
  ```
- **Code de statut** : 200 OK

#### 10. Évaluation après course terminée

- **Acteur** : Client
- **Endpoint** : `POST /api/reviews/`
- **Payload (mobile)** :

  ```json
  {
    "rating": 4.5,
    "comment": "Excellent service, capitaine très professionnel!",
    "content_type": 42, // ID du content type Trip
    "object_id": 346, // ID de la course évaluée
    "is_public": true // Si l'avis peut être affiché publiquement (optionnel)
  }
  ```

  Reponse:
  {
  "id": 2,
  "author": {
  "id": 161,
  "email": "<EMAIL>",
  "first_name": "Marie",
  "last_name": "Dubois",
  "phone_number": "+33123456789",
  "type": "CLIENT",
  "profile_picture": "https://cdn.commodore.com/profiles/marie.jpg",
  "is_active": true
  },
  "trip": null,
  "rating": 5,
  "comment": "Excellent service, capitaine très professionnel!",
  "is_public": true,
  "created_at": "2025-06-16T07:53:53.576212+02:00",
  "updated_at": "2025-06-16T07:53:53.576212+02:00",
  "responses": [],
  "average_rating": 5.0,
  "reviewed_object_info": {
  "type": "Trip",
  "id": 31,
  "name": "Course 31 - Cotonou → CALAVI"
  },
  "reported_count": 0,
  "is_verified": false
  }

- **Remarque** : Les champs d'aspects détaillés (`punctuality`, `cleanliness`, etc.) ne sont pas requis ni attendus côté mobile. Seuls les champs visibles sur la maquette doivent être envoyés.

### Différences avec une Course Simple

1. **Calcul du prix** :

   - Course Simple : distance (km) × tarif par km
   - Mise à Disposition : durée (heures) × tarif horaire

2. **Localisation** :

   - Le lieu de départ et d'arrivée est souvent identique pour une Mise à Disposition
   - La course suit un itinéraire libre pendant la durée réservée

3. **Paramètres spécifiques** :
   - `start_date` : Date de début de la mise à disposition
   - `duration_hours` : Durée réservée (1-24 heures)

### Statuts de la course

- `PENDING` : En attente d'une réponse du capitaine
- `ACCEPTED` : Acceptée par le capitaine, en attente de paiement
- `PAID` : Payée, en attente de démarrage
- `STARTED` : En cours
- `COMPLETED` : Terminée
- `CANCELLED` : Annulée

### Particularités des Navettes Gratuites

1. **Spécificités** :

   - Gratuites pour le client final (financées par l'établissement partenaire)
   - Gérées par des bateliers (et non des capitaines)
   - Pas de sélection de devis (le service est automatiquement attribué)
   - Pas de paiement en ligne

2. **API spécifiques** :

   - API dédiée pour les bateliers : `/api/boatman/`
   - API pour les établissements : `/api/establishment/`

3. **Flux d'information** :
   - Le client fait une demande de navette
   - L'établissement confirme et assigne un batelier
   - Le batelier exécute la course
   - Aucune transaction financière directe avec le client

### État des lieux de l'implémentation

Les trois types de courses sont actuellement implémentés dans le système Commodore avec leur workflow complet :

1. **Course Simple** : 100% implémentée avec tous les endpoints et fonctionnalités
2. **Mise à Disposition** : 100% implémentée avec calcul spécifique des prix basé sur la durée
3. **Navette Gratuite** : 100% implémentée avec gestion des établissements partenaires et bateliers

## Conclusion

Cette documentation détaille l'ensemble des API et workflows pour les trois types de courses implémentés dans Commodore :

1. **Courses Simples** - Transport direct d'un point A à un point B
2. **Mises à Disposition** - Location d'un bateau avec capitaine à l'heure
3. **Navettes Gratuites** - Service de transport offert par les établissements partenaires

Chaque type de course suit un workflow spécifique avec ses propres endpoints, contraintes de validation, et statuts. La documentation fournie est basée sur l'analyse du code source réel et représente fidèlement le comportement du système tel qu'il est implémenté.

Les API REST suivent une structure cohérente et normalisée avec des points communs entre les différents types de courses (authentification, gestion des localisations, etc.) mais aussi des particularités propres à chaque type (calcul de prix, workflow de validation, acteurs impliqués).

Les différentes sécurités et contrôles d'accès sont mis en place pour assurer que seuls les utilisateurs ayant les droits appropriés peuvent effectuer certaines actions (par exemple, seul le client associé à une course peut la payer, seul le capitaine assigné peut la démarrer, etc.).

## 3. Navette Gratuite (NAVETTES_GRATUITES / SHUTTLE)

### Description

Une navette gratuite est un service offert par les établissements partenaires pour transporter les clients vers leur établissement. Ce service est gratuit pour le client.

### Payload complet pour créer une demande de navette gratuite

```json
{
"departure_))))))]]]@@^^\\location": {
    "city_name": "Port de Cannes",
    "coordinates": {
      "latitude": 43.5528,
      "longitude": 7.0174
    },
    "timestamp": "2025-06-11T06:26:25+02:00"
  },
  "arrival_location": {
    "city_name": "Restaurant La Plage",
    "coordinates": {
      "latitude": 43.53,
      "longitude": 7.04
    },
    "timestamp": "2025-06-11T06:26:25+02:00"
  },
  "passenger_count": 2,
  "establishment": 5,
  "departure_date": "2025-06-13",
  "departure_time": "19:30:00",
  "message": "Nous aimerions réserver une table pour 20h"
}
```

### Workflow complet détaillé

#### 1. Création de la demande

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/requests/shuttle/`
- **Payload** : Voir ci-dessus
- **Réponse** : Confirmation que la demande a été créée et sera traitée par l'établissement
  Réponse :

```json
{
  "trip_request": {
    "id": 48,
    "departure_location": {
      "city_name": "Calavi",
      "coordinates": {
        "latitude": 43.5528,
        "longitude": 7.0174
      },
      "timestamp": "2025-06-20T06:26:25+02:00"
    },
    "arrival_location": {
      "city_name": "Djeffa",
      "coordinates": {
        "latitude": 20.53,
        "longitude": 7.04
      },
      "timestamp": "2025-06-11T06:26:25+02:00"
    },
    "client": {
      "user": {
        "id": 161,
        "email": "<EMAIL>",
        "first_name": "Marie",
        "last_name": "Dubois",
        "phone_number": "+33123456789",
        "type": "CLIENT",
        "profile_picture": "https://cdn.commodore.com/profiles/marie.jpg",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": "1990-05-14",
      "nationality": "FR",
      "preferred_language": "fr",
      "emergency_contact_name": "Jean Dubois Armel",
      "emergency_contact_phone": "+33698765432"
    },
    "distance_km": "2560.02",
    "establishment": 211,
    "establishment_details": {
      "user": {
        "id": 211,
        "email": "<EMAIL>",
        "first_name": "",
        "last_name": "",
        "phone_number": "+***********",
        "type": "ESTABLISHMENT",
        "profile_picture": "",
        "is_active": true
      },
      "name": "Nobu",
      "type": "RESTAURANT",
      "address": "Route de l'Escalet, 83350 Ramatuelle",
      "description": "Niché au cœur de la baie de Ramatuelle, le Blue Wave Beach Club vous accueille dans un cadre idyllique mêlant élégance et détente.",
      "main_photo": "/media/https%3A/cdn.commodore.com/establishments/nobu_main.jpg",
      "secondary_photos": [
        "https://cdn.commodore.com/establishments/nobu_1.jpg",
        "https://cdn.commodore.com/establishments/nobu_2.jpg",
        "https://cdn.commodore.com/establishments/nobu_3.jpg"
      ],
      "wallet_balance": "0.00",
      "business_name": "",
      "business_type": "",
      "registration_number": "",
      "tax_id": "",
      "opening_hours": {},
      "services_offered": [],
      "average_rating": "0.00",
      "location_coordinates": "",
      "website": "",
      "social_media": {}
    },
    "trip_type": "SHUTTLE",
    "status": "PENDING",
    "passenger_count": 2,
    "created_at": "2025-06-19T17:13:40.366426+02:00",
    "updated_at": "2025-06-19T17:13:40.377123+02:00",
    "expires_at": "2025-06-19T17:23:40.366426+02:00",
    "departure_date": "2025-06-19",
    "departure_time": "19:30:00",
    "message": "Nous aimerions réserver une table pour 20h"
  },
  "message": "Demande de navette créée. L'établissement sera notifié.",
  "distance_to_establishment": "2560.02 km"
}
```

#### 2. Notification de l'établissement

- **Acteur** : Système
- **Endpoint interne** : Utilise le service de notification
- **Contenu** : L'établissement reçoit une notification avec les détails de la demande de navette

#### 3. Traitement par l'établissement

- **Acteur** : Établissement
- **Endpoint** : `GET /api/establishments/shuttle-requests/`
- **Réponse** : Liste des demandes de navettes en attente

```json
{
  "status": "success",
  "data": {
    "requests": [
      {
        "request_id": "48",
        "client_name": "Marie Dubois",
        "client_phone": "+33123456789",
        "departure": "Calavi",
        "destination": "Djeffa",
        "date": "2025-06-19",
        "time": "19:30",
        "passengers": 2,
        "message": "Nous aimerions réserver une table pour 20h",
        "created_at": "2025-06-19T15:13:40.366426+00:00"
      },
      {
        "request_id": "47",
        "client_name": "Marie Dubois",
        "client_phone": "+33123456789",
        "departure": "Calavi",
        "destination": "Djeffa",
        "date": "2025-06-19",
        "time": "19:30",
        "passengers": 2,
        "message": "Nous aimerions réserver une table pour 20h",
        "created_at": "2025-06-19T15:08:53.302471+00:00"
      }
    ]
  }
}
```

-**Endpoint d'assignation** : `GET /api/boats/` -**Response** :retournera le bateau du batelier que tu as créé.

```json
[
  {
    "id": 86,
    "name": "Bateau par défaut",
    "registration_number": "AUTO-214",
    "boat_type": "CLASSIC",
    "capacity": 4,
    "status": "AVAILABLE",
    "captain_name": "Armel Smith",
    "location": ""
  },
  {
    "id": 84,
    "name": "Beneteau Flyer Armel 7.7 SUNdeck",
    "registration_number": "MA-3455-CM",
    "boat_type": "CLASSIC",
    "capacity": 10,
    "status": "AVAILABLE",
    "captain_name": "Armel Smith",
    "location": "Zone par défaut"
  }
]
```

- **Endpoint d'assignation** : `POST /api/establishments/shuttle-requests/{request_id}/assign/`
- **Payload** :
  ```json
  {
    "boatman_id": 12,
    "boat_id": 5,
    "message": "Merci de prendre en charge cette navette"
  }
  ```
- **Réponse** : Confirmation que la navette a été assignée au batelier
{
	"status": "success",
	"message": "Demande acceptée et course créée",
	"trip_id": "33"
}

---

### Option B : Rechercher un capitaine indépendant

Dans le cas où l’établissement ne souhaite pas utiliser son propre batelier, il peut rechercher un capitaine indépendant disponible sur la plateforme.  
Le flux est similaire aux **Courses Simples** avec génération de devis, mais la demande est initiée par l’établissement, pas par le client.

1. **Lister les capitaines disponibles et les devis**
   - **Acteur** : Établissement  
   - **Endpoint** : `GET /api/establishments/shuttle-requests/{request_id}/quotes/`  
   - **Réponse** : Tableau de devis triés par prix croissant.  
     ```json
     {
       "status": "success",
       "data": {
         "quotes": [
           {
             "quote_id": 981,
             "captain": {
               "id": 203,
               "name": "Jules Lefevre",
               "rating": 4.5
             },
             "boat": {
               "id": 84,
               "name": "Beneteau Flyer 7.7 SUNdeck",
               "capacity": 8
             },
             "distance_km": 0.3,
             "base_price": "28.00",
             "currency": "EUR",
             "estimated_pickup_time": "2025-06-20T18:25:00+02:00"
           },
           {
             "quote_id": 982,
             "captain": { "id": 228, "name": "Michael Johnson", "rating": 4.8 },
             "boat": {
               "id": 85,
               "name": "Yamaha 242X E-Series",
               "capacity": 10
             },
             "distance_km": 0.1,
             "base_price": "30.00",
             "currency": "EUR",
             "estimated_pickup_time": "2025-06-20T18:28:00+02:00"
           }
         ]
       }
     }
     ```

2. **Choisir un devis et envoyer la demande au capitaine**
   - **Acteur** : Établissement  
   - **Endpoint** : `POST /api/trips/quotes/{quote_id}/choose/`  
   - **Payload** :  
     ```json
     {
       "message": "Merci de prendre en charge cette navette pour notre client."
     }
     ```
   - **Réponse** :
     ```json
     {
       "success": true,
       "trip": {
         "id": 793,
         "status": "PENDING",
         "total_price": "28.00",
         "captain": { "id": 203, "name": "Jules Lefevre" }
       },
       "next_step": "Attendez la réponse du capitaine"
     }
     ```

3. **Acceptation de la course par le capitaine**

   - **Acteur** : Capitaine
   - **Endpoint** : `POST /api/trips/{trip_id}/accept/`
   - **Payload** :
     ```json
     {
       "estimated_pickup_time": "2025-06-20T18:25:00+02:00"
     }
     ```
   - **Réponse** : Confirmation que la course est acceptée et passe au statut `ACCEPTED`.

4. **Paiement de la course par l’établissement**
   - **Acteur** : Établissement
   - **Endpoint** : `POST /api/trips/{trip_id}/payment/`
   - **Payload (carte)** :
     ```json
     {
       "payment_method": "CARD",
       "card": {
         "number": "****************",
         "exp_month": 12,
         "exp_year": 2025,
         "cvc": "123"
       }
     }
     ```
   - **OU Payload (solde établissement)** :
     ```json
     {
       "payment_method": "WALLET"
     }
     ```
   - **Réponse** :
     ```json
     {
       "success": true,
       "payment_status": "PAID",
       "message": "Paiement effectué avec succès"
     }
     ```

Une fois la course payée, le workflow reprend à l’étape **5 – Téléchargement du QR code d’embarquement**, puis suit les mêmes étapes jusqu’à la fin de la navette.

---

#### 4. Actions du batelier

#### 4. Actions du batelier
- **Acteur** : Batelier
- **Endpoint (liste)** : `GET /api/boatman/shuttles/`
- **Réponse** : Liste paginée des navettes qui lui sont assignées
- Aucun endpoint d'acceptation supplémentaire : la navette est déjà assignée par l'établissement. Le batelier peut directement démarrer la navette.

#### 5. Téléchargement du QR code d'embarquement
- **Acteur** : Client
- **Endpoint** : `GET /api/trips/{trip_id}/qr-code/`
- **Réponse** : Image du QR code ou URL vers l'image (même pour les navettes gratuites)

#### 6. Validation du QR code client
- **Acteur** : Batelier
- **Endpoint** : `POST /api/boatman/shuttle/{shuttle_id}/validate-qr/`
- **Payload** :
  ```json
  {
    "qr_code": "<qr_code_scanné>"
  }
  ```
- **Réponse** : Confirmation de la validation du QR code (embarquement validé)

#### 7. Démarrage de la navette

- **Acteur** : Batelier
- **Endpoint** : `POST /api/boatman/shuttle/{shuttle_id}/start/`
-
- **Réponse** : Confirmation du démarrage de la navette

#### 8. Suivi en temps réel

- **Acteur** : Client/Batelier/Établissement
- **Endpoint** : `GET /api/boatman/shuttle/{shuttle_id}/track/`
- **Réponse** : Positions actuelles et historiques

#### 9. Fin de la navette

- **Acteur** : Batelier
- **Endpoint** : `POST /api/boatman/shuttle/{shuttle_id}/end/`
- **Payload** :
- **Réponse** : Confirmation de la fin de la navette

#### 10. Évaluation

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/{trip_id}/review/`
- **Payload** :
  ```json
  {
    "rating": 5,
    "comment": "Service de navette parfait et ponctuel"
  }
  ```
- **Réponse** : Confirmation de l'évaluation

### Acteurs impliqués

- **Client** : Crée la demande de navette, télécharge le QR code, peut évaluer le service
- **Établissement** : Reçoit la demande, l'assigne à un batelier
- **Batelier** : Reçoit l'assignation, accepte la navette, effectue la navette
- **Système** : Gère les notifications, génère le QR code, calcule la distance

## Gestion du Tableau de Bord Établissement

### 1. Récupération des informations du tableau de bord

#### Endpoint

```
GET /api/establishments/dashboard/
```

#### Headers

```
Authorization: Bearer <token_etablissement>
```

#### Réponse

```json
{
  "status": "success",
  "data": {
    "establishment_name": "Nobu",
    "establishment_type": "RESTAURANT",
    "available_balance": 1250.5,
    "total_shuttles": 42,
    "pending_requests": 3,
    "availability": true,
    "shuttles": [
      {
        "shuttle_id": "550e8400-e29b-41d4-a716-446655440000",
        "date": "2025-06-15T20:30:00+02:00",
        "status": "Terminé",
        "departure": "Port de Cannes",
        "destination": "Île Sainte-Marguerite",
        "client": "Jean Dupont",
        "amount": 0.0
      },
      {
        "shuttle_id": "550e8400-e29b-41d4-a716-446655440001",
        "date": "2025-06-16T21:00:00+02:00",
        "status": "À venir",
        "departure": "Port de Cannes",
        "destination": "Île Saint-Honorat",
        "client": "Marie Martin",
        "amount": 0.0
      }
    ]
  }
}
```

### 2. Rechargement du portefeuille établissement

#### Endpoint

```
POST /api/trips/wallet/recharge/
```

#### Headers

```
Authorization: Bearer <token_etablissement>
Content-Type: application/json
```

#### Payload

```json
{
  "amount": 25.0,
  "payment_method_id": "pm_1PJ8Xj2eZvKYlo2C6Q4XKQ2x"
}
```

#### Réponse

```json
{
  "status": "succeeded",
  "amount": 25.0,
  "currency": "eur",
  "new_balance": 1275.5,
  "transaction_id": "txn_1PJ8Xj2eZvKYlo2C6Q4XKQ2x"
}
```

### 3. Vérification du solde du portefeuille

#### Endpoint

```
GET /api/trips/wallet/balance/
```

#### Headers

```
Authorization: Bearer <token_etablissement>
```

#### Réponse

```json
{
  "balance": 1275.5,
  "currency": "eur",
  "last_transaction": "2025-06-19T15:30:00+02:00"
}
```

### 4. Paiement d'une course par l'établissement

#### Endpoint

```
POST /api/trips/{trip_id}/payment/
```

#### Headers

```
Authorization: Bearer <token_etablissement>
Content-Type: application/json
```

#### Payload (paiement par carte)

```json
{
  "payment_method": "card",
  "payment_method_id": "pm_1PJ8Xj2eZvKYlo2C6Q4XKQ2x"
}
```

#### Payload (paiement par portefeuille)

```json
{
  "payment_method": "wallet"
}
```

#### Réponse

```json
{
  "status": "succeeded",
  "amount": 25.0,
  "currency": "eur",
  "payment_id": "pay_1PJ8Xj2eZvKYlo2C6Q4XKQ2x",
  "new_balance": 1250.5
}
```

## Mise à jour du Profil Établissement

### Endpoint

```
PATCH /api/accounts/establishment/profile/
```

### Headers

```
Authorization: Bearer <token_etablissement>
Content-Type: application/json
```

### Champs pouvant être mis à jour (PATCH)

Les champs suivants peuvent être modifiés directement via l’endpoint de mise à jour :

- **Utilisateur** : `first_name`, `last_name`, `phone_number`, `profile_picture`
- **Établissement** (dans `establishment_profile`) : `name`, `type`, `address`, `description`, `main_photo`, `secondary_photos`, `business_name`, `business_type`, `registration_number`, `tax_id`, `opening_hours`, `services_offered`, `average_rating`, `location_coordinates`, `website`, `social_media`

> ℹ️ **Disponibilité** : l’état `availability` / `is_available` est géré par un endpoint dédié :
>
> ```
> POST /api/establishments/toggle-availability/
> ```
>
> Envoyez `{ "availability": true }` ou `{ "availability": false }` pour activer ou désactiver votre disponibilité.
>
> Ce champ n’est donc pas pris en charge par le PATCH du profil.

> **Réponse attendue** :
>
> ```json
> {
>   "status": "success",
>   "availability": true
> }
> ```

### Exemple de payload

```json
{
  "first_name": "Jean",
  "last_name": "Dupont",
  "phone_number": "+***********",
  "establishment_profile": {
    "name": "Nobu Cannes",
    "description": "Restaurant de sushis avec vue sur la mer",
    "address": "Port Canto, 06400 Cannes",
    "website": "https://nobu-cannes.com"
  }
}
```

### Exemple de réponse

```json
{
  "status": "success",
  "data": {
    "updated_fields": [
      "first_name",
      "last_name",
      "phone_number",
      "establishment_profile.name",
      "establishment_profile.description",
      "establishment_profile.address",
      "establishment_profile.website"
    ],
    "profile_updated_at": "2025-06-20T16:58:00+02:00"
  },
  "message": "Profil mis à jour (7 champs modifiés)",
  "profile": {
    "id": 5,
    "user_type": "ESTABLISHMENT",
    "first_name": "Jean",
    "last_name": "Dupont",
    "phone_number": "+***********",
    "establishment": {
      "name": "Nobu Cannes",
      "description": "Restaurant de sushis avec vue sur la mer",
      "address": "Port Canto, 06400 Cannes",
      "website": "https://nobu-cannes.com"
    }
  }
}
```

## Comment tester chaque type de course

### Pour tester une Course Simple

1. **Acteur** : Client (type CLIENT)
2. **Endpoint** : `POST /api/trips/requests/simple/`
3. **Payload** : Utiliser le payload de course simple fourni ci-dessus
4. **Suivi** : Utiliser l'ID de la demande retourné pour suivre le statut via `GET /api/trips/requests/{request_id}/`
5. **Acceptation du devis** : `POST /api/trips/quotes/{quote_id}/accept/`
6. **Paiement** : `POST /api/trips/{trip_id}/payment/`
7. **Compensation carbone (optionnel)** : `POST /api/trips/{trip_id}/carbon-compensation/`
8. **QR Code** : `GET /api/trips/{trip_id}/qr-code/`

### Pour tester une Mise à Disposition

1. **Acteur** : Client (type CLIENT)
2. **Endpoint** : `POST /api/trips/requests/hourly/`
3. **Payload** : Utiliser le payload de mise à disposition fourni ci-dessus
4. **Suivi** : Utiliser l'ID de la demande retourné pour suivre le statut via `GET /api/trips/requests/{request_id}/`
5. **Acceptation du devis** : `POST /api/trips/quotes/{quote_id}/accept/`
6. **Paiement** : `POST /api/trips/{trip_id}/payment/`
7. **Compensation carbone (optionnel)** : `POST /api/trips/{trip_id}/carbon-compensation/`
8. **QR Code** : `GET /api/trips/{trip_id}/qr-code/`

### Pour tester une Navette Gratuite

1. **Acteur** : Client (type CLIENT)
2. **Endpoint** : `POST /api/trips/requests/shuttle/`
3. **Payload** : Utiliser le payload de navette gratuite fourni ci-dessus
4. **Suivi** : Utiliser l'ID de la demande retourné pour suivre le statut via `GET /api/trips/requests/{request_id}/`
5. **Côté établissement** :
   - Se connecter en tant qu'établissement
   - Voir les demandes : `GET /api/establishments/shuttle-requests/`
   - Assigner à un batelier : `POST /api/establishments/shuttle-requests/{request_id}/assign/`
6. **Côté batelier** :
   - Se connecter en tant que batelier
   - Voir les navettes assignées : `GET /api/boatman/assigned-shuttles/`
   - Accepter la navette : `POST /api/boatman/shuttles/{shuttle_id}/accept/`
7. **QR Code** : `GET /api/trips/{trip_id}/qr-code/`

## 4. Espace Capitaine (CAPTAIN SPACE – Mobile)

### 1. Dashboard Capitaine

- **Acteur** : Capitaine
- **Endpoint** : `GET /api/trips/captain/dashboard/`
- **Réponse** :

  ```json
  {
    "captain": {
      "id": 209,
      "full_name": "Dossou DAHOUI",
      "avatar_url": "https://cdn.commodore.com/profiles/alpha.jpg",
      "language": "",
      "currency": "EUR",
      "is_available": true
    },
    "wallet": {
      "available_balance": 0
    },
    "stats": {
      "trips_completed": 1,
      "revenue_total": 18366.3
    },
    "upcoming_trips": []
  }
  ```

````

### 2. Historique des Courses
- **Endpoint** : `GET /api/trips/captain/history/`
- **Query params** : `status`, `date_from`, `date_to`
- **Réponse** :
  ```json
  {
    "success": true,
    "trips": [
      {
        "id": 30,
        "status": "COMPLETED",
        "start_label": "Port de Cannes",
        "end_label": "Îles de Lérins",
        "scheduled_start_time": "2025-06-12T09:43:16.110844+02:00",
        "scheduled_end_time": "2025-06-12T10:43:16.110844+02:00",
        "actual_start_time": "2025-06-12T12:31:13.250338+02:00",
        "actual_end_time": "2025-06-12T13:20:29.924226+02:00",
        "client_name": "Marie Dubois",
        "boat_name": "Alpha One",
        "boat_thumbnail": "https://cdn.commodore.com/boats/alpha1.jpg",
        "passenger_count": 8,
        "total_price": "18366.30",
        "payment_status": "PAID",
        "trip_type": "COURSE_SIMPLE"
      }
    ],
    "statistics": {
      "total_trips": 1,
      "completed_trips": 1,
      "cancelled_trips": 0,
      "completion_rate": 100,
      "total_revenue": 18366.3,
      "average_rating": 4.8,
      "recent_activity": {
        "trips_last_30_days": 1,
        "completed_last_30_days": 1
      }
    }
  }
````

### 3. Détail d’une Course

- **Endpoint** : `GET /api/trips/captain/trips/{trip_id}/`
- **Réponse** :
  ```json
  {
    "id": 346,
    "status": "ACCEPTED",
    "scheduled_start_time": "2025-06-20T09:00:00+02:00",
    "actual_start_time": null,
    "actual_end_time": null,
    "start_location": "Port de Cannes",
    "end_location": "Îles de Lérins",
    "passenger_count": 6,
    "price": "600.00",
    "currency": "EUR",
    "client": {
      "id": 45,
      "name": "Jean Dupont",
      "phone": "+33123456789",
      "avatar_url": ""
    },
    "boat": {
      "id": 90,
      "name": "La Belle Vie",
      "type": "LUXURY",
      "capacity": 8
    },
    "can_start": true,
    "can_complete": false
  }
  ```

### 4. Disponibilité

- **Endpoint** : `GET /api/trips/captain/availability/`
- **Réponse** :

  ```json
  {
    "id": 30,
    "status": "COMPLETED",
    "scheduled_start_time": "2025-06-12T09:43:16.110844+02:00",
    "actual_start_time": "2025-06-12T12:31:13.250338+02:00",
    "actual_end_time": "2025-06-12T13:20:29.924226+02:00",
    "start_location": "Port de Cannes",
    "end_location": "Îles de Lérins",
    "passenger_count": 8,
    "total_price": "18366.30",
    "client": {
      "id": 161,
      "name": "Marie Dubois",
      "phone": "+33123456789",
      "avatar_url": "https://cdn.commodore.com/profiles/marie.jpg"
    },
    "boat": {
      "id": 80,
      "name": "Alpha One",
      "type": "CLASSIC",
      "capacity": 8,
      "registration_number": "BN000114"
    },
    "can_start": false,
    "can_complete": false
  }
  ```

- **Mise à jour** : `POST /api/trips/captain/availability/` avec :
  ```json
  {
    "is_available": true
  }
  ```
  **Réponse :**
  ```json
  {
    "success": true,
    "is_available": true
  }
  ```

### 5. Portefeuille Capitaine

- **Endpoint** : `GET /api/payments/wallet/`
- **Réponse** :
  ```json
  {
    "success": true,
    "balance": 1200.5,
    "transactions": [
      {
        "id": 123,
        "amount": 350.0,
        "date": "2025-06-10T14:30:22+02:00",
        "type": "CREDIT",
        "description": "Course #45 - Port de Cannes",
        "status": "COMPLETED"
      },
      {
        "id": 122,
        "amount": -200.0,
        "date": "2025-06-05T09:15:43+02:00",
        "type": "DEBIT",
        "description": "Retrait vers compte bancaire",
        "status": "COMPLETED"
      }
    ]
  }
  ```

---

### 7. Chatbot Commodore (Intégration Mobile)

#### Endpoint pour poser une question au chatbot

- **POST** `/api/rag/chat/api/`

#### Exemple de requête JSON

```json
{
  "message": "combien de temps prend un capitaine et a combien est l'emprunte carbone",
  "profile": "Client"
}
```

- `message` : texte de la question de l'utilisateur
- `profile` : (optionnel) profil utilisateur (`Client`, `Capitaine`, `Établissement`)

#### Exemple de réponse JSON

```json
{
  "response": "Bonjour! Pour répondre à votre question, les capitanes de Commodore sont experts qualifiés et expérimentés dans leur domaine. Ils ont une formation spécifique pour satisfaire les besoins de nos clients. Concernant l'empreinte carbone, Commodore propose une option de compensation pour les trajets effectués. Vous pouvez opter pour ce service lors de la réservation de votre course et nous vous enverrons un rapport détaillé de votre impact carbone. Nous sommes également en train de développer des solutions pour réduire notre impact environnemental.\n\nIl est important de noter que nos capitanes sont professionnels qualifiés et ont une grande expérience dans leur domaine. Si vous avez d'autres questions ou préférez discuter plus en détail de vos besoins, n'hésitez pas à nous contacter. Nous sommes là pour vous aider! Pour réserver une course ou en savoir plus sur nos services, veuillez visiter notre site web ou nous contacter par téléphone.\n\nConseil : Il est recommandé de réserver votre course avec au moins 30 minutes d'avance pour garantir la disponibilité du capitaine et du bateau-taxi souhaités. Vous pouvez également configurer vos paramètres de notification pour recevoir des mises à jour en temps réel sur l'état de votre réservation.\n\nConformément au RGPD, vos données personnelles sont traitées de manière sécurisée et ne sont conservées que pour la durée nécessaire au service.\n\nPour toute assistance supplémentaire, contactez notre équipe à <EMAIL>.",
  "sources": [
    {
      "title": "Informations supplementaires",
      "section": "# Informations supplémentaires - Commodore"
    },
    {
      "title": "Informations supplementaires",
      "section": "# Informations supplémentaires - Commodore"
    },
    {
      "title": "Informations supplementaires",
      "section": "# Informations supplémentaires - Commodore"
    },
    {
      "title": "Informations supplementaires",
      "section": "# Informations supplémentaires - Commodore"
    },
    {
      "title": "Informations supplementaires",
      "section": "# Informations supplémentaires - Commodore"
    }
  ],
  "response_time": 3.84,
  "profile": "Client",
  "message_id": "65b235b1-ca72-4288-8314-095242dde162"
}
```

- `response` : réponse générée par le chatbot
- `sources` : liste des documents consultés pour la réponse
- `response_time` : temps de génération (secondes)
- `profile` : profil utilisateur
- `message_id` : identifiant unique du message (pour feedback, etc.)

---

### 8. Lien entre l’application Message et le module RAG

Le module **RAG** (Retrieval Augmented Generation) est le moteur d’intelligence qui génère les réponses du chatbot Commodore.
L’application **message** (modèle `ChatMessage`) sert à enregistrer chaque question et chaque réponse dans la base de données.

#### Fonctionnement

- Quand un utilisateur pose une question depuis l’application mobile, un objet `ChatMessage` est créé côté serveur.
- Le module RAG utilise l’historique des `ChatMessage` pour générer une réponse adaptée et contextuelle.
- La réponse du RAG est aussi enregistrée comme un `ChatMessage` (rôle = assistant).
- Chaque message a un identifiant unique (`message_id`) pour assurer le suivi, le feedback, ou la suppression (RGPD).

#### Importance pour l’intégration mobile

- **message_id** permet de lier chaque échange mobile à l’historique serveur.
- Il permet d’envoyer un feedback, de retrouver une discussion, ou de demander la suppression d’un message précis.
- Toute l’intelligence (RAG) et la mémoire (message) sont donc synchronisées entre le mobile et le backend Commodore.

**En résumé** :

- Le module RAG génère les réponses intelligentes.
- L’application message garde la mémoire de chaque échange.
- Le champ `message_id` fait le lien entre les deux et permet un suivi précis côté mobile.
