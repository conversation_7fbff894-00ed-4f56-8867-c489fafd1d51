"""
Service de génération et validation des QR codes pour les courses.
Supporte les trois types de voyages : courses payantes, navettes gratuites, et paiements post-voyage.
"""

import qrcode
import io
import base64
import hashlib
import uuid
from django.conf import settings
from django.urls import reverse
from django.utils import timezone
import json


def generate_secure_trip_code(trip):
    """
    Génère un code sécurisé unique pour une course.

    Args:
        trip: Instance du modèle Trip

    Returns:
        str: Code sécurisé unique
    """
    # Créer une chaîne unique basée sur les détails de la course
    unique_elements = [
        str(trip.id),
        str(trip.client.user.id) if trip.client else 'no_client',
        str(trip.captain.user.id) if trip.captain else 'no_captain',
        str(trip.scheduled_start_time.timestamp()) if trip.scheduled_start_time else str(timezone.now().timestamp()),
        str(uuid.uuid4())
    ]

    unique_string = '_'.join(unique_elements)

    # Créer un hash SHA-256 et prendre les 16 premiers caractères
    qr_hash = hashlib.sha256(unique_string.encode()).hexdigest()[:16]

    # Format standardisé: COMMODORE_TRIP_[ID]_[HASH]
    return f"COMMODORE_TRIP_{trip.id}_{qr_hash}"


def generate_trip_qr_code(trip):
    """
    Génère un QR code sécurisé pour un ticket de course.

    Args:
        trip: L'objet Trip

    Returns:
        str: Code sécurisé (pas d'image, juste le code)
    """
    try:
        # Générer le code sécurisé
        secure_code = generate_secure_trip_code(trip)

        # Stocker le code dans la base de données
        trip.qr_code = secure_code
        trip.save(update_fields=['qr_code'])

        return secure_code

    except Exception as e:
        print(f"Erreur lors de la génération du QR code: {str(e)}")
        return None


def generate_trip_qr_image(trip):
    """
    Génère une image QR code pour un ticket de course.

    Args:
        trip: L'objet Trip

    Returns:
        str: QR code encodé en base64
    """
    try:
        # Utiliser le code sécurisé ou en générer un nouveau
        qr_content = trip.qr_code or generate_trip_qr_code(trip)

        if not qr_content:
            return None

        # Créer le QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_M,
            box_size=10,
            border=4,
        )
        qr.add_data(qr_content)
        qr.make(fit=True)

        # Créer l'image
        img = qr.make_image(fill_color="black", back_color="white")

        # Convertir en base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"

    except Exception as e:
        print(f"Erreur lors de la génération de l'image QR code: {str(e)}")
        return None


def generate_simple_qr_code(trip_id):
    """
    Génère un QR code simple avec juste l'ID de la course

    Args:
        trip_id: ID de la course

    Returns:
        str: QR code encodé en base64
    """
    try:
        # URL de vérification simple
        verification_url = f"{settings.FRONTEND_URL}/verify/{trip_id}"

        # Créer le QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_M,
            box_size=8,
            border=2,
        )
        qr.add_data(verification_url)
        qr.make(fit=True)

        # Créer l'image
        img = qr.make_image(fill_color="black", back_color="white")

        # Convertir en base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"

    except Exception as e:
        print(f"Erreur lors de la génération du QR code simple: {str(e)}")
        return None


def validate_trip_qr_code(trip, provided_qr):
    """
    Valide un QR code fourni pour une course.

    Args:
        trip: Instance du modèle Trip
        provided_qr: QR code fourni par le client

    Returns:
        tuple: (is_valid: bool, message: str)
    """
    # Vérifications de base
    if not trip.qr_code:
        return False, "Aucun QR code généré pour cette course"

    if not provided_qr:
        return False, "QR code requis"

    # Vérifier que le QR code correspond
    if trip.qr_code != provided_qr:
        return False, "QR code invalide"

    # Vérifier le statut de la course
    if trip.status not in [trip.Status.ACCEPTED, trip.Status.IN_PROGRESS]:
        return False, f"Impossible de valider le QR code pour une course avec le statut: {trip.get_status_display()}"

    # Vérifier que la course est payée (sauf pour les navettes gratuites)
    if hasattr(trip, 'trip_type') and trip.trip_type != 'NAVETTES_GRATUITES' and trip.payment_status != 'PAID':
        return False, "Course non payée - validation impossible"

    # Vérifier l'heure (ne peut pas être validé trop tôt)
    if trip.scheduled_start_time:
        time_until_start = trip.scheduled_start_time - timezone.now()
        if time_until_start.total_seconds() > 30 * 60:  # Plus de 30 minutes avant
            return False, "Validation trop précoce - veuillez attendre l'heure de départ"

    return True, "QR code validé avec succès"


def generate_carbon_compensation_qr(trip, compensation_amount):
    """
    Génère un QR code pour le paiement de compensation carbone.

    Args:
        trip: Instance du modèle Trip
        compensation_amount: Montant de la compensation

    Returns:
        str: QR code pour la compensation carbone
    """
    unique_elements = [
        'CARBON_COMPENSATION',
        str(trip.id),
        str(compensation_amount),
        str(timezone.now().timestamp()),
        str(uuid.uuid4())
    ]

    unique_string = '_'.join(unique_elements)
    qr_hash = hashlib.sha256(unique_string.encode()).hexdigest()[:16]

    return f"COMMODORE_CARBON_{trip.id}_{qr_hash}"


def generate_tip_qr(trip, tip_amount):
    """
    Génère un QR code pour le paiement de pourboire.

    Args:
        trip: Instance du modèle Trip
        tip_amount: Montant du pourboire

    Returns:
        str: QR code pour le pourboire
    """
    unique_elements = [
        'TIP_PAYMENT',
        str(trip.id),
        str(trip.captain.user.id) if trip.captain else 'no_captain',
        str(tip_amount),
        str(timezone.now().timestamp()),
        str(uuid.uuid4())
    ]

    unique_string = '_'.join(unique_elements)
    qr_hash = hashlib.sha256(unique_string.encode()).hexdigest()[:16]

    return f"COMMODORE_TIP_{trip.id}_{qr_hash}"


def verify_qr_code(qr_data):
    """
    Vérifie un QR code et retourne les informations de la course.
    Supporte les nouveaux formats sécurisés.

    Args:
        qr_data: Données du QR code (code sécurisé, JSON string ou URL)

    Returns:
        dict: Informations de vérification
    """
    try:
        from .models import Trip

        # Nouveau format sécurisé COMMODORE_*
        if qr_data.startswith('COMMODORE_'):
            parts = qr_data.split('_')

            if len(parts) >= 4 and parts[1] == 'TRIP':
                try:
                    trip_id = int(parts[2])
                    trip = Trip.objects.get(id=trip_id)

                    # Valider le QR code
                    is_valid, message = validate_trip_qr_code(trip, qr_data)

                    return {
                        'valid': is_valid,
                        'trip': trip,
                        'message': message,
                        'qr_type': 'TRIP'
                    }

                except (ValueError, Trip.DoesNotExist):
                    return {
                        'valid': False,
                        'message': 'Course non trouvée'
                    }

            elif len(parts) >= 4 and parts[1] in ['CARBON', 'TIP']:
                try:
                    trip_id = int(parts[2])
                    trip = Trip.objects.get(id=trip_id)

                    return {
                        'valid': True,
                        'trip': trip,
                        'message': f'QR code {parts[1].lower()} valide',
                        'qr_type': parts[1]
                    }

                except (ValueError, Trip.DoesNotExist):
                    return {
                        'valid': False,
                        'message': 'Course non trouvée'
                    }

        # Format URL legacy
        elif qr_data.startswith('http') and '/verify/' in qr_data:
            trip_id = qr_data.split('/verify/')[-1]
            try:
                trip = Trip.objects.get(id=int(trip_id))
                return {
                    'valid': True,
                    'trip': trip,
                    'message': 'Ticket valide (format legacy)',
                    'qr_type': 'LEGACY'
                }
            except Trip.DoesNotExist:
                return {
                    'valid': False,
                    'message': 'Ticket non trouvé'
                }

        # Format JSON legacy
        else:
            try:
                data = json.loads(qr_data)
                trip_id = data.get('trip_id')

                if not trip_id:
                    return {
                        'valid': False,
                        'message': 'QR code invalide'
                    }

                trip = Trip.objects.get(id=trip_id)

                # Vérifications supplémentaires
                if trip.status == 'CANCELLED':
                    return {
                        'valid': False,
                        'trip': trip,
                        'message': 'Ticket annulé'
                    }

                return {
                    'valid': True,
                    'trip': trip,
                    'message': 'Ticket valide (format JSON legacy)',
                    'qr_data': data,
                    'qr_type': 'JSON_LEGACY'
                }

            except (json.JSONDecodeError, Trip.DoesNotExist):
                return {
                    'valid': False,
                    'message': 'QR code invalide ou ticket non trouvé'
                }

    except Exception as e:
        return {
            'valid': False,
            'message': f'Erreur de vérification: {str(e)}'
        }
