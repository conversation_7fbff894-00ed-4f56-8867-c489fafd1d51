"""
Module de gestion de l'espace capitaine.

Ce module contient les vues spécifiques aux capitaines pour gérer
leurs courses, leur profil et leurs statistiques.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count, Avg, Sum
from django.utils import timezone
from datetime import timedelta
from .models import Trip, TripQuote
from .serializers import TripSerializer, TripPreviewSerializer, TripDetailCaptainSerializer, CaptainTripHistorySerializer
from accounts.models import Captain
from boats.models import Boat
from accounts.permissions import IsCaptain
from reviews.models import Review  # Import the correct Review model
from django.contrib.contenttypes.models import ContentType  # Import ContentType for generic foreign key


class CaptainTripsHistoryView(APIView):
    """
    Endpoint pour voir l'historique des courses du capitaine.
    """
    permission_classes = [IsAuthenticated, IsCaptain]

    def get(self, request):
        """
        Retourne l'historique des courses du capitaine connecté.
        
        URL: GET /api/trips/captain/history/
        Query params:
        - status: filtrer par statut (COMPLETED, CANCELLED, etc.)
        - limit: nombre de courses à retourner (défaut: 20)
        - offset: décalage pour pagination
        - date_from: date de début (YYYY-MM-DD)
        - date_to: date de fin (YYYY-MM-DD)
        """
        
        try:
            # La permission IsCaptain s'en charge déjà
            captain = request.user.captain
            
            # Paramètres de filtrage
            status_filter = request.GET.get('status')
            date_from = request.GET.get('date_from')
            date_to = request.GET.get('date_to')
            
            # Construire la requête
            trips_query = Trip.objects.filter(captain=captain)
            
            # Filtres
            if status_filter:
                trips_query = trips_query.filter(status=status_filter)
            
            if date_from:
                from django.utils.dateparse import parse_date
                date_from_parsed = parse_date(date_from)
                if date_from_parsed:
                    trips_query = trips_query.filter(created_at__date__gte=date_from_parsed)
            
            if date_to:
                from django.utils.dateparse import parse_date
                date_to_parsed = parse_date(date_to)
                if date_to_parsed:
                    trips_query = trips_query.filter(created_at__date__lte=date_to_parsed)
            
            # Ordonner par date décroissante
            trips_query = trips_query.order_by('-created_at')
            
            # Sérialiser les données avec le sérialiseur optimisé
            serializer = CaptainTripHistorySerializer(trips_query, many=True)
            
            # Calculer les statistiques
            stats = self._calculate_captain_stats(captain, trips_query)
            
            return Response({
                'success': True,
                'trips': serializer.data,
                'statistics': stats
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Erreur lors de la récupération de l\'historique: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _calculate_captain_stats(self, captain, trips_query):
        """Calcule les statistiques du capitaine"""
        
        # Statistiques générales
        total_trips = trips_query.count()
        completed_trips = trips_query.filter(status=Trip.Status.COMPLETED).count()
        cancelled_trips = trips_query.filter(
            status__in=[Trip.Status.CANCELLED, Trip.Status.CANCELLED_BY_CLIENT, Trip.Status.CANCELLED_BY_CAPTAIN]
        ).count()
        
        # Revenus totaux
        total_revenue = trips_query.filter(
            status=Trip.Status.COMPLETED,
            payment_status='PAID'
        ).aggregate(total=Sum('total_price'))['total'] or 0
        
        # Note moyenne
        avg_rating = captain.average_rating or 0
        
        # Statistiques temporelles (30 derniers jours)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_trips = trips_query.filter(created_at__gte=thirty_days_ago)
        recent_completed = recent_trips.filter(status=Trip.Status.COMPLETED).count()
        
        return {
            'total_trips': total_trips,
            'completed_trips': completed_trips,
            'cancelled_trips': cancelled_trips,
            'completion_rate': round((completed_trips / total_trips * 100) if total_trips > 0 else 0, 1),
            'total_revenue': float(total_revenue),
            'average_rating': float(avg_rating),
            'recent_activity': {
                'trips_last_30_days': recent_trips.count(),
                'completed_last_30_days': recent_completed
            }
        }


class CaptainDashboardView(APIView):
    """
    Endpoint pour le tableau de bord du capitaine.
    """
    permission_classes = [IsAuthenticated, IsCaptain]

    def get(self, request):
        """
        Retourne les informations du tableau de bord du capitaine.
        
        URL: GET /api/trips/captain/dashboard/
        """
        
        try:
            # Vérifier que l'utilisateur est un capitaine
            if not hasattr(request.user, 'captain'):
                return Response({
                    'error': 'Seuls les capitaines peuvent voir leur tableau de bord'
                }, status=status.HTTP_403_FORBIDDEN)
            
            captain = request.user.captain
            
            # Prochaines courses (en attente ou acceptées), limité à 3
            upcoming_statuses = [
                getattr(Trip.Status, s)
                for s in (
                    'PENDING',
                    'ACCEPTED',
                    'SCHEDULED'
                ) if hasattr(Trip.Status, s)
            ]

            upcoming_trips_qs = (
                Trip.objects.filter(captain=captain, status__in=upcoming_statuses)
                .order_by('scheduled_start_time')[:3]
            )

            upcoming_trips = TripPreviewSerializer(upcoming_trips_qs, many=True).data

            # Statistiques globales
            # Calculer le nombre réel de courses terminées
            trips_completed = Trip.objects.filter(captain=captain, status=Trip.Status.COMPLETED).count()
            
            # Mettre à jour le modèle Captain pour assurer la cohérence avec l'interface d'administration
            if captain.total_trips != trips_completed:
                captain.total_trips = trips_completed
                captain.save(update_fields=['total_trips'])
            revenue_total = Trip.objects.filter(
                captain=captain,
                status=Trip.Status.COMPLETED,
                payment_status='PAID'
            ).aggregate(total=Sum('total_price'))['total'] or 0

            # Corrected fetch for captain reviews
            content_type = ContentType.objects.get_for_model(Captain)
            comments = [
                {'text': review.content, 'created_at': review.created_at}
                for review in Review.objects.filter(type='CAPTAIN', object_id=captain.id, content_type=content_type).order_by('-created_at')
            ]

            return Response({
                'captain': {
                    'id': captain.user.id,
                    'full_name': captain.user.get_full_name(),
                    'avatar_url': captain.user.profile_picture if captain.user.profile_picture else '',
                    'language': captain.user.preferred_language if hasattr(captain.user, 'preferred_language') else '',
                    'currency': 'EUR',
                    'is_available': captain.is_available,
                },
                'wallet': {
                    'available_balance': float(captain.wallet_balance)
                },
                'stats': {
                    'trips_completed': trips_completed,
                    'revenue_total': float(revenue_total)
                },
                'upcoming_trips': upcoming_trips,
                'comments': comments,  # Include comments in the response
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Erreur lors de la récupération du tableau de bord: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CaptainAvailabilityView(APIView):
    """
    Endpoint pour gérer la disponibilité du capitaine.
    """
    permission_classes = [IsAuthenticated, IsCaptain]

    def get(self, request):
        """Retourne le statut de disponibilité actuel"""
        
        if not hasattr(request.user, 'captain'):
            return Response({
                'error': 'Seuls les capitaines peuvent voir leur disponibilité'
            }, status=status.HTTP_403_FORBIDDEN)
        
        captain = request.user.captain
        
        return Response({
            'success': True,
            'availability': {
                'is_available': captain.is_available,
                'status': captain.availability_status,
                'current_location': captain.current_location
            }
        })

    def post(self, request):
        """
        Met à jour la disponibilité du capitaine (champ unique).
        """
        try:
            if not hasattr(request.user, 'captain'):
                return Response({'error': 'Seuls les capitaines peuvent modifier leur disponibilité'}, status=status.HTTP_403_FORBIDDEN)
            captain = request.user.captain

            is_available = request.data.get('is_available')
            if is_available is None:
                return Response({'error': 'Le champ is_available est requis'}, status=status.HTTP_400_BAD_REQUEST)
            captain.is_available = bool(is_available)
            captain.save(update_fields=['is_available'])
            return Response({'success': True, 'is_available': captain.is_available}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# -------------------------------------------------
# Détail d'une course pour l'espace capitaine
# -------------------------------------------------

class CaptainTripDetailView(APIView):
    """Endpoint détaillé d'une course pour le capitaine."""
    permission_classes = [IsAuthenticated, IsCaptain]
    
    def get(self, request, trip_id):
        try:
            # Vérifier que l'utilisateur est bien capitaine
            if not hasattr(request.user, 'captain'):
                return Response({'error': 'Seuls les capitaines peuvent consulter cette ressource'}, status=status.HTTP_403_FORBIDDEN)

            trip = get_object_or_404(Trip, id=trip_id, captain=request.user.captain)

            serializer = TripDetailCaptainSerializer(trip)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
