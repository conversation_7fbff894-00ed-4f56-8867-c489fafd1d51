from django.db import models
from django.utils import timezone


class APIDocumentation(models.Model):
    """
    Modèle pour stocker la documentation générée par OpenAI
    """
    title = models.CharField(max_length=200, default="Commodore Taxi Boat API Documentation")
    content = models.TextField()
    generated_at = models.DateTimeField(default=timezone.now)
    openai_model = models.CharField(max_length=50, default="gpt-4")
    is_active = models.BooleanField(default=True)
    version = models.CharField(max_length=20, default="1.0")
    
    class Meta:
        ordering = ['-generated_at']
        verbose_name = "Documentation API"
        verbose_name_plural = "Documentations API"
    
    def __str__(self):
        return f"{self.title} - {self.generated_at.strftime('%Y-%m-%d %H:%M')}"


class APIEndpoint(models.Model):
    """
    Modèle pour cataloguer les endpoints de l'API
    """
    path = models.Char<PERSON>ield(max_length=200)
    method = models.Cha<PERSON><PERSON><PERSON>(max_length=10)
    description = models.TextField(blank=True)
    app_name = models.Char<PERSON>ield(max_length=50)
    view_name = models.CharField(max_length=100)
    parameters = models.JSONField(default=dict, blank=True)
    response_example = models.JSONField(default=dict, blank=True)
    is_documented = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['path', 'method']
        ordering = ['app_name', 'path']
    
    def __str__(self):
        return f"{self.method} {self.path}"
