import os
import sys
import django
from datetime import datetime, timedelta
from decimal import Decimal
from django.utils import timezone

# Configurer l'environnement Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

# Importer les modèles après avoir configuré Django
from django.contrib.auth import get_user_model
from accounts.models import Client, Captain
from boats.models import Boat
from trips.models import Trip, Location

User = get_user_model()

def create_test_data():
    print("Création des données de test...")
    
    # Créer un superutilisateur si nécessaire
    if not User.objects.filter(email='<EMAIL>').exists():
        User.objects.create_superuser(
            email='<EMAIL>',
            password='admin123',
            is_active=True
        )
        print("Superutilisateur créé: <EMAIL> / admin123")
    
    # Créer un utilisateur client
    client_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'client123',
            'first_name': 'Jean',
            'last_name': 'Dupont',
            'type': 'CLIENT',
            'is_active': True,
            'email_verified': True
        }
    )
    if created:
        client_user.set_password('client123')
        client_user.save()
        print("Utilisateur client créé: <EMAIL> / client123")
    
    # Créer un client associé à l'utilisateur
    client, created = Client.objects.get_or_create(
        user=client_user
    )
    if created:
        print("Client créé pour Jean Dupont")
    
    # Créer un utilisateur capitaine
    captain_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'captain123',
            'first_name': 'Michel',
            'last_name': 'Leroy',
            'type': 'CAPTAIN',
            'is_active': True,
            'email_verified': True
        }
    )
    if created:
        captain_user.set_password('captain123')
        captain_user.save()
        print("Utilisateur capitaine créé: <EMAIL> / captain123")
    
    # Créer un capitaine associé à l'utilisateur
    captain, created = Captain.objects.get_or_create(
        user=captain_user,
        defaults={
            'experience': '10 ans en mer'
        }
    )
    if created:
        print("Capitaine créé pour Michel Leroy")
    
    # Créer un bateau
    boat, created = Boat.objects.get_or_create(
        name='Blue Wave',
        defaults={
            'registration_number': 'FR12345',
            'captain': captain,
            'color': 'Bleu',
            'capacity': 8,
            'fuel_type': 'DIESEL',
            'fuel_consumption': Decimal('12.5')
        }
    )
    if created:
        print("Bateau créé: Blue Wave")
    
    # Créer des trajets
    for i in range(1, 4):
        # Définir les heures de départ et d'arrivée avec timezone
        start_time = timezone.now() + timedelta(days=i)
        end_time = start_time + timedelta(hours=2)
        
        trip, created = Trip.objects.get_or_create(
            client=client,
            captain=captain,
            boat=boat,
            start_location=f"Port de départ {i}",  # Champ obligatoire
            end_location=f"Destination {i}",  # Champ obligatoire
            defaults={
                'status': 'PENDING',
                'base_price': Decimal(f'{100 + i * 50}.00'),  # Prix de base
                'additional_charges': Decimal('0.00'),  # Frais supplémentaires
                'tip': Decimal('0.00'),  # Pourboire
                'total_price': Decimal(f'{100 + i * 50}.00'),  # Prix total initial = prix de base
                'scheduled_start_time': start_time,  # Heure de départ prévue
                'scheduled_end_time': end_time,  # Heure d'arrivée prévue
                'passenger_count': 2,  # Nombre de passagers
                'passenger_names': [f'Passager {j}' for j in range(1, 3)],
                'tracking_data': [],
                'payment_status': 'PENDING'  # Statut du paiement
            }
        )
        if created:
            print(f"Trajet {i} créé: {trip.total_price} EUR, ID={trip.id}")
    
    print("\nListe des trajets disponibles pour les tests:")
    for i, trip in enumerate(Trip.objects.all()):
        print(f"Trajet {i+1}: ID={trip.id}, Prix={trip.total_price} EUR, Statut={trip.status}")
    
    print("\nVous pouvez maintenant exécuter les tests de paiement avec les IDs de trajets ci-dessus.")

if __name__ == "__main__":
    create_test_data()
