#!/usr/bin/env python3
"""
Script de test pour évaluer les améliorations conversationnelles du RAG.
"""

import os
import sys
import django
from pathlib import Path

# Ajouter le répertoire du projet au path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from rag.rag_service import RagService
from rag.models import ChatSession, Document
from django.contrib.auth import get_user_model

def test_conversational_improvements():
    """
    Teste les améliorations conversationnelles du RAG.
    """
    print("🚀 Test des améliorations conversationnelles du RAG Commodore")
    print("=" * 60)
    
    try:
        # Initialiser le service RAG
        rag_service = RagService()
        print("✅ Service RAG initialisé avec succès")
        
        # Créer un utilisateur de test
        User = get_user_model()
        test_user, created = User.objects.get_or_create(
            username='test_conversational',
            defaults={'email': '<EMAIL>', 'is_superuser': True}
        )
        
        # Créer une session de test
        session = ChatSession.objects.create(
            user=test_user,
            title="Test Conversationnel"
        )
        print(f"✅ Session de test créée: {session.id}")
        
        # Tests conversationnels
        test_cases = [
            # Salutations
            ("salut", "Client"),
            ("bonjour", "Capitaine"),
            ("hello", "Établissement"),
            
            # Questions sociales
            ("comment ça va ?", "Client"),
            ("comment allez-vous ?", "Client"),
            
            # Demandes d'aide vagues
            ("aide-moi", "Client"),
            ("j'ai besoin d'aide", "Client"),
            ("que peux-tu faire ?", "Client"),
            
            # Problèmes
            ("j'ai un problème", "Client"),
            ("ça ne marche pas", "Client"),
            ("je suis bloqué", "Capitaine"),
            
            # Questions vagues nécessitant clarification
            ("comment ça marche ?", "Client"),
            ("c'est quoi commodore ?", "Client"),
            
            # Au revoir
            ("merci", "Client"),
            ("au revoir", "Client"),
            ("bonne journée", "Capitaine"),
        ]
        
        print("\n🧪 Tests des réponses conversationnelles:")
        print("-" * 50)
        
        for i, (message, profile) in enumerate(test_cases, 1):
            print(f"\n{i}. Test: '{message}' (Profil: {profile})")
            print("-" * 30)
            
            try:
                # Analyser l'intention
                intent, entities = rag_service._analyze_query(message)
                print(f"🎯 Intention détectée: {intent}")
                print(f"🏷️  Entités: {entities}")
                
                # Générer la réponse
                response = rag_service.generate_response(session, message, profile)
                print(f"🤖 Réponse: {response[:200]}{'...' if len(response) > 200 else ''}")
                
            except Exception as e:
                print(f"❌ Erreur: {str(e)}")
            
            print()
        
        print("✅ Tests terminés avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {str(e)}")
        import traceback
        traceback.print_exc()

def test_intention_detection():
    """
    Teste spécifiquement la détection d'intentions.
    """
    print("\n🎯 Test de la détection d'intentions")
    print("=" * 40)
    
    try:
        rag_service = RagService()
        
        test_messages = [
            "salut",
            "bonjour comment allez-vous",
            "j'ai un gros problème",
            "comment ça marche commodore",
            "aide-moi s'il te plaît",
            "merci beaucoup",
            "comment réserver un bateau",
            "quel est le prix",
            "au revoir",
            "ça va bien et vous",
        ]
        
        for message in test_messages:
            intent, entities = rag_service._analyze_query(message)
            print(f"'{message}' → Intent: {intent}, Entités: {entities}")
            
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")

if __name__ == "__main__":
    test_intention_detection()
    test_conversational_improvements()
