from rest_framework import serializers
from .models import (
    <PERSON>, Shuttle, TripRequest, SimpleTripRequest,
    HourlyTripRequest, ShuttleTripRequest, TripQuote
)
from accounts.serializers import Client<PERSON>erializer, CaptainSerializer, EstablishmentSerializer
from boats.serializers import BoatSerializer
from boats.models import Boat
from .carbon_calculator import CarbonFootprintCalculator
from payments.models import Payment
from accounts.models import Captain, Establishment
from django.utils import timezone

class TripSerializer(serializers.ModelSerializer):
    client = ClientSerializer(read_only=True)
    captain = CaptainSerializer(read_only=True)
    boat = BoatSerializer(read_only=True)
    establishment = EstablishmentSerializer(read_only=True)
    distance_km = serializers.DecimalField(max_digits=8, decimal_places=2, read_only=True)

    # Informations supplémentaires pour l'affichage mobile
    captain_profile_picture = serializers.CharField(source='captain.user.profile_picture', read_only=True)

    # --- Carbon footprint / compensation details ---
    carbon_footprint = serializers.SerializerMethodField()
    eco_message = serializers.SerializerMethodField()
    can_compensate = serializers.SerializerMethodField()
    already_compensated = serializers.SerializerMethodField()
    compensation_payment_id = serializers.SerializerMethodField()
    captain_full_name = serializers.CharField(source='captain.user.get_full_name', read_only=True)
    boat_photos = serializers.JSONField(source='boat.photos', read_only=True)
    qr_code = serializers.CharField(read_only=True)

    class Meta:
        model = Trip
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'status', 'actual_start_time',
                          'actual_end_time', 'current_location', 'tracking_data',
                          'total_price', 'payment_status', 'qr_code', 'distance_km', 'carbon_footprint', 'eco_message', 'can_compensate', 'already_compensated', 'compensation_payment_id')

    def get_distance_km(self, obj):
        # Tente de lire des coordonnées JSON dans start_location et end_location
        import json
        from math import radians, cos, sin, asin, sqrt
        try:
            dep_coords = None
            arr_coords = None
            if hasattr(obj, 'start_location') and hasattr(obj, 'end_location'):
                if isinstance(obj.start_location, dict):
                    dep_coords = obj.start_location.get('coordinates')
                else:
                    try:
                        dep = json.loads(obj.start_location)
                        dep_coords = dep.get('coordinates')
                    except Exception:
                        dep_coords = None
                if isinstance(obj.end_location, dict):
                    arr_coords = obj.end_location.get('coordinates')
                else:
                    try:
                        arr = json.loads(obj.end_location)
                        arr_coords = arr.get('coordinates')
                    except Exception:
                        arr_coords = None
            if dep_coords and arr_coords:
                dep_lat = dep_coords.get('latitude')
                dep_lon = dep_coords.get('longitude')
                arr_lat = arr_coords.get('latitude')
                arr_lon = arr_coords.get('longitude')
                if all([dep_lat, dep_lon, arr_lat, arr_lon]):
                    dep_lat, dep_lon, arr_lat, arr_lon = map(float, [dep_lat, dep_lon, arr_lat, arr_lon])
                    dep_lat, dep_lon, arr_lat, arr_lon = map(radians, [dep_lat, dep_lon, arr_lat, arr_lon])
                    dlat = arr_lat - dep_lat
                    dlon = arr_lon - dep_lon
                    a = sin(dlat/2)**2 + cos(dep_lat) * cos(arr_lat) * sin(dlon/2)**2
                    c = 2 * asin(sqrt(a))
                    r = 6371
                    return round(c * r, 2)
        except Exception:
            pass
        return None

    def validate(self, data):
        # Vérifier que l'heure de fin est après l'heure de début
        if data.get('scheduled_end_time') and data.get('scheduled_start_time'):
            if data['scheduled_end_time'] <= data['scheduled_start_time']:
                raise serializers.ValidationError({
                    "scheduled_end_time": "L'heure de fin doit être après l'heure de début"
                })

        # Vérifier que le nombre de passagers ne dépasse pas la capacité du bateau
        if data.get('passenger_count') and data.get('boat'):
            if data['passenger_count'] > data['boat'].capacity:
                raise serializers.ValidationError({
                    "passenger_count": "Le nombre de passagers dépasse la capacité du bateau"
                })

        return data

    # ------------------ Carbon helper methods ------------------
    def _get_carbon_data(self, obj):
        """Compute carbon data when trip completed."""
        if obj.status == Trip.Status.COMPLETED:
            try:
                return CarbonFootprintCalculator.calculate_trip_carbon_data(obj)
            except Exception:
                return None
        return None

    def get_carbon_footprint(self, obj):
        data = self._get_carbon_data(obj)
        return data.get('calculation') if data else None

    def get_eco_message(self, obj):
        data = self._get_carbon_data(obj)
        return data.get('eco_message') if data else None

    def get_can_compensate(self, obj):
        data = self._get_carbon_data(obj)
        return data.get('can_compensate') if data else False

    def get_already_compensated(self, obj):
        return Payment.objects.filter(
            trip=obj,
            type=Payment.PaymentType.CARBON_OFFSET,
            status=Payment.Status.COMPLETED
        ).exists()

    def get_compensation_payment_id(self, obj):
        payment = Payment.objects.filter(
            trip=obj,
            type=Payment.PaymentType.CARBON_OFFSET,
            status=Payment.Status.COMPLETED
        ).first()
        return payment.id if payment else None

class TripListSerializer(serializers.ModelSerializer):
    """Sérialiseur simplifié pour la liste des courses avec profils complets"""
    client_name = serializers.CharField(source='client.user.get_full_name', read_only=True)
    captain_name = serializers.CharField(source='captain.user.get_full_name', read_only=True)
    boat_name = serializers.CharField(source='boat.name', read_only=True)
    client_profile = ClientSerializer(source='client', read_only=True)
    captain_profile = CaptainSerializer(source='captain', read_only=True)

    class Meta:
        model = Trip
        fields = (
            'id', 'client_name', 'captain_name', 'boat_name', 'start_location',
            'end_location', 'scheduled_start_time', 'status', 'total_price',
            'client_profile', 'captain_profile',
        )

class ShuttleSerializer(serializers.ModelSerializer):
    establishment = EstablishmentSerializer(read_only=True)
    boat = BoatSerializer(read_only=True)
    captain = CaptainSerializer(read_only=True)
    depart = serializers.CharField(source='departure_name', read_only=True)
    lieu_arriver = serializers.CharField(source='arrival_name', read_only=True)
    available_seats = serializers.SerializerMethodField()
    distance_km = serializers.DecimalField(max_digits=8, decimal_places=2, read_only=True)

    class Meta:
        model = Shuttle
        fields = [
            'id', 'depart', 'lieu_arriver', 'departure_time', 'arrival_time',
            'price_per_person', 'available_seats', 'status',
            'max_capacity', 'current_bookings', 'is_recurring', 'distance_km',
            'establishment', 'boat', 'captain',
        ]
        read_only_fields = ('created_at', 'updated_at', 'current_bookings')

    def get_available_seats(self, obj):
        return max(0, obj.max_capacity - obj.current_bookings)

    def get_distance_km(self, obj):
        # Tente de lire des coordonnées JSON dans start_location et end_location
        import json
        from math import radians, cos, sin, asin, sqrt
        try:
            dep_coords = None
            arr_coords = None
            # On suppose que start_location et end_location sont des strings JSON ou des objets
            if hasattr(obj, 'start_location') and hasattr(obj, 'end_location'):
                # Si ce sont des dicts (ex: migration future), on prend direct
                if isinstance(obj.start_location, dict):
                    dep_coords = obj.start_location.get('coordinates')
                else:
                    try:
                        dep = json.loads(obj.start_location)
                        dep_coords = dep.get('coordinates')
                    except Exception:
                        dep_coords = None
                if isinstance(obj.end_location, dict):
                    arr_coords = obj.end_location.get('coordinates')
                else:
                    try:
                        arr = json.loads(obj.end_location)
                        arr_coords = arr.get('coordinates')
                    except Exception:
                        arr_coords = None
            if dep_coords and arr_coords:
                dep_lat = dep_coords.get('latitude')
                dep_lon = dep_coords.get('longitude')
                arr_lat = arr_coords.get('latitude')
                arr_lon = arr_coords.get('longitude')
                if all([dep_lat, dep_lon, arr_lat, arr_lon]):
                    # Formule de Haversine
                    dep_lat, dep_lon, arr_lat, arr_lon = map(float, [dep_lat, dep_lon, arr_lat, arr_lon])
                    dep_lat, dep_lon, arr_lat, arr_lon = map(radians, [dep_lat, dep_lon, arr_lat, arr_lon])
                    dlat = arr_lat - dep_lat
                    dlon = arr_lon - dep_lon
                    a = sin(dlat/2)**2 + cos(dep_lat) * cos(arr_lat) * sin(dlon/2)**2
                    c = 2 * asin(sqrt(a))
                    r = 6371
                    return round(c * r, 2)
        except Exception:
            pass
        return None

    def validate(self, data):
        # Vérifier que l'heure d'arrivée est après l'heure de départ
        if data.get('arrival_time') and data.get('departure_time'):
            if data['arrival_time'] <= data['departure_time']:
                raise serializers.ValidationError({
                    "arrival_time": "L'heure d'arrivée doit être après l'heure de départ"
                })

        # Vérifier que la capacité maximale est cohérente avec le bateau
        if data.get('max_capacity') and data.get('boat'):
            if data['max_capacity'] > data['boat'].capacity:
                raise serializers.ValidationError({
                    "max_capacity": "La capacité maximale ne peut pas dépasser la capacité du bateau"
                })

        return data

class ShuttleListSerializer(serializers.ModelSerializer):
    """Sérialiseur simplifié pour la liste des navettes"""
    depart = serializers.CharField(source='departure_name', read_only=True)
    lieu_arriver = serializers.CharField(source='arrival_name', read_only=True)
    available_seats = serializers.SerializerMethodField()
    distance_km = serializers.DecimalField(max_digits=8, decimal_places=2, read_only=True)

    class Meta:
        model = Shuttle
        exclude = ('route_name', 'start_location', 'end_location', 'stops', 'departure_name', 'arrival_name')

    def get_available_seats(self, obj):
        return max(0, obj.max_capacity - obj.current_bookings)

    def get_distance_km(self, obj):
        import json
        from math import radians, cos, sin, asin, sqrt
        try:
            dep_coords = None
            arr_coords = None
            if hasattr(obj, 'start_location') and hasattr(obj, 'end_location'):
                if isinstance(obj.start_location, dict):
                    dep_coords = obj.start_location.get('coordinates')
                else:
                    try:
                        dep = json.loads(obj.start_location)
                        dep_coords = dep.get('coordinates')
                    except Exception:
                        dep_coords = None
                if isinstance(obj.end_location, dict):
                    arr_coords = obj.end_location.get('coordinates')
                else:
                    try:
                        arr = json.loads(obj.end_location)
                        arr_coords = arr.get('coordinates')
                    except Exception:
                        arr_coords = None
            if dep_coords and arr_coords:
                dep_lat = dep_coords.get('latitude')
                dep_lon = dep_coords.get('longitude')
                arr_lat = arr_coords.get('latitude')
                arr_lon = arr_coords.get('longitude')
                if all([dep_lat, dep_lon, arr_lat, arr_lon]):
                    dep_lat, dep_lon, arr_lat, arr_lon = map(float, [dep_lat, dep_lon, arr_lat, arr_lon])
                    dep_lat, dep_lon, arr_lat, arr_lon = map(radians, [dep_lat, dep_lon, arr_lat, arr_lon])
                    dlat = arr_lat - dep_lat
                    dlon = arr_lon - dep_lon
                    a = sin(dlat/2)**2 + cos(dep_lat) * cos(arr_lat) * sin(dlon/2)**2
                    c = 2 * asin(sqrt(a))
                    r = 6371
                    return round(c * r, 2)
        except Exception:
            pass
        return None



# Nouveaux serializers pour les trois types de courses

class LocationField(serializers.Field):
    """Champ personnalisé pour valider la structure de localisation"""

    def to_representation(self, value):
        return value

    def to_internal_value(self, data):
        if not isinstance(data, dict):
            raise serializers.ValidationError("La localisation doit être un objet.")

        # Vérifier la structure requise
        required_fields = ['city_name', 'coordinates', 'timestamp']
        for field in required_fields:
            if field not in data:
                raise serializers.ValidationError(f"Le champ '{field}' est requis.")

        # Vérifier la structure des coordonnées
        coordinates = data.get('coordinates', {})
        required_coords = ['latitude', 'longitude']
        for coord in required_coords:
            if coord not in coordinates:
                raise serializers.ValidationError(f"La coordonnée '{coord}' est requise.")

        return data


class TripRequestSerializer(serializers.ModelSerializer):
    """Serializer de base pour les demandes de courses"""

    departure_location = LocationField()
    arrival_location = LocationField()
    client = ClientSerializer(read_only=True)
    distance_km = serializers.DecimalField(max_digits=8, decimal_places=2, read_only=True)

    class Meta:
        model = TripRequest
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'expires_at', 'distance_km', 'status')

    def validate_passenger_count(self, value):
        if value < 1:
            raise serializers.ValidationError("Le nombre de passagers doit être au moins 1.")
        if value > 50:  # Limite raisonnable
            raise serializers.ValidationError("Le nombre de passagers ne peut pas dépasser 50.")
        return value


class SimpleTripRequestSerializer(TripRequestSerializer):
    """Serializer pour les courses simples"""

    boat_type = serializers.ChoiceField(choices=Boat.BoatTypes.choices)
    distance_km = serializers.DecimalField(max_digits=8, decimal_places=2, read_only=True)

    class Meta:
        model = SimpleTripRequest
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'expires_at', 'distance_km', 'status', 'trip_type')

    def validate(self, data):
        # Vérifier que si une date est programmée, elle est dans le futur
        scheduled_date = data.get('scheduled_date')
        if scheduled_date and scheduled_date < timezone.now().date():
            raise serializers.ValidationError({
                "scheduled_date": "La date programmée doit être dans le futur."
            })
        return data


class HourlyTripRequestSerializer(TripRequestSerializer):
    """Serializer pour les mises à disposition"""

    boat_type = serializers.ChoiceField(choices=Boat.BoatTypes.choices)
    distance_km = serializers.DecimalField(max_digits=8, decimal_places=2, read_only=True)

    class Meta:
        model = HourlyTripRequest
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'expires_at', 'distance_km', 'status', 'trip_type')

    def validate(self, data):
        # Vérifier que la date de début est dans le futur
        start_date = data.get('start_date')
        if start_date and start_date < timezone.now().date():
            raise serializers.ValidationError({
                "start_date": "La date de début doit être dans le futur."
            })

        # Vérifier la durée
        duration_hours = data.get('duration_hours')
        if duration_hours and duration_hours > 24:
            raise serializers.ValidationError({
                "duration_hours": "La durée ne peut pas dépasser 24 heures."
            })

        return data


class ShuttleTripRequestSerializer(TripRequestSerializer):
    """Serializer pour les navettes gratuites"""
    establishment = serializers.SerializerMethodField(read_only=True)
    establishment_details = serializers.SerializerMethodField(read_only=True)
    status = serializers.CharField(source='get_status_display', read_only=True)
    distance_km = serializers.DecimalField(max_digits=8, decimal_places=2, read_only=True)

    # Pour les navettes, on n'utilise pas arrival_location car on utilise les coordonnées de l'établissement
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Retirer arrival_location des champs requis pour les navettes
        if 'arrival_location' in self.fields:
            self.fields['arrival_location'].required = False

    def get_establishment(self, obj):
        return obj.establishment.user.id if obj.establishment else None

    def get_establishment_details(self, obj):
        from accounts.serializers import EstablishmentSerializer
        if obj.establishment:
            data = EstablishmentSerializer(obj.establishment).data
            data['type'] = getattr(obj.establishment, 'type', None)
            data['is_available'] = getattr(obj.establishment, 'is_available', None)
            # Ajouter les coordonnées de l'établissement
            if obj.establishment.longitude and obj.establishment.latitude:
                data['coordinates'] = {
                    'longitude': float(obj.establishment.longitude),
                    'latitude': float(obj.establishment.latitude)
                }
            return data
        return None

    class Meta:
        model = ShuttleTripRequest
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'expires_at', 'distance_km', 'status', 'trip_type')

    def validate(self, data):
        # Vérifier que la date de départ est dans le futur
        departure_date = data.get('departure_date')
        if departure_date and departure_date < timezone.now().date():
            raise serializers.ValidationError({
                "departure_date": "La date de départ doit être dans le futur."
            })

        # Pour les navettes, on n'utilise pas arrival_location du client
        # Le système utilisera automatiquement les coordonnées de l'établissement
        return data


class TripQuoteSerializer(serializers.ModelSerializer):
    """Serializer pour les devis de courses"""

    captain_details = CaptainSerializer(source='captain', read_only=True)
    boat_details = BoatSerializer(source='boat', read_only=True)

    class Meta:
        model = TripQuote
        fields = '__all__'
        read_only_fields = ('created_at', 'captain_name', 'captain_rating', 'boat_name', 'boat_capacity')

    def create(self, validated_data):
        # Remplir automatiquement les informations du capitaine et du bateau
        captain = validated_data['captain']
        boat = validated_data['boat']

        validated_data['captain_name'] = captain.user.get_full_name()
        validated_data['captain_rating'] = captain.average_rating
        validated_data['boat_name'] = boat.name or f"Bateau {boat.id}"
        validated_data['boat_capacity'] = boat.capacity or 0

        return super().create(validated_data)


class TripPreviewSerializer(serializers.ModelSerializer):
    """Sérialiseur ultra-léger pour l’aperçu des courses (dashboard capitaine)."""
    start_label = serializers.SerializerMethodField()
    end_label = serializers.SerializerMethodField()
    datetime = serializers.DateTimeField(source='scheduled_start_time', read_only=True)
    thumbnail = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    price = serializers.DecimalField(source='total_price', max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = Trip
        fields = (
            'id',
            'status',
            'datetime',
            'start_label',
            'end_label',
            'thumbnail',
            'name',
            'price',
        )

    def get_start_label(self, obj):
        return obj.start_location.get('label') if isinstance(obj.start_location, dict) else obj.start_location

    def get_end_label(self, obj):
        return obj.end_location.get('label') if isinstance(obj.end_location, dict) else obj.end_location

    def get_thumbnail(self, obj):
        # Affiche la première photo du bateau si dispo, sinon vide
        if obj.boat and obj.boat.photos:
            photos = obj.boat.photos if isinstance(obj.boat.photos, list) else []
            return photos[0] if photos else ''
        return ''

    def get_name(self, obj):
        # Nom du bateau en priorité, sinon nom du client/établissement
        if obj.boat and obj.boat.name:
            return obj.boat.name
        if obj.client and obj.client.user:
            return obj.client.user.get_full_name()
        return ''


class CaptainTripHistorySerializer(serializers.ModelSerializer):
    """
    Sérialiseur optimisé pour l'historique des courses du capitaine.
    Contient uniquement les champs nécessaires pour l'affichage mobile.
    """
    # Informations de base de la course
    start_label = serializers.SerializerMethodField()
    end_label = serializers.SerializerMethodField()
    client_name = serializers.CharField(source='client.user.get_full_name', read_only=True)
    boat_name = serializers.CharField(source='boat.name', read_only=True)
    boat_thumbnail = serializers.SerializerMethodField()
    
    class Meta:
        model = Trip
        fields = (
            'id',
            'status',
            'start_label',
            'end_label',
            'scheduled_start_time',
            'scheduled_end_time',
            'actual_start_time',
            'actual_end_time',
            'client_name',
            'boat_name',
            'boat_thumbnail',
            'passenger_count',
            'total_price',
            'payment_status',
            'trip_type'
        )
    
    def get_start_label(self, obj):
        return obj.start_location.get('label') if isinstance(obj.start_location, dict) else obj.start_location
    
    def get_end_label(self, obj):
        return obj.end_location.get('label') if isinstance(obj.end_location, dict) else obj.end_location
    
    def get_boat_thumbnail(self, obj):
        if obj.boat and obj.boat.photos:
            photos = obj.boat.photos if isinstance(obj.boat.photos, list) else []
            return photos[0] if photos else ''
        return ''


class TripDetailCaptainSerializer(serializers.ModelSerializer):
    """Sérialiseur détaillé pour une course côté capitaine (mobile)."""
    client = serializers.SerializerMethodField()
    boat = serializers.SerializerMethodField()
    can_start = serializers.SerializerMethodField()
    can_complete = serializers.SerializerMethodField()

    class Meta:
        model = Trip
        fields = (
            'id',
            'status',
            'scheduled_start_time',
            'actual_start_time',
            'actual_end_time',
            'start_location',
            'end_location',
            'passenger_count',
            'total_price',
            'client',
            'boat',
            'can_start',
            'can_complete',
        )

    def get_client(self, obj):
        if obj.client and obj.client.user:
            return {
                'id': obj.client.user.id,
                'name': obj.client.user.get_full_name(),
                'phone': obj.client.user.phone_number if hasattr(obj.client.user, 'phone_number') else '',
                'avatar_url': obj.client.user.profile_picture if obj.client.user.profile_picture else ''
            }
        return None

    def get_boat(self, obj):
        boat = obj.boat
        if boat:
            return {
                'id': boat.id,
                'name': boat.name,
                'type': boat.boat_type,
                'capacity': boat.capacity,
                'registration_number': boat.registration_number,
            }
        return None

    def get_can_start(self, obj):
        return obj.can_start() if hasattr(obj, 'can_start') else False

    def get_can_complete(self, obj):
        return obj.can_complete() if hasattr(obj, 'can_complete') else False
