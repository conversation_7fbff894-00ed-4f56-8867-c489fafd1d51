from django.db import models
from django.utils.translation import gettext_lazy as _
from accounts.models import User
from trips.models import Trip

class ChatRoom(models.Model):
    class RoomType(models.TextChoices):
        TRIP = 'TRIP', _('Course')
        SUPPORT = 'SUPPORT', _('Support')
        GROUP = 'GROUP', _('Groupe')

    name = models.CharField(_('nom'), max_length=100)
    type = models.CharField(_('type'), max_length=20, choices=RoomType.choices)
    participants = models.ManyToManyField(User, related_name='chat_rooms')
    trip = models.OneToOneField(Trip, on_delete=models.CASCADE, null=True, blank=True, related_name='chat_room')
    is_active = models.BooleanField(_('actif'), default=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)

    class Meta:
        verbose_name = _('salon de discussion')
        verbose_name_plural = _('salons de discussion')
        ordering = ['-updated_at']

    def __str__(self):
        return f'{self.name} ({self.type})'

class Message(models.Model):
    class MessageType(models.TextChoices):
        TEXT = 'TEXT', _('Texte')
        IMAGE = 'IMAGE', _('Image')
        LOCATION = 'LOCATION', _('Localisation')
        SYSTEM = 'SYSTEM', _('Système')

    room = models.ForeignKey(ChatRoom, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    type = models.CharField(_('type'), max_length=20, choices=MessageType.choices, default=MessageType.TEXT)
    content = models.TextField(_('contenu'))
    attachment = models.FileField(_('pièce jointe'), upload_to='chat_attachments/', null=True, blank=True)
    metadata = models.JSONField(_('métadonnées'), default=dict)

    # Statut du message
    is_read = models.BooleanField(_('lu'), default=False)
    read_by = models.ManyToManyField(User, related_name='read_messages')
    delivered_to = models.ManyToManyField(User, related_name='delivered_messages')

    # Métadonnées
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    deleted_at = models.DateTimeField(_('supprimé le'), null=True, blank=True)

    class Meta:
        verbose_name = _('message')
        verbose_name_plural = _('messages')
        ordering = ['created_at']

    def __str__(self):
        return f'Message de {self.sender.email} - {self.created_at}'

class ChatbotSession(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chatbot_sessions')
    session_id = models.UUIDField(_('ID de session'), unique=True)
    context = models.JSONField(_('contexte'), default=dict)
    last_interaction = models.DateTimeField(_('dernière interaction'), auto_now=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)

    class Meta:
        verbose_name = _('session chatbot')
        verbose_name_plural = _('sessions chatbot')
        ordering = ['-last_interaction']

    def __str__(self):
        return f'Session {self.session_id} - {self.user.email}'

class ChatbotMessage(models.Model):
    class Role(models.TextChoices):
        USER = 'USER', _('Utilisateur')
        ASSISTANT = 'ASSISTANT', _('Assistant')
        SYSTEM = 'SYSTEM', _('Système')

    session = models.ForeignKey(ChatbotSession, on_delete=models.CASCADE, related_name='messages')
    role = models.CharField(_('rôle'), max_length=20, choices=Role.choices)
    content = models.TextField(_('contenu'))
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)

    class Meta:
        verbose_name = _('message chatbot')
        verbose_name_plural = _('messages chatbot')
        ordering = ['created_at']

    def __str__(self):
        return f'{self.role} - {self.created_at}'
