# Generated by Django 4.2.8 on 2025-05-30 23:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=255, verbose_name="Titre")),
                ("content", models.TextField(verbose_name="Contenu")),
                (
                    "source",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Source"
                    ),
                ),
                ("url", models.URLField(blank=True, null=True, verbose_name="URL")),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        verbose_name="Date de création",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="Date de mise à jour"
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="Catégorie"
                    ),
                ),
                (
                    "tags",
                    models.JSONField(blank=True, default=list, verbose_name="Tags"),
                ),
                (
                    "embedding_generated",
                    models.BooleanField(default=False, verbose_name="Embedding généré"),
                ),
                (
                    "embedding_updated_at",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Date de mise à jour de l'embedding",
                    ),
                ),
            ],
            options={
                "verbose_name": "Document",
                "verbose_name_plural": "Documents",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DocumentChunk",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("content", models.TextField(verbose_name="Contenu du chunk")),
                ("chunk_index", models.IntegerField(verbose_name="Index du chunk")),
                (
                    "embedding",
                    models.JSONField(
                        blank=True, null=True, verbose_name="Embedding vectoriel"
                    ),
                ),
                (
                    "embedding_generated",
                    models.BooleanField(default=False, verbose_name="Embedding généré"),
                ),
                (
                    "embedding_updated_at",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Date de mise à jour de l'embedding",
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Métadonnées du chunk (section, profil, etc.)",
                        verbose_name="Métadonnées",
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chunks",
                        to="rag.document",
                    ),
                ),
            ],
            options={
                "verbose_name": "Chunk de document",
                "verbose_name_plural": "Chunks de documents",
                "ordering": ["document", "chunk_index"],
                "unique_together": {("document", "chunk_index")},
            },
        ),
        migrations.CreateModel(
            name="ChatSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Titre"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        verbose_name="Date de création",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="Date de mise à jour"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Session de chat",
                "verbose_name_plural": "Sessions de chat",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="ChatMessage",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("user", "Utilisateur"),
                            ("assistant", "Assistant"),
                            ("system", "Système"),
                        ],
                        max_length=10,
                        verbose_name="Rôle",
                    ),
                ),
                ("content", models.TextField(verbose_name="Contenu")),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        verbose_name="Date de création",
                    ),
                ),
                (
                    "retrieved_documents",
                    models.ManyToManyField(
                        blank=True,
                        related_name="referenced_in_messages",
                        to="rag.documentchunk",
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="rag.chatsession",
                    ),
                ),
            ],
            options={
                "verbose_name": "Message de chat",
                "verbose_name_plural": "Messages de chat",
                "ordering": ["session", "created_at"],
            },
        ),
        migrations.CreateModel(
            name="ChatFeedback",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "feedback_type",
                    models.CharField(
                        choices=[("positive", "Positif"), ("negative", "Négatif")],
                        max_length=10,
                        verbose_name="Type de feedback",
                    ),
                ),
                (
                    "comments",
                    models.TextField(
                        blank=True, null=True, verbose_name="Commentaires"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Date de création"
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="Adresse IP"
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(blank=True, null=True, verbose_name="User Agent"),
                ),
                (
                    "message",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feedbacks",
                        to="rag.chatmessage",
                        verbose_name="Message",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="chat_feedbacks",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Utilisateur",
                    ),
                ),
            ],
            options={
                "verbose_name": "Feedback de chat",
                "verbose_name_plural": "Feedbacks de chat",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["feedback_type"], name="rag_chatfee_feedbac_91f03c_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="rag_chatfee_created_be2a69_idx"
                    ),
                ],
            },
        ),
    ]
