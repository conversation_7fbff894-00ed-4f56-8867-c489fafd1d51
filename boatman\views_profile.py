"""
Vues de gestion du profil pour l'espace batelier.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from accounts.permissions import IsBoatman
from django.contrib.auth import get_user_model

from accounts.models import Captain

User = get_user_model()


class BoatmanProfileView(APIView):
    """
    Gestion du profil du batelier.
    
    GET/PATCH /api/boatman/profile/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def get(self, request):
        """Récupérer le profil du batelier"""
        
        
        captain = request.user.captain
        user = request.user
        
        # Récupérer le bateau principal
        main_boat = captain.boats.first()
        
        boat_data = None
        if main_boat:
            boat_data = {
                'id': main_boat.id,
                'name': main_boat.name,
                'registration_number': main_boat.registration_number,
                'boat_type': main_boat.boat_type,
                'capacity': main_boat.capacity,
                'color': getattr(main_boat, 'color', ''),
                'fuel_type': getattr(main_boat, 'fuel_type', ''),
                'fuel_consumption': getattr(main_boat, 'fuel_consumption', 0),
                'photos': getattr(main_boat, 'photos', []),
                'features': getattr(main_boat, 'features', []),
                'zone_served': getattr(main_boat, 'zone_served', ''),
                'radius': getattr(main_boat, 'radius', 0),
                'is_available': main_boat.is_available,
                'last_maintenance': getattr(main_boat, 'last_maintenance', None),
                'next_maintenance': getattr(main_boat, 'next_maintenance', None),
                'created_at': main_boat.created_at,
                'updated_at': main_boat.updated_at
            }
        
        return Response({
            'status': 'success',
            'data': {
                'captain_id': str(captain.user.id),  # Utiliser user.id comme clé primaire
                'personal_info': {
                    'name': user.get_full_name(),
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'email': user.email,
                    'phone': user.phone_number,
                    'profile_picture': user.profile_picture,
                    'date_joined': user.date_joined.isoformat()
                },
                'professional_info': {
                    'experience': captain.experience,
                    'license_number': captain.license_number,
                    'availability_status': captain.availability_status,
                    'is_available': captain.is_available,
                    'average_rating': float(captain.average_rating) if captain.average_rating else 0.0,
                    'total_trips': captain.total_trips if hasattr(captain, 'total_trips') else 0,
                    'rate_per_km': float(captain.rate_per_km),
                    'rate_per_hour': float(captain.rate_per_hour)
                },
                'boat': boat_data,
                'contact_preferences': {
                    'email_notifications': True,
                    'sms_notifications': True,
                    'push_notifications': True
                },
                'account_status': {
                    'is_verified': getattr(captain, 'is_verified', False),
                    'documents_uploaded': getattr(captain, 'documents_uploaded', False),
                    'background_check': getattr(captain, 'background_check_status', 'pending')
                }
            },
            'message': 'Profil récupéré avec succès',
            'timestamp': timezone.now().isoformat()
        })

    def patch(self, request):
        """Mettre à jour le profil du batelier"""
        
        # La permission IsBoatman s'en charge déjà, cette vérification est redondante
        
        captain = request.user.captain
        user = request.user
        
        # Champs modifiables
        updatable_user_fields = ['first_name', 'last_name', 'phone_number', 'profile_picture']
        updatable_captain_fields = [
            'experience', 'license_number', 'availability_status', 'is_available',
            'rate_per_km', 'rate_per_hour', 'certifications', 'specializations', 'years_of_experience'
        ]
        
        updated_fields = []
        
        # Gestion spéciale de la photo de profil
        if 'profile_picture' in request.data:
            profile_picture_url = request.data['profile_picture']
            if profile_picture_url != user.profile_picture:
                user.profile_picture = profile_picture_url
                updated_fields.append('profile_picture')
        
        # Mettre à jour les champs utilisateur
        for field in updatable_user_fields:
            if field in request.data:
                old_value = getattr(user, field)
                new_value = request.data[field]
                
                if old_value != new_value:
                    setattr(user, field, new_value)
                    updated_fields.append(field)
        
        # Mettre à jour les champs capitaine directement depuis la racine du JSON
        for field in updatable_captain_fields:
            if field in request.data:
                old_value = getattr(captain, field, None)
                new_value = request.data[field]
                
                if old_value != new_value:
                    setattr(captain, field, new_value)
                    updated_fields.append(field)
        
        # Gestion spéciale des préférences de contact
        if 'contact_preferences' in request.data:
            contact_prefs = request.data['contact_preferences']
            if isinstance(contact_prefs, dict):
                if 'email_notifications' in contact_prefs:
                    updated_fields.append('contact_preferences.email_notifications')
                if 'sms_notifications' in contact_prefs:
                    updated_fields.append('contact_preferences.sms_notifications')
                if 'push_notifications' in contact_prefs:
                    updated_fields.append('contact_preferences.push_notifications')
        
        # Tous les champs sont optionnels pour le batelier
        # Aucune vérification de champs obligatoires n'est nécessaire
        
        # Nous avons déjà mis à jour les données utilisateur et capitaine dans les boucles précédentes
        # Sauvegardons les modifications
        user.save()
        captain.save()
        
        # Traitement des informations du bateau si présentes
        if 'boat' in request.data:
            boat_data = request.data['boat']
            
            # Tous les champs du bateau sont également optionnels
            # Aucune vérification de champs obligatoires n'est nécessaire
            
            # Récupérer le bateau existant ou en créer un nouveau
            boat = captain.boats.first()
            
            from boats.models import Boat as BoatModel
            from boats.validators import validate_boat_type, validate_fuel_type
            
            # Si le bateau n'existe pas, en créer un nouveau
            if not boat:
                try:
                    # Valider les données du bateau
                    if 'boat_type' in boat_data:
                        validate_boat_type(boat_data['boat_type'])
                    if 'fuel_type' in boat_data:
                        validate_fuel_type(boat_data['fuel_type'])
                    
                    # Créer un nouveau bateau
                    boat = BoatModel(
                        captain=captain,
                        name=boat_data.get('name', ''),
                        registration_number=boat_data.get('registration_number', ''),
                        boat_type=boat_data.get('boat_type', 'CLASSIC'),
                        capacity=boat_data.get('capacity', 4),
                        color=boat_data.get('color', ''),
                        fuel_type=boat_data.get('fuel_type', 'GASOLINE'),
                        fuel_consumption=boat_data.get('fuel_consumption', 0),
                        photos=boat_data.get('photos', []),
                        zone_served=boat_data.get('zone_served', ''),
                        radius=boat_data.get('radius', 10),
                        is_available=boat_data.get('is_available', True)
                    )
                    boat.save()
                    updated_fields.append('boat.created')
                except Exception as e:
                    # Continuer avec le reste des mises à jour même si la création du bateau échoue
                    pass
            else:
                # Mettre à jour le bateau existant
                try:
                    # Valider les données du bateau
                    if 'boat_type' in boat_data:
                        validate_boat_type(boat_data['boat_type'])
                    if 'fuel_type' in boat_data:
                        validate_fuel_type(boat_data['fuel_type'])
                    
                    # Champs modifiables du bateau
                    boat_fields = [
                        'name', 'registration_number', 'boat_type', 'capacity', 
                        'color', 'fuel_type', 'fuel_consumption', 'photos', 
                        'zone_served', 'radius', 'is_available'
                    ]
                    
                    # Mettre à jour les champs du bateau
                    for field in boat_fields:
                        if field in boat_data:
                            old_value = getattr(boat, field, None)
                            new_value = boat_data[field]
                            
                            if old_value != new_value:
                                setattr(boat, field, new_value)
                                updated_fields.append(f'boat.{field}')
                    
                    # Sauvegarder les modifications du bateau
                    if any(f.startswith('boat.') for f in updated_fields):
                        boat.save()
                except Exception as e:
                    # Continuer avec le reste des mises à jour même si la mise à jour du bateau échoue
                    pass
        
        # Sauvegarder les modifications
        if updated_fields:
            user.save()
            captain.save()
        
        return Response({
            'status': 'success',
            'data': {
                'updated_fields': updated_fields,
                'profile_updated_at': timezone.now().isoformat()
            },
            'message': f'Profil mis à jour ({len(updated_fields)} champ{"s" if len(updated_fields) > 1 else ""} modifié{"s" if len(updated_fields) > 1 else ""})',
            'timestamp': timezone.now().isoformat()
        })


class BoatmanBoatProfileView(APIView):
    """
    Gestion du profil du bateau.
    
    GET/PATCH /api/boatman/boat/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def get(self, request):
        """Récupérer les informations du bateau"""
        
        # La permission IsBoatman s'en charge déjà, cette vérification est redondante
        
        captain = request.user.captain
        # Récupérer uniquement le premier bateau (selon la logique métier, un batelier ne doit avoir qu'un seul bateau)
        boat = captain.boats.first()
        
        if not boat:
            return Response({
                'status': 'error',
                'error': 'Aucun bateau trouvé pour ce batelier',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)
        
        boat_data = {
            'id': boat.id,
            'name': boat.name,
            'registration': boat.registration_number,
            'capacity': boat.capacity,
            'boat_type': boat.boat_type,
            'fuel_type': getattr(boat, 'fuel_type', 'essence'),
            'fuel_consumption': getattr(boat, 'fuel_consumption', 12.0),
            'year': getattr(boat, 'year', None),
            'manufacturer': getattr(boat, 'manufacturer', ''),
            'model': getattr(boat, 'model', ''),
            'length': getattr(boat, 'length', None),
            'photos': boat.photos if isinstance(boat.photos, list) else [],
            'features': getattr(boat, 'features', []) if hasattr(boat, 'features') else [],
            'is_available': boat.is_available,
            'zone_served': boat.zone_served,
            'radius': boat.radius,
            'created_at': boat.created_at.isoformat() if hasattr(boat, 'created_at') else None
        }
        
        return Response({
            'status': 'success',
            'data': {
                'boat': boat_data
            },
            'message': 'Informations du bateau récupérées',
            'timestamp': timezone.now().isoformat()
        })

    def patch(self, request):
        """Mettre à jour les informations du bateau"""
        
        # La permission IsBoatman s'en charge déjà, cette vérification est redondante
        
        captain = request.user.captain
        
        # Récupérer le bateau unique du batelier
        # Selon la logique métier, un batelier ne doit avoir qu'un seul bateau
        boat = captain.boats.first()
        if not boat:
            return Response({
                'status': 'error',
                'error': 'Aucun bateau trouvé pour ce batelier',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Champs modifiables avec leur mapping dans le modèle Boat
        field_mapping = {
            'name': 'name',
            'registration': 'registration_number',
            'capacity': 'capacity',
            'boat_type': 'boat_type',
            'fuel_type': 'fuel_type',
            'fuel_consumption': 'fuel_consumption',
            'year': 'year',
            'manufacturer': 'manufacturer',
            'model': 'model',
            'length': 'length',
            'features': 'features',
            'is_available': 'is_available',
            'zone_served': 'zone_served',
            'radius': 'radius'
        }
        
        updated_fields = []
        
        # Gestion spéciale des photos du bateau
        if 'photos' in request.data:
            photos = request.data['photos']
            # Vérifier que photos est une liste
            if isinstance(photos, list):
                # Limiter à 4 photos maximum
                photos = photos[:4]
                boat.photos = photos
                updated_fields.append('photos')
        
        # Gestion spéciale des features du bateau
        if 'features' in request.data:
            features = request.data['features']
            # Vérifier que features est une liste
            if isinstance(features, list):
                boat.features = features
                updated_fields.append('features')
        
        # Vérification spéciale pour registration_number (contrainte d'unicité)
        if 'registration' in request.data:
            new_registration = request.data['registration']
            # Vérifier si un autre bateau utilise déjà ce numéro d'immatriculation
            from boats.models import Boat as BoatModel
            existing_boat = BoatModel.objects.filter(registration_number=new_registration).exclude(pk=boat.pk).first()
            if existing_boat:
                return Response({
                    'status': 'error',
                    'error': f'Le numéro d\'immatriculation {new_registration} est déjà utilisé par un autre bateau',
                    'error_code': 400
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Vérification spéciale pour boat_type et fuel_type (valeurs autorisées)
        from boats.validators import validate_boat_type, validate_fuel_type
        
        try:
            if 'boat_type' in request.data:
                validate_boat_type(request.data['boat_type'])
                
            if 'fuel_type' in request.data:
                validate_fuel_type(request.data['fuel_type'])
                
        except Exception as e:
            return Response({
                'status': 'error',
                'error': str(e),
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Traiter tous les autres champs
        for field, boat_field in field_mapping.items():
            if field in request.data and field not in ['photos', 'features']:
                if hasattr(boat, boat_field):
                    # Convertir les valeurs si nécessaire
                    new_value = request.data[field]
                    
                    # Mettre à jour la valeur sans vérifier l'ancienne valeur
                    # (pour éviter les problèmes de comparaison de types)
                    setattr(boat, boat_field, new_value)
                    updated_fields.append(field)
        
        # Sauvegarder les modifications
        if updated_fields:
            boat.save()
        
        return Response({
            'status': 'success',
            'data': {
                'boat_id': boat.id,
                'updated_fields': updated_fields,
                'boat_updated_at': timezone.now().isoformat()
            },
            'message': f'Bateau mis à jour ({len(updated_fields)} champ{"s" if len(updated_fields) > 1 else ""} modifié{"s" if len(updated_fields) > 1 else ""})',
            'timestamp': timezone.now().isoformat()
        })
