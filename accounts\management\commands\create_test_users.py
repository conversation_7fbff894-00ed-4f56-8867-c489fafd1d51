from django.core.management.base import BaseCommand
from django.utils import timezone
from accounts.models import User, Captain, Establishment

class Command(BaseCommand):
    help = 'Crée un utilisateur capitaine et un utilisateur établissement avec leurs profils validés'

    def handle(self, *args, **options):
        # Création du capitaine
        captain_email = "<EMAIL>"
        captain_password = "CaptainTest123!"
        
        captain_user = User.objects.create_user(
            email=captain_email,
            password=captain_password,
            first_name="<PERSON>",
            last_name="Captain",
            phone_number="+***********",
            type="CAPTAIN",
            is_active=True,
            email_verified=True
        )

        # Création du profil capitaine (champs adaptés au modèle)
        Captain.objects.create(
            user=captain_user,
            experience="Expérience test",
            average_rating=5.0,
            total_trips=0,
            wallet_balance=0,
            is_available=True,
            current_location="Port de Cannes",
            license_number="CAP123456",
            license_expiry_date=timezone.now().date() + timezone.timedelta(days=365),
            years_of_experience=5,
            certifications=[],
            specializations=[],
            availability_status="AVAILABLE",
            boat_photos=[],
            rate_per_km=10.0,
            rate_per_hour=50.0
        )

        # Création de l'établissement
        establishment_email = "<EMAIL>"
        establishment_password = "EstablishmentTest123!"
        
        establishment_user = User.objects.create_user(
            email=establishment_email,
            password=establishment_password,
            first_name="Beach",
            last_name="Club",
            phone_number="+33687654321",
            type="ESTABLISHMENT",
            is_active=True,
            email_verified=True
        )

        # Création du profil établissement (champs adaptés au modèle)
        Establishment.objects.create(
            user=establishment_user,
            name="Beach Club Test",
            type="PRIVATE_BEACH",
            address="123 Plage Sud, Cannes",
            description="Etablissement test.",
            main_photo="",
            secondary_photos=[],
            wallet_balance=0,
            business_name="Beach Club Test",
            business_type="BEACH_CLUB",
            registration_number="1234567890",
            tax_id="FR123456789",
            opening_hours={},
            services_offered=[],
            average_rating=5.0
        )

        self.stdout.write(self.style.SUCCESS(f'''
Utilisateurs créés avec succès !

CAPITAINE :
- Email: {captain_email}
- Mot de passe: {captain_password}

ÉTABLISSEMENT :
- Email: {establishment_email}
- Mot de passe: {establishment_password}
'''))
