from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.conf import settings
from django.utils import timezone
import stripe
import logging
from datetime import timedelta
from .models import Payment
from .serializers import PaymentSerializer
from .stripe_utils import create_refund
# Utiliser directement le modèle Trip de l'application trips
from trips.models import Trip

# Configure Stripe API key
stripe.api_key = settings.STRIPE_SECRET_KEY

# Configuration du logger
logger = logging.getLogger(__name__)

class AutoRefundViewSet(viewsets.ViewSet):
    """ViewSet pour gérer les remboursements automatiques"""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['post'])
    def refund_canceled_booking(self, request):
        """Rembourse automatiquement une réservation annulée"""
        booking_id = request.data.get('booking_id')
        reason = request.data.get('reason', 'requested_by_customer')

        if not booking_id:
            return Response({'error': 'booking_id est requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            booking = Trip.objects.get(id=booking_id)

            # Vérifier si la réservation est annulée
            if booking.status != 'CANCELED':
                return Response({'error': 'La réservation n\'est pas annulée'}, status=status.HTTP_400_BAD_REQUEST)

            # Trouver le paiement associé à cette réservation
            payment = Payment.objects.filter(booking=booking, status='COMPLETED').first()

            if not payment:
                return Response({'error': 'Aucun paiement complété trouvé pour cette réservation'}, status=status.HTTP_404_NOT_FOUND)

            if not payment.stripe_payment_id:
                return Response({'error': 'Ce paiement n\'a pas d\'ID Stripe associé'}, status=status.HTTP_400_BAD_REQUEST)

            if payment.status == 'REFUNDED':
                return Response({'error': 'Ce paiement a déjà été remboursé'}, status=status.HTTP_400_BAD_REQUEST)

            # Calculer le montant du remboursement en fonction de la politique d'annulation
            refund_amount = self._calculate_refund_amount(booking, payment)

            if refund_amount <= 0:
                return Response({'error': 'Aucun remboursement n\'est dû selon la politique d\'annulation'}, status=status.HTTP_400_BAD_REQUEST)

            # Préparer les métadonnées
            metadata = {
                'payment_id': str(payment.id),
                'booking_id': str(booking.id),
                'reason': reason,
                'auto_refund': 'true'
            }


            # Convertir le montant en centimes
            amount_cents = int(refund_amount * 100)

            # Créer le remboursement
            refund = create_refund(
                payment_intent_id=payment.stripe_payment_id,
                amount=amount_cents,
                reason=reason,
                metadata=metadata
            )

            if 'error' in refund:
                return Response({'error': refund['error']}, status=status.HTTP_400_BAD_REQUEST)

            # Mettre à jour le paiement
            if refund_amount < payment.amount:
                payment.status = 'PARTIALLY_REFUNDED'
                payment.refund_amount = refund_amount
            else:
                payment.status = 'REFUNDED'
                payment.refund_amount = payment.amount

            payment.refund_id = refund.id
            payment.refund_reason = reason
            payment.save()

            # Envoyer une notification de remboursement initié
            try:
                from notifications.services import create_refund_initiated_notification
                create_refund_initiated_notification(payment)
                logger.info(f"Notification de remboursement initié envoyée pour le paiement {payment.id}")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la notification de remboursement initié: {str(e)}")

            return Response({
                'status': 'Remboursement effectué',
                'refund_id': refund.id,
                'payment_id': payment.id,
                'amount': refund_amount
            })
        except Trip.DoesNotExist:
            return Response({'error': 'Réservation non trouvée'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Erreur lors du remboursement automatique: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _calculate_refund_amount(self, booking, payment):
        """
        Calcule le montant du remboursement en fonction de la politique d'annulation

        Règles:
        - Annulation plus de 24h avant le départ: remboursement à 100%
        - Annulation entre 12h et 24h avant le départ: remboursement à 50%
        - Annulation moins de 12h avant le départ: aucun remboursement
        """
        now = timezone.now()

        # Si la date de départ n'est pas définie, remboursement à 100%
        if not booking.departure_time:
            return payment.amount

        # Calculer la différence entre maintenant et le départ
        time_diff = booking.departure_time - now

        # Plus de 24h avant le départ
        if time_diff > timedelta(hours=24):
            return payment.amount

        # Entre 12h et 24h avant le départ
        elif time_diff > timedelta(hours=12):
            return payment.amount * 0.5

        # Moins de 12h avant le départ
        else:
            return 0

    @action(detail=False, methods=['post'])
    def process_refund_requests(self, request):
        """Traite toutes les demandes de remboursement en attente"""
        # Cette méthode pourrait être appelée par un cron job

        try:
            # Trouver toutes les réservations annulées avec des paiements non remboursés
            bookings = Trip.objects.filter(
                status='CANCELED',
                payment__status='COMPLETED'
            ).distinct()

            results = {
                'processed': 0,
                'failed': 0,
                'details': []
            }

            for booking in bookings:
                try:
                    # Trouver le paiement associé
                    payment = Payment.objects.filter(booking=booking, status='COMPLETED').first()

                    if not payment or not payment.stripe_payment_id:
                        results['details'].append({
                            'booking_id': str(booking.id),
                            'status': 'failed',
                            'reason': 'Paiement non trouvé ou sans ID Stripe'
                        })
                        results['failed'] += 1
                        continue

                    # Calculer le montant du remboursement
                    refund_amount = self._calculate_refund_amount(booking, payment)

                    if refund_amount <= 0:
                        results['details'].append({
                            'booking_id': str(booking.id),
                            'status': 'skipped',
                            'reason': 'Aucun remboursement dû selon la politique'
                        })
                        continue

                    # Préparer les métadonnées
                    metadata = {
                        'payment_id': str(payment.id),
                        'booking_id': str(booking.id),
                        'reason': 'requested_by_customer',
                        'auto_refund': 'true',
                        'batch_process': 'true'
                    }

                    # Convertir le montant en centimes
                    amount_cents = int(refund_amount * 100)

                    # Créer le remboursement
                    refund = create_refund(
                        payment_intent_id=payment.stripe_payment_id,
                        amount=amount_cents,
                        reason='requested_by_customer',
                        metadata=metadata
                    )

                    if 'error' in refund:
                        results['details'].append({
                            'booking_id': str(booking.id),
                            'status': 'failed',
                            'reason': refund['error']
                        })
                        results['failed'] += 1
                        continue

                    # Mettre à jour le paiement
                    if refund_amount < payment.amount:
                        payment.status = 'PARTIALLY_REFUNDED'
                        payment.refund_amount = refund_amount
                    else:
                        payment.status = 'REFUNDED'
                        payment.refund_amount = payment.amount

                    payment.refund_id = refund.id
                    payment.refund_reason = 'requested_by_customer'
                    payment.save()

                    # Envoyer une notification de remboursement initié
                    try:
                        from notifications.services import create_refund_initiated_notification
                        create_refund_initiated_notification(payment)
                        logger.info(f"Notification de remboursement initié envoyée pour le paiement {payment.id}")
                    except Exception as e:
                        logger.error(f"Erreur lors de l'envoi de la notification de remboursement initié: {str(e)}")

                    results['details'].append({
                        'booking_id': str(booking.id),
                        'status': 'success',
                        'refund_id': refund.id,
                        'amount': refund_amount
                    })
                    results['processed'] += 1

                except Exception as e:
                    logger.error(f"Erreur lors du traitement du remboursement pour la réservation {booking.id}: {str(e)}")
                    results['details'].append({
                        'booking_id': str(booking.id),
                        'status': 'failed',
                        'reason': str(e)
                    })
                    results['failed'] += 1

            return Response(results)
        except Exception as e:
            logger.error(f"Erreur lors du traitement des remboursements: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
