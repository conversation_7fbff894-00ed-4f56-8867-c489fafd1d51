from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Review, ReviewResponse, ReviewReport

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('author', 'type', 'rating', 'title', 'is_verified', 'is_public',
                   'reported_count', 'created_at')
    list_filter = ('type', 'is_verified', 'is_public', 'rating', 'created_at')
    search_fields = ('author__email', 'title', 'comment', 'pros', 'cons')
    readonly_fields = ('created_at', 'updated_at', 'reported_count')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (_('Informations générales'), {
            'fields': ('author', 'type', 'trip')
        }),
        (_('Objet évalué'), {
            'fields': ('content_type', 'object_id')
        }),
        (_('Évaluation globale'), {
            'fields': ('rating', 'title', 'comment')
        }),
        (_('Détails'), {
            'fields': ('pros', 'cons')
        }),
        (_('Critères spécifiques'), {
            'fields': ('cleanliness_rating', 'communication_rating',
                      'punctuality_rating', 'value_rating')
        }),
        (_('Statut'), {
            'fields': ('is_verified', 'is_public', 'reported_count')
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(ReviewResponse)
class ReviewResponseAdmin(admin.ModelAdmin):
    list_display = ('review', 'author', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('review__title', 'author__email', 'content')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (_('Référence'), {
            'fields': ('review', 'author')
        }),
        (_('Contenu'), {
            'fields': ('content',)
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(ReviewReport)
class ReviewReportAdmin(admin.ModelAdmin):
    list_display = ('review', 'reporter', 'reason', 'status', 'created_at')
    list_filter = ('reason', 'status', 'created_at')
    search_fields = ('review__title', 'reporter__email', 'description', 'resolution_notes')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (_('Signalement'), {
            'fields': ('review', 'reporter', 'reason')
        }),
        (_('Détails'), {
            'fields': ('description',)
        }),
        (_('Résolution'), {
            'fields': ('status', 'resolved_at', 'resolution_notes')
        }),
        (_('Horodatage'), {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )
