from django.db import models
from django.contrib.auth.models import User, AbstractUser
from django.utils import timezone

class User(AbstractUser):
    """
    User model representing all users in the system.

    This is the base user model that extends Django's AbstractUser.
    It includes additional fields specific to the Commodore application.

    Attributes:
        phone (str): User's phone number
        profile_picture (ImageField): User's profile picture
        creation_date (DateTime): Date and time when the user was created
        is_verified (bool): Whether the user's account has been verified
        verification_code (str): Code sent to user for verification
        verification_code_expiry (DateTime): Expiry time for verification code
        reset_password_code (str): Code sent to user for password reset
        reset_password_code_expiry (DateTime): Expiry time for reset code
        apple_id (str): Apple ID for Apple Sign In
        facebook_id (str): Facebook ID for Facebook Sign In
        google_id (str): Google ID for Google Sign In
        role (str): Role of the user (client, captain, admin)
        stripe_customer_id (str): Stripe customer ID for payments
    """
    ROLE_CHOICES = [
        ('client', 'Client'),
        ('captain', 'Capitaine'),
        ('admin', 'Administrateur'),
        ('establishment', 'Établissement'),
    ]

    phone = models.CharField(max_length=20, blank=True)
    profile_picture = models.ImageField(upload_to='profiles/', blank=True, null=True)
    creation_date = models.DateTimeField(default=timezone.now)
    is_verified = models.BooleanField(default=False)
    verification_code = models.CharField(max_length=6, blank=True, null=True)
    verification_code_expiry = models.DateTimeField(blank=True, null=True)
    reset_password_code = models.CharField(max_length=6, blank=True, null=True)
    reset_password_code_expiry = models.DateTimeField(blank=True, null=True)
    apple_id = models.CharField(max_length=255, blank=True, null=True)
    facebook_id = models.CharField(max_length=255, blank=True, null=True)
    google_id = models.CharField(max_length=255, blank=True, null=True)
    role = models.CharField(max_length=15, choices=ROLE_CHOICES, default='client', verbose_name='Rôle')
    stripe_customer_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='ID client Stripe')

    # Résoudre le conflit avec le modèle User de Django
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name='custom_user_set',
        related_query_name='custom_user',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name='custom_user_set',
        related_query_name='custom_user',
    )

    def sign_up(self, email, phone, password):
        """
        Register a new user with the provided information.

        Args:
            email (str): User's email address
            phone (str): User's phone number
            password (str): User's password (will be hashed)

        Returns:
            bool: True if registration was successful
        """
        import random
        from django.core.mail import send_mail
        from django.conf import settings

        # Validate password
        is_valid, error_message = self.validate_password(password)
        if not is_valid:
            raise ValueError(error_message)

        # Générer un username temporaire unique (sera modifiable par l'utilisateur plus tard)
        import uuid
        self.username = f"user_{uuid.uuid4().hex[:8]}"
        self.email = email
        self.phone = phone
        self.set_password(password)

        # Generate verification code
        verification_code = ''.join([str(random.randint(0, 9)) for _ in range(6)])
        self.verification_code = verification_code
        self.verification_code_expiry = timezone.now() + timezone.timedelta(hours=24)

        self.save()

        # Send verification email
        from django.template.loader import render_to_string
        from django.utils.html import strip_tags
        import logging
        import os

        logger = logging.getLogger(__name__)

        # Vérifier si les templates existent
        template_html_path = os.path.join('templates', 'emails', 'verification_code.html')
        template_txt_path = os.path.join('templates', 'emails', 'verification_code.txt')

        logger.info(f"Tentative d'envoi d'email de vérification à {email}")
        logger.info(f"Vérification de l'existence des templates: HTML={os.path.exists(template_html_path)}, TXT={os.path.exists(template_txt_path)}")

        subject = 'Vérification de votre compte Commodore'
        context = {
            'user': self,
            'verification_code': verification_code
        }

        # Rendre les templates HTML et texte
        try:
            html_message = render_to_string('emails/verification_code.html', context)
            text_message = render_to_string('emails/verification_code.txt', context)
            logger.info("Templates d'email rendus avec succès")
        except Exception as template_error:
            logger.error(f"Erreur lors du rendu des templates: {str(template_error)}")
            # Utiliser un message de secours si les templates ne peuvent pas être rendus
            text_message = f"Votre code de vérification Commodore est: {verification_code}"
            html_message = f"<p>Votre code de vérification Commodore est: <strong>{verification_code}</strong></p>"

        from_email = settings.DEFAULT_FROM_EMAIL
        recipient_list = [email]

        # Afficher les informations de configuration email
        logger.info(f"Configuration email: BACKEND={settings.EMAIL_BACKEND}, HOST={settings.EMAIL_HOST}, PORT={settings.EMAIL_PORT}")
        logger.info(f"FROM={from_email}, TO={recipient_list}")

        try:
            # Imprimer le code de vérification dans la console pour le débogage
            print(f"\n\n=== CODE DE VÉRIFICATION POUR {email}: {verification_code} ===\n\n")

            send_mail(
                subject=subject,
                message=text_message,
                from_email=from_email,
                recipient_list=recipient_list,
                html_message=html_message,
                fail_silently=False
            )
            logger.info(f"Email de vérification envoyé à {email}")
        except Exception as e:
            # Log l'erreur mais ne fait pas échouer l'inscription
            logger.error(f"Erreur lors de l'envoi de l'email de vérification à {email}: {str(e)}")
            print(f"Erreur lors de l'envoi de l'email de vérification: {str(e)}")

        return True

    def sign_up_with_apple(self, apple_id, email=None, name=None):
        """
        Register a new user with Apple Sign In.

        Args:
            apple_id (str): Apple ID from Apple Sign In
            email (str, optional): User's email address
            name (str, optional): User's name

        Returns:
            bool: True if registration was successful
        """
        import uuid

        self.apple_id = apple_id

        if email:
            self.email = email
            self.username = email
        else:
            # Generate a random username if email is not provided
            self.username = f"apple_{uuid.uuid4().hex[:10]}"

        if name:
            # Split name into first and last name
            parts = name.split(' ', 1)
            self.first_name = parts[0]
            if len(parts) > 1:
                self.last_name = parts[1]

        # Apple-authenticated users are automatically verified
        self.is_verified = True

        # Generate a random password
        password = uuid.uuid4().hex
        self.set_password(password)

        self.save()
        return True

    def sign_up_with_facebook(self, facebook_id, email=None, name=None):
        """
        Register a new user with Facebook Sign In.

        Args:
            facebook_id (str): Facebook ID from Facebook Sign In
            email (str, optional): User's email address
            name (str, optional): User's name

        Returns:
            bool: True if registration was successful
        """
        import uuid

        self.facebook_id = facebook_id

        if email:
            self.email = email
            self.username = email
        else:
            # Generate a random username if email is not provided
            self.username = f"fb_{uuid.uuid4().hex[:10]}"

        if name:
            # Split name into first and last name
            parts = name.split(' ', 1)
            self.first_name = parts[0]
            if len(parts) > 1:
                self.last_name = parts[1]

        # Facebook-authenticated users are automatically verified
        self.is_verified = True

        # Generate a random password
        password = uuid.uuid4().hex
        self.set_password(password)

        self.save()
        return True

    def sign_up_with_google(self, google_id, email=None, name=None):
        """
        Register a new user with Google Sign In.

        Args:
            google_id (str): Google ID from Google Sign In
            email (str, optional): User's email address
            name (str, optional): User's name

        Returns:
            bool: True if registration was successful
        """
        import uuid

        self.google_id = google_id

        if email:
            self.email = email
            self.username = email
        else:
            # Generate a random username if email is not provided
            self.username = f"google_{uuid.uuid4().hex[:10]}"

        if name:
            # Split name into first and last name
            parts = name.split(' ', 1)
            self.first_name = parts[0]
            if len(parts) > 1:
                self.last_name = parts[1]

        # Google-authenticated users are automatically verified
        self.is_verified = True

        # Generate a random password
        password = uuid.uuid4().hex
        self.set_password(password)

        self.save()
        return True

    def sign_in(self, email_or_phone, password):
        """Sign in a user"""
        # This will be handled by Django's authentication system
        pass

    def update_profile(self, first_name, last_name, profile_picture):
        """
        Update user profile information.

        Args:
            first_name (str): User's first name
            last_name (str): User's last name
            profile_picture (File, optional): User's profile picture

        Returns:
            bool: True if update was successful
        """
        self.first_name = first_name
        self.last_name = last_name
        if profile_picture:
            self.profile_picture = profile_picture
        self.save()
        return True

    def verify_account(self, code):
        """
        Verify user account with verification code.

        Args:
            code (str): Verification code sent to user

        Returns:
            bool: True if verification was successful, False otherwise
        """
        # Check if code is valid and not expired
        if (self.verification_code == code and
            self.verification_code_expiry and
            self.verification_code_expiry > timezone.now()):

            self.is_verified = True
            self.verification_code = None
            self.verification_code_expiry = None
            self.save()
            return True

        return False

    def request_password_reset(self):
        """
        Request a password reset.

        Generates a reset code and sends it to the user's email.

        Returns:
            bool: True if request was successful
        """
        import random
        from django.core.mail import send_mail
        from django.conf import settings

        # Generate reset code
        reset_code = ''.join([str(random.randint(0, 9)) for _ in range(6)])
        self.reset_password_code = reset_code
        self.reset_password_code_expiry = timezone.now() + timezone.timedelta(hours=1)

        self.save()

        # Send reset email
        subject = 'Reset your Commodore password'
        message = f'Your password reset code is: {reset_code}'
        from_email = settings.DEFAULT_FROM_EMAIL
        recipient_list = [self.email]

        try:
            send_mail(subject, message, from_email, recipient_list)
        except Exception as e:
            # Log the error but don't fail the request
            print(f"Error sending reset email: {str(e)}")

        return True

    def reset_password(self, code, new_password, confirm_password):
        """
        Reset user password with reset code.

        Args:
            code (str): Reset code sent to user
            new_password (str): New password
            confirm_password (str): Confirmation of new password

        Returns:
            bool: True if reset was successful, False otherwise
        """
        # Check if passwords match
        if new_password != confirm_password:
            raise ValueError("Passwords do not match")

        # Validate password
        is_valid, error_message = self.validate_password(new_password)
        if not is_valid:
            raise ValueError(error_message)

        # Check if code is valid and not expired
        if (self.reset_password_code == code and
            self.reset_password_code_expiry and
            self.reset_password_code_expiry > timezone.now()):

            self.set_password(new_password)
            self.reset_password_code = None
            self.reset_password_code_expiry = None
            self.save()
            return True

        return False

    def validate_password(self, password):
        """
        Validate password strength.

        Args:
            password (str): Password to validate

        Returns:
            tuple: (is_valid, error_message)
        """
        import re

        # Check length
        if len(password) < 8:
            return False, "Password must be at least 8 characters long"

        # Check for uppercase
        if not re.search(r'[A-Z]', password):
            return False, "Password must contain at least one uppercase letter"

        # Check for lowercase
        if not re.search(r'[a-z]', password):
            return False, "Password must contain at least one lowercase letter"

        # Check for digits
        if not re.search(r'\d', password):
            return False, "Password must contain at least one digit"

        # Check for special characters
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False, "Password must contain at least one special character"

        return True, ""

    def deactivate_account(self, anonymize=False):
        """
        Désactive le compte utilisateur au lieu de le supprimer.
        Préserve l'intégrité référentielle tout en empêchant l'utilisateur de se connecter.

        Args:
            anonymize (bool): Si True, anonymise les données personnelles de l'utilisateur

        Returns:
            bool: True si la désactivation a réussi
        """
        import uuid

        self.is_active = False

        if anonymize:
            # Anonymiser les données personnelles
            self.first_name = "Utilisateur"
            self.last_name = "Désactivé"
            self.email = f"utilisateur_desactive_{uuid.uuid4().hex[:8]}@example.com"
            self.phone = ""
            self.profile_picture = None

        self.save()
        return True

class NotificationSettings(models.Model):
    """
    Paramètres de notification pour les utilisateurs.

    Ce modèle permet aux utilisateurs de configurer leurs préférences
    de notification pour différents canaux (email, push, SMS).

    Attributes:
        user (User): Utilisateur associé aux paramètres
        email_enabled (bool): Notifications par email activées
        push_enabled (bool): Notifications push activées
        sms_enabled (bool): Notifications SMS activées
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_settings',
                              verbose_name="Utilisateur")
    email_enabled = models.BooleanField(default=True, verbose_name="Notifications par email")
    push_enabled = models.BooleanField(default=True, verbose_name="Notifications push")
    sms_enabled = models.BooleanField(default=False, verbose_name="Notifications SMS")

    class Meta:
        verbose_name = "Paramètres de notification"
        verbose_name_plural = "Paramètres de notification"

    def __str__(self):
        return f"Paramètres de notification de {self.user.get_full_name() or self.user.username}"

class ImpactStatistics(models.Model):
    """Track environmental impact statistics for passengers"""
    total_carbon_offset = models.FloatField(default=0.0)
    total_contributions = models.IntegerField(default=0)

    def update_statistics(self, contribution):
        """Update statistics based on a carbon contribution"""
        self.total_carbon_offset += contribution.co2_offset
        self.total_contributions += 1
        self.save()

class Badge(models.Model):
    """Environmental badges for passengers"""
    BADGE_TYPES = (
        ('ECO_WARRIOR', 'Eco Warrior'),
        ('GREEN_TRAVELER', 'Green Traveler'),
        ('CARBON_NEUTRAL', 'Carbon Neutral'),
    )
    type = models.CharField(max_length=20, choices=BADGE_TYPES)
    description = models.TextField()
    awarded_date = models.DateTimeField(auto_now_add=True)

    def award_badge(self, passenger):
        """Award a badge to a passenger"""
        passenger.badges.add(self)

class Passenger(models.Model):
    """Passenger model extending User"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='passenger_profile')
    credit_balance = models.FloatField(default=0.0)
    impact_statistics = models.OneToOneField(ImpactStatistics, on_delete=models.CASCADE)
    preferred_payment_method = models.CharField(max_length=50, blank=True)
    stripe_customer_id = models.CharField(max_length=255, blank=True, null=True)
    badges = models.ManyToManyField(Badge, blank=True)

    def browse_boats(self, location):
        """Browse available boats near a location"""
        # Implementation will use geolocation queries
        pass

    def create_booking(self, booking_details):
        """Create a new booking"""
        # Implementation will create a Booking object
        pass

    def track_booking(self, booking_id):
        """Track an existing booking"""
        # Implementation will return tracking data
        pass

    def make_payment(self, booking_id, amount):
        """Make a payment for a booking"""
        # Implementation will create a Payment object
        pass

    def add_tip(self, captain_id, amount):
        """Add a tip for a captain"""
        # Implementation will create a tip payment
        pass

    def contribute_carbon_offset(self, amount):
        """Make a carbon offset contribution"""
        # Implementation will create a CarbonContribution
        pass

    def chat_with_establishment(self, establishment_id, message):
        """Send a chat message to an establishment"""
        # Implementation will create a Message
        pass

class AvailabilityCalendar(models.Model):
    """Calendar for captain/boatman availability"""

    def add_slot(self, start_time, end_time):
        """Add an availability time slot"""
        TimeSlot.objects.create(
            calendar=self,
            start_time=start_time,
            end_time=end_time
        )

    def get_available_slots(self):
        """Get all available time slots"""
        return self.time_slots.all()

class TimeSlot(models.Model):
    """Time slot for availability calendar"""
    calendar = models.ForeignKey(AvailabilityCalendar, on_delete=models.CASCADE, related_name='time_slots')
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()

    def create_slot(self, start_time, end_time):
        """Create a new time slot"""
        self.start_time = start_time
        self.end_time = end_time
        self.save()

class Captain(models.Model):
    """Captain model extending User"""
    CAPTAIN_STATUS = (
        ('PENDING', 'Pending'),
        ('ACTIVE', 'Active'),
        ('SUSPENDED', 'Suspended'),
    )
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='captain_profile')
    availability = models.OneToOneField(AvailabilityCalendar, on_delete=models.CASCADE)
    stripe_connect_id = models.CharField(max_length=255, blank=True)
    status = models.CharField(max_length=20, choices=CAPTAIN_STATUS, default='PENDING')
    earnings = models.FloatField(default=0.0)

    def sign_up_captain(self, email, phone):
        """Register as a captain"""
        # Implementation will create a Captain profile
        return True

    def update_availability(self, availability):
        """Update availability calendar"""
        # Implementation will update availability slots
        pass

    def accept_booking(self, booking_id):
        """Accept a booking request"""
        # Implementation will update booking status
        pass

    def decline_booking(self, booking_id):
        """Decline a booking request"""
        # Implementation will update booking status
        pass

    def view_earnings(self):
        """View earnings report"""
        # Implementation will return earnings data
        pass

    def scan_qr_code(self, booking_id):
        """Scan a booking QR code"""
        # Implementation will validate QR code
        return True

class CreditWallet(models.Model):
    """Credit wallet for establishments"""
    balance = models.FloatField(default=0.0)

    def purchase_credits(self, amount):
        """Purchase credits"""
        self.balance += amount
        self.save()

    def deduct_credits(self, amount):
        """Deduct credits"""
        if self.balance >= amount:
            self.balance -= amount
            self.save()
            return True
        return False

    def check_balance(self, amount):
        """Check if balance is sufficient"""
        return self.balance >= amount

    def alert_low_balance(self):
        """Alert when balance is low"""
        # Implementation will send notification
        pass

class Establishment(models.Model):
    """Establishment model extending User"""
    ESTABLISHMENT_TYPES = (
        ('RESTAURANT', 'Restaurant'),
        ('BEACH', 'Beach'),
        ('OTHER', 'Other'),
    )
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='establishment_profile')
    name = models.CharField(max_length=255)
    address = models.CharField(max_length=255)
    type = models.CharField(max_length=20, choices=ESTABLISHMENT_TYPES)
    service_zone = models.TextField()  # Will be converted to GeoPolygon in a more complete implementation
    has_internal_boatmen = models.BooleanField(default=False)
    credit_wallet = models.OneToOneField(CreditWallet, on_delete=models.CASCADE)

    def register_establishment(self, name, address, type, service_zone):
        """Register a new establishment"""
        self.name = name
        self.address = address
        self.type = type
        self.service_zone = service_zone
        self.save()
        return True

    def purchase_credits(self, amount):
        """Purchase credits"""
        return self.credit_wallet.purchase_credits(amount)

    def activate_free_shuttle_service(self):
        """Activate free shuttle service"""
        # Implementation will update establishment settings
        pass

    def generate_qr_code(self, booking_id):
        """Generate QR code for a booking"""
        # Implementation will create a QR code
        pass

    def validate_shuttle_request(self, booking_id):
        """Validate a shuttle request"""
        # Implementation will validate and update booking
        pass

    def assign_trip(self, booking_id, boatman_or_captain_id):
        """Assign a trip to a boatman or captain"""
        # Implementation will update booking assignment
        pass

    def view_trip_history(self):
        """View trip history"""
        # Implementation will return trip history
        pass

    def chat_with_passenger(self, passenger_id, message):
        """Send a chat message to a passenger"""
        # Implementation will create a Message
        pass

class Boatman(models.Model):
    """Boatman model for establishment employees"""
    name = models.CharField(max_length=255)
    phone = models.CharField(max_length=20)
    photo = models.ImageField(upload_to='boatmen/', blank=True, null=True)
    availability = models.OneToOneField(AvailabilityCalendar, on_delete=models.CASCADE)
    establishment = models.ForeignKey(Establishment, on_delete=models.CASCADE, related_name='boatmen')

    def receive_mission(self, booking_id):
        """Receive a mission notification"""
        # Implementation will create a notification
        pass

    def update_mission_status(self, booking_id, status):
        """Update mission status"""
        # Implementation will update booking status
        pass

    def scan_qr_code(self, booking_id):
        """Scan a booking QR code"""
        # Implementation will validate QR code
        return True
