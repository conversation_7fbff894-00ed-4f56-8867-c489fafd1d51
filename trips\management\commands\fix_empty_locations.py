from django.core.management.base import BaseCommand
from trips.models import Trip, Shuttle, ShuttleTripRequest
from django.db import transaction

class Command(BaseCommand):
    help = "Corrige les navettes et courses avec start_location ou end_location vide en récupérant les coordonnées depuis la demande d'origine."

    def handle(self, *args, **options):
        self.stdout.write(self.style.WARNING('Correction des champs de localisation vides...'))
        with transaction.atomic():
            # Correction des Trip (courses)
            trips_fixed = 0
            for trip in Trip.objects.all():
                changed = False
                # Si start_location ou end_location est vide
                if trip.start_location == {} or trip.end_location == {}:
                    # Chercher la demande d'origine si possible
                    if hasattr(trip, 'shuttle') and trip.shuttle:
                        # Chercher la ShuttleTripRequest liée à la navette
                        shuttle_request = ShuttleTripRequest.objects.filter(id=trip.shuttle.id).first()
                        if shuttle_request:
                            if trip.start_location == {}:
                                trip.start_location = shuttle_request.departure_location
                                changed = True
                            if trip.end_location == {}:
                                trip.end_location = shuttle_request.arrival_location
                                changed = True
                if changed:
                    trip.save()
                    trips_fixed += 1
            # Correction des Shuttle (navettes)
            shuttles_fixed = 0
            for shuttle in Shuttle.objects.all():
                changed = False
                if shuttle.start_location == {} or shuttle.end_location == {}:
                    # Chercher la ShuttleTripRequest liée à la navette
                    shuttle_request = ShuttleTripRequest.objects.filter(id=shuttle.id).first()
                    if shuttle_request:
                        if shuttle.start_location == {}:
                            shuttle.start_location = shuttle_request.departure_location
                            changed = True
                        if shuttle.end_location == {}:
                            shuttle.end_location = shuttle_request.arrival_location
                            changed = True
                if changed:
                    shuttle.save()
                    shuttles_fixed += 1
        self.stdout.write(self.style.SUCCESS(f'Correction terminée : {trips_fixed} courses et {shuttles_fixed} navettes corrigées.'))
