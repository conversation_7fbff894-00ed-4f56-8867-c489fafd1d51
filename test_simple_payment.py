"""
Test simple du système de paiements pour vérifier les corrections.
"""

import os
import sys
import django
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from payments.models import Wallet
from payments.wallet_security_service import WalletSecurityService

User = get_user_model()

def test_wallet_security():
    """Test simple du service de sécurité des portefeuilles"""
    print("🧪 TEST SIMPLE DU SYSTÈME DE PAIEMENTS")
    print("-" * 50)
    
    try:
        # 1. Créer un utilisateur de test
        try:
            user = User.objects.get(email='<EMAIL>')
            user.delete()
        except User.DoesNotExist:
            pass
        
        user = User.objects.create_user(
            email='<EMAIL>',
            password='TestSimple123!',
            first_name='Test',
            last_name='Simple',
            type='CLIENT'
        )
        
        print(f"✅ Utilisateur créé: {user.email}")
        
        # 2. Récupérer le portefeuille
        wallet = Wallet.objects.get(user=user)
        print(f"✅ Portefeuille trouvé: {wallet.id} - Solde: {wallet.balance}€")
        
        # 3. Test de crédit
        credit_result = WalletSecurityService.credit_wallet_secure(
            wallet_id=wallet.id,
            amount=Decimal('100.00'),
            description="Test de crédit",
            reference="TEST_CREDIT_001",
            user=user
        )
        
        print(f"✅ Crédit réussi: {credit_result}")
        
        # 4. Vérifier le nouveau solde
        wallet.refresh_from_db()
        print(f"✅ Nouveau solde: {wallet.balance}€")
        
        # 5. Test de débit
        debit_result = WalletSecurityService.debit_wallet_secure(
            wallet_id=wallet.id,
            amount=Decimal('25.50'),
            description="Test de débit",
            reference="TEST_DEBIT_001",
            user=user
        )
        
        print(f"✅ Débit réussi: {debit_result}")
        
        # 6. Vérifier le solde final
        wallet.refresh_from_db()
        print(f"✅ Solde final: {wallet.balance}€")
        
        # 7. Nettoyer
        user.delete()
        print("✅ Nettoyage effectué")
        
        print("\n🎉 TOUS LES TESTS SIMPLES ONT RÉUSSI !")
        return True
        
    except Exception as e:
        print(f"❌ ERREUR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_wallet_security()
    sys.exit(0 if success else 1)
