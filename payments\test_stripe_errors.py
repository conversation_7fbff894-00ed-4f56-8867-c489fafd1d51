from django.test import TestCase
from unittest.mock import patch, MagicMock
from .stripe_utils import (
    create_payment_intent, create_checkout_session, create_connect_account,
    create_account_link, create_transfer, handle_webhook_event,
    create_customer, create_payment_method, attach_payment_method,
    detach_payment_method, list_payment_methods, create_refund
)
import stripe

class StripeErrorsTestCase(TestCase):
    """Tests pour les cas d'erreur des fonctions utilitaires Stripe"""

    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent_card_error(self, mock_create):
        """Test de la gestion des erreurs de carte"""
        # Simuler une erreur de carte
        mock_create.side_effect = stripe.error.CardError(
            message="Your card was declined.",
            param="number",
            code="card_declined",
            user_message="Votre carte a été refusée."
        )
        
        # Appeler la fonction
        result = create_payment_intent(amount=1000)
        
        # Vérifier le résultat
        self.assertIn('error', result)
        self.assertIn('Erreur de carte', result['error'])
    
    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent_rate_limit_error(self, mock_create):
        """Test de la gestion des erreurs de limite de taux"""
        # Simuler une erreur de limite de taux
        mock_create.side_effect = stripe.error.RateLimitError(
            message="Too many requests made to the API too quickly."
        )
        
        # Appeler la fonction
        result = create_payment_intent(amount=1000)
        
        # Vérifier le résultat
        self.assertIn('error', result)
        self.assertIn('Trop de requêtes', result['error'])
    
    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent_invalid_request_error(self, mock_create):
        """Test de la gestion des erreurs de requête invalide"""
        # Simuler une erreur de requête invalide
        mock_create.side_effect = stripe.error.InvalidRequestError(
            message="Invalid parameters.",
            param="amount"
        )
        
        # Appeler la fonction
        result = create_payment_intent(amount=1000)
        
        # Vérifier le résultat
        self.assertIn('error', result)
        self.assertIn('Paramètres de paiement invalides', result['error'])
    
    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent_authentication_error(self, mock_create):
        """Test de la gestion des erreurs d'authentification"""
        # Simuler une erreur d'authentification
        mock_create.side_effect = stripe.error.AuthenticationError(
            message="Invalid API key provided."
        )
        
        # Appeler la fonction
        result = create_payment_intent(amount=1000)
        
        # Vérifier le résultat
        self.assertIn('error', result)
        self.assertIn("Erreur d'authentification", result['error'])
    
    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent_api_connection_error(self, mock_create):
        """Test de la gestion des erreurs de connexion API"""
        # Simuler une erreur de connexion API
        mock_create.side_effect = stripe.error.APIConnectionError(
            message="Network error."
        )
        
        # Appeler la fonction
        result = create_payment_intent(amount=1000)
        
        # Vérifier le résultat
        self.assertIn('error', result)
        self.assertIn('Impossible de se connecter', result['error'])
    
    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent_stripe_error(self, mock_create):
        """Test de la gestion des erreurs génériques Stripe"""
        # Simuler une erreur générique Stripe
        mock_create.side_effect = stripe.error.StripeError(
            message="Something went wrong."
        )
        
        # Appeler la fonction
        result = create_payment_intent(amount=1000)
        
        # Vérifier le résultat
        self.assertIn('error', result)
        self.assertIn('Une erreur est survenue', result['error'])
    
    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent_unexpected_error(self, mock_create):
        """Test de la gestion des erreurs inattendues"""
        # Simuler une erreur inattendue
        mock_create.side_effect = Exception("Unexpected error.")
        
        # Appeler la fonction
        result = create_payment_intent(amount=1000)
        
        # Vérifier le résultat
        self.assertIn('error', result)
        self.assertIn('Une erreur inattendue', result['error'])
    
    @patch('stripe.Webhook.construct_event')
    def test_handle_webhook_event_invalid_payload(self, mock_construct_event):
        """Test de la gestion des erreurs de payload invalide"""
        # Simuler une erreur de payload invalide
        mock_construct_event.side_effect = ValueError("Invalid payload")
        
        # Appeler la fonction
        result = handle_webhook_event(
            payload=b'{"id": "evt_test123"}',
            sig_header='t=123,v1=abc'
        )
        
        # Vérifier le résultat
        self.assertIn('error', result)
        self.assertEqual('Payload invalide', result['error'])
    
    @patch('stripe.Webhook.construct_event')
    def test_handle_webhook_event_invalid_signature(self, mock_construct_event):
        """Test de la gestion des erreurs de signature invalide"""
        # Simuler une erreur de signature invalide
        mock_construct_event.side_effect = stripe.error.SignatureVerificationError(
            message="Invalid signature",
            sig_header='t=123,v1=abc'
        )
        
        # Appeler la fonction
        result = handle_webhook_event(
            payload=b'{"id": "evt_test123"}',
            sig_header='t=123,v1=abc'
        )
        
        # Vérifier le résultat
        self.assertIn('error', result)
        self.assertEqual('Signature invalide', result['error'])
