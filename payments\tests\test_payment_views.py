import pytest
from unittest.mock import patch, MagicMock
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from payments.models import Payment
# Utiliser directement le modèle Trip de l'application trips
from trips.models import Trip
from accounts.models import User, Client as Passenger, Captain
# Adaptations pour les modèles qui n'existent pas dans la structure actuelle
class ImpactStatistics:
    pass

class AvailabilityCalendar:
    pass

pytestmark = pytest.mark.unit

@pytest.fixture
def api_client():
    """Fixture pour le client API"""
    return APIClient()

@pytest.fixture
def user():
    """Fixture pour un utilisateur"""
    return User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='SecurePassword123!'
    )

@pytest.fixture
def impact_statistics():
    """Fixture pour les statistiques d'impact"""
    return ImpactStatistics.objects.create()

@pytest.fixture
def passenger(user, impact_statistics):
    """Fixture pour un passager"""
    return Passenger.objects.create(
        user=user,
        impact_statistics=impact_statistics,
        stripe_customer_id='cus_test123'
    )

@pytest.fixture
def captain_user():
    """Fixture pour un utilisateur capitaine"""
    return User.objects.create_user(
        username='captainuser',
        email='<EMAIL>',
        password='SecurePassword123!'
    )

@pytest.fixture
def availability_calendar():
    """Fixture pour un calendrier de disponibilité"""
    return AvailabilityCalendar.objects.create()

@pytest.fixture
def captain(captain_user, availability_calendar):
    """Fixture pour un capitaine"""
    return Captain.objects.create(
        user=captain_user,
        availability=availability_calendar,
        stripe_connect_id='acct_test123'
    )

@pytest.fixture
def booking(passenger):
    """Fixture pour une réservation"""
    return Trip.objects.create(
        passenger=passenger,
        status='PENDING'
    )

@pytest.fixture
def payment(booking):
    """Fixture pour un paiement"""
    return Payment.objects.create(
        booking=booking,
        amount=100.0,
        type='TRIP',
        status='PENDING',
        stripe_payment_id='pi_test123'
    )

@pytest.mark.django_db
class TestPaymentViewSet:
    """Tests pour PaymentViewSet"""

    @patch('payments.stripe_utils.create_payment_intent')
    def test_process_payment(self, mock_create_payment_intent, api_client, user, payment):
        """Test de la méthode process_payment"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_create_payment_intent.return_value = MagicMock(
            id='pi_test456',
            client_secret='pi_test456_secret',
            amount=10000,
            currency='eur',
            status='requires_payment_method'
        )
        
        # Appeler la vue
        url = reverse('payment-process-payment', args=[payment.id])
        data = {
            'amount': '100.00',
            'payment_method_id': 'pm_test123',
            'customer_id': 'cus_test123',
            'save_payment_method': True,
            'description': 'Test payment'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert 'client_secret' in response.data
        assert 'payment_id' in response.data
        assert 'payment_intent_id' in response.data
        
        # Vérifier que le paiement a été mis à jour
        payment.refresh_from_db()
        assert payment.status == 'PENDING'
        assert payment.stripe_payment_id == 'pi_test456'
        assert payment.stripe_customer_id == 'cus_test123'
        assert payment.stripe_payment_method_id == 'pm_test123'
    
    @patch('payments.stripe_utils.create_refund')
    def test_refund_payment(self, mock_create_refund, api_client, user, payment):
        """Test de la méthode refund_payment"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Mettre à jour le paiement pour qu'il soit complété
        payment.status = 'COMPLETED'
        payment.save()
        
        # Simuler la réponse de Stripe
        mock_create_refund.return_value = MagicMock(
            id='re_test123',
            amount=10000,
            status='succeeded'
        )
        
        # Appeler la vue
        url = reverse('payment-refund-payment', args=[payment.id])
        data = {
            'reason': 'requested_by_customer'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert response.data['status'] == 'Paiement remboursé'
        assert 'refund_id' in response.data
        
        # Vérifier que le paiement a été mis à jour
        payment.refresh_from_db()
        assert payment.status == 'REFUNDED'
        assert payment.refund_id == 're_test123'
        assert payment.refund_reason == 'requested_by_customer'
    
    @patch('payments.stripe_utils.create_checkout_session')
    def test_create_checkout(self, mock_create_checkout_session, api_client, user, booking):
        """Test de la méthode create_checkout"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_create_checkout_session.return_value = MagicMock(
            id='cs_test123',
            url='https://checkout.stripe.com/test',
            payment_intent='pi_test123'
        )
        
        # Appeler la vue
        url = reverse('payment-create-checkout')
        data = {
            'amount': '100.00',
            'booking_id': str(booking.id),
            'type': 'TRIP',
            'success_url': 'https://example.com/success',
            'cancel_url': 'https://example.com/cancel',
            'product_name': 'Test Product',
            'product_description': 'Test Description',
            'customer_id': 'cus_test123',
            'locale': 'fr'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert 'session_id' in response.data
        assert 'payment_id' in response.data
        assert 'checkout_url' in response.data
        
        # Vérifier qu'un paiement a été créé
        payment = Payment.objects.get(id=response.data['payment_id'])
        assert payment.booking == booking
        assert payment.amount == 100.0
        assert payment.type == 'TRIP'
        assert payment.status == 'PENDING'
        assert payment.stripe_customer_id == 'cus_test123'
    
    def test_booking_payments(self, api_client, user, booking, payment):
        """Test de la méthode booking_payments"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Appeler la vue
        url = reverse('payment-booking-payments')
        response = api_client.get(url, {'booking_id': str(booking.id)})
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['id'] == payment.id
        assert response.data[0]['amount'] == payment.amount
        assert response.data[0]['type'] == payment.type
        assert response.data[0]['status'] == payment.status

@pytest.mark.django_db
class TestCaptainPaymentViewSet:
    """Tests pour CaptainPaymentViewSet"""

    @patch('payments.stripe_utils.create_connect_account')
    def test_create_connect_account(self, mock_create_connect_account, api_client, user, captain):
        """Test de la méthode create_connect_account"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_create_connect_account.return_value = MagicMock(
            id='acct_test456',
            email='<EMAIL>',
            country='FR',
            business_type='individual'
        )
        
        # Appeler la vue
        url = reverse('captain-payments-create-connect-account')
        data = {
            'captain_id': str(captain.id),
            'email': '<EMAIL>',
            'country': 'FR',
            'business_type': 'individual'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert response.data['account_id'] == 'acct_test456'
        assert response.data['captain_id'] == captain.id
        
        # Vérifier que le capitaine a été mis à jour
        captain.refresh_from_db()
        assert captain.stripe_connect_id == 'acct_test456'
    
    @patch('payments.stripe_utils.create_account_link')
    def test_create_account_link(self, mock_create_account_link, api_client, user, captain):
        """Test de la méthode create_account_link"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_create_account_link.return_value = MagicMock(
            url='https://connect.stripe.com/test',
            created=**********,
            expires_at=**********
        )
        
        # Appeler la vue
        url = reverse('captain-payments-create-account-link')
        data = {
            'captain_id': str(captain.id),
            'refresh_url': 'https://example.com/refresh',
            'return_url': 'https://example.com/return',
            'type': 'account_onboarding'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert 'url' in response.data
        assert 'expires_at' in response.data
