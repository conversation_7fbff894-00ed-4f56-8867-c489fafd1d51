import os
import time
import hashlib
import logging
from typing import List, Dict, Any, Tuple, Optional
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache, caches
import json
import retrying
from .models import Document, DocumentChunk, ChatSession, ChatMessage

# Les imports lourds sont maintenant déplacés dans les fonctions qui les utilisent :
# - numpy : utilisé dans les calculs de similarité
# - langchain.* : utilisé dans les méthodes de traitement de texte et LLM
# - sentence-transformers/transformers : utilisé dans les embeddings
# - faiss : utilisé dans la recherche vectorielle

# Configuration du logger pour le suivi des événements et erreurs
logger = logging.getLogger(__name__)

# Configuration DeepInfra
DEEPINFRA_API_KEY = os.getenv("DEEPINFRA_API_KEY")
LLM_MODEL = os.getenv("LLM_MODEL", "meta-llama/Meta-Llama-3-8B-Instruct")

# Configuration des embeddings
EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"  # Modèle léger et performant

# Configuration du chunking pour le découpage des documents
CHUNK_SIZE = 800  # Taille maximale des chunks, optimisée pour préserver les FAQ
CHUNK_OVERLAP = 150  # Chevauchement entre chunks pour maintenir le contexte
SECTION_SEPARATOR = "⸻"  # Séparateur utilisé pour diviser les sections du document

# Configuration de la récupération des chunks pertinents
TOP_K_RETRIEVAL = 5  # Nombre maximum de chunks à récupérer pour une requête
SIMILARITY_THRESHOLD = 0.85  # Seuil de similarité pour filtrer les chunks pertinents

# Prompt système définissant le comportement du chatbot
SYSTEM_PROMPT = """
Tu es l'assistant virtuel de Commodore Taxi Boat, une plateforme innovante de réservation de taxis maritimes.
Tu es chaleureux, professionnel et toujours prêt à aider. Tu t'exprimes de manière naturelle et conversationnelle, comme un vrai conseiller humain.

Personnalité et ton :
- Accueillant et bienveillant dans tes salutations
- Proactif : tu poses des questions de suivi quand c'est pertinent
- Empathique : tu comprends les préoccupations des utilisateurs
- Professionnel mais accessible, jamais robotique
- Tu utilises des expressions naturelles comme "Bien sûr !", "Parfait !", "Je comprends votre situation"

Gestion des conversations :
- Salutations : Réponds chaleureusement et demande comment tu peux aider
- Questions vagues : Pose des questions de clarification pour mieux aider
- Remerciements : Réponds naturellement et propose ton aide continue
- Fin de conversation : Termine sur une note positive et ouverte

Règles de réponse :
- Base-toi sur la documentation Commodore quand elle existe
- Si l'info n'est pas disponible, utilise ton expertise métier pour donner des conseils utiles
- Structure tes réponses de façon naturelle, pas forcément en 3 parties
- Adapte ton vocabulaire au profil utilisateur (Client, Établissement, Capitaine)
- N'hésite pas à utiliser des émojis occasionnellement pour être plus chaleureux

Exemples de dialogues naturels :
- Utilisateur : "Salut"
  Toi : "Bonjour ! 😊 Comment allez-vous aujourd'hui ? Je suis là pour vous aider avec toutes vos questions sur Commodore Taxi Boat. Que puis-je faire pour vous ?"

- Utilisateur : "Comment ça marche ?"
  Toi : "Excellente question ! Pour mieux vous orienter, vous souhaitez savoir comment fonctionne la réservation d'un bateau-taxi, ou vous vous demandez plutôt comment devenir capitaine ? Dites-moi ce qui vous intéresse le plus !"

- Utilisateur : "J'ai un problème"
  Toi : "Je suis désolé d'apprendre que vous rencontrez des difficultés. Pouvez-vous me dire de quel type de problème il s'agit ? Est-ce lié à une réservation, un paiement, ou autre chose ? Je vais faire de mon mieux pour vous aider à le résoudre."
"""

# Personas spécifiques par profil utilisateur
USER_PERSONAS = {
    "Client": """
PROFIL CLIENT - Ton adapté :
Tu t'adresses à un utilisateur final qui souhaite réserver des trajets en bateau-taxi.
- Ton chaleureux et accessible
- Explications simples et pratiques
- Focus sur l'expérience utilisateur
- Utilise des emojis pour la convivialité
- Exemples concrets d'utilisation
- Rassure sur la sécurité et la simplicité
""",

    "Capitaine": """
PROFIL CAPITAINE - Ton adapté :
Tu t'adresses à un professionnel maritime qui utilise la plateforme pour son activité.
- Ton respectueux et professionnel
- Informations techniques précises
- Focus sur l'efficacité opérationnelle
- Moins d'emojis, plus de substance
- Conseils pour optimiser l'activité
- Compréhension des enjeux métier
""",

    "Établissement": """
PROFIL ÉTABLISSEMENT - Ton adapté :
Tu t'adresses à un professionnel du tourisme/hôtellerie qui recommande le service.
- Ton business et orienté partenariat
- Focus sur la valeur ajoutée pour les clients
- Informations sur les commissions et avantages
- Ton professionnel mais engageant
- Conseils pour intégrer le service
- Mise en avant des bénéfices commerciaux
"""
}

# Prompt de récupération pour formater la requête utilisateur avec contexte
RETRIEVAL_PROMPT = """
Contexte pertinent (classé par ordre de pertinence) :
{context}

Historique récent de la conversation :
{history}

Question actuelle de l'utilisateur (profil : {profile}) :
{question}

Instructions pour la réponse :
1. Réponds uniquement en français
2. Base ta réponse en priorité sur le contexte fourni. Si le contexte est absent ou insuffisant, suis les directives du SYSTEM_PROMPT pour élaborer une réponse utile et rassurante.
3. Structure ta réponse avec une introduction directe, des détails pertinents et une conclusion/suggestion
4. Utilise des listes à puces ou numérotées pour les instructions en plusieurs étapes
5. Adapte ton ton au profil de l'utilisateur ({profile})
6. Sois concis et précis, évite le jargon technique sauf avec les capitaines
7. Évite d'indiquer que l'information n'est pas dans le contexte ou de dire 'je ne sais pas', sauf si la question est très spécifique (technique/légale) et que le SYSTEM_PROMPT ne permet pas de formuler une réponse générale pertinente.

Réponse (en français, formatée clairement) :
"""

class RagService:
    """
    Service pour la gestion du système RAG (Retrieval Augmented Generation) pour Commodore Taxi Boat.

    Ce service gère :
    - Le découpage des documents en chunks pour une récupération efficace.
    - La génération d'embeddings pour représenter les chunks sous forme de vecteurs.
    - La récupération des chunks pertinents en fonction des requêtes utilisateur.
    - La génération de réponses basées sur les chunks récupérés et le modèle de langage.

    Il est conçu pour un chatbot multicanal (web et mobile) répondant aux questions des Clients, Établissements et Capitaines.
    """

    def __init__(self):
        """
        Initialise le service RAG avec les modèles Gemini.

        Charge la clé API, configure les modèles d'embeddings et de chat, et prépare le text splitter pour le chunking.
        """
        # Vérifier que la clé API est définie
        if not os.environ.get('DEEPINFRA_API_TOKEN'):
            raise ValueError("DEEPINFRA_API_TOKEN non défini dans les variables d'environnement")

        # Les modèles seront initialisés à la demande pour économiser la mémoire
        self._embeddings = None
        self._llm = None
        self._text_splitter = None
        self.vectorstore = None

    @property
    def embeddings(self):
        """Initialise le modèle d'embeddings à la demande"""
        if self._embeddings is None:
            from langchain_huggingface import HuggingFaceEmbeddings
            self._embeddings = HuggingFaceEmbeddings(
                model_name=EMBEDDING_MODEL,
                cache_folder=".cache/huggingface"
            )
        return self._embeddings

    @property
    def llm(self):
        """Initialise le modèle LLM à la demande"""
        if self._llm is None:
            from langchain_community.llms import DeepInfra
            self._llm = DeepInfra(
                model_id=LLM_MODEL,
                deepinfra_api_token=os.environ.get('DEEPINFRA_API_TOKEN'),
                model_kwargs={
                    "temperature": 0.8,  # Légèrement plus créatif pour générer de meilleures réponses
                    "max_tokens": 1024,
                    "top_p": 0.9,
                    "repetition_penalty": 1.1  # Éviter les répétitions
                }
            )
        return self._llm

    @property
    def text_splitter(self):
        """Initialise le text splitter à la demande"""
        if self._text_splitter is None:
            from langchain.text_splitter import RecursiveCharacterTextSplitter
            self._text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=CHUNK_SIZE,
                chunk_overlap=CHUNK_OVERLAP,
                length_function=len,
                separators=[SECTION_SEPARATOR, "\n\n", "\n", ".", " ", ""]
            )
        return self._text_splitter

    # Méthode _create_llm_model supprimée car elle n'est plus nécessaire avec DeepInfra

    def process_document(self, document: Document) -> None:
        """
        Traite un document en le découpant en chunks et en générant des embeddings.

        Args:
            document: Le document Django contenant le titre et le contenu à traiter.
        """
        logger.info(f"Traitement du document: {document.title}")

        # Diviser le document en sections basées sur le séparateur
        sections = document.content.split(SECTION_SEPARATOR)
        chunks = []
        for section_idx, section in enumerate(sections):
            section = section.strip()
            if not section:
                continue
            # Découper la section en chunks plus petits
            section_chunks = self.text_splitter.split_text(section)
            for chunk_idx, chunk_content in enumerate(section_chunks):
                # Ajouter des métadonnées pour identifier le profil et la section
                chunks.append({
                    "content": chunk_content,
                    "metadata": {
                        "section_idx": section_idx,
                        "section_title": section.split("\n")[0][:100],  # Utiliser la première ligne comme titre
                        "profile": self._infer_profile(section)
                    }
                })

        # Supprimer les chunks existants pour éviter les doublons
        document.chunks.all().delete()

        # Créer et sauvegarder les nouveaux chunks
        for i, chunk_data in enumerate(chunks):
            chunk = DocumentChunk.objects.create(
                document=document,
                content=chunk_data["content"],
                chunk_index=i,
                metadata=chunk_data["metadata"]
            )

            # Fonction interne pour générer l'embedding avec retry
            @retrying.retry(
                wait_exponential_multiplier=1000,
                wait_exponential_max=10000,
                stop_max_attempt_number=3
            )
            def generate_embedding(chunk_content):
                return self.embeddings.embed_query(chunk_content)

            try:
                # Générer et sauvegarder l'embedding
                embedding = generate_embedding(chunk_data["content"])
                chunk.embedding = embedding
                chunk.embedding_generated = True
                chunk.embedding_updated_at = timezone.now()
                chunk.save()
                logger.info(f"Embedding généré pour le chunk {i}")
            except Exception as e:
                logger.error(f"Erreur lors de la génération de l'embedding pour le chunk {i}: {str(e)}")

        # Mettre à jour le statut du document
        document.embedding_generated = True
        document.embedding_updated_at = timezone.now()
        document.save()

        logger.info(f"Traitement du document terminé: {document.title}")

    def _infer_profile(self, section: str) -> str:
        """
        Détermine le profil (Client, Établissement, Capitaine) d'une section.

        Args:
            section: Le contenu de la section à analyser.

        Returns:
            str: Le profil inféré ou "Général" si non déterminé.
        """
        section_lower = section.lower()
        if "client" in section_lower:
            return "Client"
        elif "établissement" in section_lower:
            return "Établissement"
        elif "capitaine" in section_lower:
            return "Capitaine"
        return "Général"

    def _load_vectorstore(self) -> None:
        """
        Charge les chunks avec embeddings dans un vectorstore FAISS pour la recherche vectorielle.
        """
        logger.info("Chargement du vectorstore FAISS")

        # Récupérer tous les chunks avec embeddings générés
        chunks = DocumentChunk.objects.filter(embedding_generated=True)
        if not chunks.exists():
            logger.warning("Aucun chunk avec embedding trouvé")
            self.vectorstore = None
            return

        # Approche sécurisée pour créer le vectorstore
        try:
            # Préparer les données pour FAISS
            texts = []
            metadatas = []
            embeddings_list = []

            # Convertir les chunks en format approprié pour FAISS
            for chunk in chunks:
                # Vérifier que le contenu est une chaîne valide
                if not isinstance(chunk.content, str) or not chunk.content.strip():
                    logger.warning(f"Chunk {chunk.id} ignoré: contenu invalide")
                    continue

                # Vérifier que l'embedding est valide
                if not chunk.embedding or not isinstance(chunk.embedding, list):
                    logger.warning(f"Chunk {chunk.id} ignoré: embedding invalide")
                    continue

                # Ajouter les données validées
                texts.append(chunk.content)
                metadatas.append({
                    "document_id": str(chunk.document.id),
                    "document_title": chunk.document.title,
                    "chunk_id": str(chunk.id),
                    "chunk_index": chunk.chunk_index,
                    "section_idx": chunk.metadata.get("section_idx", 0),
                    "section_title": chunk.metadata.get("section_title", ""),
                    "profile": chunk.metadata.get("profile", "Général")
                })
                embeddings_list.append(chunk.embedding)

            # Vérifier qu'il y a des données valides
            if not texts or not embeddings_list:
                logger.warning("Aucun chunk valide trouvé pour créer le vectorstore")
                self.vectorstore = None
                return

            # Approche simplifiée : utiliser la méthode from_embeddings
            from langchain_community.vectorstores.utils import DistanceStrategy
            from langchain_community.vectorstores import FAISS

            # Créer le vectorstore FAISS
            text_embeddings = list(zip(texts, embeddings_list))
            self.vectorstore = FAISS.from_embeddings(
                text_embeddings=text_embeddings,
                embedding=self.embeddings,
                metadatas=metadatas,
                distance_strategy=DistanceStrategy.COSINE
            )

            logger.info(f"Vectorstore FAISS chargé avec {len(texts)} documents")

        except Exception as e:
            logger.error(f"Erreur lors du chargement du vectorstore: {str(e)}")
            import traceback
            traceback.print_exc()
            self.vectorstore = None

    def retrieve_relevant_chunks(self, query: str, user_profile: Optional[str] = None) -> List[Tuple[DocumentChunk, float]]:
        """
        Récupère les chunks les plus pertinents pour une requête, en tenant compte du profil utilisateur.
        Utilise une recherche sémantique avancée avec embeddings.

        Args:
            query: La requête textuelle de l'utilisateur.
            user_profile: Le profil de l'utilisateur (Client, Établissement, Capitaine), optionnel.

        Returns:
            Liste de tuples (chunk, score) triés par pertinence.
        """
        start_time = time.time()
        logger.info(f"Récupération des chunks pertinents pour la requête: {query} (profil: {user_profile})")

        # Charger le vectorstore si nécessaire
        if self.vectorstore is None:
            vectorstore_start = time.time()
            self._load_vectorstore()
            logger.debug(f"Chargement du vectorstore en {time.time() - vectorstore_start:.2f} secondes")

        if self.vectorstore is None:
            logger.warning("Vectorstore non disponible")
            return []

        try:
            # Analyser la requête pour détecter les intentions et entités
            analysis_start = time.time()
            intent, entities = self._analyze_query(query)
            logger.debug(f"Analyse de la requête en {time.time() - analysis_start:.2f} secondes")
            logger.info(f"Intention détectée: {intent}, Entités: {entities}")

            # Générer l'embedding pour la requête
            embedding_start = time.time()
            query_embedding = self.embeddings.embed_query(query)
            logger.debug(f"Génération de l'embedding en {time.time() - embedding_start:.2f} secondes")

            # Utiliser les paramètres de recherche configurés
            threshold = 0.85
            k = TOP_K_RETRIEVAL

        except Exception as e:
            logger.error(f"Erreur lors de la génération de l'embedding pour la requête: {str(e)}")
            return []

        # Effectuer une recherche de similarité avancée
        search_start = time.time()
        try:
            # Vérifier quelle méthode est disponible
            if hasattr(self.vectorstore, 'similarity_search_by_vector_with_relevance_scores'):
                # Méthode standard
                results = self.vectorstore.similarity_search_by_vector_with_relevance_scores(
                    query_embedding,
                    k=k,
                    score_threshold=threshold
                )
            elif hasattr(self.vectorstore, 'similarity_search_with_score_by_vector'):
                # Méthode alternative
                results = self.vectorstore.similarity_search_with_score_by_vector(
                    query_embedding,
                    k=k
                )
                # Filtrer par seuil de similarité
                results = [(doc, score) for doc, score in results if score >= threshold]
            else:
                # Fallback à la recherche simple
                docs = self.vectorstore.similarity_search_by_vector(
                    query_embedding,
                    k=k
                )
                # Pas de scores disponibles, utiliser des scores fictifs
                results = [(doc, 1.0) for doc in docs]
                logger.warning("Utilisation de la recherche simple sans scores")

            logger.debug(f"Recherche vectorielle en {time.time() - search_start:.2f} secondes")
        except Exception as e:
            logger.error(f"Erreur lors de la recherche vectorielle: {str(e)}")
            return []

        # Optimisation: Précharger tous les chunks en une seule requête
        processing_start = time.time()
        chunk_ids = []
        doc_scores = {}
        doc_metadata = {}

        for doc, score in results:
            if hasattr(doc, 'metadata') and doc.metadata and doc.metadata.get("chunk_id"):
                chunk_id = doc.metadata.get("chunk_id")
                chunk_ids.append(chunk_id)
                doc_scores[chunk_id] = score
                doc_metadata[chunk_id] = doc.metadata

        # Récupérer tous les chunks en une seule requête
        chunks_dict = {}
        if chunk_ids:
            chunks = DocumentChunk.objects.filter(id__in=chunk_ids).select_related('document')
            chunks_dict = {str(chunk.id): chunk for chunk in chunks}

        logger.debug(f"Récupération des chunks en {time.time() - processing_start:.2f} secondes")

        # Traiter les résultats
        relevant_chunks = []
        for chunk_id in chunk_ids:
            try:
                if chunk_id not in chunks_dict:
                    logger.warning(f"Chunk {chunk_id} non trouvé dans la base de données")
                    continue

                chunk = chunks_dict[chunk_id]
                score = doc_scores[chunk_id]
                metadata = doc_metadata[chunk_id]
                chunk_profile = metadata.get("profile", "Général")

                # Facteurs d'ajustement du score
                profile_factor = 1.0
                content_factor = 1.0
                recency_factor = 1.0

                # Ajuster le score pour prioriser les chunks correspondant au profil
                if user_profile:
                    if chunk_profile == user_profile:
                        profile_factor = 1.2  # Bonus pour le profil exact
                    elif chunk_profile == "Général":
                        profile_factor = 1.0  # Neutre pour le profil général
                    else:
                        profile_factor = 0.8  # Pénalité pour les autres profils

                # Ajuster le score en fonction du contenu (présence d'entités)
                if entities:
                    # Optimisation: Utiliser une recherche plus efficace pour les entités
                    chunk_content_lower = chunk.content.lower()
                    entity_matches = sum(1 for entity in entities if entity.lower() in chunk_content_lower)
                    if entity_matches > 0:
                        content_factor = 1.0 + (0.1 * entity_matches)  # Bonus pour chaque entité trouvée

                # Ajuster le score en fonction de la récence (si disponible)
                if hasattr(chunk, 'updated_at') and chunk.updated_at:
                    from django.utils import timezone
                    days_old = (timezone.now() - chunk.updated_at).days
                    if days_old < 30:
                        recency_factor = 1.1  # Bonus pour les contenus récents

                # Score final ajusté
                adjusted_score = score * profile_factor * content_factor * recency_factor

                relevant_chunks.append((chunk, adjusted_score))
            except Exception as e:
                logger.warning(f"Erreur lors du traitement d'un résultat de recherche: {str(e)}")
                continue

        # Trier et limiter les résultats
        relevant_chunks = sorted(relevant_chunks, key=lambda x: x[1], reverse=True)[:TOP_K_RETRIEVAL]

        total_time = time.time() - start_time
        logger.info(f"Récupération terminée: {len(relevant_chunks)} chunks pertinents trouvés en {total_time:.2f} secondes")
        return relevant_chunks

    def retrieve_relevant_chunks_enhanced(self, query: str, user_profile: Optional[str] = None,
                                        intent: str = None, entities: List[str] = None,
                                        session: ChatSession = None) -> List[Tuple[DocumentChunk, float]]:
        """
        Version améliorée de la récupération de chunks avec scoring intelligent.

        Args:
            query: La requête utilisateur
            user_profile: Le profil utilisateur
            intent: L'intention détectée
            entities: Les entités extraites
            session: La session de conversation pour le contexte

        Returns:
            Liste des chunks pertinents avec leurs scores améliorés
        """
        # Récupération de base
        base_results = self.retrieve_relevant_chunks(query, user_profile)

        if not base_results:
            return base_results

        # Améliorer les scores basés sur le contexte
        enhanced_results = []

        for chunk, base_score in base_results:
            enhanced_score = base_score

            # Bonus pour les entités correspondantes
            if entities:
                chunk_content_lower = chunk.content.lower()
                entity_matches = sum(1 for entity in entities if entity in chunk_content_lower)
                if entity_matches > 0:
                    enhanced_score += 0.1 * entity_matches  # Bonus de 0.1 par entité trouvée

            # Bonus basé sur l'intention
            if intent:
                intent_keywords = {
                    'specific_question': ['comment', 'pourquoi', 'quand', 'où', 'qui'],
                    'problem_report': ['problème', 'erreur', 'bug', 'ne fonctionne pas'],
                    'help_request': ['aide', 'assistance', 'support', 'guide'],
                    'clarification_needed': ['expliquer', 'comprendre', 'savoir']
                }

                if intent in intent_keywords:
                    chunk_content_lower = chunk.content.lower()
                    keyword_matches = sum(1 for keyword in intent_keywords[intent]
                                        if keyword in chunk_content_lower)
                    if keyword_matches > 0:
                        enhanced_score += 0.05 * keyword_matches

            # Bonus pour la fraîcheur du contenu (si disponible)
            if hasattr(chunk, 'updated_at') and chunk.updated_at:
                from datetime import datetime, timezone
                days_old = (datetime.now(timezone.utc) - chunk.updated_at).days
                if days_old < 30:  # Contenu récent
                    enhanced_score += 0.02

            # Bonus pour les chunks avec métadonnées riches
            if chunk.metadata:
                if chunk.metadata.get('section_title'):
                    enhanced_score += 0.01
                if chunk.metadata.get('importance', 'normal') == 'high':
                    enhanced_score += 0.03

            # Pénalité pour les chunks trop courts ou trop longs
            content_length = len(chunk.content)
            if content_length < 50:  # Trop court
                enhanced_score -= 0.05
            elif content_length > 2000:  # Trop long
                enhanced_score -= 0.02

            enhanced_results.append((chunk, enhanced_score))

        # Trier par score amélioré et limiter les résultats
        enhanced_results.sort(key=lambda x: x[1], reverse=True)

        # Filtrage de diversité pour éviter les doublons similaires
        filtered_results = self._filter_similar_chunks(enhanced_results[:15])

        return filtered_results[:10]  # Retourner les 10 meilleurs

    def _filter_similar_chunks(self, chunks_with_scores: List[Tuple[DocumentChunk, float]]) -> List[Tuple[DocumentChunk, float]]:
        """
        Filtre les chunks similaires pour améliorer la diversité des résultats.
        """
        if len(chunks_with_scores) <= 1:
            return chunks_with_scores

        filtered = []

        for chunk, score in chunks_with_scores:
            is_similar = False

            for existing_chunk, _ in filtered:
                # Vérifier la similarité basée sur le contenu
                similarity = self._calculate_content_similarity(chunk.content, existing_chunk.content)
                if similarity > 0.8:  # Seuil de similarité
                    is_similar = True
                    break

            if not is_similar:
                filtered.append((chunk, score))

        return filtered

    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """
        Calcule la similarité entre deux contenus de chunks.
        """
        # Méthode simple basée sur les mots communs
        words1 = set(content1.lower().split())
        words2 = set(content2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _analyze_query(self, query: str) -> Tuple[str, List[str]]:
        """
        Analyse la requête pour détecter l'intention et les entités de manière plus conversationnelle.

        Args:
            query: La requête textuelle de l'utilisateur.

        Returns:
            Tuple (intention, liste d'entités)
        """
        query_lower = query.lower().strip()

        # Détection des salutations et conversations sociales
        greetings = ["salut", "bonjour", "bonsoir", "hello", "hi", "coucou", "hey"]
        farewells = ["au revoir", "bye", "à bientôt", "merci", "bonne journée", "bonne soirée"]
        social_queries = ["comment allez-vous", "comment ça va", "ça va", "comment vous allez"]

        # Détection des questions d'aide générale
        help_patterns = [
            "aide", "aider", "que peux-tu faire", "que pouvez-vous faire",
            "comment m'aider", "comment puis-je", "j'ai besoin", "pouvez-vous m'aider"
        ]

        # Détection des problèmes et frustrations
        problem_patterns = [
            "problème", "souci", "bug", "erreur", "ne fonctionne pas", "ne marche pas",
            "j'arrive pas", "impossible", "bloqué", "coincé", "frustré", "énervé"
        ]

        # Détection des questions vagues nécessitant clarification
        vague_patterns = [
            "comment ça marche", "comment faire", "c'est quoi", "qu'est-ce que",
            "expliquer", "comprendre", "savoir plus", "en savoir plus"
        ]

        # Mots-clés pour questions spécifiques
        specific_keywords = ["comment", "où", "quand", "qui", "pourquoi", "combien", "quel", "quelle", "quels", "quelles"]

        # Entités importantes pour Commodore (enrichies)
        entity_patterns = {
            "paiement": ["payer", "paiement", "carte", "crédit", "remboursement", "prix", "tarif", "coût", "facture", "solde"],
            "réservation": ["réserver", "réservation", "commander", "commande", "annuler", "annulation", "modifier", "changer"],
            "qr_code": ["qr", "code", "scanner", "scan", "embarquement", "embarquer"],
            "bateau": ["bateau", "taxi", "navette", "embarcation", "capitaine", "trajet", "course"],
            "compte": ["compte", "profil", "inscription", "connexion", "mot de passe", "login", "s'inscrire"],
            "support": ["support", "aide", "assistance", "contact", "service client", "problème"]
        }

        # Déterminer l'intention principale
        if any(greeting in query_lower for greeting in greetings):
            intent = "greeting"
        elif any(farewell in query_lower for farewell in farewells):
            intent = "farewell"
        elif any(social in query_lower for social in social_queries):
            intent = "social"
        elif any(help_pattern in query_lower for help_pattern in help_patterns):
            intent = "help_request"
        elif any(problem in query_lower for problem in problem_patterns):
            intent = "problem_report"
        elif any(vague in query_lower for vague in vague_patterns):
            intent = "clarification_needed"
        elif any(keyword in query_lower for keyword in specific_keywords):
            intent = "specific_question"
        elif len(query_lower.split()) <= 3:  # Questions très courtes
            intent = "clarification_needed"
        else:
            intent = "general_inquiry"

        # Extraire les entités
        entities = []
        for entity_type, patterns in entity_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                entities.append(entity_type)
                # Ajouter les mots spécifiques trouvés
                for pattern in patterns:
                    if pattern in query_lower and len(pattern) > 3:
                        entities.append(pattern)

        return intent, entities

    def _select_response_method(self, query: str, intent: str = None) -> str:
        """
        Sélectionne la méthode de réponse appropriée en fonction de la requête et de l'intention détectée.

        Args:
            query: La requête de l'utilisateur
            intent: L'intention détectée par _analyze_query

        Returns:
            La méthode à utiliser
        """
        query_lower = query.lower()

        # Utiliser l'intention détectée en priorité
        if intent:
            if intent == "greeting":
                return 'greeting'
            elif intent == "farewell":
                return 'farewell'
            elif intent == "social":
                return 'social'
            elif intent == "help_request":
                return 'capabilities'
            elif intent == "problem_report":
                return 'problem_support'
            elif intent == "clarification_needed":
                return 'clarification'

        # Vérifications spécifiques par mots-clés (fallback)
        if any(kw in query_lower for kw in ["présente", "présentation", "qui es-tu", "qui êtes-vous"]):
            return 'introduction'

        if any(kw in query_lower for kw in ["fonctionnalité", "que peux-tu faire", "aide-moi", "comment m'aider"]):
            return 'capabilities'

        if any(kw in query_lower for kw in ["qr", "qr code", "code", "scanner", "scan"]):
            return 'qr_code'

        if any(kw in query_lower for kw in ["payer", "paiement", "course", "carte", "crédit", "prépayé", "solde", "prix"]):
            return 'payment'

        if any(kw in query_lower for kw in ["réserver", "réservation", "commander", "commande", "bateau", "taxi"]):
            return 'reservation'

        if any(kw in query_lower for kw in ["annuler", "annulation", "remboursement", "remboursé", "rembourser", "annule"]):
            return 'cancellation'

        # Par défaut, utiliser le LLM
        return 'llm'

    def _get_predefined_response(self, method: str, user_profile: Optional[str] = None) -> str:
        """
        Renvoie une réponse prédéfinie en fonction de la méthode et du profil utilisateur.

        Args:
            method: La méthode de réponse
            user_profile: Le profil de l'utilisateur

        Returns:
            La réponse prédéfinie
        """
        if method == 'greeting':
            # Adapter les salutations selon le profil utilisateur
            if user_profile == "Capitaine":
                responses = [
                    "Bonjour ! Comment puis-je vous assister dans vos activités de capitaine aujourd'hui ?",
                    "Salut ! Ravi de vous voir. Comment puis-je vous aider à optimiser votre activité sur Commodore ?",
                    "Bonjour et bienvenue ! Comment puis-je vous accompagner dans votre travail de capitaine ?"
                ]
            elif user_profile == "Établissement":
                responses = [
                    "Bonjour ! Comment puis-je vous accompagner dans le développement de votre activité aujourd'hui ?",
                    "Salut ! Ravi de vous voir. Comment puis-je vous aider à mieux intégrer nos services pour vos clients ?",
                    "Bonjour et bienvenue ! Comment puis-je vous assister dans votre partenariat avec Commodore ?"
                ]
            else:  # Client par défaut
                responses = [
                    "Bonjour ! 😊 Comment allez-vous aujourd'hui ? Je suis là pour vous aider avec toutes vos questions sur Commodore Taxi Boat. Que puis-je faire pour vous ?",
                    "Salut ! Ravi de vous voir ! Comment puis-je vous assister aujourd'hui avec vos besoins de transport maritime ?",
                    "Bonjour et bienvenue ! Je suis votre assistant Commodore. Comment puis-je vous aider à naviguer sur nos services aujourd'hui ?"
                ]
            import random
            return random.choice(responses)

        elif method == 'farewell':
            responses = [
                "Merci et bonne journée ! N'hésitez pas à revenir si vous avez d'autres questions. À bientôt ! 😊",
                "Au revoir ! J'espère avoir pu vous aider. Passez une excellente journée !",
                "À bientôt ! Je reste disponible si vous avez besoin d'aide avec Commodore Taxi Boat."
            ]
            import random
            return random.choice(responses)

        elif method == 'social':
            # Adapter les réponses sociales selon le profil
            if user_profile == "Capitaine":
                responses = [
                    "Je vais très bien, merci ! Et vous, comment se passe votre activité ? Y a-t-il quelque chose que je peux faire pour vous aider ?",
                    "Ça va parfaitement bien ! J'espère que vos trajets se passent bien aussi ! Comment puis-je vous assister ?",
                    "Très bien, merci ! Et vous ? Comment puis-je vous accompagner dans votre travail aujourd'hui ?"
                ]
            elif user_profile == "Établissement":
                responses = [
                    "Je vais très bien, merci ! Et vous, comment se développe votre activité ? Comment puis-je vous aider ?",
                    "Ça va parfaitement bien ! J'espère que vos clients sont satisfaits de nos services ! Que puis-je faire pour vous ?",
                    "Très bien, merci ! Et vous ? Comment puis-je vous accompagner dans votre partenariat avec nous ?"
                ]
            else:  # Client par défaut
                responses = [
                    "Ça va très bien, merci de demander ! 😊 Et vous, comment ça se passe de votre côté ? En quoi puis-je vous aider aujourd'hui ?",
                    "Tout va bien, merci ! J'espère que vous passez une bonne journée. Que puis-je faire pour vous aider ?",
                    "Je vais très bien, merci ! Prêt à vous aider avec toutes vos questions sur Commodore. Comment puis-je vous assister ?"
                ]
            import random
            return random.choice(responses)

        elif method == 'problem_support':
            return """
Je suis désolé d'apprendre que vous rencontrez des difficultés. 😔

Pouvez-vous me dire de quel type de problème il s'agit ? Par exemple :
• Un souci avec une réservation
• Un problème de paiement
• Une difficulté technique avec l'application
• Un problème d'embarquement

Plus vous me donnez de détails, mieux je pourrai vous aider à résoudre la situation rapidement !
"""

        elif method == 'clarification':
            return """
Excellente question ! Pour mieux vous orienter, pouvez-vous me préciser ce qui vous intéresse le plus ?

Par exemple :
• Comment réserver un bateau-taxi
• Les options de paiement disponibles
• Comment devenir capitaine
• Les tarifs et conditions
• Autre chose ?

Dites-moi ce qui vous préoccupe et je vous donnerai toutes les informations nécessaires ! 😊
"""

        elif method == 'introduction':
            if user_profile == "Client":
                return """
Bonjour ! Je suis l'assistant virtuel de Commodore Taxi Boat, spécialisé pour les clients.

Je peux vous aider à :
• Réserver un bateau-taxi
• Gérer vos réservations existantes
• Comprendre les options de paiement
• Répondre à vos questions sur le service

N'hésitez pas à me poser des questions spécifiques sur votre trajet maritime !
"""
            elif user_profile == "Capitaine":
                return """
Bonjour Capitaine ! Je suis l'assistant virtuel de Commodore Taxi Boat.

Je peux vous aider à :
• Gérer vos courses
• Comprendre le système de paiement
• Optimiser votre planning
• Résoudre les problèmes techniques

Comment puis-je vous assister aujourd'hui dans votre activité de Capitaine ?
"""
            else:
                return """
Bonjour ! Je suis l'assistant virtuel de Commodore Taxi Boat.

Je peux vous aider avec toutes vos questions concernant notre service de taxi-boat.
Que vous soyez client, capitaine ou établissement partenaire, je suis là pour vous guider.

Comment puis-je vous aider aujourd'hui ?
"""

        elif method == 'capabilities':
            return """
Je peux vous aider avec de nombreuses questions concernant Commodore Taxi Boat :

• **Réservations** : Comment réserver, modifier ou annuler une course
• **Paiements** : Options de paiement, crédits prépayés, remboursements
• **Embarquement** : QR code, procédure d'embarquement, bagages
• **Capitaines** : Certification, évaluation, pourboires
• **Établissements** : Partenariats, intégration, commission

Je peux également vous fournir des informations sur nos conditions générales, notre politique de confidentialité et nos initiatives environnementales.

Quelle information recherchez-vous aujourd'hui ?
"""

        elif method == 'qr_code':
            return """
Sans QR code, le Capitaine n'a pas le droit de vous embarquer. Le QR code est obligatoire car il permet de :
- Vérifier que la course est bien payée
- Lutter contre les fraudes
- Garantir une prise en charge sécurisée

Vous pouvez retrouver votre QR code dans la section "Mes réservations" de l'application Commodore, sur l'écran de confirmation de course, ou dans la notification reçue avant l'arrivée du bateau.
"""

        elif method == 'payment':
            return """
Pour payer votre course sur Commodore, vous avez deux options principales:

1. Le paiement classique par carte bancaire (Visa, Mastercard, Apple Pay...) au moment de chaque réservation
2. L'achat de crédits prépayés (ex. : 100 € = 100 crédits) qui seront automatiquement débités à chaque course

Les crédits vous permettent de gagner du temps à chaque réservation, de réserver plus rapidement sans sortir votre carte, et de mieux maîtriser votre budget.

Pour recharger vos crédits, allez dans votre profil Commodore > "Mon solde". Vous pouvez aussi activer un rechargement automatique quand votre solde descend sous un certain seuil.
"""

        elif method == 'reservation':
            return """
Pour réserver un bateau-taxi sur Commodore, suivez ces étapes simples:

1. Ouvrez l'application Commodore
2. Sélectionnez votre point de départ et d'arrivée sur la carte
3. Choisissez l'heure de départ souhaitée
4. Sélectionnez le nombre de passagers
5. Vérifiez le prix et confirmez votre réservation
6. Payez avec votre carte bancaire ou vos crédits prépayés

Vous recevrez une confirmation par email et notification. Un QR code vous sera fourni, que vous devrez présenter au Capitaine lors de l'embarquement.
"""

        elif method == 'cancellation':
            return """
Pour annuler une réservation sur Commodore:

1. Allez dans "Mes réservations" dans l'application
2. Sélectionnez la réservation à annuler
3. Appuyez sur "Annuler la réservation"

Politique d'annulation:
- Annulation plus de 24h avant le départ: remboursement à 100%
- Entre 24h et 2h avant le départ: remboursement à 50%
- Moins de 2h avant le départ: aucun remboursement

Le remboursement sera effectué sur votre moyen de paiement initial ou en crédits Commodore selon votre choix.
"""

        # Fallback
        return None

    def generate_response(self, session: ChatSession, user_message: str, user_profile: Optional[str] = None) -> str:
        """
        Génère une réponse à un message utilisateur en utilisant le système RAG avancé.
        Utilise la recherche sémantique et l'analyse d'intention pour des réponses plus précises.

        Args:
            session: La session de chat Django.
            user_message: Le message texte de l'utilisateur.
            user_profile: Le profil de l'utilisateur (Client, Établissement, Capitaine), optionnel.

        Returns:
            La réponse générée par le modèle.
        """
        logger.info(f"Génération de réponse pour la session {session.id} (profil: {user_profile})")

        try:
            # Analyser la requête pour détecter l'intention et les entités AVANT de sélectionner la méthode
            intent, entities = self._analyze_query(user_message)
            logger.info(f"Intention détectée: {intent}, Entités: {entities}")

            # Sélectionner la méthode de réponse appropriée en utilisant l'intention
            method = self._select_response_method(user_message, intent)
            logger.info(f"Méthode de réponse sélectionnée: {method}")

            # Enregistrer le message utilisateur
            user_msg = ChatMessage.objects.create(
                session=session,
                role=ChatMessage.ROLE_USER,
                content=user_message
            )

            # Vérifier si nous avons une réponse prédéfinie pour cette méthode
            predefined_response = self._get_predefined_response(method, user_profile)

            # Si nous avons une réponse prédéfinie et que ce n'est pas une méthode LLM, l'utiliser
            if predefined_response and method != 'llm':
                logger.info(f"Utilisation d'une réponse prédéfinie pour la méthode: {method}")

                # Adapter la réponse au contexte conversationnel
                contextual_response = self._adapt_response_to_context(
                    predefined_response, session, intent, entities, user_profile
                )

                # Post-traiter la réponse prédéfinie
                response = self._post_process_response(contextual_response, intent, entities, user_profile)

                # Enregistrer la réponse dans la session
                assistant_msg = ChatMessage.objects.create(
                    session=session,
                    role=ChatMessage.ROLE_ASSISTANT,
                    content=response
                )

                # Ajouter des chunks génériques comme sources pour les réponses prédéfinies
                from rag.models import Document
                docs = Document.objects.filter(embedding_generated=True)
                if docs.exists():
                    doc = docs.first()
                    chunks = doc.chunks.all()[:3]  # Prendre les 3 premiers chunks pour simplifier
                    for chunk in chunks:
                        assistant_msg.retrieved_documents.add(chunk)

                logger.info(f"Réponse prédéfinie contextualisée générée avec succès pour la session {session.id}")
                return response

            # Récupérer les chunks pertinents avec la recherche sémantique optimisée
            relevant_chunks = self.retrieve_relevant_chunks_enhanced(
                user_message, user_profile, intent, entities, session
            )

            # Gérer le cas où aucun chunk pertinent n'est trouvé
            if not relevant_chunks:
                logger.warning("Aucun chunk pertinent trouvé pour la requête")

                # Essayer une recherche plus large si aucun résultat n'est trouvé
                fallback_chunks = self._fallback_search(user_message, user_profile)

                if not fallback_chunks:
                    # Réponse par défaut si aucune information n'est trouvée
                    response = "Je suis désolé, mais je n'ai pas trouvé d'informations pertinentes pour répondre à votre question. Contactez <EMAIL> pour plus de détails."
                    assistant_msg = ChatMessage.objects.create(
                        session=session,
                        role=ChatMessage.ROLE_ASSISTANT,
                        content=response
                    )
                    return response
                else:
                    # Utiliser les résultats de la recherche de secours
                    relevant_chunks = fallback_chunks
                    logger.info(f"Utilisation de la recherche de secours: {len(relevant_chunks)} chunks trouvés")

            # Construire le contexte à partir des chunks récupérés, avec pondération par pertinence
            weighted_chunks = []
            for chunk, score in relevant_chunks:
                # Formater le chunk avec son score pour un meilleur contexte
                formatted_chunk = f"Section {chunk.metadata.get('section_title', 'Inconnue')} (pertinence: {score:.2f}):\n{chunk.content}"
                weighted_chunks.append((formatted_chunk, score))

            # Trier les chunks par score de pertinence et les joindre
            weighted_chunks.sort(key=lambda x: x[1], reverse=True)
            context = "\n\n".join([chunk for chunk, _ in weighted_chunks])

            # Récupérer et formater l'historique conversationnel de manière intelligente
            history = self._build_conversational_history(session, user_msg)

            # Adapter le prompt en fonction de l'intention détectée
            if intent == "specific":
                # Prompt plus précis pour les questions spécifiques
                retrieval_prompt = """
                Contexte (sections pertinentes, triées par pertinence):
                {context}

                Historique de la conversation:
                {history}

                Question spécifique de l'utilisateur (profil {profile}):
                {question}

                Réponse (en français, formatée clairement et précisément, en répondant directement à la question):
                """
            else:
                # Prompt standard pour les autres types de questions
                retrieval_prompt = RETRIEVAL_PROMPT

            # Adapter le prompt selon l'intention détectée pour une réponse plus naturelle et contextuelle
            # Récupérer le persona adapté au profil utilisateur
            persona = USER_PERSONAS.get(user_profile, USER_PERSONAS["Client"])

            if intent in ["greeting", "social", "farewell"]:
                # Prompt conversationnel pour les interactions sociales
                prompt = f"""
{SYSTEM_PROMPT}

{persona}

Contexte de la conversation :
{history}

Message de l'utilisateur (profil: {user_profile or 'Client'}) :
{user_message}

L'utilisateur engage une conversation sociale. Réponds selon le persona adapté à son profil, de manière chaleureuse et naturelle. Sois accueillant, pose des questions de suivi pour aider l'utilisateur, et montre que tu es là pour l'aider.

Réponse conversationnelle :
"""
            elif intent == "problem_report":
                # Prompt empathique pour les problèmes
                prompt = f"""
{SYSTEM_PROMPT}

{persona}

Contexte pertinent de la documentation :
{context}

Historique de la conversation :
{history}

Problème signalé (profil: {user_profile or 'Client'}) :
{user_message}

L'utilisateur rencontre un problème. Réponds selon le persona adapté à son profil, avec empathie et professionnalisme. Montre que tu comprends sa situation, pose des questions pour clarifier le problème, et propose des solutions concrètes basées sur le contexte disponible.

Réponse empathique et solution :
"""
            elif intent == "clarification_needed":
                # Prompt pour les questions vagues nécessitant clarification
                prompt = f"""
{SYSTEM_PROMPT}

{persona}

Historique de la conversation :
{history}

Question vague de l'utilisateur (profil: {user_profile or 'Client'}) :
{user_message}

L'utilisateur pose une question qui nécessite des clarifications. Réponds selon le persona adapté à son profil, de manière proactive en proposant plusieurs options ou en posant des questions de suivi pour mieux comprendre ses besoins. Sois guidant et utile.

Réponse avec clarifications :
"""
            else:
                # Prompt standard mais conversationnel avec contexte enrichi
                prompt = f"""
{SYSTEM_PROMPT}

{persona}

Contexte pertinent de la documentation :
{context}

Historique de la conversation :
{history}

Question de l'utilisateur (profil: {user_profile or 'Client'}) :
{user_message}

Réponds selon le persona adapté au profil utilisateur, de façon naturelle et conversationnelle en français. Utilise le contexte documentaire et l'historique de conversation pour fournir une réponse personnalisée et pertinente. Sois proactif et pose des questions de suivi si nécessaire.

Ta réponse doit être :
- Naturelle et conversationnelle (pas robotique)
- Adaptée au profil utilisateur et au contexte de la conversation
- Professionnelle mais accessible
- Sans balises HTML ni caractères bizarres

Réponse :
"""

            # Générer la réponse directement sans fonction interne
            try:
                # Générer la réponse en appelant directement le modèle avec retry
                @retrying.retry(
                    wait_exponential_multiplier=1000,
                    wait_exponential_max=10000,
                    stop_max_attempt_number=3
                )
                def invoke_llm_with_retry(text):
                    return self.llm.invoke(text)
                
                # Appeler le LLM
                raw_response = invoke_llm_with_retry(prompt)
                
                # Nettoyer la réponse
                response = raw_response.strip()
                
                # Retirer les formatages intempestifs s'ils sont présents
                patterns_to_remove = [
                    "Réponse :", 
                    "[Insert your answer here]",
                    "[Votre réponse ici]",
                    "|",
                    "Note:",
                    "Please respond with your answer.",
                    "I will be waiting for your response."
                ]
                
                for pattern in patterns_to_remove:
                    if pattern in response:
                        response = response.replace(pattern, "").strip()
                        
            except Exception as e:
                logger.error(f"Erreur lors de la génération de la réponse: {str(e)}")
                response = "Je suis désolé, mais j'ai rencontré une erreur. Veuillez réessayer."

            # Note: Une implémentation plus avancée avec sélection de modèle pourrait être ajoutée ici
            # dans une future mise à jour, après des tests approfondis

            # Post-traitement de la réponse
            response = self._post_process_response(response, intent, entities, user_profile)

            # Enregistrer la réponse de l'assistant
            assistant_msg = ChatMessage.objects.create(
                session=session,
                role=ChatMessage.ROLE_ASSISTANT,
                content=response
            )

            # Enregistrer les chunks utilisés pour la réponse
            for chunk, _ in relevant_chunks:
                assistant_msg.retrieved_documents.add(chunk)


            logger.info(f"Réponse générée avec succès pour la session {session.id}")
            return response

        except Exception as e:
            logger.error(f"Erreur lors de la génération de la réponse: {str(e)}")
            import traceback
            traceback.print_exc()
            error_response = "Je suis désolé, mais j'ai rencontré une erreur. Veuillez réessayer <NAME_EMAIL>."
            ChatMessage.objects.create(
                session=session,
                role=ChatMessage.ROLE_ASSISTANT,
                content=error_response
            )
            return error_response

    def _fallback_search(self, query: str, user_profile: Optional[str] = None) -> List[Tuple[DocumentChunk, float]]:
        """
        Recherche de secours avec des paramètres plus souples quand la recherche principale ne donne pas de résultats.

        Args:
            query: La requête textuelle de l'utilisateur.
            user_profile: Le profil de l'utilisateur.

        Returns:
            Liste de tuples (chunk, score) triés par pertinence.
        """
        logger.info(f"Exécution de la recherche de secours pour: {query}")

        # Vérifier si le vectorstore est disponible
        if self.vectorstore is None:
            logger.warning("Vectorstore non disponible pour la recherche de secours")
            return []

        # Simplifier la requête en ne gardant que les mots-clés importants
        simplified_query = self._simplify_query(query)

        if not simplified_query:
            logger.warning("Requête simplifiée vide")
            return []

        try:
            # Générer l'embedding pour la requête simplifiée
            query_embedding = self.embeddings.embed_query(simplified_query)

            # Recherche avec un seuil de similarité plus bas
            try:
                # Vérifier quelle méthode est disponible
                if hasattr(self.vectorstore, 'similarity_search_by_vector_with_relevance_scores'):
                    # Méthode standard
                    results = self.vectorstore.similarity_search_by_vector_with_relevance_scores(
                        query_embedding,
                        k=TOP_K_RETRIEVAL * 3,
                        score_threshold=0.5  # Seuil plus bas pour la recherche de secours
                    )
                elif hasattr(self.vectorstore, 'similarity_search_with_score_by_vector'):
                    # Méthode alternative
                    results = self.vectorstore.similarity_search_with_score_by_vector(
                        query_embedding,
                        k=TOP_K_RETRIEVAL * 3
                    )
                    # Filtrer par seuil de similarité
                    results = [(doc, score) for doc, score in results if score >= 0.5]
                else:
                    # Fallback à la recherche simple
                    docs = self.vectorstore.similarity_search_by_vector(
                        query_embedding,
                        k=TOP_K_RETRIEVAL * 3
                    )
                    # Pas de scores disponibles, utiliser des scores fictifs
                    results = [(doc, 1.0) for doc in docs]
                    logger.warning("Utilisation de la recherche simple sans scores pour la recherche de secours")
            except Exception as e:
                logger.error(f"Erreur lors de la recherche vectorielle de secours: {str(e)}")
                return []

            relevant_chunks = []
            for doc, score in results:
                try:
                    # Vérifier que les métadonnées sont valides
                    if not hasattr(doc, 'metadata') or not doc.metadata:
                        logger.warning("Document sans métadonnées ignoré dans la recherche de secours")
                        continue

                    chunk_id = doc.metadata.get("chunk_id")
                    if not chunk_id:
                        logger.warning("Document sans chunk_id ignoré dans la recherche de secours")
                        continue

                    # Récupérer le chunk correspondant
                    chunk = DocumentChunk.objects.get(id=chunk_id)

                    # Appliquer un facteur de boost pour le profil utilisateur si spécifié
                    if user_profile and doc.metadata.get("profile") == user_profile:
                        score *= 1.2  # Bonus de 20% pour les chunks correspondant au profil

                    relevant_chunks.append((chunk, score))
                except DocumentChunk.DoesNotExist:
                    logger.warning(f"Chunk {chunk_id} non trouvé dans la base de données")
                    continue
                except Exception as e:
                    logger.warning(f"Erreur lors du traitement d'un résultat de recherche: {str(e)}")
                    continue

            # Trier et limiter les résultats
            sorted_chunks = sorted(relevant_chunks, key=lambda x: x[1], reverse=True)[:TOP_K_RETRIEVAL]
            logger.info(f"Recherche de secours: {len(sorted_chunks)} chunks pertinents trouvés")
            return sorted_chunks

        except Exception as e:
            logger.error(f"Erreur lors de la recherche de secours: {str(e)}")
            import traceback
            traceback.print_exc()
            return []

    def _simplify_query(self, query: str) -> str:
        """
        Simplifie une requête en extrayant les mots-clés importants.

        Args:
            query: La requête originale.

        Returns:
            Requête simplifiée.
        """
        # Liste de mots vides en français
        stopwords = ["le", "la", "les", "un", "une", "des", "du", "de", "ce", "cette", "ces",
                    "mon", "ma", "mes", "ton", "ta", "tes", "son", "sa", "ses", "notre", "nos",
                    "votre", "vos", "leur", "leurs", "je", "tu", "il", "elle", "nous", "vous",
                    "ils", "elles", "et", "ou", "mais", "donc", "car", "pour", "par", "avec",
                    "sans", "en", "dans", "sur", "sous", "avant", "après", "pendant", "comme",
                    "si", "que", "qui", "quoi", "dont", "où", "quand", "comment", "pourquoi"]

        # Extraire les mots-clés
        words = query.lower().split()
        keywords = [word for word in words if word not in stopwords and len(word) > 2]

        # Ajouter des mots importants même s'ils sont dans les stopwords
        important_words = ["comment", "où", "quand", "pourquoi", "qui", "quoi"]
        for word in important_words:
            if word in query.lower() and word not in keywords:
                keywords.append(word)

        return " ".join(keywords)

    def _build_conversational_history(self, session: ChatSession, current_user_msg: ChatMessage) -> str:
        """
        Construit un historique conversationnel intelligent et contextuel.

        Args:
            session: La session de chat
            current_user_msg: Le message utilisateur actuel (à exclure)

        Returns:
            Historique formaté pour le contexte conversationnel
        """
        # Récupérer les messages récents (limité pour éviter de surcharger le contexte)
        recent_messages = session.messages.exclude(id=current_user_msg.id).order_by('-created_at')[:10]

        if not recent_messages:
            return "Début de la conversation."

        # Inverser pour avoir l'ordre chronologique
        recent_messages = list(reversed(recent_messages))

        # Analyser le contexte conversationnel
        conversation_context = self._analyze_conversation_context(recent_messages)

        # Formater l'historique de manière conversationnelle
        history_entries = []

        # Ajouter un résumé du contexte si pertinent
        if conversation_context['topics']:
            topics_str = ", ".join(conversation_context['topics'][:3])
            history_entries.append(f"[Contexte: Discussion sur {topics_str}]")

        # Formater les messages récents
        for i, msg in enumerate(recent_messages[-6:]):  # Garder seulement les 6 derniers
            role = "Vous" if msg.role == ChatMessage.ROLE_USER else "Moi"

            # Raccourcir les messages trop longs
            content = msg.content
            if len(content) > 100:
                content = content[:97] + "..."

            # Ajouter un indicateur temporel relatif
            if i == len(recent_messages[-6:]) - 1:
                time_indicator = " (à l'instant)"
            elif i >= len(recent_messages[-6:]) - 3:
                time_indicator = " (récemment)"
            else:
                time_indicator = ""

            history_entries.append(f"{role}: {content}{time_indicator}")

        return "\n".join(history_entries)

    def _analyze_conversation_context(self, messages) -> Dict[str, Any]:
        """
        Analyse le contexte d'une conversation pour identifier les sujets et l'état émotionnel.

        Args:
            messages: Liste des messages à analyser

        Returns:
            Dictionnaire avec le contexte analysé
        """
        context = {
            'topics': [],
            'emotional_state': 'neutral',
            'user_needs': [],
            'conversation_stage': 'ongoing'
        }

        if not messages:
            return context

        # Analyser les sujets abordés
        topic_keywords = {
            'réservation': ['réserver', 'réservation', 'commander', 'commande', 'bateau', 'taxi'],
            'paiement': ['payer', 'paiement', 'carte', 'crédit', 'prix', 'tarif', 'solde'],
            'problème technique': ['problème', 'bug', 'erreur', 'ne marche pas', 'bloqué'],
            'compte utilisateur': ['compte', 'profil', 'connexion', 'mot de passe', 'inscription'],
            'embarquement': ['qr', 'code', 'embarquer', 'embarquement', 'capitaine'],
            'annulation': ['annuler', 'annulation', 'remboursement', 'modifier']
        }

        # Analyser l'état émotionnel
        positive_indicators = ['merci', 'parfait', 'super', 'excellent', 'génial', 'content']
        negative_indicators = ['problème', 'frustré', 'énervé', 'difficile', 'impossible', 'nul']

        all_text = ' '.join([msg.content.lower() for msg in messages])

        # Détecter les sujets
        for topic, keywords in topic_keywords.items():
            if any(keyword in all_text for keyword in keywords):
                context['topics'].append(topic)

        # Détecter l'état émotionnel
        if any(indicator in all_text for indicator in positive_indicators):
            context['emotional_state'] = 'positive'
        elif any(indicator in all_text for indicator in negative_indicators):
            context['emotional_state'] = 'negative'

        # Détecter le stade de conversation
        if len(messages) <= 2:
            context['conversation_stage'] = 'beginning'
        elif any(word in all_text for word in ['merci', 'au revoir', 'bonne journée']):
            context['conversation_stage'] = 'ending'

        return context

    def _adapt_response_to_context(self, base_response: str, session: ChatSession,
                                 intent: str, entities: List[str], user_profile: Optional[str] = None) -> str:
        """
        Adapte une réponse de base au contexte conversationnel actuel.

        Args:
            base_response: La réponse de base à adapter
            session: La session de conversation
            intent: L'intention détectée
            entities: Les entités extraites
            user_profile: Le profil utilisateur

        Returns:
            Réponse adaptée au contexte
        """
        # Analyser le contexte de la conversation
        recent_messages = session.messages.order_by('-created_at')[:5]
        conversation_context = self._analyze_conversation_context(recent_messages)

        # Adaptations basées sur l'état émotionnel
        if conversation_context['emotional_state'] == 'negative':
            # Ajouter de l'empathie pour les utilisateurs frustrés
            if intent == "greeting":
                base_response = base_response.replace(
                    "Comment puis-je vous aider ?",
                    "Je vois que vous pourriez avoir besoin d'assistance. Comment puis-je vous aider à résoudre votre situation ?"
                )
            elif intent == "problem_report":
                base_response = "Je comprends votre frustration. " + base_response

        elif conversation_context['emotional_state'] == 'positive':
            # Maintenir l'énergie positive
            if intent == "greeting":
                base_response = base_response.replace("😊", "😊✨")

        # Adaptations basées sur les sujets de conversation
        if 'réservation' in conversation_context['topics']:
            if intent == "greeting" and conversation_context['conversation_stage'] != 'beginning':
                base_response += "\n\nJe vois que nous parlions de réservation. Souhaitez-vous continuer sur ce sujet ?"

        if 'problème technique' in conversation_context['topics']:
            if intent in ["greeting", "social"]:
                base_response += "\n\nComment se passe la résolution de votre problème technique ?"

        # Adaptations basées sur le profil utilisateur
        if user_profile == "Capitaine":
            # Ton plus professionnel pour les capitaines
            base_response = base_response.replace("😊", "")
            if intent == "greeting":
                base_response = base_response.replace(
                    "Comment puis-je vous aider ?",
                    "Comment puis-je vous assister dans vos activités de capitaine ?"
                )

        elif user_profile == "Établissement":
            # Focus business pour les établissements
            if intent == "greeting":
                base_response = base_response.replace(
                    "Comment puis-je vous aider ?",
                    "Comment puis-je vous accompagner dans le développement de votre activité ?"
                )

        # Adaptations basées sur le stade de conversation
        if conversation_context['conversation_stage'] == 'ending':
            if intent == "greeting":
                # L'utilisateur revient après avoir dit au revoir
                base_response = "Re-bonjour ! 😊 Vous avez pensé à autre chose ? Je suis là pour vous aider."

        return base_response

    def _post_process_response(self, response: str, intent: str, entities: List[str], user_profile: Optional[str] = None) -> str:
        """
        Post-traite la réponse générée pour améliorer sa qualité.

        Args:
            response: La réponse générée par le modèle.
            intent: L'intention détectée dans la requête.
            entities: Les entités détectées dans la requête.
            user_profile: Le profil de l'utilisateur.

        Returns:
            Réponse post-traitée.
        """
        start_time = time.time()
        logger.debug(f"Début du post-traitement de la réponse")

        # Vérifier si la réponse est vide ou trop courte
        if not response or len(response.strip()) < 10:
            return "Je suis désolé, mais je n'ai pas pu générer une réponse appropriée. Veuillez reformuler votre question <NAME_EMAIL> pour plus d'aide."

        # Nettoyer la réponse (voir bloc précédent pour tout le nettoyage, paragraphes, etc.)
        import re
        meta_patterns = [
            r"score\s*[:=].*",
            r"classement\s*[:=].*",
            r"note\s*[:=].*",
            r"temps de réponse\s*[:=].*",
            r"crit[eè]re.*",
            r"évaluation.*",
            r"conclusion.*",
            r"---+",
            r"^#.*$",
        ]
        lines = response.strip().splitlines()
        cleaned_lines = []
        for line in lines:
            skip = False
            for pat in meta_patterns:
                if re.match(pat, line.strip(), re.IGNORECASE):
                    skip = True
                    break
            if not skip:
                cleaned_lines.append(line)
        response = " ".join(cleaned_lines)

        # Remplacer tous les '\\n' littéraux et \n restants par un espace ou un vrai saut de ligne pour mobile
        response = response.replace('\\n', ' ')
        response = response.replace('\n', ' ')
        response = re.sub(r'\s+', ' ', response)
        response = response.strip()

        # Découper en phrases et regrouper en paragraphes courts (max 3 phrases/para)
        sentences = re.split(r'(?<=[.!?]) +', response)
        paragraphs = []
        para = []
        for s in sentences:
            if s:
                para.append(s)
                if len(para) == 3:
                    paragraphs.append(' '.join(para))
                    para = []
        if para:
            paragraphs.append(' '.join(para))

        # Limiter à 4 paragraphes ou 600 caractères max pour mobile
        result = []
        char_count = 0
        for para in paragraphs:
            if len(result) >= 4 or char_count > 600:
                break
            if char_count + len(para) > 600:
                para = para[:600-char_count] + '...'
            result.append(para)
            char_count += len(para)
        response = '\n\n'.join(result).strip()

        # Ajouter une formule de politesse si absente
        polite_endings = ["merci", "bonne journée", "cordialement", "n'hésitez pas"]
        has_polite_ending = any(ending in response.lower() for ending in polite_endings)
        if not has_polite_ending:
            response = response.rstrip() + "\n\nN'hésitez pas si vous avez d'autres questions !"

        # Ajouter une mention RGPD si pertinent
        rgpd_keywords = ["données", "personnel", "information", "email", "téléphone", "compte", "confidentialité", "privé"]
        needs_rgpd = any(keyword in response.lower() for keyword in rgpd_keywords) or any(entity in entities for entity in rgpd_keywords)
        if needs_rgpd and "RGPD" not in response and "rgpd" not in response.lower():
            response += "\n\nConformément au RGPD, vos données personnelles sont traitées de manière sécurisée."

        # Vérifier la présence d'informations de contact si nécessaire
        contact_keywords = ["problème", "difficulté", "assistance", "aide", "support", "contacter"]
        needs_contact = any(keyword in response.lower() for keyword in contact_keywords)
        if needs_contact and "<EMAIL>" not in response:
            response += "\n\nPour toute assistance supplémentaire, contactez notre équipe à <EMAIL>."

        logger.debug(f"Post-traitement terminé en {time.time() - start_time:.2f} secondes")
        return response

    def get_offline_faqs(self, profile: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Récupère les questions fréquentes pour le support hors ligne.

        Args:
            profile: Le profil de l'utilisateur (Client, Établissement, Capitaine), optionnel.
            limit: Nombre maximum de FAQ à retourner.

        Returns:
            Liste de dictionnaires contenant les questions et réponses fréquentes.
        """
        logger.info(f"Récupération des FAQ pour le support hors ligne (profil: {profile}, limit: {limit})")

        try:
            # Récupérer toutes les clés du cache offline
            offline_cache = caches['offline']
            all_keys = offline_cache.keys("offline_qa_*")
        except Exception as e:
            logger.warning(f"Erreur lors de l'accès au cache offline: {str(e)}")
            return self._generate_default_faqs(profile)

        # Si aucune clé n'est trouvée, générer des FAQ par défaut
        if not all_keys:
            return self._generate_default_faqs(profile)

        # Récupérer les FAQ du cache
        faqs = []
        for key in all_keys[:limit]:
            cached_data = offline_cache.get(key)
            if cached_data and (not profile or cached_data.get('profile') == profile or cached_data.get('profile') == 'Général'):
                faqs.append({
                    'question': cached_data.get('question', ''),
                    'response': cached_data.get('response', ''),
                    'intent': cached_data.get('intent', 'unknown'),
                    'entities': cached_data.get('entities', []),
                    'profile': cached_data.get('profile', 'Général')
                })

        # Trier les FAQ par pertinence (spécifiques d'abord, puis générales)
        faqs.sort(key=lambda x: (
            0 if x.get('profile') == profile else 1,
            0 if x.get('intent') == 'specific' else 1,
            len(x.get('entities', [])),
            -len(x.get('question', ''))
        ))

        return faqs[:limit]

    def _generate_default_faqs(self, profile: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Génère des FAQ par défaut pour le support hors ligne.

        Args:
            profile: Le profil de l'utilisateur.

        Returns:
            Liste de dictionnaires contenant les questions et réponses par défaut.
        """
        default_faqs = [
            {
                'question': 'Comment réserver un bateau-taxi ?',
                'response': 'Pour réserver un bateau-taxi sur Commodore, ouvrez l\'application, sélectionnez votre point de départ et d\'arrivée, choisissez l\'heure de départ, indiquez le nombre de passagers, vérifiez le prix et confirmez votre réservation. Vous recevrez une confirmation par email avec un QR code à présenter au capitaine.',
                'intent': 'specific',
                'entities': ['réservation', 'bateau', 'taxi'],
                'profile': 'Client'
            },
            {
                'question': 'Comment payer ma course ?',
                'response': 'Vous pouvez payer votre course sur Commodore par carte bancaire (Visa, Mastercard, Apple Pay) lors de la réservation ou utiliser des crédits prépayés. Pour recharger vos crédits, allez dans votre profil > "Mon solde".',
                'intent': 'specific',
                'entities': ['paiement', 'course', 'crédit'],
                'profile': 'Client'
            },
            {
                'question': 'Comment annuler ma réservation ?',
                'response': 'Pour annuler une réservation, allez dans "Mes réservations", sélectionnez la réservation à annuler et appuyez sur "Annuler". Les conditions de remboursement dépendent du délai : 100% si plus de 24h avant, 50% entre 24h et 2h, aucun remboursement si moins de 2h avant le départ.',
                'intent': 'specific',
                'entities': ['annulation', 'réservation', 'remboursement'],
                'profile': 'Client'
            },
            {
                'question': 'Où trouver mon QR code ?',
                'response': 'Vous pouvez retrouver votre QR code dans la section "Mes réservations" de l\'application Commodore, sur l\'écran de confirmation de course, ou dans la notification reçue avant l\'arrivée du bateau.',
                'intent': 'specific',
                'entities': ['qr_code', 'code', 'scanner'],
                'profile': 'Client'
            },
            {
                'question': 'Quand suis-je payé pour mes courses ?',
                'response': 'En tant que Capitaine, vous êtes payé via Stripe Connect sous 7 jours ouvrés maximum après la course, après déduction de la commission Commodore (20% pour les trajets simples, 10% pour les mises à disposition, 5% via le module intégré).',
                'intent': 'specific',
                'entities': ['paiement', 'capitaine', 'commission'],
                'profile': 'Capitaine'
            }
        ]

        # Filtrer par profil si spécifié
        if profile:
            default_faqs = [faq for faq in default_faqs if faq['profile'] == profile or faq['profile'] == 'Général']

        return default_faqs

    def export_offline_data(self) -> Dict[str, Any]:
        """
        Exporte les données pour le support hors ligne.

        Returns:
            Dictionnaire contenant les données pour le support hors ligne.
        """
        logger.info("Exportation des données pour le support hors ligne")

        # Récupérer les FAQ pour chaque profil
        client_faqs = self.get_offline_faqs(profile='Client', limit=20)
        captain_faqs = self.get_offline_faqs(profile='Capitaine', limit=10)
        establishment_faqs = self.get_offline_faqs(profile='Établissement', limit=10)
        general_faqs = self.get_offline_faqs(limit=10)

        # Récupérer les documents les plus importants
        documents = []
        for doc in Document.objects.filter(embedding_generated=True).order_by('-updated_at')[:5]:
            documents.append({
                'title': doc.title,
                'content': doc.content[:1000],  # Limiter la taille pour l'export
                'category': doc.category,
                'updated_at': doc.updated_at.isoformat() if doc.updated_at else None
            })

        # Créer le package de données hors ligne
        from django.utils import timezone
        offline_data = {
            'faqs': {
                'client': client_faqs,
                'captain': captain_faqs,
                'establishment': establishment_faqs,
                'general': general_faqs
            },
            'documents': documents,
            'version': '1.0',
            'generated_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timezone.timedelta(days=7)).isoformat()
        }

        # Mettre en cache le package complet
        try:
            offline_cache = caches['offline']
            offline_cache.set('offline_data_package', offline_data)
        except Exception as e:
            logger.warning(f"Erreur lors de l'accès au cache offline: {str(e)}")

        return offline_data


# Instance singleton du service RAG
rag_service = RagService()