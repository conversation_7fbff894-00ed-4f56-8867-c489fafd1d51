"""
Vues pour l'espace Établissement.

Ce module contient toutes les vues pour la gestion de l'espace établissement,
incluant le tableau de bord, la gestion des navettes, l'enregistrement des bateliers,
et la gestion des paiements.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Count, Sum, Q
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings
from datetime import datetime, timedelta
import random
import string
from decimal import Decimal
from drf_spectacular.utils import extend_schema, OpenApiParameter
from accounts.permissions import IsEstablishment

from accounts.models import Establishment, Captain, Client
from boats.models import Boat
from trips.models import Trip, TripRequest, ShuttleTripRequest
from payments.models import Payment, Wallet
from notifications.services import create_notification

User = get_user_model()


@extend_schema(tags=["Establishments"])
class EstablishmentDashboardView(APIView):
    """
    Tableau de bord de l'établissement.

    GET /api/establishments/dashboard/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    @extend_schema(tags=["Establishments"], responses=None)
    def get(self, request):
        """Récupérer les données du tableau de bord"""

        # La permission IsEstablishment s'en charge déjà
        establishment = request.user.establishment

        establishment = request.user.establishment

        # Statistiques des navettes
        total_shuttles = Trip.objects.filter(
            trip_type='NAVETTES_GRATUITES',
            # Filtrer par établissement si nécessaire
        ).count()

        pending_requests = ShuttleTripRequest.objects.filter(
            establishment=establishment,
            status='PENDING'
        ).count()

        # Solde du portefeuille
        wallet = getattr(request.user, 'wallet', None)
        available_balance = wallet.balance if wallet else Decimal('0.00')

        # Courses récentes (navettes)
        recent_shuttles = Trip.objects.filter(
            trip_type='NAVETTES_GRATUITES',
            created_at__gte=timezone.now() - timedelta(days=30)
        ).order_by('-created_at')[:10]

        shuttles_data = []
        for shuttle in recent_shuttles:
            shuttles_data.append({
                'shuttle_id': str(shuttle.id),
                'date': shuttle.scheduled_start_time.isoformat() if shuttle.scheduled_start_time else None,
                'status': self._get_status_display(shuttle.status),
                'departure': shuttle.start_location,
                'destination': shuttle.end_location,
                'client': shuttle.client.user.get_full_name() if shuttle.client else 'N/A',
                'amount': float(shuttle.total_price)
            })

        # Disponibilité de l'établissement
        availability = establishment.is_available

        return Response({
            'status': 'success',
            'data': {
                'establishment_name': establishment.name,
                'establishment_type': establishment.get_type_display(),
                'available_balance': float(available_balance),
                'total_shuttles': total_shuttles,
                'pending_requests': pending_requests,
                'availability': availability,
                'shuttles': shuttles_data
            }
        })

    def _get_status_display(self, status):
        """Convertir le statut en français"""
        status_map = {
            'PENDING': 'En attente',
            'ACCEPTED': 'À venir',
            'IN_PROGRESS': 'En cours',
            'COMPLETED': 'Terminé',
            'CANCELLED': 'Annulé',
            'REJECTED': 'Annulé'
        }
        return status_map.get(status, status)


@extend_schema(tags=["Establishments"], request=OpenApiParameter(name="availability", type=bool, required=True), responses=None)
class EstablishmentAvailabilityToggleView(APIView):
    """
    Basculer la disponibilité de l'établissement.

    POST /api/establishments/toggle-availability/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    @extend_schema(tags=["Establishments"], request=None, responses=None)
    def post(self, request):
        """Basculer la disponibilité"""

        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)

        establishment = request.user.establishment
        availability = request.data.get('availability')

        if availability is None:
            return Response({
                'error': 'Le paramètre availability est requis'
            }, status=status.HTTP_400_BAD_REQUEST)

        establishment.is_available = bool(availability)
        establishment.save()

        return Response({
            'status': 'success',
            'availability': establishment.is_available
        })


@extend_schema(tags=["Establishments"], parameters=[
    OpenApiParameter(name="filter", type=str, required=False),
    OpenApiParameter(name="page", type=int, required=False),
    OpenApiParameter(name="limit", type=int, required=False)
], responses=None)
class EstablishmentShuttlesView(APIView):
    """
    Voir toutes les courses/navettes de l'établissement.

    GET /api/establishments/shuttles/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    @extend_schema(tags=["Establishments"], responses=None)
    def get(self, request):
        """Récupérer toutes les navettes avec filtres et pagination"""

        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)

        establishment = request.user.establishment

        # Paramètres de filtrage et pagination
        filter_status = request.GET.get('filter')
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))
        offset = (page - 1) * limit

        # Requête de base pour les navettes de cet établissement
        shuttles_query = ShuttleTripRequest.objects.filter(
            establishment=establishment
        )

        # Appliquer le filtre de statut si fourni
        if filter_status:
            status_map = {
                'En attente': 'PENDING',
                'À venir': 'ACCEPTED',
                'En cours': 'IN_PROGRESS',
                'Terminé': 'COMPLETED',
                'Annulé': ['CANCELLED', 'REJECTED']
            }

            if filter_status in status_map:
                mapped_status = status_map[filter_status]
                if isinstance(mapped_status, list):
                    shuttles_query = shuttles_query.filter(status__in=mapped_status)
                else:
                    shuttles_query = shuttles_query.filter(status=mapped_status)

        # Pagination
        total_count = shuttles_query.count()
        shuttles = shuttles_query.order_by('-created_at')[offset:offset+limit]

        # Sérialiser les données
        shuttles_data = []
        for shuttle in shuttles:
            shuttles_data.append({
                'id': str(shuttle.id),
                'type': 'SHUTTLE',
                'date': shuttle.departure_date.isoformat() if shuttle.departure_date else None,
                'time': shuttle.departure_time.strftime('%H:%M') if getattr(shuttle, 'departure_time', None) else None,
                'status': self._get_status_display(shuttle.status),
                'departure': shuttle.departure_location.get('city_name', '') if shuttle.departure_location else '',
                'destination': shuttle.arrival_location.get('city_name', '') if shuttle.arrival_location else '',
                'client_name': shuttle.client.user.get_full_name() if shuttle.client else 'N/A',
                'client_phone': shuttle.client.user.phone_number if shuttle.client and hasattr(shuttle.client.user, 'phone_number') else '',
                'passengers': shuttle.passenger_count if hasattr(shuttle, 'passenger_count') else None,
                'amount': 0.00,  # Navettes gratuites
                'message': getattr(shuttle, 'message', ''),
                'created_at': shuttle.created_at.isoformat() if hasattr(shuttle, 'created_at') else None
            })

        return Response({
            'status': 'success',
            'data': {
                'items': shuttles_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total_count
                }
            }
        })

    def _get_status_display(self, status):
        """Convertir le statut en français"""
        status_map = {
            'PENDING': 'En attente',
            'ACCEPTED': 'À venir',
            'IN_PROGRESS': 'En cours',
            'COMPLETED': 'Terminé',
            'CANCELLED': 'Annulé',
            'REJECTED': 'Annulé'
        }
        return status_map.get(status, status)


@extend_schema(tags=["Establishments"], responses=None)
class EstablishmentShuttleRequestsView(APIView):
    """
    Voir les demandes de navettes en attente.

    GET /api/establishments/shuttle-requests/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    @extend_schema(tags=["Establishments"], responses=None)
    def get(self, request):
        """Récupérer les demandes de navettes en attente"""

        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)

        establishment = request.user.establishment

        # Récupérer les demandes en attente
        pending_requests = ShuttleTripRequest.objects.filter(
            establishment=establishment,
            status='PENDING'
        ).order_by('-created_at')

        requests_data = []
        for req in pending_requests:
            requests_data.append({
                'id': str(req.id),
                'type': 'SHUTTLE_REQUEST',
                'date': req.departure_date.isoformat() if req.departure_date else None,
                'time': req.departure_time.strftime('%H:%M') if req.departure_time else None,
                'status': self._get_status_display(req.status) if hasattr(self, '_get_status_display') else req.status,
                'departure': req.departure_location.get('city_name', '') if req.departure_location else '',
                'destination': req.arrival_location.get('city_name', '') if req.arrival_location else '',
                'client_name': req.client.user.get_full_name() if req.client else 'N/A',
                'client_phone': req.client.user.phone_number if req.client and hasattr(req.client.user, 'phone_number') else '',
                'passengers': req.passenger_count,
                'amount': 0.00,
                'message': req.message or '',
                'created_at': req.created_at.isoformat() if hasattr(req, 'created_at') else None,
                'distance_km': float(req.distance_km) if req.distance_km is not None else None
            })

        return Response({
            'status': 'success',
            'data': {
                'items': requests_data
            }
        })