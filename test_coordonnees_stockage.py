#!/usr/bin/env python
"""
Test pour vérifier que les coordonnées sont bien stockées dans la course
"""

import os
import django
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, Client
from trips.models import Trip, ShuttleTripRequest
from trips.serializers import ShuttleTripRequestSerializer
from django.utils import timezone
from datetime import date, timedelta, datetime

def test_coordonnees_stockage():
    print("🗺️ Test de stockage des coordonnées dans la course")
    print("=" * 60)
    
    # 1. Créer une demande de navette (sans arrival_location)
    user_payload = {
        "departure_location": {
            "city_name": "Port de Cannes",
            "coordinates": {
                "latitude": 43.5528,
                "longitude": 7.0174
            },
            "timestamp": timezone.now().isoformat()
        },
        "passenger_count": 2,
        "departure_date": (date.today() + timedelta(days=1)).strftime('%Y-%m-%d'),
        "departure_time": "19:30:00",
        "message": "Test stockage coordonnées"
    }
    
    # 2. Trouver établissement et client
    establishment = Establishment.objects.filter(
        longitude__isnull=False, 
        latitude__isnull=False
    ).first()
    
    client = Client.objects.first()
    
    if not establishment or not client:
        print("❌ Données manquantes")
        return False
    
    print(f"✅ Client: {client.user.email}")
    print(f"✅ Établissement: {establishment.name}")
    print(f"   Coordonnées établissement: {establishment.latitude}, {establishment.longitude}")
    
    # 3. Créer la demande de navette
    serializer = ShuttleTripRequestSerializer(data=user_payload)
    if not serializer.is_valid():
        print(f"❌ Erreurs sérialiseur: {serializer.errors}")
        return False
    
    shuttle_request = serializer.save(client=client, establishment=establishment)
    print(f"✅ Demande navette créée avec ID: {shuttle_request.id}")
    
    # 4. Calculer la distance
    distance = shuttle_request.calculate_distance()
    print(f"✅ Distance calculée: {distance} km")
    
    # 5. Simuler l'acceptation par l'établissement (création de la course)
    # Simuler la création de la course comme le fait l'établissement
    from boats.models import Boat
    from accounts.models import Captain
    from trips.models import Shuttle
    from decimal import Decimal

    
    # Trouver un capitaine et un bateau
    captain = Captain.objects.first()
    boat = Boat.objects.first()
    
    if not captain or not boat:
        print("❌ Capitaine ou bateau manquant")
        return False
    
    # Créer une navette
    scheduled_start = timezone.make_aware(datetime.combine(
        shuttle_request.departure_date,
        shuttle_request.departure_time
    ))
    scheduled_end = scheduled_start + timedelta(hours=1)
    duration_minutes = 60
    
    shuttle = Shuttle.objects.create(
        establishment=establishment,
        boat=boat,  # Ajouter le bateau
        captain=captain,  # Ajouter le capitaine
        route_name=f"Navette vers {establishment.name}",
        start_location=shuttle_request.departure_location,
        end_location=shuttle_request.arrival_location,
        departure_time=scheduled_start,
        arrival_time=scheduled_end,
        max_capacity=shuttle_request.passenger_count,
        current_bookings=shuttle_request.passenger_count,
        price_per_person=Decimal('0.00'),
        status=Shuttle.Status.SCHEDULED
    )
    
    # Préparer les coordonnées complètes pour la course
    start_location_data = shuttle_request.departure_location if shuttle_request.departure_location else {}
    
    end_location_data = {
        'city_name': establishment.name,
        'coordinates': {
            'latitude': float(establishment.latitude) if establishment.latitude else 0,
            'longitude': float(establishment.longitude) if establishment.longitude else 0
        },
        'timestamp': timezone.now().isoformat()
    }
    
    # Créer la course (Trip)
    trip = Trip.objects.create(
        client=shuttle_request.client,
        captain=captain,
        boat=boat,
        establishment=establishment,
        shuttle=shuttle,
        trip_type=Trip.TripType.NAVETTES_GRATUITES,
        start_location=start_location_data,  # JSON complet avec coordonnées client
        end_location=end_location_data,      # JSON complet avec coordonnées établissement
        scheduled_start_time=scheduled_start,
        scheduled_end_time=scheduled_end,
        estimated_duration=duration_minutes,
        passenger_count=shuttle_request.passenger_count,
        distance_km=shuttle_request.distance_km,  # Distance calculée
        base_price=Decimal('0.00'),
        additional_charges=Decimal('0.00'),
        tip=Decimal('0.00'),
        total_price=Decimal('0.00'),
        payment_method='FREE_SHUTTLE',
        payment_status='PAID',
        status=Trip.Status.ACCEPTED
    )
    
    print(f"✅ Course créée avec ID: {trip.id}")
    
    # 6. Vérifier que les coordonnées sont bien stockées
    print("\n🔍 Vérification du stockage des coordonnées:")
    print("=" * 50)
    
    print("📍 Coordonnées de départ (client):")
    print(f"   JSON: {trip.start_location}")
    if isinstance(trip.start_location, dict) and 'coordinates' in trip.start_location:
        coords = trip.start_location['coordinates']
        print(f"   ✅ Latitude: {coords.get('latitude')}")
        print(f"   ✅ Longitude: {coords.get('longitude')}")
    else:
        print("   ❌ Coordonnées manquantes")
        return False
    
    print("\n📍 Coordonnées d'arrivée (établissement):")
    print(f"   JSON: {trip.end_location}")
    if isinstance(trip.end_location, dict) and 'coordinates' in trip.end_location:
        coords = trip.end_location['coordinates']
        print(f"   ✅ Latitude: {coords.get('latitude')}")
        print(f"   ✅ Longitude: {coords.get('longitude')}")
    else:
        print("   ❌ Coordonnées manquantes")
        return False
    
    print(f"\n📏 Distance stockée: {trip.distance_km} km")
    
    return True

if __name__ == "__main__":
    success = test_coordonnees_stockage()
    print(f"\n{'🎉 Test réussi!' if success else '❌ Test échoué'}")
    
    if success:
        print("\n✅ RÉSUMÉ:")
        print("- Les coordonnées du client sont stockées dans trip.start_location")
        print("- Les coordonnées de l'établissement sont stockées dans trip.end_location")
        print("- La distance calculée est stockée dans trip.distance_km")
        print("- Toutes les informations nécessaires pour le front-end sont disponibles")
