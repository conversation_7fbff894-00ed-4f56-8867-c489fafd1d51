"""
Script pour résoudre le conflit de migration avec django-allauth
Ce script va:
1. Supprimer les tables problématiques
2. Désactiver temporairement django-allauth
3. Migrer la base de données
4. Réactiver django-allauth
"""
import os
import sys
import django
from django.conf import settings

# Ajouter le répertoire du projet au path
sys.path.append('D:\\commodore')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

# Fonction pour exécuter une commande SQL
def execute_sql(sql):
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute(sql)
    print(f"SQL exécuté: {sql}")

# 1. Supprimer les tables problématiques
print("Suppression des tables problématiques...")
tables_to_drop = [
    'account_emailaddress',
    'account_emailconfirmation',
    'socialaccount_socialaccount',
    'socialaccount_socialapp',
    'socialaccount_socialapp_sites',
    'socialaccount_socialtoken'
]

for table in tables_to_drop:
    try:
        execute_sql(f"DROP TABLE IF EXISTS {table} CASCADE;")
    except Exception as e:
        print(f"Erreur lors de la suppression de {table}: {e}")

# 2. Modifier le fichier settings.py pour désactiver temporairement django-allauth
print("Désactivation temporaire de django-allauth...")

settings_path = 'D:\\commodore\\commodore\\settings.py'
with open(settings_path, 'r', encoding='utf-8') as f:
    settings_content = f.read()

# Sauvegarder une copie du fichier settings.py original
with open(settings_path + '.bak', 'w', encoding='utf-8') as f:
    f.write(settings_content)

# Désactiver temporairement django-allauth en commentant les apps
allauth_apps = [
    "'allauth',",
    "'allauth.account',",
    "'allauth.socialaccount',",
    "'allauth.socialaccount.providers.facebook',",
    "'allauth.socialaccount.providers.google',",
    "'allauth.socialaccount.providers.apple',",
    "'dj_rest_auth',",
    "'dj_rest_auth.registration',",
]

modified_settings = settings_content
for app in allauth_apps:
    modified_settings = modified_settings.replace(app, f"# {app}")

with open(settings_path, 'w', encoding='utf-8') as f:
    f.write(modified_settings)

print("django-allauth temporairement désactivé. Exécutez maintenant:")
print("python manage.py migrate")
print("\nAprès la migration, exécutez:")
print("python scripts/fix_django_allauth_part2.py")
