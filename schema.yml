openapi: 3.0.3
info:
  title: Commodore API
  version: 2.0.0
  description: "\n    # \U0001F6A4 API Commodore - Plateforme de Transport Maritime\n\
    \n    ## \U0001F4CB Vue d'ensemble\n    L'API Commodore permet de gérer une plateforme\
    \ complète de transport maritime avec :\n    - **Gestion des utilisateurs** (Clients,\
    \ Capitaines, Établissements)\n    - **Système de réservation** de courses et\
    \ navettes\n    - **Paiements sécurisés** avec portefeuilles virtuels\n    - **Suivi\
    \ en temps réel** des trajets\n    - **Notifications** push et email\n    - **Chat**\
    \ intégré entre utilisateurs\n\n    ## \U0001F510 Authentification\n    L'API\
    \ utilise l'authentification par token. Incluez le header :\n    ```\n    Authorization:\
    \ Token votre_token_ici\n    ```\n\n    ## \U0001F4F1 Applications\n    - **Accounts**\
    \ : Gestion des comptes utilisateurs\n    - **Trips** : Réservation et gestion\
    \ des courses\n    - **Payments** : Paiements et portefeuilles\n    - **Boats**\
    \ : Gestion des bateaux\n    - **Chat** : Messagerie instantanée\n    - **Notifications**\
    \ : Système de notifications\n\n    ## \U0001F310 Environnements\n    - **Production**\
    \ : https://api.commodore.com\n    - **Staging** : https://staging-api.commodore.com\n\
    \    - **Development** : http://localhost:8000\n\n    ## \U0001F4DE Support\n\
    \    - Email : <EMAIL>\n    - Documentation : https://docs.commodore.com\n\
    \    "
  contact:
    name: Support Commodore
    email: <EMAIL>
    url: https://commodore.com/support
  license:
    name: Propriétaire
    url: https://commodore.com/license
paths:
  /:
    get:
      operationId: root_list
      parameters:
      - in: query
        name: boat_type
        schema:
          type: string
      - in: query
        name: capacity_min
        schema:
          type: integer
      - in: query
        name: location
        schema:
          type: string
      tags:
      - Boats
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BoatList'
          description: ''
    post:
      operationId: root_create
      parameters:
      - in: query
        name: boat_type
        schema:
          type: string
      - in: query
        name: capacity_min
        schema:
          type: integer
      - in: query
        name: location
        schema:
          type: string
      tags:
      - Boats
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BoatRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/BoatRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/BoatRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Boat'
          description: ''
  /{boat_id}/maintenance/:
    get:
      operationId: maintenance_list
      parameters:
      - in: path
        name: boat_id
        schema:
          type: integer
        required: true
      tags:
      - Boats
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MaintenanceRecord'
          description: ''
    post:
      operationId: maintenance_create
      parameters:
      - in: path
        name: boat_id
        schema:
          type: integer
        required: true
      tags:
      - Boats
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceRecord'
          description: ''
  /{id}/:
    get:
      operationId: root_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Boats
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BoatDetail'
          description: ''
    patch:
      operationId: root_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Boats
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedBoatDetailRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedBoatDetailRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedBoatDetailRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BoatDetail'
          description: ''
    delete:
      operationId: root_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Boats
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /{id}/availability/:
    get:
      operationId: availability_retrieve
      parameters:
      - in: query
        name: end_date
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: query
        name: start_date
        schema:
          type: string
        required: true
      tags:
      - Boats
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Boat'
          description: ''
  /{id}/toggle_availability/:
    post:
      operationId: toggle_availability_create
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Boats
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /auth/login/:
    post:
      operationId: auth_login_create
      description: |-
        Check the credentials and return the REST Token
        if the credentials are valid and authenticated.
        Calls Django Auth login method to register User ID
        in Django session framework

        Accept the following POST parameters: username, password
        Return the REST Framework Token Object's key.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LoginRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LoginRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Token'
          description: ''
  /auth/logout/:
    post:
      operationId: auth_logout_create
      description: |-
        Calls Django logout method and delete the Token object
        assigned to the current User object.

        Accepts/Returns nothing.
      tags:
      - auth
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /auth/password/change/:
    post:
      operationId: auth_password_change_create
      description: |-
        Calls Django Auth SetPasswordForm save method.

        Accepts the following POST parameters: new_password1, new_password2
        Returns the success/fail message.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordChangeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordChangeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordChangeRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /auth/password/reset/:
    post:
      operationId: auth_password_reset_create
      description: |-
        Calls Django Auth PasswordResetForm save method.

        Accepts the following POST parameters: email
        Returns the success/fail message.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordResetRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordResetRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /auth/password/reset/confirm/:
    post:
      operationId: auth_password_reset_confirm_create
      description: |-
        Password reset e-mail link is confirmed, therefore
        this resets the user's password.

        Accepts the following POST parameters: token, uid,
            new_password1, new_password2
        Returns the success/fail message.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetConfirmRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordResetConfirmRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordResetConfirmRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /auth/registration/:
    post:
      operationId: auth_registration_create
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /auth/registration/resend-email/:
    post:
      operationId: auth_registration_resend_email_create
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResendEmailVerificationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ResendEmailVerificationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ResendEmailVerificationRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /auth/registration/verify-email/:
    post:
      operationId: auth_registration_verify_email_create
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifyEmailRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/VerifyEmailRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/VerifyEmailRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /auth/user/:
    get:
      operationId: auth_user_retrieve
      description: |-
        Reads and updates UserModel fields
        Accepts GET, PUT, PATCH methods.

        Default accepted fields: username, first_name, last_name
        Default display fields: pk, username, email, first_name, last_name
        Read-only fields: pk, email

        Returns UserModel fields.
      tags:
      - auth
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetails'
          description: ''
    put:
      operationId: auth_user_update
      description: |-
        Reads and updates UserModel fields
        Accepts GET, PUT, PATCH methods.

        Default accepted fields: username, first_name, last_name
        Default display fields: pk, username, email, first_name, last_name
        Read-only fields: pk, email

        Returns UserModel fields.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserDetailsRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserDetailsRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserDetailsRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetails'
          description: ''
    patch:
      operationId: auth_user_partial_update
      description: |-
        Reads and updates UserModel fields
        Accepts GET, PUT, PATCH methods.

        Default accepted fields: username, first_name, last_name
        Default display fields: pk, username, email, first_name, last_name
        Read-only fields: pk, email

        Returns UserModel fields.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserDetailsRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserDetailsRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserDetailsRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetails'
          description: ''
  /available/:
    get:
      operationId: available_list
      parameters:
      - in: query
        name: boat_type
        schema:
          type: string
      - in: query
        name: capacity_min
        schema:
          type: integer
      - in: query
        name: date_end
        schema:
          type: string
      - in: query
        name: date_start
        schema:
          type: string
      - in: query
        name: location
        schema:
          type: string
      tags:
      - Boats
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BoatList'
          description: ''
  /boatman/availability/:
    get:
      operationId: boatman_availability_retrieve
      description: Récupérer le statut de disponibilité
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: boatman_availability_create
      description: Mettre à jour la disponibilité
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/boat/:
    get:
      operationId: boatman_boat_retrieve
      description: Récupérer les informations du bateau
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: boatman_boat_partial_update
      description: Mettre à jour les informations du bateau
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/change-password/:
    post:
      operationId: boatman_change_password_create
      description: Changer le mot de passe
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/dashboard/:
    get:
      operationId: boatman_dashboard_retrieve
      description: Récupérer les données du tableau de bord
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/forgot-password/:
    post:
      operationId: boatman_forgot_password_create
      description: Initier la réinitialisation du mot de passe
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /boatman/login/:
    post:
      operationId: boatman_login_create
      description: Authentifier un batelier
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /boatman/payment-methods/:
    get:
      operationId: boatman_payment_methods_retrieve
      description: Récupérer les méthodes de paiement
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: boatman_payment_methods_create
      description: Ajouter/Mettre à jour une méthode de paiement
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/profile/:
    get:
      operationId: boatman_profile_retrieve
      description: Récupérer le profil du batelier
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: boatman_profile_partial_update
      description: Mettre à jour le profil du batelier
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/shuttle/{shuttle_id}/:
    get:
      operationId: boatman_shuttle_retrieve
      description: Récupérer les détails d'une course
      parameters:
      - in: path
        name: shuttle_id
        schema:
          type: integer
        required: true
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/shuttle/{shuttle_id}/end/:
    post:
      operationId: boatman_shuttle_end_create
      description: Terminer une course
      parameters:
      - in: path
        name: shuttle_id
        schema:
          type: integer
        required: true
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/shuttle/{shuttle_id}/start/:
    post:
      operationId: boatman_shuttle_start_create
      description: Démarrer une course
      parameters:
      - in: path
        name: shuttle_id
        schema:
          type: integer
        required: true
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/shuttle/{shuttle_id}/track/:
    get:
      operationId: boatman_shuttle_track_retrieve
      description: Suivre une course
      parameters:
      - in: path
        name: shuttle_id
        schema:
          type: integer
        required: true
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/shuttle/{shuttle_id}/validate-qr/:
    post:
      operationId: boatman_shuttle_validate_qr_create
      description: Valider le QR code
      parameters:
      - in: path
        name: shuttle_id
        schema:
          type: integer
        required: true
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/shuttles/:
    get:
      operationId: boatman_shuttles_retrieve
      description: Récupérer la liste des courses
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/verify-code/:
    post:
      operationId: boatman_verify_code_create
      description: Vérifier le code de vérification
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /boatman/wallet/:
    get:
      operationId: boatman_wallet_retrieve
      description: Récupérer les informations du portefeuille
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /boatman/wallet/withdraw/:
    post:
      operationId: boatman_wallet_withdraw_create
      description: Effectuer un retrait
      tags:
      - boatman
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /captains/:
    get:
      operationId: captains_list
      tags:
      - Accounts
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Captain'
          description: ''
  /captains/{id}/:
    get:
      operationId: captains_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Accounts
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Captain'
          description: ''
    patch:
      operationId: captains_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Accounts
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Captain'
          description: ''
  /chat/chatbot/clear/{session_id}/:
    delete:
      operationId: chat_chatbot_clear_destroy
      description: Supprime une session de chatbot et tous ses messages.
      parameters:
      - in: path
        name: session_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /chat/chatbot/feedback/:
    post:
      operationId: chat_chatbot_feedback_create
      description: |-
        Endpoint pour soumettre un feedback sur une réponse du chatbot.
        Permet aux utilisateurs d'évaluer la qualité des réponses pour amélioration future.
      tags:
      - chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /chat/chatbot/feedback/stats/:
    get:
      operationId: chat_chatbot_feedback_stats_retrieve
      description: Récupère des statistiques sur les feedbacks pour les administrateurs.
      tags:
      - chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /chat/chatbot/history/:
    get:
      operationId: chat_chatbot_history_retrieve
      description: |-
        Récupère l'historique des messages pour une session de chatbot.
        Si aucun session_id n'est fourni, retourne toutes les sessions de l'utilisateur.
      tags:
      - chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /chat/chatbot/history/{session_id}/:
    get:
      operationId: chat_chatbot_history_retrieve_2
      description: |-
        Récupère l'historique des messages pour une session de chatbot.
        Si aucun session_id n'est fourni, retourne toutes les sessions de l'utilisateur.
      parameters:
      - in: path
        name: session_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /chat/chatbot/message/:
    post:
      operationId: chat_chatbot_message_create
      description: |-
        Endpoint pour interagir avec le chatbot alimenté par Meta-Llama-3.
        Reçoit un message de l'utilisateur et retourne une réponse générée.
      tags:
      - chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /chat/chatbot/sessions/:
    get:
      operationId: chat_chatbot_sessions_list
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChatbotSession'
          description: ''
    post:
      operationId: chat_chatbot_sessions_create
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatbotSessionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatbotSessionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatbotSessionRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatbotSession'
          description: ''
  /chat/chatbot/sessions/{id}/:
    get:
      operationId: chat_chatbot_sessions_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatbotSession'
          description: ''
    put:
      operationId: chat_chatbot_sessions_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatbotSessionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatbotSessionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatbotSessionRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatbotSession'
          description: ''
    patch:
      operationId: chat_chatbot_sessions_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedChatbotSessionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedChatbotSessionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedChatbotSessionRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatbotSession'
          description: ''
    delete:
      operationId: chat_chatbot_sessions_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /chat/chatbot/sessions/{session_pk}/messages/:
    get:
      operationId: chat_chatbot_sessions_messages_list
      parameters:
      - in: path
        name: session_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChatbotMessage'
          description: ''
    post:
      operationId: chat_chatbot_sessions_messages_create
      parameters:
      - in: path
        name: session_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatbotMessageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatbotMessageRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatbotMessageRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatbotMessage'
          description: ''
  /chat/chatbot/sessions/{session_pk}/messages/{id}/:
    get:
      operationId: chat_chatbot_sessions_messages_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: session_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatbotMessage'
          description: ''
    put:
      operationId: chat_chatbot_sessions_messages_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: session_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatbotMessageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatbotMessageRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatbotMessageRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatbotMessage'
          description: ''
    patch:
      operationId: chat_chatbot_sessions_messages_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: session_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedChatbotMessageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedChatbotMessageRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedChatbotMessageRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatbotMessage'
          description: ''
    delete:
      operationId: chat_chatbot_sessions_messages_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: session_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /chat/rooms/:
    get:
      operationId: chat_rooms_list
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChatRoomList'
          description: ''
    post:
      operationId: chat_rooms_create
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRoomRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatRoomRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatRoomRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatRoom'
          description: ''
  /chat/rooms/{id}/:
    get:
      operationId: chat_rooms_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatRoom'
          description: ''
    put:
      operationId: chat_rooms_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRoomRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatRoomRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatRoomRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatRoom'
          description: ''
    patch:
      operationId: chat_rooms_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedChatRoomRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedChatRoomRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedChatRoomRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatRoom'
          description: ''
    delete:
      operationId: chat_rooms_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /chat/rooms/{id}/mark_as_read/:
    post:
      operationId: chat_rooms_mark_as_read_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRoomRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatRoomRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatRoomRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /chat/rooms/{room_pk}/messages/:
    get:
      operationId: chat_rooms_messages_list
      parameters:
      - in: path
        name: room_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Message'
          description: ''
    post:
      operationId: chat_rooms_messages_create
      parameters:
      - in: path
        name: room_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MessageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MessageRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MessageRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
          description: ''
  /chat/rooms/{room_pk}/messages/{id}/:
    get:
      operationId: chat_rooms_messages_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: room_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
          description: ''
    put:
      operationId: chat_rooms_messages_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: room_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MessageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MessageRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MessageRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
          description: ''
    patch:
      operationId: chat_rooms_messages_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: room_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedMessageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedMessageRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedMessageRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
          description: ''
    delete:
      operationId: chat_rooms_messages_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: room_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /chat/rooms/{room_pk}/messages/{id}/mark_as_read/:
    post:
      operationId: chat_rooms_messages_mark_as_read_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: room_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MessageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MessageRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MessageRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /chat/rooms/{room_pk}/messages/{id}/soft_delete/:
    post:
      operationId: chat_rooms_messages_soft_delete_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: room_pk
        schema:
          type: string
        required: true
      tags:
      - Chat
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MessageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MessageRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MessageRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /clients/:
    get:
      operationId: clients_list
      tags:
      - Accounts
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Client'
          description: ''
  /establishments/:
    get:
      operationId: establishments_list
      tags:
      - Accounts
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Establishment'
          description: ''
  /establishments/{id}/:
    get:
      operationId: establishments_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Accounts
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Establishment'
          description: ''
    patch:
      operationId: establishments_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Accounts
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Establishment'
          description: ''
  /establishments/available-resources/:
    get:
      operationId: establishments_available_resources_retrieve
      description: Récupérer les bateaux et capitaines disponibles
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/boatmen/:
    get:
      operationId: establishments_boatmen_retrieve
      description: Récupérer la liste des bateliers
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/boatmen/{captain_id}/:
    get:
      operationId: establishments_boatmen_retrieve_2
      description: Récupérer les détails d'un batelier
      parameters:
      - in: path
        name: captain_id
        schema:
          type: integer
        required: true
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/dashboard/:
    get:
      operationId: establishments_dashboard_retrieve
      description: Récupérer les données du tableau de bord
      tags:
      - Establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/payments/history/:
    get:
      operationId: establishments_payments_history_retrieve
      description: Récupérer l'historique des paiements
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/payments/stats/:
    get:
      operationId: establishments_payments_stats_retrieve
      description: Récupérer les statistiques des paiements
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/register-boatman/:
    post:
      operationId: establishments_register_boatman_create
      description: Enregistrer un nouveau batelier avec envoi d'email
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/shuttle-requests/:
    get:
      operationId: establishments_shuttle_requests_retrieve
      description: Récupérer les demandes de navettes en attente
      tags:
      - Establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/shuttle-requests/{request_id}/accept/:
    post:
      operationId: establishments_shuttle_requests_accept_create
      description: Accepter une demande de navette
      parameters:
      - in: path
        name: request_id
        schema:
          type: integer
        required: true
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/shuttle-requests/{request_id}/reject/:
    post:
      operationId: establishments_shuttle_requests_reject_create
      description: Rejeter une demande de navette
      parameters:
      - in: path
        name: request_id
        schema:
          type: integer
        required: true
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/shuttles/:
    get:
      operationId: establishments_shuttles_retrieve
      description: Récupérer toutes les navettes avec filtres et pagination
      parameters:
      - in: query
        name: filter
        schema:
          type: string
      - in: query
        name: limit
        schema:
          type: integer
      - in: query
        name: page
        schema:
          type: integer
      tags:
      - Establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/toggle-availability/:
    post:
      operationId: establishments_toggle_availability_create
      description: Basculer la disponibilité
      tags:
      - Establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/wallet/:
    get:
      operationId: establishments_wallet_retrieve
      description: Récupérer les informations du portefeuille
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /establishments/wallet/add-funds/:
    post:
      operationId: establishments_wallet_add_funds_create
      description: Ajouter des fonds au portefeuille
      tags:
      - establishments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /favorites/captains/:
    get:
      operationId: favorites_captains_retrieve
      description: Récupérer la liste des capitaines favoris de l'utilisateur.
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: favorites_captains_create
      description: Ajouter un nouveau capitaine favori.
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /favorites/captains/{id}/:
    get:
      operationId: favorites_captains_retrieve_2
      description: Récupérer les détails d'un capitaine favori.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: favorites_captains_partial_update
      description: Mettre à jour les notes d'un capitaine favori.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: favorites_captains_destroy
      description: Supprimer un capitaine des favoris.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /favorites/locations/:
    get:
      operationId: favorites_locations_retrieve
      description: Récupérer la liste des emplacements favoris de l'utilisateur.
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: favorites_locations_create
      description: Ajouter un nouvel emplacement favori.
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /favorites/locations/{id}/:
    get:
      operationId: favorites_locations_retrieve_2
      description: Récupérer les détails d'un emplacement favori.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: favorites_locations_partial_update
      description: Mettre à jour un emplacement favori.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: favorites_locations_destroy
      description: Supprimer un emplacement favori.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - favorites
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /login/:
    post:
      operationId: login_create
      tags:
      - Authentication
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /maintenance/{id}/:
    get:
      operationId: maintenance_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - maintenance
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: maintenance_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - maintenance
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: maintenance_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - maintenance
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /notifications/devices/:
    get:
      operationId: notifications_devices_list
      tags:
      - Notifications
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Device'
          description: ''
    post:
      operationId: notifications_devices_create
      tags:
      - Notifications
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
  /notifications/devices/{id}/:
    get:
      operationId: notifications_devices_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
    put:
      operationId: notifications_devices_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
    patch:
      operationId: notifications_devices_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDeviceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDeviceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDeviceRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
    delete:
      operationId: notifications_devices_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /notifications/devices/{id}/deactivate/:
    post:
      operationId: notifications_devices_deactivate_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /notifications/notifications/:
    get:
      operationId: notifications_notifications_list
      tags:
      - Notifications
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Notification'
          description: ''
    post:
      operationId: notifications_notifications_create
      tags:
      - Notifications
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
          description: ''
  /notifications/notifications/{id}/:
    get:
      operationId: notifications_notifications_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
          description: ''
    put:
      operationId: notifications_notifications_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
          description: ''
    patch:
      operationId: notifications_notifications_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedNotificationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedNotificationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedNotificationRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
          description: ''
    delete:
      operationId: notifications_notifications_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /notifications/notifications/{id}/mark_as_read/:
    post:
      operationId: notifications_notifications_mark_as_read_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - Notifications
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /notifications/notifications/mark_all_as_read/:
    post:
      operationId: notifications_notifications_mark_all_as_read_create
      tags:
      - Notifications
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /notifications/notifications/unread_count/:
    get:
      operationId: notifications_notifications_unread_count_retrieve
      tags:
      - Notifications
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /password/reset/:
    post:
      operationId: password_reset_create
      tags:
      - Authentication
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetRequestRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordResetRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordResetRequestRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /password/reset/verify/:
    post:
      operationId: password_reset_verify_create
      tags:
      - Authentication
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetVerifyRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordResetVerifyRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordResetVerifyRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /payments/admin/wallet/add-funds/:
    post:
      operationId: payments_admin_wallet_add_funds_create
      description: |-
        Ajoute des fonds au portefeuille d'un utilisateur.

        Parameters:
            user_id (int): ID de l'utilisateur
            amount (decimal): Montant à ajouter

        Returns:
            Response: Détails du portefeuille mis à jour
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/carbon-offset/:
    post:
      operationId: payments_carbon_offset_create
      description: |-
        Paie la compensation carbone pour une course ou une navette.

        Args:
            request: Requête HTTP contenant l'ID de la course ou de la navette et le montant de la compensation

        Returns:
            Response: Données de la transaction au format JSON
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/earnings/:
    get:
      operationId: payments_earnings_retrieve
      description: |-
        Retourne l'historique des revenus par course.

        URL: GET /api/payments/earnings/
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/maintenance/{maintenance_id}/pay/:
    post:
      operationId: payments_maintenance_pay_create
      description: Traite le paiement d'un service de maintenance.
      parameters:
      - in: path
        name: maintenance_id
        schema:
          type: integer
        required: true
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/promotions/:
    post:
      operationId: payments_promotions_create
      description: Traite le paiement pour la promotion d'un service.
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/shuttles/{shuttle_id}/pay/:
    post:
      operationId: payments_shuttles_pay_create
      description: Traite le paiement d'une réservation de navette.
      parameters:
      - in: path
        name: shuttle_id
        schema:
          type: integer
        required: true
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/tip/:
    post:
      operationId: payments_tip_create
      description: |-
        Ajoute un pourboire pour une course ou une navette.

        Args:
            request: Requête HTTP contenant l'ID de la course ou de la navette et le montant du pourboire

        Returns:
            Response: Données de la transaction au format JSON
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/transactions/:
    get:
      operationId: payments_transactions_retrieve
      description: Récupère la liste des transactions de l'utilisateur.
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/transactions/{id}/:
    get:
      operationId: payments_transactions_retrieve_2
      description: Récupère les détails d'une transaction.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/transactions/{id}/refund/:
    post:
      operationId: payments_transactions_refund_create
      description: |-
        Rembourse un paiement pour une transaction.

        Args:
            request: Requête HTTP contenant les détails du remboursement
            id: ID de la transaction à rembourser (depuis l'URL)

        Returns:
            Response: Données de la transaction de remboursement au format JSON
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/trip/:
    post:
      operationId: payments_trip_create
      description: |-
        Paie une course avec le portefeuille de l'utilisateur connecté.

        Args:
            request: Requête HTTP
            trip_id: ID de la course (depuis l'URL)

        Returns:
            Response: Données de la transaction au format JSON
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/trips/{trip_id}/pay/:
    post:
      operationId: payments_trips_pay_create
      description: |-
        Paie une course avec le portefeuille de l'utilisateur connecté.

        Args:
            request: Requête HTTP
            trip_id: ID de la course (depuis l'URL)

        Returns:
            Response: Données de la transaction au format JSON
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/trips/{trip_id}/shared_pay/:
    post:
      operationId: payments_trips_shared_pay_create
      description: |-
        Paie une part de course partagée avec le portefeuille de l'utilisateur connecté.

        Args:
            request: Requête HTTP
            trip_id: ID de la course (depuis l'URL)

        Returns:
            Response: Données de la transaction au format JSON
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/wallet/:
    get:
      operationId: wallet_detail
      description: "\n        Récupère les détails complets du portefeuille de l'utilisateur\
        \ connecté.\n\n        **Informations retournées :**\n        - Solde actuel\n\
        \        - Points de fidélité\n        - Total gagné et dépensé\n        -\
        \ Historique des transactions récentes\n        "
      summary: Détails du portefeuille
      tags:
      - 💰 Payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletDetailResponse'
              examples:
                Succès:
                  value:
                    id: 123
                    user_id: 456
                    balance: '150.75'
                    loyalty_points: 25
                    total_earned: '500.00'
                    total_spent: '349.25'
                    created_at: '2025-01-01T10:00:00Z'
                    updated_at: '2025-01-15T14:30:00Z'
          description: ''
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
  /payments/wallet/add_credits/:
    post:
      operationId: wallet_recharge
      description: "\n        Crée une session de paiement Stripe pour recharger le\
        \ portefeuille de l'utilisateur.\n\n        **Processus :**\n        1. Validation\
        \ du montant (1€ - 1000€)\n        2. Création d'une session Stripe\n    \
        \    3. Retour de l'URL de paiement\n        4. Confirmation automatique via\
        \ webhook\n\n        **Méthodes de paiement supportées :**\n        - Carte\
        \ bancaire\n        - Apple Pay\n        - Google Pay\n        "
      summary: Recharger le portefeuille
      tags:
      - 💰 Payments
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WalletRechargeRequestRequest'
            examples:
              RequêteDeRecharge:
                value:
                  amount: '50.00'
                  payment_method_id: pm_1234567890
                summary: Requête de recharge
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/WalletRechargeRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/WalletRechargeRequestRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletRechargeResponse'
              examples:
                RequêteDeRecharge:
                  value:
                    amount: '50.00'
                    payment_method_id: pm_1234567890
                  summary: Requête de recharge
          description: ''
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
  /payments/wallet/captain/:
    get:
      operationId: payments_wallet_captain_retrieve
      description: |-
        Retourne le solde et l'historique des transactions du capitaine.

        URL: GET /api/payments/wallet/
        Query params:
        - limit: nombre de transactions à retourner (défaut: 20)
        - offset: décalage pour pagination
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /payments/wallet/recharge/:
    post:
      operationId: wallet_recharge_2
      description: "\n        Crée une session de paiement Stripe pour recharger le\
        \ portefeuille de l'utilisateur.\n\n        **Processus :**\n        1. Validation\
        \ du montant (1€ - 1000€)\n        2. Création d'une session Stripe\n    \
        \    3. Retour de l'URL de paiement\n        4. Confirmation automatique via\
        \ webhook\n\n        **Méthodes de paiement supportées :**\n        - Carte\
        \ bancaire\n        - Apple Pay\n        - Google Pay\n        "
      summary: Recharger le portefeuille
      tags:
      - 💰 Payments
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WalletRechargeRequestRequest'
            examples:
              RequêteDeRecharge:
                value:
                  amount: '50.00'
                  payment_method_id: pm_1234567890
                summary: Requête de recharge
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/WalletRechargeRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/WalletRechargeRequestRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletRechargeResponse'
              examples:
                RequêteDeRecharge:
                  value:
                    amount: '50.00'
                    payment_method_id: pm_1234567890
                  summary: Requête de recharge
          description: ''
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
        '401':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: ''
  /payments/withdraw/:
    post:
      operationId: payments_withdraw_create
      description: |-
        Permet au capitaine de demander un retrait de fonds.

        URL: POST /api/payments/withdraw/

        Body: {
            "amount": 150.00,
            "bank_account": {
                "iban": "FR76...",
                "bic": "BNPAFRPP",
                "account_holder": "John Doe"
            },
            "reason": "Retrait mensuel"
        }
      tags:
      - payments
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /profile/:
    get:
      operationId: profile_retrieve
      tags:
      - Accounts
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
    patch:
      operationId: profile_partial_update
      description: |-
        Enhanced PATCH method to support captain-specific validations
        and mobile app requirements.
      tags:
      - Accounts
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserProfileRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserProfileRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserProfileRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
  /rag/chat/api/:
    post:
      operationId: rag_chat_api_create
      description: |-
        API pour le chat.

        Reçoit un message de l'utilisateur et retourne une réponse générée par le modèle.
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /rag/documents/:
    get:
      operationId: rag_documents_list
      description: |-
        ViewSet pour la gestion des documents.

        Permet de créer, lire, mettre à jour et supprimer des documents,
        ainsi que de générer des embeddings pour ces documents.
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Document'
          description: ''
    post:
      operationId: rag_documents_create
      description: |-
        ViewSet pour la gestion des documents.

        Permet de créer, lire, mettre à jour et supprimer des documents,
        ainsi que de générer des embeddings pour ces documents.
      tags:
      - rag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentCreateRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentCreate'
          description: ''
  /rag/documents/{id}/:
    get:
      operationId: rag_documents_retrieve
      description: |-
        ViewSet pour la gestion des documents.

        Permet de créer, lire, mettre à jour et supprimer des documents,
        ainsi que de générer des embeddings pour ces documents.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this Document.
        required: true
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    put:
      operationId: rag_documents_update
      description: |-
        ViewSet pour la gestion des documents.

        Permet de créer, lire, mettre à jour et supprimer des documents,
        ainsi que de générer des embeddings pour ces documents.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this Document.
        required: true
      tags:
      - rag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    patch:
      operationId: rag_documents_partial_update
      description: |-
        ViewSet pour la gestion des documents.

        Permet de créer, lire, mettre à jour et supprimer des documents,
        ainsi que de générer des embeddings pour ces documents.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this Document.
        required: true
      tags:
      - rag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDocumentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDocumentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDocumentRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    delete:
      operationId: rag_documents_destroy
      description: |-
        ViewSet pour la gestion des documents.

        Permet de créer, lire, mettre à jour et supprimer des documents,
        ainsi que de générer des embeddings pour ces documents.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this Document.
        required: true
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /rag/documents/{id}/process/:
    post:
      operationId: rag_documents_process_create
      description: Traite un document en générant des embeddings pour ses chunks.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this Document.
        required: true
      tags:
      - rag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
  /rag/feedback/:
    post:
      operationId: rag_feedback_create
      description: |-
        API pour soumettre un feedback sur une réponse du chatbot.

        Paramètres:
        - message_id: ID du message sur lequel porte le feedback
        - feedback_type: 'positive' ou 'negative'
        - comments: Commentaires optionnels (requis si feedback_type est 'negative')

        Retourne:
        - 201 Created si le feedback a été enregistré avec succès
        - 400 Bad Request si les paramètres sont invalides
        - 404 Not Found si le message n'existe pas
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /rag/messages/:
    get:
      operationId: rag_messages_list
      description: Liste les messages d'une session spécifique si session_id est fourni.
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChatMessage'
          description: ''
  /rag/messages/{id}/:
    get:
      operationId: rag_messages_retrieve
      description: |-
        ViewSet pour la lecture des messages de chat.

        Permet uniquement de lire les messages, pas de les créer directement
        (ils sont créés via l'action send_message du ChatSessionViewSet).
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatMessage'
          description: ''
  /rag/offline/data/:
    get:
      operationId: rag_offline_data_retrieve
      description: |-
        Récupère les données pour le support hors ligne.

        Args:
            request: La requête HTTP.
            format: Le format de la réponse.

        Returns:
            Response: Les données pour le support hors ligne.
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /rag/offline/faqs/:
    get:
      operationId: rag_offline_faqs_retrieve
      description: |-
        Récupère les FAQ pour le support hors ligne.

        Args:
            request: La requête HTTP.
            format: Le format de la réponse.

        Returns:
            Response: Les FAQ pour le support hors ligne.
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /rag/sessions/:
    get:
      operationId: rag_sessions_list
      description: |-
        ViewSet pour la gestion des sessions de chat.

        Permet de créer, lire, mettre à jour et supprimer des sessions de chat,
        ainsi que d'envoyer des messages et de recevoir des réponses.
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChatSession'
          description: ''
    post:
      operationId: rag_sessions_create
      description: Crée une nouvelle session et renvoie tous les champs.
      tags:
      - rag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatSessionCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatSessionCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatSessionCreateRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatSessionCreate'
          description: ''
  /rag/sessions/{id}/:
    get:
      operationId: rag_sessions_retrieve
      description: |-
        ViewSet pour la gestion des sessions de chat.

        Permet de créer, lire, mettre à jour et supprimer des sessions de chat,
        ainsi que d'envoyer des messages et de recevoir des réponses.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatSession'
          description: ''
    put:
      operationId: rag_sessions_update
      description: |-
        ViewSet pour la gestion des sessions de chat.

        Permet de créer, lire, mettre à jour et supprimer des sessions de chat,
        ainsi que d'envoyer des messages et de recevoir des réponses.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - rag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatSessionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatSessionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatSessionRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatSession'
          description: ''
    patch:
      operationId: rag_sessions_partial_update
      description: |-
        ViewSet pour la gestion des sessions de chat.

        Permet de créer, lire, mettre à jour et supprimer des sessions de chat,
        ainsi que d'envoyer des messages et de recevoir des réponses.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - rag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedChatSessionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedChatSessionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedChatSessionRequest'
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatSession'
          description: ''
    delete:
      operationId: rag_sessions_destroy
      description: |-
        ViewSet pour la gestion des sessions de chat.

        Permet de créer, lire, mettre à jour et supprimer des sessions de chat,
        ainsi que d'envoyer des messages et de recevoir des réponses.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - rag
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /rag/sessions/{id}/send_message/:
    post:
      operationId: rag_sessions_send_message_create
      description: Envoie un message à la session de chat et génère une réponse.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - rag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatSessionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatSessionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatSessionRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatSession'
          description: ''
  /register/:
    post:
      operationId: register_create
      tags:
      - Authentication
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /resend-verification-code/:
    post:
      operationId: resend_verification_code_create
      description: |-
        Vue pour permettre aux utilisateurs de demander un nouveau code de vérification
        lorsqu'ils n'ont pas reçu le premier email ou si leur code a expiré.
      tags:
      - Authentication
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResendVerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ResendVerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ResendVerificationCodeRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /reviews/:
    get:
      operationId: reviews_list
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReviewList'
          description: ''
    post:
      operationId: reviews_create
      tags:
      - Reviews
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReviewRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ReviewRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ReviewRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Review'
          description: ''
  /reviews/{id}/:
    get:
      operationId: reviews_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Review'
          description: ''
    patch:
      operationId: reviews_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: reviews_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /reviews/{review_id}/report/:
    post:
      operationId: reviews_report_create
      parameters:
      - in: path
        name: review_id
        schema:
          type: integer
        required: true
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /reviews/{review_id}/respond/:
    post:
      operationId: reviews_respond_create
      parameters:
      - in: path
        name: review_id
        schema:
          type: integer
        required: true
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /reviews/responses/{id}/:
    patch:
      operationId: reviews_responses_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: reviews_responses_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /reviews/statistics/:
    get:
      operationId: reviews_statistics_list
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReviewList'
          description: ''
  /reviews/user/:
    get:
      operationId: reviews_user_list
      tags:
      - Reviews
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReviewList'
          description: ''
  /social-login/:
    post:
      operationId: social_login_create
      tags:
      - Authentication
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SocialLoginRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SocialLoginRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SocialLoginRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /token/refresh/:
    post:
      operationId: token_refresh_create
      description: |-
        Takes a refresh type JSON web token and returns an access type JSON web
        token if the refresh token is valid.
      tags:
      - token
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenRefresh'
          description: ''
  /trips/:
    get:
      operationId: trips_retrieve
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: trips_create
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{id}/:
    get:
      operationId: trips_retrieve_2
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: trips_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: trips_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /trips/{trip_id}/accept/:
    post:
      operationId: trips_accept_create
      description: |-
        Permet au capitaine d'accepter une course.

        URL: POST /api/trips/{trip_id}/accept/

        Body: {
            "estimated_pickup_time": "2024-01-15T16:30:00Z",
            "captain_notes": "Message pour le client"
        }
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/cancel/:
    patch:
      operationId: trips_cancel_partial_update
      description: Annuler une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/carbon-compensation/:
    post:
      operationId: trips_carbon_compensation_create
      description: Créer un paiement de compensation carbone
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/carbon-footprint/:
    get:
      operationId: trips_carbon_footprint_retrieve
      description: Récupérer les données d'empreinte carbone pour une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/complete/:
    patch:
      operationId: trips_complete_partial_update
      description: Terminer une course (IN_PROGRESS → COMPLETED)
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/delay/:
    patch:
      operationId: trips_delay_partial_update
      description: Signaler un retard sur une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/generate-qr/:
    post:
      operationId: trips_generate_qr_create
      description: |-
        Régénère le QR code pour un ticket donné.

        Réponse:
        {
            "success": true,
            "qr_code": "data:image/png;base64,..."
        }
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/history/:
    get:
      operationId: trips_history_retrieve
      description: Historique complet des positions d'une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/location/:
    post:
      operationId: trips_location_create
      description: Mettre à jour la position du capitaine pendant une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/notes/:
    patch:
      operationId: trips_notes_partial_update
      description: Ajouter des notes pendant une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/payment/:
    post:
      operationId: trips_payment_create
      description: Effectuer le paiement d'une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/payment/status/:
    get:
      operationId: trips_payment_status_retrieve
      description: Récupérer le statut de paiement d'une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/problem/:
    patch:
      operationId: trips_problem_partial_update
      description: Signaler un problème sur une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/qr-code/:
    get:
      operationId: trips_qr_code_retrieve
      description: Récupérer le QR code d'une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/reject/:
    post:
      operationId: trips_reject_create
      description: |-
        Permet au capitaine de refuser une course.

        URL: POST /api/trips/{trip_id}/reject/

        Body: {
            "reason": "Raison du refus"
        }
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/start/:
    patch:
      operationId: trips_start_partial_update
      description: Démarrer une course (ACCEPTED → IN_PROGRESS)
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/status/:
    get:
      operationId: trips_status_retrieve
      description: Obtenir le statut détaillé d'une course
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/tip/:
    post:
      operationId: trips_tip_create
      description: Créer un paiement de pourboire
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/{trip_id}/tracking/:
    get:
      operationId: trips_tracking_retrieve
      description: Suivre une course en temps réel
      parameters:
      - in: path
        name: trip_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/captain/availability/:
    get:
      operationId: trips_captain_availability_retrieve
      description: Retourne le statut de disponibilité actuel
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: trips_captain_availability_create
      description: Met à jour la disponibilité du capitaine
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/captain/dashboard/:
    get:
      operationId: trips_captain_dashboard_retrieve
      description: |-
        Retourne les informations du tableau de bord du capitaine.

        URL: GET /api/trips/captain/dashboard/
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/captain/history/:
    get:
      operationId: trips_captain_history_retrieve
      description: |-
        Retourne l'historique des courses du capitaine connecté.

        URL: GET /api/trips/captain/history/
        Query params:
        - status: filtrer par statut (COMPLETED, CANCELLED, etc.)
        - limit: nombre de courses à retourner (défaut: 20)
        - offset: décalage pour pagination
        - date_from: date de début (YYYY-MM-DD)
        - date_to: date de fin (YYYY-MM-DD)
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/cleanup/expired/:
    post:
      operationId: trips_cleanup_expired_create
      description: Marquer les demandes expirées comme EXPIRED
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/pending/:
    get:
      operationId: trips_pending_retrieve
      description: |-
        Retourne les courses en attente d'acceptation pour le capitaine connecté.

        URL: GET /api/trips/pending/
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/quotes/{quote_id}/accept/:
    post:
      operationId: trips_quotes_accept_create
      description: Accepter un devis et créer la course
      parameters:
      - in: path
        name: quote_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: trips_quotes_accept_partial_update
      description: Mettre à jour le statut d'une demande
      parameters:
      - in: path
        name: quote_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/quotes/{quote_id}/choose/:
    post:
      operationId: trips_quotes_choose_create
      description: |-
        Permet au client de choisir un devis et d'envoyer la demande au capitaine.

        URL: POST /api/trips/quotes/{quote_id}/choose/

        Body: {
            "message": "Message optionnel pour le capitaine"
        }
      parameters:
      - in: path
        name: quote_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/requests/{id}/:
    get:
      operationId: trips_requests_retrieve
      description: Récupérer les détails d'une demande avec ses devis
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: trips_requests_partial_update
      description: Mettre à jour le statut d'une demande
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/requests/hourly/:
    post:
      operationId: trips_requests_hourly_create
      description: Créer une demande de mise à disposition et retourner les devis
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/requests/shuttle/:
    get:
      operationId: trips_requests_shuttle_retrieve
      description: Lister les demandes de navettes (pour les établissements)
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: trips_requests_shuttle_create
      description: Créer une demande de navette gratuite
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/requests/shuttle/{request_id}/accept/:
    patch:
      operationId: trips_requests_shuttle_accept_partial_update
      description: Accepter une demande de navette (établissement)
      parameters:
      - in: path
        name: request_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/requests/shuttle/{request_id}/reject/:
    patch:
      operationId: trips_requests_shuttle_reject_partial_update
      description: Refuser une demande de navette (établissement)
      parameters:
      - in: path
        name: request_id
        schema:
          type: integer
        required: true
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/requests/simple/:
    post:
      operationId: trips_requests_simple_create
      description: Créer une demande de course simple et retourner les devis
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/shuttle/list/:
    get:
      operationId: trips_shuttle_list_retrieve
      description: Lister les demandes de navettes pour un établissement
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/shuttle/resources/:
    get:
      operationId: trips_shuttle_resources_retrieve
      description: Lister les bateaux et capitaines disponibles pour les navettes
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/shuttle/schedule/:
    get:
      operationId: trips_shuttle_schedule_retrieve
      description: Gérer le planning des navettes d'un établissement
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/shuttles/:
    get:
      operationId: trips_shuttles_retrieve
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: trips_shuttles_create
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/shuttles/{id}/:
    get:
      operationId: trips_shuttles_retrieve_2
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: trips_shuttles_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: trips_shuttles_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /trips/verify-qr/:
    post:
      operationId: trips_verify_qr_create
      description: |-
        Vérifie un QR code de ticket.

        Body attendu:
        {
            "trip_id": 123  // OU
            "qr_data": "json_string_from_qr"  // OU
            "verification_url": "https://app.com/verify/123"
        }

        Réponse:
        {
            "valid": true/false,
            "message": "Message d'état",
            "ticket_info": { ... },  // Si valide
            "error_code": "CODE"     // Si invalide
        }
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/wallet/balance/:
    get:
      operationId: trips_wallet_balance_retrieve
      description: Récupérer le solde du portefeuille de l'utilisateur
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /trips/wallet/recharge/:
    post:
      operationId: trips_wallet_recharge_create
      description: Recharger le portefeuille de l'utilisateur
      tags:
      - trips
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /users/{id}/:
    get:
      operationId: users_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Accounts
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
  /verify-email/:
    post:
      operationId: verify_email_create
      tags:
      - Authentication
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/VerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/VerificationCodeRequest'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /webhooks/stripe/:
    post:
      operationId: webhooks_stripe_create
      description: |-
        Traite un événement webhook Stripe.

        Args:
            request: Requête HTTP contenant l'événement Stripe

        Returns:
            HttpResponse: Réponse HTTP 200 OK
      tags:
      - webhooks
      security:
      - tokenAuth: []
      - cookieAuth: []
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
components:
  schemas:
    Boat:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          nullable: true
          title: Nom
          maxLength: 100
        registration_number:
          type: string
          nullable: true
          title: Numéro d'immatriculation
          maxLength: 50
        boat_type:
          enum:
          - BLUE
          - CLASSIC
          - LUXE
          - BOAT_XL
          - NAVETTE
          - ''
          - null
          type: string
          description: |-
            * `BLUE` - Blue
            * `CLASSIC` - Classic
            * `LUXE` - Luxe
            * `BOAT_XL` - Boat XL
            * `NAVETTE` - Navette
          nullable: true
          title: Type de bateau
        capacity:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Capacité
        color:
          type: string
          nullable: true
          title: Couleur
          maxLength: 50
        fuel_type:
          enum:
          - DIESEL
          - GASOLINE
          - ELECTRIC
          - HYBRID
          - ''
          - null
          type: string
          description: |-
            * `DIESEL` - Diesel
            * `GASOLINE` - Essence
            * `ELECTRIC` - Électrique
            * `HYBRID` - Hybride
          nullable: true
          title: Type de carburant
        fuel_consumption:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,2})?$
          nullable: true
          title: Consommation de carburant
          description: L/100km
        photos:
          type: object
          additionalProperties: {}
          description: Liste de liens (max 4)
        zone_served:
          type: string
          title: Zone desservie
          description: Zone géographique desservie
          maxLength: 255
        radius:
          type: integer
          maximum: 100
          minimum: 1
          title: Rayon (km)
          description: Rayon de service en km (1-100)
        captain:
          type: object
          additionalProperties: {}
          nullable: true
          readOnly: true
        establishment:
          type: object
          additionalProperties: {}
          nullable: true
          readOnly: true
        is_available:
          type: boolean
          title: Disponible
        last_maintenance:
          type: string
          format: date
          nullable: true
          title: Dernière maintenance
        next_maintenance:
          type: string
          format: date
          nullable: true
          title: Prochaine maintenance
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Mis à jour le
        maintenance_records:
          type: array
          items:
            $ref: '#/components/schemas/MaintenanceRecord'
          readOnly: true
    BoatDetail:
      type: object
      description: Sérialiseur détaillé pour un bateau spécifique
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          nullable: true
          title: Nom
          maxLength: 100
        registration_number:
          type: string
          nullable: true
          title: Numéro d'immatriculation
          maxLength: 50
        boat_type:
          enum:
          - BLUE
          - CLASSIC
          - LUXE
          - BOAT_XL
          - NAVETTE
          - ''
          - null
          type: string
          description: |-
            * `BLUE` - Blue
            * `CLASSIC` - Classic
            * `LUXE` - Luxe
            * `BOAT_XL` - Boat XL
            * `NAVETTE` - Navette
          nullable: true
          title: Type de bateau
        capacity:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Capacité
        color:
          type: string
          nullable: true
          title: Couleur
          maxLength: 50
        fuel_type:
          enum:
          - DIESEL
          - GASOLINE
          - ELECTRIC
          - HYBRID
          - ''
          - null
          type: string
          description: |-
            * `DIESEL` - Diesel
            * `GASOLINE` - Essence
            * `ELECTRIC` - Électrique
            * `HYBRID` - Hybride
          nullable: true
          title: Type de carburant
        fuel_consumption:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,2})?$
          nullable: true
          title: Consommation de carburant
          description: L/100km
        photos:
          type: object
          additionalProperties: {}
          description: Liste de liens (max 4)
        zone_served:
          type: string
          title: Zone desservie
          description: Zone géographique desservie
          maxLength: 255
        radius:
          type: integer
          maximum: 100
          minimum: 1
          title: Rayon (km)
          description: Rayon de service en km (1-100)
        captain:
          type: object
          additionalProperties: {}
          nullable: true
          readOnly: true
        establishment:
          type: object
          additionalProperties: {}
          nullable: true
          readOnly: true
        is_available:
          type: boolean
          title: Disponible
        last_maintenance:
          allOf:
          - $ref: '#/components/schemas/MaintenanceRecord'
          readOnly: true
        next_maintenance:
          allOf:
          - $ref: '#/components/schemas/MaintenanceRecord'
          nullable: true
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Mis à jour le
        maintenance_records:
          type: array
          items:
            $ref: '#/components/schemas/MaintenanceRecord'
          readOnly: true
    BoatList:
      type: object
      description: Sérialiseur simplifié pour la liste des bateaux
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          nullable: true
          title: Nom
          maxLength: 100
        registration_number:
          type: string
          nullable: true
          title: Numéro d'immatriculation
          maxLength: 50
        boat_type:
          enum:
          - BLUE
          - CLASSIC
          - LUXE
          - BOAT_XL
          - NAVETTE
          - ''
          - null
          type: string
          description: |-
            * `BLUE` - Blue
            * `CLASSIC` - Classic
            * `LUXE` - Luxe
            * `BOAT_XL` - Boat XL
            * `NAVETTE` - Navette
          nullable: true
          title: Type de bateau
        capacity:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Capacité
        status:
          type: string
          readOnly: true
        captain_name:
          type: string
          readOnly: true
        establishment_name:
          type: string
          readOnly: true
        location:
          type: string
          readOnly: true
    BoatRequest:
      type: object
      properties:
        name:
          type: string
          nullable: true
          title: Nom
          maxLength: 100
        registration_number:
          type: string
          nullable: true
          title: Numéro d'immatriculation
          maxLength: 50
        boat_type:
          enum:
          - BLUE
          - CLASSIC
          - LUXE
          - BOAT_XL
          - NAVETTE
          - ''
          - null
          type: string
          description: |-
            * `BLUE` - Blue
            * `CLASSIC` - Classic
            * `LUXE` - Luxe
            * `BOAT_XL` - Boat XL
            * `NAVETTE` - Navette
          nullable: true
          title: Type de bateau
        capacity:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Capacité
        color:
          type: string
          nullable: true
          title: Couleur
          maxLength: 50
        fuel_type:
          enum:
          - DIESEL
          - GASOLINE
          - ELECTRIC
          - HYBRID
          - ''
          - null
          type: string
          description: |-
            * `DIESEL` - Diesel
            * `GASOLINE` - Essence
            * `ELECTRIC` - Électrique
            * `HYBRID` - Hybride
          nullable: true
          title: Type de carburant
        fuel_consumption:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,2})?$
          nullable: true
          title: Consommation de carburant
          description: L/100km
        photos:
          type: object
          additionalProperties: {}
          description: Liste de liens (max 4)
        zone_served:
          type: string
          title: Zone desservie
          description: Zone géographique desservie
          maxLength: 255
        radius:
          type: integer
          maximum: 100
          minimum: 1
          title: Rayon (km)
          description: Rayon de service en km (1-100)
        is_available:
          type: boolean
          title: Disponible
        last_maintenance:
          type: string
          format: date
          nullable: true
          title: Dernière maintenance
        next_maintenance:
          type: string
          format: date
          nullable: true
          title: Prochaine maintenance
    Captain:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        experience:
          type: string
          nullable: true
          title: Expérience
        average_rating:
          type: string
          format: decimal
          pattern: ^-?\d{0,1}(?:\.\d{0,2})?$
          nullable: true
          title: Note moyenne
        total_trips:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Nombre total de courses
        wallet_balance:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: Solde du portefeuille
        is_available:
          type: boolean
          nullable: true
          title: Disponible
        current_location:
          type: string
          nullable: true
          title: Position actuelle
          maxLength: 255
        license_number:
          type: string
          nullable: true
          title: Numéro de licence
          maxLength: 50
        license_expiry_date:
          type: string
          format: date
          nullable: true
          title: Date d'expiration de la licence
        years_of_experience:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Années d'expérience
        certifications:
          type: object
          additionalProperties: {}
          nullable: true
        specializations:
          type: object
          additionalProperties: {}
          nullable: true
          title: Spécialisations
        availability_status:
          type: string
          nullable: true
          title: Statut de disponibilité
          maxLength: 20
        boat_photos:
          type: object
          additionalProperties: {}
          nullable: true
          title: Photos du bateau
        rate_per_km:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
          title: Tarif par kilomètre (€)
        rate_per_hour:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
          title: Tarif par heure (€)
        boat:
          type: string
    CaptainRequest:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/UserRequest'
        experience:
          type: string
          nullable: true
          title: Expérience
        average_rating:
          type: string
          format: decimal
          pattern: ^-?\d{0,1}(?:\.\d{0,2})?$
          nullable: true
          title: Note moyenne
        total_trips:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Nombre total de courses
        wallet_balance:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: Solde du portefeuille
        is_available:
          type: boolean
          nullable: true
          title: Disponible
        current_location:
          type: string
          nullable: true
          title: Position actuelle
          maxLength: 255
        license_number:
          type: string
          nullable: true
          title: Numéro de licence
          maxLength: 50
        license_expiry_date:
          type: string
          format: date
          nullable: true
          title: Date d'expiration de la licence
        years_of_experience:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Années d'expérience
        certifications:
          type: object
          additionalProperties: {}
          nullable: true
        specializations:
          type: object
          additionalProperties: {}
          nullable: true
          title: Spécialisations
        availability_status:
          type: string
          nullable: true
          title: Statut de disponibilité
          maxLength: 20
        boat_photos:
          type: object
          additionalProperties: {}
          nullable: true
          title: Photos du bateau
        rate_per_km:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
          title: Tarif par kilomètre (€)
        rate_per_hour:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
          title: Tarif par heure (€)
        boat:
          type: string
    ChatMessage:
      type: object
      description: Sérialiseur pour les messages de chat.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        role:
          enum:
          - user
          - assistant
          - system
          type: string
          description: |-
            * `user` - Utilisateur
            * `assistant` - Assistant
            * `system` - Système
          title: Rôle
        content:
          type: string
          title: Contenu
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Date de création
        retrieved_documents:
          type: string
          readOnly: true
      required:
      - content
      - role
    ChatMessageRequest:
      type: object
      description: Sérialiseur pour les messages de chat.
      properties:
        role:
          enum:
          - user
          - assistant
          - system
          type: string
          description: |-
            * `user` - Utilisateur
            * `assistant` - Assistant
            * `system` - Système
          title: Rôle
        content:
          type: string
          minLength: 1
          title: Contenu
      required:
      - content
      - role
    ChatRoom:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        participants:
          type: array
          items:
            $ref: '#/components/schemas/User'
          readOnly: true
        trip:
          allOf:
          - $ref: '#/components/schemas/Trip'
          readOnly: true
        last_message:
          type: string
          readOnly: true
        unread_count:
          type: string
          readOnly: true
        name:
          type: string
          title: Nom
          maxLength: 100
        type:
          enum:
          - TRIP
          - SUPPORT
          - GROUP
          type: string
          description: |-
            * `TRIP` - Course
            * `SUPPORT` - Support
            * `GROUP` - Groupe
        is_active:
          type: boolean
          title: Actif
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Mis à jour le
      required:
      - name
      - type
    ChatRoomList:
      type: object
      description: Sérialiseur simplifié pour la liste des salons de discussion
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          title: Nom
          maxLength: 100
        type:
          enum:
          - TRIP
          - SUPPORT
          - GROUP
          type: string
          description: |-
            * `TRIP` - Course
            * `SUPPORT` - Support
            * `GROUP` - Groupe
        other_participant:
          type: string
          readOnly: true
        last_message:
          type: string
          readOnly: true
        unread_count:
          type: string
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Mis à jour le
      required:
      - name
      - type
    ChatRoomRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          title: Nom
          maxLength: 100
        type:
          enum:
          - TRIP
          - SUPPORT
          - GROUP
          type: string
          description: |-
            * `TRIP` - Course
            * `SUPPORT` - Support
            * `GROUP` - Groupe
        is_active:
          type: boolean
          title: Actif
      required:
      - name
      - type
    ChatSession:
      type: object
      description: Sérialiseur pour les sessions de chat.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        title:
          type: string
          nullable: true
          title: Titre
          maxLength: 255
        user:
          type: integer
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Date de création
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Date de mise à jour
        message_count:
          type: string
          readOnly: true
        messages:
          type: array
          items:
            $ref: '#/components/schemas/ChatMessage'
          readOnly: true
      required:
      - user
    ChatSessionCreate:
      type: object
      description: Sérialiseur pour la création de sessions de chat.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        title:
          type: string
          nullable: true
          title: Titre
          maxLength: 255
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Date de création
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Date de mise à jour
    ChatSessionCreateRequest:
      type: object
      description: Sérialiseur pour la création de sessions de chat.
      properties:
        title:
          type: string
          nullable: true
          title: Titre
          maxLength: 255
    ChatSessionRequest:
      type: object
      description: Sérialiseur pour les sessions de chat.
      properties:
        title:
          type: string
          nullable: true
          title: Titre
          maxLength: 255
        user:
          type: integer
      required:
      - user
    ChatbotMessage:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        role:
          enum:
          - USER
          - ASSISTANT
          - SYSTEM
          type: string
          description: |-
            * `USER` - Utilisateur
            * `ASSISTANT` - Assistant
            * `SYSTEM` - Système
          title: Rôle
        content:
          type: string
          title: Contenu
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        session:
          type: integer
      required:
      - content
      - role
      - session
    ChatbotMessageRequest:
      type: object
      properties:
        role:
          enum:
          - USER
          - ASSISTANT
          - SYSTEM
          type: string
          description: |-
            * `USER` - Utilisateur
            * `ASSISTANT` - Assistant
            * `SYSTEM` - Système
          title: Rôle
        content:
          type: string
          minLength: 1
          title: Contenu
        session:
          type: integer
      required:
      - content
      - role
      - session
    ChatbotSession:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        messages:
          type: array
          items:
            $ref: '#/components/schemas/ChatbotMessage'
          readOnly: true
        user:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        session_id:
          type: string
          format: uuid
          readOnly: true
          title: ID de session
        context:
          type: object
          additionalProperties: {}
          title: Contexte
        last_interaction:
          type: string
          format: date-time
          readOnly: true
          title: Dernière interaction
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
    ChatbotSessionRequest:
      type: object
      properties:
        context:
          type: object
          additionalProperties: {}
          title: Contexte
    Client:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        wallet_balance:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: Solde du portefeuille
        date_of_birth:
          type: string
          format: date
          nullable: true
          title: Date de naissance
        nationality:
          type: string
          nullable: true
          title: Nationalité
          maxLength: 50
        preferred_language:
          type: string
          nullable: true
          title: Langue préférée
          maxLength: 10
        emergency_contact_name:
          type: string
          nullable: true
          title: Nom du contact d'urgence
          maxLength: 100
        emergency_contact_phone:
          type: string
          nullable: true
          title: Téléphone du contact d'urgence
          maxLength: 15
    ClientRequest:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/UserRequest'
        wallet_balance:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: Solde du portefeuille
        date_of_birth:
          type: string
          format: date
          nullable: true
          title: Date de naissance
        nationality:
          type: string
          nullable: true
          title: Nationalité
          maxLength: 50
        preferred_language:
          type: string
          nullable: true
          title: Langue préférée
          maxLength: 10
        emergency_contact_name:
          type: string
          nullable: true
          title: Nom du contact d'urgence
          maxLength: 100
        emergency_contact_phone:
          type: string
          nullable: true
          title: Téléphone du contact d'urgence
          maxLength: 15
    Device:
      type: object
      description: Sérialiseur pour le modèle Device.
      properties:
        id:
          type: integer
          readOnly: true
        device_id:
          type: string
          title: Identifiant de l'appareil
          maxLength: 255
        device_type:
          enum:
          - ANDROID
          - IOS
          - WEB
          type: string
          description: |-
            * `ANDROID` - Android
            * `IOS` - iOS
            * `WEB` - Web
          title: Type d'appareil
        name:
          type: string
          title: Nom de l'appareil
          maxLength: 255
        push_token:
          type: string
          title: Token push
        is_active:
          type: boolean
          title: Actif
        last_used:
          type: string
          format: date-time
          readOnly: true
          title: Dernière utilisation
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
      required:
      - device_id
      - device_type
    DeviceRequest:
      type: object
      description: Sérialiseur pour le modèle Device.
      properties:
        device_id:
          type: string
          minLength: 1
          title: Identifiant de l'appareil
          maxLength: 255
        device_type:
          enum:
          - ANDROID
          - IOS
          - WEB
          type: string
          description: |-
            * `ANDROID` - Android
            * `IOS` - iOS
            * `WEB` - Web
          title: Type d'appareil
        name:
          type: string
          title: Nom de l'appareil
          maxLength: 255
        push_token:
          type: string
          title: Token push
        is_active:
          type: boolean
          title: Actif
      required:
      - device_id
      - device_type
    Document:
      type: object
      description: Sérialiseur pour les documents.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        title:
          type: string
          title: Titre
          maxLength: 255
        content:
          type: string
          title: Contenu
        source:
          type: string
          nullable: true
          maxLength: 255
        url:
          type: string
          format: uri
          nullable: true
          maxLength: 200
        category:
          type: string
          nullable: true
          title: Catégorie
          maxLength: 100
        tags:
          type: object
          additionalProperties: {}
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Date de création
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Date de mise à jour
        embedding_generated:
          type: boolean
          readOnly: true
          title: Embedding généré
        chunks:
          type: array
          items:
            $ref: '#/components/schemas/DocumentChunk'
          readOnly: true
      required:
      - content
      - title
    DocumentChunk:
      type: object
      description: Sérialiseur pour les chunks de documents.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        document:
          type: string
          format: uuid
          readOnly: true
        content:
          type: string
          title: Contenu du chunk
        chunk_index:
          type: integer
          readOnly: true
          title: Index du chunk
        embedding_generated:
          type: boolean
          readOnly: true
          title: Embedding généré
      required:
      - content
    DocumentChunkRequest:
      type: object
      description: Sérialiseur pour les chunks de documents.
      properties:
        content:
          type: string
          minLength: 1
          title: Contenu du chunk
      required:
      - content
    DocumentCreate:
      type: object
      description: Sérialiseur pour la création de documents.
      properties:
        title:
          type: string
          title: Titre
          maxLength: 255
        content:
          type: string
          title: Contenu
        source:
          type: string
          nullable: true
          maxLength: 255
        url:
          type: string
          format: uri
          nullable: true
          maxLength: 200
        category:
          type: string
          nullable: true
          title: Catégorie
          maxLength: 100
        tags:
          type: object
          additionalProperties: {}
      required:
      - content
      - title
    DocumentCreateRequest:
      type: object
      description: Sérialiseur pour la création de documents.
      properties:
        title:
          type: string
          minLength: 1
          title: Titre
          maxLength: 255
        content:
          type: string
          minLength: 1
          title: Contenu
        source:
          type: string
          nullable: true
          maxLength: 255
        url:
          type: string
          format: uri
          nullable: true
          maxLength: 200
        category:
          type: string
          nullable: true
          title: Catégorie
          maxLength: 100
        tags:
          type: object
          additionalProperties: {}
      required:
      - content
      - title
    DocumentRequest:
      type: object
      description: Sérialiseur pour les documents.
      properties:
        title:
          type: string
          minLength: 1
          title: Titre
          maxLength: 255
        content:
          type: string
          minLength: 1
          title: Contenu
        source:
          type: string
          nullable: true
          maxLength: 255
        url:
          type: string
          format: uri
          nullable: true
          maxLength: 200
        category:
          type: string
          nullable: true
          title: Catégorie
          maxLength: 100
        tags:
          type: object
          additionalProperties: {}
      required:
      - content
      - title
    ErrorResponse:
      type: object
      description: Réponse d'erreur standard
      properties:
        error:
          type: string
          description: Message d'erreur
        code:
          type: string
          description: Code d'erreur
        details:
          type: object
          additionalProperties: {}
          description: Détails de l'erreur
      required:
      - error
    Establishment:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        name:
          type: string
          nullable: true
          title: Nom
          maxLength: 100
        type:
          enum:
          - RESTAURANT
          - HOTEL
          - PRIVATE_BEACH
          - ''
          - null
          type: string
          description: |-
            * `RESTAURANT` - Restaurant
            * `HOTEL` - Hôtel
            * `PRIVATE_BEACH` - Plage privée
          nullable: true
          title: Type d'établissement
        address:
          type: string
          nullable: true
          title: Adresse
          maxLength: 255
        description:
          type: string
          nullable: true
        main_photo:
          type: string
          format: uri
          nullable: true
          title: Photo principale
        secondary_photos:
          type: object
          additionalProperties: {}
          nullable: true
          title: Photos secondaires
        wallet_balance:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: Solde du portefeuille
        business_name:
          type: string
          nullable: true
          title: Nom commercial
          maxLength: 100
        business_type:
          type: string
          nullable: true
          title: Type d'activité
          maxLength: 50
        registration_number:
          type: string
          nullable: true
          title: Numéro d'enregistrement
          maxLength: 50
        tax_id:
          type: string
          nullable: true
          title: Numéro de TVA
          maxLength: 50
        opening_hours:
          type: object
          additionalProperties: {}
          nullable: true
          title: Horaires d'ouverture
        services_offered:
          type: object
          additionalProperties: {}
          nullable: true
          title: Services proposés
        average_rating:
          type: string
          format: decimal
          pattern: ^-?\d{0,1}(?:\.\d{0,2})?$
          nullable: true
          title: Note moyenne
        location_coordinates:
          type: string
          nullable: true
          title: Coordonnées GPS
          maxLength: 50
        website:
          type: string
          format: uri
          nullable: true
          title: Site web
          maxLength: 200
        social_media:
          type: object
          additionalProperties: {}
          nullable: true
          title: Réseaux sociaux
    EstablishmentRequest:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/UserRequest'
        name:
          type: string
          nullable: true
          title: Nom
          maxLength: 100
        type:
          enum:
          - RESTAURANT
          - HOTEL
          - PRIVATE_BEACH
          - ''
          - null
          type: string
          description: |-
            * `RESTAURANT` - Restaurant
            * `HOTEL` - Hôtel
            * `PRIVATE_BEACH` - Plage privée
          nullable: true
          title: Type d'établissement
        address:
          type: string
          nullable: true
          title: Adresse
          maxLength: 255
        description:
          type: string
          nullable: true
        main_photo:
          type: string
          format: binary
          nullable: true
          title: Photo principale
        secondary_photos:
          type: object
          additionalProperties: {}
          nullable: true
          title: Photos secondaires
        wallet_balance:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: Solde du portefeuille
        business_name:
          type: string
          nullable: true
          title: Nom commercial
          maxLength: 100
        business_type:
          type: string
          nullable: true
          title: Type d'activité
          maxLength: 50
        registration_number:
          type: string
          nullable: true
          title: Numéro d'enregistrement
          maxLength: 50
        tax_id:
          type: string
          nullable: true
          title: Numéro de TVA
          maxLength: 50
        opening_hours:
          type: object
          additionalProperties: {}
          nullable: true
          title: Horaires d'ouverture
        services_offered:
          type: object
          additionalProperties: {}
          nullable: true
          title: Services proposés
        average_rating:
          type: string
          format: decimal
          pattern: ^-?\d{0,1}(?:\.\d{0,2})?$
          nullable: true
          title: Note moyenne
        location_coordinates:
          type: string
          nullable: true
          title: Coordonnées GPS
          maxLength: 50
        website:
          type: string
          format: uri
          nullable: true
          title: Site web
          maxLength: 200
        social_media:
          type: object
          additionalProperties: {}
          nullable: true
          title: Réseaux sociaux
    LoginRequest:
      type: object
      properties:
        username:
          type: string
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 1
      required:
      - password
    MaintenanceRecord:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        maintenance_type:
          enum:
          - ROUTINE
          - REPAIR
          - INSPECTION
          - EMERGENCY
          type: string
          description: |-
            * `ROUTINE` - Maintenance de routine
            * `REPAIR` - Réparation
            * `INSPECTION` - Inspection
            * `EMERGENCY` - Urgence
          title: Type de maintenance
        description:
          type: string
        cost:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          title: Coût
        performed_at:
          type: string
          format: date-time
          title: Effectué le
        performed_by:
          type: string
          title: Effectué par
          maxLength: 100
        notes:
          type: string
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        boat:
          type: integer
          readOnly: true
      required:
      - cost
      - description
      - maintenance_type
      - performed_at
      - performed_by
    MaintenanceRecordRequest:
      type: object
      properties:
        maintenance_type:
          enum:
          - ROUTINE
          - REPAIR
          - INSPECTION
          - EMERGENCY
          type: string
          description: |-
            * `ROUTINE` - Maintenance de routine
            * `REPAIR` - Réparation
            * `INSPECTION` - Inspection
            * `EMERGENCY` - Urgence
          title: Type de maintenance
        description:
          type: string
          minLength: 1
        cost:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          title: Coût
        performed_at:
          type: string
          format: date-time
          title: Effectué le
        performed_by:
          type: string
          minLength: 1
          title: Effectué par
          maxLength: 100
        notes:
          type: string
      required:
      - cost
      - description
      - maintenance_type
      - performed_at
      - performed_by
    Message:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        sender:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        read_by:
          type: array
          items:
            $ref: '#/components/schemas/User'
          readOnly: true
        delivered_to:
          type: array
          items:
            $ref: '#/components/schemas/User'
          readOnly: true
        type:
          enum:
          - TEXT
          - IMAGE
          - LOCATION
          - SYSTEM
          type: string
          description: |-
            * `TEXT` - Texte
            * `IMAGE` - Image
            * `LOCATION` - Localisation
            * `SYSTEM` - Système
        content:
          type: string
          title: Contenu
        attachment:
          type: string
          format: uri
          nullable: true
          title: Pièce jointe
        metadata:
          type: object
          additionalProperties: {}
          title: Métadonnées
        is_read:
          type: boolean
          title: Lu
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Mis à jour le
        deleted_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: Supprimé le
        room:
          type: integer
      required:
      - content
      - room
    MessageRequest:
      type: object
      properties:
        type:
          enum:
          - TEXT
          - IMAGE
          - LOCATION
          - SYSTEM
          type: string
          description: |-
            * `TEXT` - Texte
            * `IMAGE` - Image
            * `LOCATION` - Localisation
            * `SYSTEM` - Système
        content:
          type: string
          minLength: 1
          title: Contenu
        attachment:
          type: string
          format: binary
          nullable: true
          title: Pièce jointe
        metadata:
          type: object
          additionalProperties: {}
          title: Métadonnées
        is_read:
          type: boolean
          title: Lu
        room:
          type: integer
      required:
      - content
      - room
    Notification:
      type: object
      description: |-
        Sérialiseur pour le modèle Notification.

        Ce sérialiseur gère la conversion des objets Notification en JSON et vice-versa.
      properties:
        id:
          type: integer
          readOnly: true
        user:
          type: integer
          readOnly: true
        type:
          enum:
          - TRIP_REQUEST
          - TRIP_ACCEPTED
          - TRIP_REJECTED
          - TRIP_STARTED
          - TRIP_COMPLETED
          - TRIP_CANCELLED
          - PAYMENT_RECEIVED
          - PAYMENT_FAILED
          - REFUND_PROCESSED
          - NEW_MESSAGE
          - SYSTEM
          type: string
          description: |-
            * `TRIP_REQUEST` - Demande de course
            * `TRIP_ACCEPTED` - Course acceptée
            * `TRIP_REJECTED` - Course refusée
            * `TRIP_STARTED` - Course démarrée
            * `TRIP_COMPLETED` - Course terminée
            * `TRIP_CANCELLED` - Course annulée
            * `PAYMENT_RECEIVED` - Paiement reçu
            * `PAYMENT_FAILED` - Paiement échoué
            * `REFUND_PROCESSED` - Remboursement traité
            * `NEW_MESSAGE` - Nouveau message
            * `SYSTEM` - Notification système
          readOnly: true
        type_display:
          type: string
          readOnly: true
        title:
          type: string
          readOnly: true
          title: Titre
        message:
          type: string
          readOnly: true
        is_read:
          type: boolean
          title: Lu
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        content_object:
          type: string
          readOnly: true
        delivery_status:
          type: string
          readOnly: true
          title: Statut d'envoi
        error_message:
          type: string
          readOnly: true
          title: Message d'erreur
        sent_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: Envoyé le
    NotificationRequest:
      type: object
      description: |-
        Sérialiseur pour le modèle Notification.

        Ce sérialiseur gère la conversion des objets Notification en JSON et vice-versa.
      properties:
        is_read:
          type: boolean
          title: Lu
    PasswordChangeRequest:
      type: object
      properties:
        new_password1:
          type: string
          minLength: 1
          maxLength: 128
        new_password2:
          type: string
          minLength: 1
          maxLength: 128
      required:
      - new_password1
      - new_password2
    PasswordResetConfirmRequest:
      type: object
      description: Serializer for confirming a password reset attempt.
      properties:
        new_password1:
          type: string
          minLength: 1
          maxLength: 128
        new_password2:
          type: string
          minLength: 1
          maxLength: 128
        uid:
          type: string
          minLength: 1
        token:
          type: string
          minLength: 1
      required:
      - new_password1
      - new_password2
      - token
      - uid
    PasswordResetRequest:
      type: object
      description: Serializer for requesting a password reset e-mail.
      properties:
        email:
          type: string
          format: email
          minLength: 1
      required:
      - email
    PasswordResetRequestRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          minLength: 1
        phone_number:
          type: string
          minLength: 1
    PasswordResetVerifyRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          minLength: 1
        phone_number:
          type: string
          minLength: 1
        code:
          type: string
          minLength: 6
          maxLength: 6
        new_password:
          type: string
          minLength: 8
      required:
      - code
      - new_password
    PatchedBoatDetailRequest:
      type: object
      description: Sérialiseur détaillé pour un bateau spécifique
      properties:
        name:
          type: string
          nullable: true
          title: Nom
          maxLength: 100
        registration_number:
          type: string
          nullable: true
          title: Numéro d'immatriculation
          maxLength: 50
        boat_type:
          enum:
          - BLUE
          - CLASSIC
          - LUXE
          - BOAT_XL
          - NAVETTE
          - ''
          - null
          type: string
          description: |-
            * `BLUE` - Blue
            * `CLASSIC` - Classic
            * `LUXE` - Luxe
            * `BOAT_XL` - Boat XL
            * `NAVETTE` - Navette
          nullable: true
          title: Type de bateau
        capacity:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Capacité
        color:
          type: string
          nullable: true
          title: Couleur
          maxLength: 50
        fuel_type:
          enum:
          - DIESEL
          - GASOLINE
          - ELECTRIC
          - HYBRID
          - ''
          - null
          type: string
          description: |-
            * `DIESEL` - Diesel
            * `GASOLINE` - Essence
            * `ELECTRIC` - Électrique
            * `HYBRID` - Hybride
          nullable: true
          title: Type de carburant
        fuel_consumption:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,2})?$
          nullable: true
          title: Consommation de carburant
          description: L/100km
        photos:
          type: object
          additionalProperties: {}
          description: Liste de liens (max 4)
        zone_served:
          type: string
          title: Zone desservie
          description: Zone géographique desservie
          maxLength: 255
        radius:
          type: integer
          maximum: 100
          minimum: 1
          title: Rayon (km)
          description: Rayon de service en km (1-100)
        is_available:
          type: boolean
          title: Disponible
    PatchedChatRoomRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          title: Nom
          maxLength: 100
        type:
          enum:
          - TRIP
          - SUPPORT
          - GROUP
          type: string
          description: |-
            * `TRIP` - Course
            * `SUPPORT` - Support
            * `GROUP` - Groupe
        is_active:
          type: boolean
          title: Actif
    PatchedChatSessionRequest:
      type: object
      description: Sérialiseur pour les sessions de chat.
      properties:
        title:
          type: string
          nullable: true
          title: Titre
          maxLength: 255
        user:
          type: integer
    PatchedChatbotMessageRequest:
      type: object
      properties:
        role:
          enum:
          - USER
          - ASSISTANT
          - SYSTEM
          type: string
          description: |-
            * `USER` - Utilisateur
            * `ASSISTANT` - Assistant
            * `SYSTEM` - Système
          title: Rôle
        content:
          type: string
          minLength: 1
          title: Contenu
        session:
          type: integer
    PatchedChatbotSessionRequest:
      type: object
      properties:
        context:
          type: object
          additionalProperties: {}
          title: Contexte
    PatchedDeviceRequest:
      type: object
      description: Sérialiseur pour le modèle Device.
      properties:
        device_id:
          type: string
          minLength: 1
          title: Identifiant de l'appareil
          maxLength: 255
        device_type:
          enum:
          - ANDROID
          - IOS
          - WEB
          type: string
          description: |-
            * `ANDROID` - Android
            * `IOS` - iOS
            * `WEB` - Web
          title: Type d'appareil
        name:
          type: string
          title: Nom de l'appareil
          maxLength: 255
        push_token:
          type: string
          title: Token push
        is_active:
          type: boolean
          title: Actif
    PatchedDocumentRequest:
      type: object
      description: Sérialiseur pour les documents.
      properties:
        title:
          type: string
          minLength: 1
          title: Titre
          maxLength: 255
        content:
          type: string
          minLength: 1
          title: Contenu
        source:
          type: string
          nullable: true
          maxLength: 255
        url:
          type: string
          format: uri
          nullable: true
          maxLength: 200
        category:
          type: string
          nullable: true
          title: Catégorie
          maxLength: 100
        tags:
          type: object
          additionalProperties: {}
    PatchedMessageRequest:
      type: object
      properties:
        type:
          enum:
          - TEXT
          - IMAGE
          - LOCATION
          - SYSTEM
          type: string
          description: |-
            * `TEXT` - Texte
            * `IMAGE` - Image
            * `LOCATION` - Localisation
            * `SYSTEM` - Système
        content:
          type: string
          minLength: 1
          title: Contenu
        attachment:
          type: string
          format: binary
          nullable: true
          title: Pièce jointe
        metadata:
          type: object
          additionalProperties: {}
          title: Métadonnées
        is_read:
          type: boolean
          title: Lu
        room:
          type: integer
    PatchedNotificationRequest:
      type: object
      description: |-
        Sérialiseur pour le modèle Notification.

        Ce sérialiseur gère la conversion des objets Notification en JSON et vice-versa.
      properties:
        is_read:
          type: boolean
          title: Lu
    PatchedUserDetailsRequest:
      type: object
      description: User model w/o password
      properties:
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
    PatchedUserProfileRequest:
      type: object
      description: Sérialiseur pour afficher et modifier le profil utilisateur et
        ses sous-profils dynamiquement.
      properties:
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
        phone_number:
          type: string
          title: Numéro de téléphone
          maxLength: 15
        client_profile:
          $ref: '#/components/schemas/ClientRequest'
        captain_profile:
          $ref: '#/components/schemas/CaptainRequest'
        establishment_profile:
          $ref: '#/components/schemas/EstablishmentRequest'
    RegisterRequest:
      type: object
      properties:
        username:
          type: string
          minLength: 1
          maxLength: 0
        email:
          type: string
          format: email
          minLength: 1
        password1:
          type: string
          writeOnly: true
          minLength: 1
        password2:
          type: string
          writeOnly: true
          minLength: 1
      required:
      - email
      - password1
      - password2
    ResendEmailVerificationRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          minLength: 1
      required:
      - email
    ResendVerificationCodeRequest:
      type: object
      description: Sérialiseur pour la demande de renvoi d'un code de vérification.
      properties:
        email:
          type: string
          format: email
          minLength: 1
      required:
      - email
    RestAuthDetail:
      type: object
      properties:
        detail:
          type: string
          readOnly: true
    Review:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        author:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        trip:
          allOf:
          - $ref: '#/components/schemas/Trip'
          readOnly: true
        responses:
          type: array
          items:
            $ref: '#/components/schemas/ReviewResponse'
          readOnly: true
        average_rating:
          type: string
          readOnly: true
        reviewed_object_info:
          type: string
          readOnly: true
        type:
          enum:
          - TRIP
          - CAPTAIN
          - CLIENT
          - BOAT
          - ESTABLISHMENT
          type: string
          description: |-
            * `TRIP` - Course
            * `CAPTAIN` - Capitaine
            * `CLIENT` - Client
            * `BOAT` - Bateau
            * `ESTABLISHMENT` - Établissement
        rating:
          type: integer
          maximum: 5
          minimum: 1
          title: Note
        title:
          type: string
          title: Titre
          maxLength: 255
        comment:
          type: string
          title: Commentaire
        pros:
          type: string
          title: Points positifs
        cons:
          type: string
          title: Points négatifs
        cleanliness_rating:
          type: integer
          maximum: 5
          minimum: 1
          nullable: true
          title: Note de propreté
        communication_rating:
          type: integer
          maximum: 5
          minimum: 1
          nullable: true
          title: Note de communication
        punctuality_rating:
          type: integer
          maximum: 5
          minimum: 1
          nullable: true
          title: Note de ponctualité
        value_rating:
          type: integer
          maximum: 5
          minimum: 1
          nullable: true
          title: Note qualité-prix
        is_verified:
          type: boolean
          readOnly: true
          title: Vérifié
        is_public:
          type: boolean
          title: Public
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Mis à jour le
        reported_count:
          type: integer
          readOnly: true
          title: Nombre de signalements
      required:
      - comment
      - rating
      - title
      - type
    ReviewList:
      type: object
      description: Sérialiseur simplifié pour la liste des avis
      properties:
        id:
          type: integer
          readOnly: true
        author_name:
          type: string
          readOnly: true
        rating:
          type: integer
          maximum: 5
          minimum: 1
          title: Note
        title:
          type: string
          title: Titre
          maxLength: 255
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        is_verified:
          type: boolean
          title: Vérifié
        response_count:
          type: string
          readOnly: true
      required:
      - rating
      - title
    ReviewRequest:
      type: object
      properties:
        content_type:
          type: integer
          writeOnly: true
        object_id:
          type: integer
          writeOnly: true
        type:
          enum:
          - TRIP
          - CAPTAIN
          - CLIENT
          - BOAT
          - ESTABLISHMENT
          type: string
          description: |-
            * `TRIP` - Course
            * `CAPTAIN` - Capitaine
            * `CLIENT` - Client
            * `BOAT` - Bateau
            * `ESTABLISHMENT` - Établissement
        rating:
          type: integer
          maximum: 5
          minimum: 1
          title: Note
        title:
          type: string
          minLength: 1
          title: Titre
          maxLength: 255
        comment:
          type: string
          minLength: 1
          title: Commentaire
        pros:
          type: string
          title: Points positifs
        cons:
          type: string
          title: Points négatifs
        cleanliness_rating:
          type: integer
          maximum: 5
          minimum: 1
          nullable: true
          title: Note de propreté
        communication_rating:
          type: integer
          maximum: 5
          minimum: 1
          nullable: true
          title: Note de communication
        punctuality_rating:
          type: integer
          maximum: 5
          minimum: 1
          nullable: true
          title: Note de ponctualité
        value_rating:
          type: integer
          maximum: 5
          minimum: 1
          nullable: true
          title: Note qualité-prix
        is_public:
          type: boolean
          title: Public
      required:
      - comment
      - content_type
      - object_id
      - rating
      - title
      - type
    ReviewResponse:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        author:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        content:
          type: string
          title: Contenu
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Mis à jour le
        review:
          type: integer
      required:
      - content
      - review
    ReviewResponseRequest:
      type: object
      properties:
        content:
          type: string
          minLength: 1
          title: Contenu
        review:
          type: integer
      required:
      - content
      - review
    SocialLoginRequest:
      type: object
      properties:
        provider:
          enum:
          - FACEBOOK
          - GOOGLE
          - APPLE
          type: string
          description: |-
            * `FACEBOOK` - Facebook
            * `GOOGLE` - Google
            * `APPLE` - Apple
        access_token:
          type: string
          minLength: 1
        user_type:
          enum:
          - CLIENT
          - CAPTAIN
          - ESTABLISHMENT
          type: string
          description: |-
            * `CLIENT` - Client
            * `CAPTAIN` - Capitaine
            * `ESTABLISHMENT` - Établissement
        username:
          type: string
          minLength: 1
        experience:
          type: string
          minLength: 1
        boat:
          type: object
          additionalProperties: {}
        name:
          type: string
          minLength: 1
        type:
          enum:
          - RESTAURANT
          - HOTEL
          - PRIVATE_BEACH
          type: string
          description: |-
            * `RESTAURANT` - Restaurant
            * `HOTEL` - Hôtel
            * `PRIVATE_BEACH` - Plage privée
        address:
          type: string
          minLength: 1
        description:
          type: string
          minLength: 1
        main_photo:
          type: string
          format: binary
        secondary_photos:
          type: array
          items:
            type: string
            format: binary
      required:
      - access_token
      - provider
    Token:
      type: object
      description: Serializer for Token model.
      properties:
        key:
          type: string
          title: Clef
          maxLength: 40
      required:
      - key
    TokenRefresh:
      type: object
      properties:
        access:
          type: string
          readOnly: true
        refresh:
          type: string
      required:
      - refresh
    TokenRefreshRequest:
      type: object
      properties:
        refresh:
          type: string
          minLength: 1
      required:
      - refresh
    Trip:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        client:
          allOf:
          - $ref: '#/components/schemas/Client'
          readOnly: true
        captain:
          allOf:
          - $ref: '#/components/schemas/Captain'
          readOnly: true
        boat:
          allOf:
          - $ref: '#/components/schemas/Boat'
          readOnly: true
        establishment:
          allOf:
          - $ref: '#/components/schemas/Establishment'
          readOnly: true
        captain_profile_picture:
          type: string
          readOnly: true
        captain_full_name:
          type: string
          readOnly: true
        boat_photos:
          type: object
          additionalProperties: {}
          readOnly: true
        qr_code:
          type: string
          readOnly: true
        trip_type:
          enum:
          - COURSE_SIMPLE
          - MISE_A_DISPOSITION
          - NAVETTES_GRATUITES
          type: string
          description: |-
            * `COURSE_SIMPLE` - Course simple
            * `MISE_A_DISPOSITION` - Mise à disposition
            * `NAVETTES_GRATUITES` - Navette gratuite
          title: Type de course
        start_location:
          type: string
          title: Lieu de départ
          maxLength: 255
        end_location:
          type: string
          title: Lieu d'arrivée
          maxLength: 255
        scheduled_start_time:
          type: string
          format: date-time
          title: Heure de départ prévue
        scheduled_end_time:
          type: string
          format: date-time
          title: Heure d'arrivée prévue
        actual_start_time:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: Heure de départ réelle
        actual_end_time:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: Heure d'arrivée réelle
        estimated_duration:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Durée estimée (minutes)
        actual_duration:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Durée réelle (minutes)
        distance_km:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,2})?$
          nullable: true
          title: Distance (km)
        passenger_count:
          type: integer
          maximum: **********
          minimum: 1
          title: Nombre de passagers
        passenger_names:
          type: object
          additionalProperties: {}
          title: Noms des passagers
        special_requests:
          type: string
          title: Demandes spéciales
        status:
          enum:
          - PENDING
          - ACCEPTED
          - REJECTED
          - IN_PROGRESS
          - COMPLETED
          - CANCELLED
          - CANCELLED_BY_CLIENT
          - CANCELLED_BY_CAPTAIN
          - DELAYED
          - PROBLEM
          type: string
          description: |-
            * `PENDING` - En attente
            * `ACCEPTED` - Acceptée
            * `REJECTED` - Refusée
            * `IN_PROGRESS` - En cours
            * `COMPLETED` - Terminée
            * `CANCELLED` - Annulée
            * `CANCELLED_BY_CLIENT` - Annulée par le client
            * `CANCELLED_BY_CAPTAIN` - Annulée par le capitaine
            * `DELAYED` - Retardée
            * `PROBLEM` - Problème technique
          readOnly: true
          title: Statut
        current_location:
          type: string
          readOnly: true
          title: Position actuelle
        tracking_data:
          type: object
          additionalProperties: {}
          readOnly: true
          title: Données de suivi
        base_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          title: Prix de base
        additional_charges:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          title: Frais supplémentaires
        tip:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          title: Pourboire
        total_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          readOnly: true
          title: Prix total
        payment_status:
          type: string
          readOnly: true
          title: Statut du paiement
        payment_method:
          type: string
          title: Méthode de paiement
          maxLength: 50
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: Créé le
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: Mis à jour le
        cancellation_reason:
          type: string
          title: Raison d'annulation
        notes:
          type: string
        delay_minutes:
          type: integer
          maximum: **********
          minimum: -**********
          title: Retard en minutes
        problem_description:
          type: string
          title: Description du problème
        captain_notes:
          type: string
          title: Notes du capitaine
        client_notes:
          type: string
          title: Notes du client
        estimated_arrival_time:
          type: string
          format: date-time
          nullable: true
          title: Heure d'arrivée estimée
        cancelled_by:
          type: integer
          nullable: true
      required:
      - base_price
      - end_location
      - passenger_count
      - scheduled_end_time
      - scheduled_start_time
      - start_location
    TripRequest:
      type: object
      properties:
        trip_type:
          enum:
          - COURSE_SIMPLE
          - MISE_A_DISPOSITION
          - NAVETTES_GRATUITES
          type: string
          description: |-
            * `COURSE_SIMPLE` - Course simple
            * `MISE_A_DISPOSITION` - Mise à disposition
            * `NAVETTES_GRATUITES` - Navette gratuite
          title: Type de course
        start_location:
          type: string
          minLength: 1
          title: Lieu de départ
          maxLength: 255
        end_location:
          type: string
          minLength: 1
          title: Lieu d'arrivée
          maxLength: 255
        scheduled_start_time:
          type: string
          format: date-time
          title: Heure de départ prévue
        scheduled_end_time:
          type: string
          format: date-time
          title: Heure d'arrivée prévue
        estimated_duration:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Durée estimée (minutes)
        actual_duration:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
          title: Durée réelle (minutes)
        distance_km:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,2})?$
          nullable: true
          title: Distance (km)
        passenger_count:
          type: integer
          maximum: **********
          minimum: 1
          title: Nombre de passagers
        passenger_names:
          type: object
          additionalProperties: {}
          title: Noms des passagers
        special_requests:
          type: string
          title: Demandes spéciales
        base_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          title: Prix de base
        additional_charges:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          title: Frais supplémentaires
        tip:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          title: Pourboire
        payment_method:
          type: string
          title: Méthode de paiement
          maxLength: 50
        cancellation_reason:
          type: string
          title: Raison d'annulation
        notes:
          type: string
        delay_minutes:
          type: integer
          maximum: **********
          minimum: -**********
          title: Retard en minutes
        problem_description:
          type: string
          title: Description du problème
        captain_notes:
          type: string
          title: Notes du capitaine
        client_notes:
          type: string
          title: Notes du client
        estimated_arrival_time:
          type: string
          format: date-time
          nullable: true
          title: Heure d'arrivée estimée
        cancelled_by:
          type: integer
          nullable: true
      required:
      - base_price
      - end_location
      - passenger_count
      - scheduled_end_time
      - scheduled_start_time
      - start_location
    User:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          title: Adresse email
          maxLength: 254
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
        phone_number:
          type: string
          title: Numéro de téléphone
          maxLength: 15
        type:
          enum:
          - CLIENT
          - CAPTAIN
          - ESTABLISHMENT
          type: string
          description: |-
            * `CLIENT` - Client
            * `CAPTAIN` - Capitaine
            * `ESTABLISHMENT` - Établissement
          title: Type d'utilisateur
        profile_picture:
          type: string
          readOnly: true
        is_active:
          type: boolean
          title: Actif
          description: Précise si l’utilisateur doit être considéré comme actif. Décochez
            ceci plutôt que de supprimer le compte.
      required:
      - email
      - first_name
      - last_name
      - type
    UserDetails:
      type: object
      description: User model w/o password
      properties:
        pk:
          type: integer
          readOnly: true
          title: ID
        email:
          type: string
          format: email
          readOnly: true
          title: Adresse email
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
    UserDetailsRequest:
      type: object
      description: User model w/o password
      properties:
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
    UserProfile:
      type: object
      description: Sérialiseur pour afficher et modifier le profil utilisateur et
        ses sous-profils dynamiquement.
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          readOnly: true
          title: Adresse email
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
        phone_number:
          type: string
          title: Numéro de téléphone
          maxLength: 15
        type:
          enum:
          - CLIENT
          - CAPTAIN
          - ESTABLISHMENT
          type: string
          description: |-
            * `CLIENT` - Client
            * `CAPTAIN` - Capitaine
            * `ESTABLISHMENT` - Établissement
          readOnly: true
          title: Type d'utilisateur
        profile_picture:
          type: string
          readOnly: true
        client_profile:
          $ref: '#/components/schemas/Client'
        captain_profile:
          $ref: '#/components/schemas/Captain'
        establishment_profile:
          $ref: '#/components/schemas/Establishment'
    UserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          minLength: 1
          title: Adresse email
          maxLength: 254
        password:
          type: string
          writeOnly: true
          minLength: 1
        password2:
          type: string
          writeOnly: true
          minLength: 1
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
        phone_number:
          type: string
          title: Numéro de téléphone
          maxLength: 15
        type:
          enum:
          - CLIENT
          - CAPTAIN
          - ESTABLISHMENT
          type: string
          description: |-
            * `CLIENT` - Client
            * `CAPTAIN` - Capitaine
            * `ESTABLISHMENT` - Établissement
          title: Type d'utilisateur
        is_active:
          type: boolean
          title: Actif
          description: Précise si l’utilisateur doit être considéré comme actif. Décochez
            ceci plutôt que de supprimer le compte.
      required:
      - email
      - first_name
      - last_name
      - password
      - password2
      - type
    VerificationCodeRequest:
      type: object
      properties:
        code:
          type: string
          minLength: 1
          maxLength: 6
      required:
      - code
    VerifyEmailRequest:
      type: object
      properties:
        key:
          type: string
          writeOnly: true
          minLength: 1
      required:
      - key
    WalletDetailResponse:
      type: object
      description: Réponse pour les détails du portefeuille
      properties:
        id:
          type: integer
          description: ID du portefeuille
        user_id:
          type: integer
          description: ID de l'utilisateur
        balance:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          description: Solde actuel en euros
        loyalty_points:
          type: integer
          description: Points de fidélité
        total_earned:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          description: Total gagné
        total_spent:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          description: Total dépensé
        created_at:
          type: string
          format: date-time
          description: Date de création
        updated_at:
          type: string
          format: date-time
          description: Dernière mise à jour
      required:
      - balance
      - created_at
      - id
      - loyalty_points
      - total_earned
      - total_spent
      - updated_at
      - user_id
    WalletRechargeRequestRequest:
      type: object
      description: Requête pour recharger le portefeuille
      properties:
        amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          description: Montant à ajouter (entre 1€ et 1000€)
        payment_method_id:
          type: string
          minLength: 1
          description: ID de la méthode de paiement Stripe
          maxLength: 255
      required:
      - amount
      - payment_method_id
    WalletRechargeResponse:
      type: object
      description: Réponse pour la recharge du portefeuille
      properties:
        status:
          type: string
          description: Statut de l'opération
        payment_id:
          type: string
          description: ID du paiement
        amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          description: Montant rechargé
        new_balance:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          description: Nouveau solde
        transaction_id:
          type: integer
          description: ID de la transaction
      required:
      - amount
      - new_balance
      - payment_id
      - status
      - transaction_id
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
    jwtAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    tokenAuth:
      type: apiKey
      in: header
      name: Authorization
      description: Token-based authentication with required prefix "Token"
servers:
- url: https://api.commodore.com
  description: Serveur de production
- url: https://staging-api.commodore.com
  description: Serveur de staging
- url: http://localhost:8000
  description: Serveur de développement
