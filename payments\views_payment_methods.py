from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.conf import settings
from .models import Payment
from .serializers import PaymentSerializer
from .stripe_utils import (
    create_customer, create_payment_method, attach_payment_method,
    detach_payment_method, list_payment_methods
)
from accounts.models import Client as Passenger  # Utiliser Client à la place de Passenger

class PaymentMethodViewSet(viewsets.ViewSet):
    """ViewSet pour gérer les méthodes de paiement Stripe"""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['post'])
    def create_customer(self, request):
        """Crée un client Stripe pour un passager"""
        passenger_id = request.data.get('passenger_id')
        email = request.data.get('email')
        name = request.data.get('name')
        phone = request.data.get('phone')

        if not passenger_id or not email:
            return Response({'error': 'passenger_id et email sont requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            passenger = Passenger.objects.get(id=passenger_id)
            
            # Créer des métadonnées pour le client
            metadata = {
                'passenger_id': str(passenger.id),
                'user_id': str(passenger.user.id),
            }
            
            # Créer un client Stripe
            customer = create_customer(
                email=email,
                name=name,
                phone=phone,
                metadata=metadata,
                description=f"Passager {passenger.user.first_name} {passenger.user.last_name}"
            )
            
            if 'error' in customer:
                return Response({'error': customer['error']}, status=status.HTTP_400_BAD_REQUEST)
            
            # Mettre à jour le passager avec l'ID du client Stripe
            passenger.stripe_customer_id = customer.id
            passenger.save()
            
            return Response({
                'customer_id': customer.id,
                'passenger_id': passenger.id
            })
        except Passenger.DoesNotExist:
            return Response({'error': 'passager non trouvé'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def add_payment_method(self, request):
        """Ajoute une méthode de paiement à un client Stripe"""
        passenger_id = request.data.get('passenger_id')
        payment_method_id = request.data.get('payment_method_id')
        
        if not passenger_id or not payment_method_id:
            return Response({'error': 'passenger_id et payment_method_id sont requis'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            passenger = Passenger.objects.get(id=passenger_id)
            
            if not passenger.stripe_customer_id:
                return Response({'error': 'le passager n\'a pas de compte client Stripe'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Attacher la méthode de paiement au client
            result = attach_payment_method(
                payment_method_id=payment_method_id,
                customer_id=passenger.stripe_customer_id
            )
            
            if 'error' in result:
                return Response({'error': result['error']}, status=status.HTTP_400_BAD_REQUEST)
            
            return Response({
                'payment_method_id': result.id,
                'type': result.type,
                'customer_id': passenger.stripe_customer_id
            })
        except Passenger.DoesNotExist:
            return Response({'error': 'passager non trouvé'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def remove_payment_method(self, request):
        """Supprime une méthode de paiement"""
        payment_method_id = request.data.get('payment_method_id')
        
        if not payment_method_id:
            return Response({'error': 'payment_method_id est requis'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Détacher la méthode de paiement
        result = detach_payment_method(payment_method_id)
        
        if 'error' in result:
            return Response({'error': result['error']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'status': 'méthode de paiement supprimée',
            'payment_method_id': result.id
        })

    @action(detail=False, methods=['get'])
    def list_methods(self, request):
        """Liste les méthodes de paiement d'un client"""
        passenger_id = request.query_params.get('passenger_id')
        
        if not passenger_id:
            return Response({'error': 'passenger_id est requis'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            passenger = Passenger.objects.get(id=passenger_id)
            
            if not passenger.stripe_customer_id:
                return Response({'error': 'le passager n\'a pas de compte client Stripe'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Lister les méthodes de paiement
            payment_methods = list_payment_methods(
                customer_id=passenger.stripe_customer_id
            )
            
            if 'error' in payment_methods:
                return Response({'error': payment_methods['error']}, status=status.HTTP_400_BAD_REQUEST)
            
            # Formater la réponse
            methods = []
            for method in payment_methods.data:
                method_data = {
                    'id': method.id,
                    'type': method.type,
                    'created': method.created,
                }
                
                if method.type == 'card' and hasattr(method, 'card'):
                    method_data['card'] = {
                        'brand': method.card.brand,
                        'last4': method.card.last4,
                        'exp_month': method.card.exp_month,
                        'exp_year': method.card.exp_year,
                    }
                
                methods.append(method_data)
            
            return Response(methods)
        except Passenger.DoesNotExist:
            return Response({'error': 'passager non trouvé'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def set_default_payment_method(self, request):
        """Définit une méthode de paiement par défaut pour un client"""
        passenger_id = request.data.get('passenger_id')
        payment_method_id = request.data.get('payment_method_id')
        
        if not passenger_id or not payment_method_id:
            return Response({'error': 'passenger_id et payment_method_id sont requis'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            passenger = Passenger.objects.get(id=passenger_id)
            
            if not passenger.stripe_customer_id:
                return Response({'error': 'le passager n\'a pas de compte client Stripe'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Mettre à jour le client avec la méthode de paiement par défaut
            try:
                customer = stripe.Customer.modify(
                    passenger.stripe_customer_id,
                    invoice_settings={
                        'default_payment_method': payment_method_id
                    }
                )
                
                # Mettre à jour le passager
                passenger.preferred_payment_method = payment_method_id
                passenger.save()
                
                return Response({
                    'status': 'méthode de paiement par défaut mise à jour',
                    'customer_id': customer.id,
                    'default_payment_method': payment_method_id
                })
            except stripe.error.StripeError as e:
                return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Passenger.DoesNotExist:
            return Response({'error': 'passager non trouvé'}, status=status.HTTP_404_NOT_FOUND)
