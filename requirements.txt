aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiohttp-retry==2.9.1
aiosignal==1.3.2
amqp==5.3.1
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.8.1
attrs==25.3.0
billiard==4.2.1
biotech==1.1.10
black==23.11.0
blinker==1.9.0
body_scan==1.1.1
botanist==1.0.0
boto3==1.34.0
botocore==1.34.162
cachetools==5.5.2
celery==5.3.6
certifi==2025.4.26
cffi==1.17.1
channels==4.0.0
channels-redis==4.1.0
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
coverage==7.8.2
cron-descriptor==1.4.5
cryptography==45.0.2
dataclasses-json==0.6.7
defusedxml==0.7.1
deprecation==2.1.0
dj-database-url==2.1.0
dj-rest-auth==5.0.1
Django==4.2.8
django-allauth==0.58.2
django-celery-beat==2.5.0
django-celery-results==2.5.1
django-cors-headers==4.3.0
django-cron==0.6.0
django-debug-toolbar==4.2.0
django-environ==0.11.2
django-facebook==6.0.3
django-jazzmin==3.0.1
django-redis==5.4.0
django-ses==3.5.0
django-storages==1.14.2
django-timezone-field==7.1
djangorestframework==3.16.0
djangorestframework-gis==1.0
djangorestframework-simplejwt==5.3.0
dnspython==2.7.0
drf-nested-routers==0.94.2
drf-spectacular==0.26.5
drf-yasg==1.21.7
facebook-sdk==3.1.0
factory-boy==3.3.0
faiss-cpu==1.11.0
Faker==37.3.0
filelock==3.18.0
flake8==6.1.0
Flask==3.1.1
frozenlist==1.6.0
fsspec==2025.5.1
geographiclib==2.0
geopy==2.4.1
google-api-core==2.25.0
google-api-python-client==2.170.0
google-auth==2.40.2
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.2
googleapis-common-protos==1.70.0
gotrue==2.11.4
greenlet==3.2.2
gunicorn==21.2.0
h11==0.16.0
h2==4.2.0
hpack==4.1.0
html5tagger==1.3.0
httpcore==1.0.9
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.32.0
hyperframe==6.1.0
idna==3.10
inflection==0.5.1
iniconfig==2.1.0
isort==5.12.0
itsdangerous==2.2.0
Jinja2==3.1.6
jmespath==1.0.1
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
kombu==5.5.3
langchain==0.3.25
langchain-community==0.3.24
langchain-core==0.3.61
langchain-huggingface==0.2.0
langchain-text-splitters==0.3.8
langsmith==0.3.42
law_dictionary==1.0.12
linkify-it-py==2.0.3
Mako==1.3.10
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.20.1
mccabe==0.7.0
mdit-py-plugins==0.4.2
mdurl==0.1.2
mpmath==1.3.0
msgpack==1.1.0
multidict==6.4.4
mypy_extensions==1.1.0
networkx==3.4.2
numpy==2.2.6
oauthlib==3.2.2
OHF==1.0.4
orjson==3.10.18
packaging==24.2
pathspec==0.12.1
pdoc3==0.11.6
pexpect==4.9.0
pillow==11.2.1
platformdirs==4.3.8
pluggy==1.6.0
postgrest==1.0.2
prompt_toolkit==3.0.51
propcache==0.3.1
proto-plus==1.26.1
protobuf==6.31.1
psutil==7.0.0
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.11.1
pycparser==2.22
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2
pyflakes==3.1.0
Pygments==2.19.1
PyJWT==2.9.0
pymongo==4.13.0
pyparsing==3.2.3
pytest==7.4.3
pytest-django==4.7.0
pytest-mock==3.14.1
python-crontab==3.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
python-social-auth==0.3.6
python3-openid==3.2.0
pytz==2025.2
PyYAML==6.0.2
qrcode==8.2
realtime==2.4.3
redis==5.3.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
retrying==1.3.4
rich==13.9.4
rides==0.0.1
rpds-py==0.25.1
rsa==4.9.1
s3transfer==0.9.0
safetensors==0.5.3
sanic==23.12.2
sanic-routing==23.12.0
scikit-learn==1.6.1
scipy==1.15.3
sentence-transformers==4.1.0
sentry-sdk==1.39.1
setuptools==80.9.0
shares==3.1.10
six==1.17.0
sniffio==1.3.1
social-auth-app-django==5.4.0
social-auth-core==4.6.1
somatic==3.0.3
SQLAlchemy==2.0.41
sqlparse==0.5.3
storage3==0.11.3
StrEnum==0.4.15
stripe==12.1.0
supabase==2.15.2
supafunc==0.9.4
sympy==1.14.0
tenacity==9.1.2
textual==3.2.0
threadpoolctl==3.6.0
tinydb==4.8.2
tokenizers==0.21.1
torch==2.7.0
tqdm==4.67.1
tracerite==1.1.1
transformers==4.52.3
twilio==8.10.0
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.13.2
tzdata==2025.2
uc-micro-py==1.0.3
Unidecode==1.4.0
uritemplate==4.1.1
urllib3==2.4.0
vine==5.1.0
wcwidth==0.2.13
websockets==14.2
Werkzeug==3.1.3
whitenoise==6.6.0
yarl==1.20.0
zstandard==0.23.0
