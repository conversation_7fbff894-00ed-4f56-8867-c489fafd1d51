"""
Vues pour la gestion des navettes par les établissements.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from decimal import Decimal
import math

from accounts.models import Establishment, Captain
from boats.models import Boat
from trips.models import Trip, Shuttle, ShuttleTripRequest, TripQuote
from notifications.services import create_notification
from trips.serializers import TripQuoteSerializer
from accounts.permissions import IsEstablishment


class EstablishmentShuttleAcceptView(APIView):
    """
    Accepter une demande de navette.
    
    POST /api/establishments/shuttle-requests/{request_id}/accept/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def post(self, request, request_id):
        """Accepter une demande de navette"""
        
        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)
        
        establishment = request.user.establishment
        
        # Récupérer la demande
        try:
            shuttle_request = ShuttleTripRequest.objects.get(
                id=request_id,
                establishment=establishment,
                status='PENDING'
            )
        except ShuttleTripRequest.DoesNotExist:
            return Response({
                'error': 'Demande non trouvée ou déjà traitée'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Récupérer l'ID de l'établissement depuis le payload
        establishment_id = request.data.get('establishment_id')

        if not establishment_id:
            return Response({
                'error': 'establishment_id est requis'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Vérifier que l'établissement correspond à celui de la demande
        if establishment_id != establishment.id:
            return Response({
                'error': 'L\'établissement ne correspond pas à la demande'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Pas d'assignation de capitaine ou bateau pour le moment
        # L'assignation se fera plus tard par l'établissement
        
        # Créer la course Trip
        # Calcul de l'heure d'arrivée prévue basé sur la distance et vitesse par défaut
        scheduled_start = timezone.make_aware(timezone.datetime.combine(
            shuttle_request.departure_date,
            shuttle_request.departure_time
        ))
        # Distance en kilomètres (pré-calculée ou recalculée)
        distance_km = float(shuttle_request.distance_km or shuttle_request.calculate_distance() or 0)
        # Vitesse moyenne par défaut (km/h), 33 km/h
        speed_kmh = 33.0
        duration_minutes = max(1, math.ceil(distance_km / speed_kmh * 60))
        scheduled_end = scheduled_start + timezone.timedelta(minutes=duration_minutes)

        # Créer la navette (Shuttle) sans capitaine ni bateau pour le moment
        shuttle = Shuttle.objects.create(
            establishment=establishment,
            route_name=f"{shuttle_request.departure_location.get('city_name', '')} -> {shuttle_request.arrival_location.get('city_name', '')}",
            departure_time=scheduled_start,
            arrival_time=scheduled_end,
            max_capacity=shuttle_request.passenger_count,  # Utiliser le nombre de passagers de la demande
            price_per_person=Decimal('0.00'),
            status=Shuttle.Status.SCHEDULED
        )

        # Préparer les coordonnées complètes pour la course
        # Coordonnées de départ (du client)
        start_location_data = shuttle_request.departure_location if shuttle_request.departure_location else {}

        # Coordonnées d'arrivée (de l'établissement depuis la base de données)
        end_location_data = {
            'city_name': establishment.name,
            'coordinates': {
                'latitude': float(establishment.latitude) if establishment.latitude else 0,
                'longitude': float(establishment.longitude) if establishment.longitude else 0
            },
            'timestamp': timezone.now().isoformat()
        }

        # Créer la course (Trip) en la liant à la navette (sans capitaine ni bateau pour le moment)
        trip = Trip.objects.create(
            client=shuttle_request.client,
            # captain et boat seront assignés plus tard
            establishment=establishment,
            shuttle=shuttle,  # Lier la course à la navette
            trip_type=Trip.TripType.NAVETTES_GRATUITES,
            start_location=start_location_data,  # JSON complet avec coordonnées client
            end_location=end_location_data,      # JSON complet avec coordonnées établissement
            scheduled_start_time=scheduled_start,
            scheduled_end_time=scheduled_end,
            estimated_duration=duration_minutes,
            passenger_count=shuttle_request.passenger_count,
            distance_km=shuttle_request.distance_km,  # Distance calculée depuis la demande
            base_price=Decimal('0.00'),
            additional_charges=Decimal('0.00'),
            tip=Decimal('0.00'),
            total_price=Decimal('0.00'),
            payment_method='FREE_SHUTTLE',
            payment_status='PAID',  # Gratuit donc considéré comme payé
            status=Trip.Status.ACCEPTED
        )
        
        # Mettre à jour la demande
        shuttle_request.status = 'ACCEPTED'
        shuttle_request.save()
        
        # Envoyer une notification au client
        create_notification(
            user=shuttle_request.client.user,
            title="Navette acceptée",
            message=f"Votre demande de navette a été acceptée par {establishment.name}",
            notification_type="SHUTTLE_ACCEPTED",
            related_object_id=trip.id
        )
        
        # La notification au capitaine sera envoyée lors de l'assignation
        
        return Response({
            'status': 'success',
            'message': 'Demande acceptée, navette et course créées',
            'trip_id': str(trip.id),
            'shuttle_id': str(shuttle.id)
        })


class EstablishmentShuttleRejectView(APIView):
    """
    Rejeter une demande de navette.
    
    POST /api/establishments/shuttle-requests/{request_id}/reject/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def post(self, request, request_id):
        """Rejeter une demande de navette"""
        
        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)
        
        establishment = request.user.establishment
        
        # Récupérer la demande
        try:
            shuttle_request = ShuttleTripRequest.objects.get(
                id=request_id,
                establishment=establishment,
                status='PENDING'
            )
        except ShuttleTripRequest.DoesNotExist:
            return Response({
                'error': 'Demande non trouvée ou déjà traitée'
            }, status=status.HTTP_404_NOT_FOUND)
        
        reason = request.data.get('reason', 'Aucune raison fournie')
        
        # Mettre à jour la demande
        shuttle_request.status = 'REJECTED'
        shuttle_request.save()
        
        # Envoyer une notification au client
        create_notification(
            user=shuttle_request.client.user,
            title="Navette refusée",
            message=f"Votre demande de navette a été refusée par {establishment.name}. Raison: {reason}",
            notification_type="SHUTTLE_REJECTED",
            related_object_id=shuttle_request.id
        )
        
        return Response({
            'status': 'success',
            'message': 'Demande rejetée'
        })


class EstablishmentAvailableResourcesView(APIView):
    """
    Récupérer les ressources disponibles (bateaux et capitaines).
    
    GET /api/establishments/available-resources/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def get(self, request):
        """Récupérer les bateaux et capitaines disponibles"""
        
        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Bateaux disponibles (tous les bateaux pour l'instant)
        boats = Boat.objects.filter(is_available=True)
        
        # Capitaines disponibles
        captains = Captain.objects.filter(
            is_available=True,
            availability_status='AVAILABLE'
        )
        
        boats_data = []
        for boat in boats:
            boats_data.append({
                'id': boat.id,
                'name': boat.name,
                'capacity': boat.capacity,
                'boat_type': boat.boat_type,
                'registration_number': boat.registration_number,
                'captain_name': f"{boat.captain.user.first_name} {boat.captain.user.last_name}" if boat.captain else 'N/A',
                'price_per_hour': float(getattr(boat, 'price_per_hour', 0.0)),
                'price_per_km': float(getattr(boat, 'price_per_km', 0.0)),
            })
        
        captains_data = []
        for captain in captains:
            # On récupère l'image de profil depuis le user lié
            image_url = ''
            if hasattr(captain.user, 'profile_picture') and captain.user.profile_picture:
                image_url = captain.user.profile_picture
            captains_data.append({
                'id': captain.user.id,
                'name': f"{captain.user.first_name} {captain.user.last_name}",
                'experience': captain.experience,
                'license_number': captain.license_number,
                'average_rating': float(captain.average_rating) if captain.average_rating else 0.0,
                'image_url': image_url,
                'price_per_hour': float(getattr(captain, 'rate_per_hour', 0.0)),
                'price_per_km': float(getattr(captain, 'rate_per_km', 0.0)),
            })
        
        return Response({
            'status': 'success',
            'data': {
                'available_boats': boats_data,
                'available_captains': captains_data
            }
        })


class EstablishmentShuttleQuotesView(APIView):
    """Lister les devis accessibles pour une demande de navette donnée.

    GET /api/establishments/shuttle-requests/{request_id}/quotes/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def get(self, request, request_id):
        if not hasattr(request.user, 'establishment'):
            return Response({'error': 'Accès refusé'}, status=status.HTTP_403_FORBIDDEN)

        establishment = request.user.establishment
        shuttle_request = get_object_or_404(ShuttleTripRequest, id=request_id, establishment=establishment)

        # Vérifier s'il existe déjà des devis disponibles
        quotes = TripQuote.objects.filter(trip_request=shuttle_request, is_available=True)
        if not quotes.exists():
            # Générer dynamiquement un devis pour chaque capitaine indépendant disponible
            available_captains = Captain.objects.filter(is_available=True, availability_status='AVAILABLE')
            for captain in available_captains:
                # Vérifier qu'il n'existe pas déjà un devis pour ce capitaine et cette demande
                if not TripQuote.objects.filter(trip_request=shuttle_request, captain=captain).exists():
                    # Détermination de la méthode de tarification
                    from decimal import Decimal
                    distance_km = Decimal(str(shuttle_request.distance_km or 0))
                    if captain.rate_per_km:
                        pricing_method = "PER_KM"
                        rate_used = captain.rate_per_km
                        base_price = rate_used * distance_km
                        unit = "km"
                    elif captain.rate_per_hour:
                        pricing_method = "PER_HOUR"
                        rate_used = captain.rate_per_hour
                        # Estimation simple : 1h (ou à remplacer par une estimation plus précise)
                        estimated_hours = Decimal('1.0')
                        base_price = rate_used * estimated_hours
                        unit = "hour"
                    else:
                        # Pas de tarif défini, ignorer ce capitaine
                        continue
                    # Chercher un bateau disponible appartenant à ce capitaine indépendant
                    boat_for_captain = Boat.objects.filter(captain=captain, is_available=True).first()
                    if not boat_for_captain:
                        # Pas de bateau pour ce capitaine : ignorer ce capitaine pour la génération de devis
                        continue
                    TripQuote.objects.create(
                        trip_request=shuttle_request,
                        captain=captain,
                        boat=boat_for_captain,  # Bateau du capitaine requis par la contrainte NOT NULL
                        base_price=base_price,
                        distance_km=distance_km,
                        rate_used=rate_used,
                        pricing_method=pricing_method,
                        unit=unit,
                        captain_name=captain.user.get_full_name(),
                        captain_rating=captain.average_rating or 0,
                        boat_name=boat_for_captain.name or "",
                        boat_capacity=boat_for_captain.capacity or 0,
                        is_available=True,
                    )
            # Rafraîchir la liste des devis
            quotes = TripQuote.objects.filter(trip_request=shuttle_request, is_available=True)
        quotes = quotes.order_by('base_price')
        serializer = TripQuoteSerializer(quotes, many=True)
        return Response({
            'status': 'success',
            'data': {
                'quotes': serializer.data
            }
        })
