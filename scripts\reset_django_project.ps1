# Script PowerShell pour réinitialiser complètement un projet Django
# Ce script va:
# 1. Supprimer la base de données
# 2. Recréer la base de données
# 3. Supprimer tous les fichiers de migration (sauf __init__.py)
# 4. Supprimer tous les dossiers __pycache__
# 5. Recréer les migrations et les appliquer
#
# USAGE: .\scripts\reset_django_project.ps1
# Ce script est safe pour les nouveaux développeurs et résout les problèmes de migrations

# Fonction pour supprimer les migrations
function Remove-Migrations {
    param (
        [string]$rootDir
    )

    Write-Host "Suppression des fichiers de migration..." -ForegroundColor Yellow

    # Trouver tous les dossiers de migrations (exclure venv)
    $migrationDirs = Get-ChildItem -Path $rootDir -Recurse -Directory | Where-Object { $_.Name -eq "migrations" -and $_.FullName -notlike "*\venv\*" }

    foreach ($dir in $migrationDirs) {
        Write-Host "Traitement du dossier: $($dir.FullName)" -ForegroundColor Cyan

        # Supprimer tous les fichiers sauf __init__.py
        Get-ChildItem -Path $dir.FullName -File | Where-Object { $_.Name -ne "__init__.py" } | ForEach-Object {
            Write-Host "  Suppression de $($_.Name)" -ForegroundColor Gray
            Remove-Item $_.FullName -Force
        }
    }

    Write-Host "Suppression des fichiers de migration terminée." -ForegroundColor Green
}

# Fonction pour supprimer les dossiers __pycache__
function Remove-PycacheDirectories {
    param (
        [string]$rootDir
    )

    Write-Host "Suppression des dossiers __pycache__..." -ForegroundColor Yellow

    # Trouver tous les dossiers __pycache__ (exclure venv)
    $pycacheDirs = Get-ChildItem -Path $rootDir -Recurse -Directory | Where-Object { $_.Name -eq "__pycache__" -and $_.FullName -notlike "*\venv\*" }

    foreach ($dir in $pycacheDirs) {
        Write-Host "  Suppression de $($dir.FullName)" -ForegroundColor Gray
        Remove-Item $dir.FullName -Recurse -Force
    }

    Write-Host "Suppression des dossiers __pycache__ terminée." -ForegroundColor Green
}

# Réinitialiser la base de données
Write-Host "Réinitialisation de la base de données..." -ForegroundColor Yellow

# Supprimer la base de données
$dropDbCommand = "DROP DATABASE IF EXISTS commodore;"
$createDbCommand = "CREATE DATABASE commodore;"

# Exécuter les commandes SQL
Write-Host "Suppression de la base de données..." -ForegroundColor Cyan
$env:PGPASSWORD = "admin"  # Mot de passe PostgreSQL
psql -U postgres -c "$dropDbCommand"

Write-Host "Création de la base de données..." -ForegroundColor Cyan
psql -U postgres -c "$createDbCommand"

# Supprimer les migrations et les caches
$projectRoot = "D:\commodore"
Remove-Migrations -rootDir $projectRoot
Remove-PycacheDirectories -rootDir $projectRoot



# Recréer les migrations et les appliquer
Write-Host "Création des nouvelles migrations..." -ForegroundColor Yellow
python manage.py makemigrations

Write-Host "Application des migrations..." -ForegroundColor Yellow
python manage.py migrate

Write-Host "Création d'un superuser..." -ForegroundColor Yellow
Write-Host "Vous devrez créer un superuser manuellement avec: python manage.py createsuperuser" -ForegroundColor Cyan

Write-Host "Réinitialisation du projet Django terminée !" -ForegroundColor Green
Write-Host "Le projet est maintenant prêt pour le développement." -ForegroundColor Green
