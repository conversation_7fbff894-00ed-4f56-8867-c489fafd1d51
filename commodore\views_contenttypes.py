from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.contenttypes.models import ContentType
from drf_spectacular.utils import extend_schema, OpenApiParameter


@extend_schema(
    tags=["Utils"],
    parameters=[
        OpenApiParameter(
            name="app_label",
            description="Filtrer par app_label (facultatif)",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="model",
            description="Filtrer par modèle (facultatif, casse insensible)",
            required=False,
            type=str,
        ),
    ],
    responses={
        200: {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "integer"},
                    "app_label": {"type": "string"},
                    "model": {"type": "string"},
                    "name": {"type": "string"},
                },
            },
        }
    },
)
class ContentTypeListView(APIView):
    """Expose la liste des ContentTypes utiles pour les avis ou autres besoins."""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        app_label = request.query_params.get("app_label")
        model = request.query_params.get("model")

        qs = ContentType.objects.all()
        if app_label:
            qs = qs.filter(app_label=app_label)
        if model:
            qs = qs.filter(model__iexact=model)

        data = [
            {
                "id": ct.id,
                "app_label": ct.app_label,
                "model": ct.model,
                "name": str(ct),
            }
            for ct in qs.order_by("app_label", "model")
        ]
        return Response(data)
