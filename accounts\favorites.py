from django.db import models
from django.utils.translation import gettext_lazy as _
from .models import User, Captain

class FavoriteLocation(models.Model):
    """Modèle pour stocker les emplacements favoris d'un utilisateur."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorite_locations')
    name = models.CharField(_('nom'), max_length=100)
    address = models.CharField(_('adresse'), max_length=255)
    coordinates = models.CharField(_('coordonnées GPS'), max_length=50, blank=True)
    notes = models.TextField(_('notes'), blank=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('emplacement favori')
        verbose_name_plural = _('emplacements favoris')
        unique_together = ['user', 'name']
        ordering = ['name']
    
    def __str__(self):
        return f"{self.user.email} - {self.name}"

class FavoriteCaptain(models.Model):
    """Mod<PERSON>le pour stocker les capitaines favoris d'un utilisateur."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorite_captains')
    captain = models.ForeignKey(Captain, on_delete=models.CASCADE, related_name='favorited_by')
    notes = models.TextField(_('notes'), blank=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('capitaine favori')
        verbose_name_plural = _('capitaines favoris')
        unique_together = ['user', 'captain']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.email} - {self.captain.user.get_full_name()}"
