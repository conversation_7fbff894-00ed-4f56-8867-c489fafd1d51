from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsA<PERSON><PERSON>icated, AllowAny
from django.contrib.auth import get_user_model, update_session_auth_hash
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from .models import <PERSON><PERSON>, Captain, Establishment
from .serializers import (
    UserSerializer, ClientSerializer, CaptainSerializer,
    EstablishmentSerializer, UserProfileSerializer
)
from .permissions import Is<PERSON><PERSON>, IsCaptain, IsBoatman, IsEstablishment, IsOwnerOrAdmin
from drf_spectacular.utils import extend_schema, OpenApiParameter

from django.shortcuts import get_object_or_404
import re
from django.utils import timezone

User = get_user_model()

@extend_schema(tags=["Accounts"], request=UserSerializer, responses=UserSerializer)
class RegisterUserView(APIView):
    permission_classes = [AllowAny]

    @extend_schema(tags=["Accounts"], request=UserSerializer, responses=UserSerializer)
    def post(self, request):
        user_type = request.data.get('type', '').upper()
        if user_type not in ['CLIENT', 'CAPTAIN', 'ESTABLISHMENT']:
            return Response(
                {'error': 'Type d\'utilisateur invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer_class = {
            'CLIENT': ClientSerializer,
            'CAPTAIN': CaptainSerializer,
            'ESTABLISHMENT': EstablishmentSerializer
        }.get(user_type)

        serializer = serializer_class(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@extend_schema(tags=["Client"])
class ClientProfilePatchView(APIView):
    permission_classes = [IsAuthenticated, IsClient]

    @extend_schema(request=UserProfileSerializer, responses=UserProfileSerializer)
    def patch(self, request):
        user = request.user
        # La permission IsClient s'en charge déjà
        client = user.client
        client = user.client
        data = request.data.copy()
        allowed_user_fields = {'first_name', 'last_name', 'phone_number', 'profile_picture', 'username'}
        allowed_client_fields = {'date_of_birth', 'nationality', 'preferred_language', 'emergency_contact_name', 'emergency_contact_phone'}
        user_data = {k: v for k, v in data.items() if k in allowed_user_fields}
        client_data = data.get('client_profile', {})
        client_data = {k: v for k, v in client_data.items() if k in allowed_client_fields}
        for k, v in user_data.items():
            setattr(user, k, v)
        user.save()
        for k, v in client_data.items():
            setattr(client, k, v)
        client.save()
        updated_fields = []
        for k, v in user_data.items():
            updated_fields.append(k)
        for k, v in client_data.items():
            updated_fields.append(f"client_profile.{k}")
        serializer = UserProfileSerializer(request.user)
        return Response({
            "status": "success",
            "data": {
                "updated_fields": updated_fields,
                "profile_updated_at": timezone.now().isoformat()
            },
            "message": f"Profil mis à jour ({len(updated_fields)} champ{'s' if len(updated_fields) > 1 else ''} modifié{'s' if len(updated_fields) > 1 else ''})",
            "profile": serializer.data
        })

@extend_schema(tags=["Captain"])
class CaptainProfilePatchView(APIView):
    permission_classes = [IsAuthenticated, IsCaptain]

    @extend_schema(request=UserProfileSerializer, responses=UserProfileSerializer)
    def patch(self, request):
        user = request.user
        # La permission IsCaptain s'en charge déjà
        captain = user.captain
        data = request.data.copy()
        allowed_user_fields = {'first_name', 'last_name', 'phone_number', 'profile_picture'}
        allowed_captain_fields = {'experience', 'certifications', 'availability_status', 'average_rating', 'total_trips', 'license_number', 'years_of_experience', 'rate_per_hour', 'rate_per_km'}
        allowed_boat_fields = {'name', 'registration_number', 'boat_type', 'capacity', 'color', 'fuel_type', 'fuel_consumption', 'photos', 'zone_served', 'radius', 'is_available'}
        user_data = {k: v for k, v in data.items() if k in allowed_user_fields}
        captain_data = data.get('captain_profile', {})
        captain_data = {k: v for k, v in captain_data.items() if k in allowed_captain_fields}
        boat_data = data.get('boat', {})
        boat_data = {k: v for k, v in boat_data.items() if k in allowed_boat_fields}
        
        # Validation du statut de disponibilité
        if 'availability_status' in captain_data and captain_data['availability_status'] not in ['AVAILABLE', 'OFFLINE']:
            return Response({
                "status": "error",
                "error": "Le statut de disponibilité doit être 'AVAILABLE' ou 'OFFLINE'.",
                "error_code": 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Les champs rate_per_km et rate_per_hour sont désormais facultatifs.
        # Aucune validation bloquante n'est appliquée : s'ils sont absents de la requête,
        # on conserve simplement les valeurs existantes en base, le cas échéant.
        # Si l'un ou l'autre est fourni, on mettra la valeur à jour plus bas sans contrainte.
        
        for k, v in user_data.items():
            setattr(user, k, v)
        user.save()
        for k, v in captain_data.items():
            setattr(captain, k, v)
        captain.save()
        boat = captain.boats.first()
        updated_fields = []
        for k, v in user_data.items():
            updated_fields.append(k)
        for k, v in captain_data.items():
            updated_fields.append(f"captain_profile.{k}")
        if boat_data:
            if not boat:
                updated_fields.append("boat.created")
            else:
                for k, v in boat_data.items():
                    if k in allowed_boat_fields:
                        updated_fields.append(f"boat.{k}")
        if boat_data:
            registration_number = boat_data.get('registration_number')
            if registration_number:
                # Import ici pour éviter les problèmes d'import circulaire
                from boats.models import Boat
                # Vérifier si un bateau avec ce numéro existe déjà
                existing_boat = Boat.objects.filter(registration_number=registration_number).first()
                if existing_boat and (not boat or existing_boat.id != boat.id):
                    return Response({
                        "status": "error",
                        "error": f"Un bateau avec le numéro d'immatriculation '{registration_number}' existe déjà.",
                        "error_code": 400
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            if not boat:
                from boats.models import Boat
                try:
                    boat = Boat(
                        captain=captain,
                        **{k: v for k, v in boat_data.items() if k in allowed_boat_fields}
                    )
                    boat.save()
                except Exception as e:
                    # Capturer d'autres erreurs potentielles lors de la création
                    return Response({
                        "status": "error",
                        "error": f"Erreur lors de la création du bateau: {str(e)}",
                        "error_code": 400
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                try:
                    for k, v in boat_data.items():
                        if k in allowed_boat_fields:
                            setattr(boat, k, v)
                    boat.save()
                except Exception as e:
                    # Capturer d'autres erreurs potentielles lors de la mise à jour
                    return Response({
                        "status": "error",
                        "error": f"Erreur lors de la mise à jour du bateau: {str(e)}",
                        "error_code": 400
                    }, status=status.HTTP_400_BAD_REQUEST)
        serializer = UserProfileSerializer(request.user)
        return Response({
            "status": "success",
            "data": {
                "updated_fields": updated_fields,
                "profile_updated_at": timezone.now().isoformat()
            },
            "message": f"Profil mis à jour ({len(updated_fields)} champ{'s' if len(updated_fields) > 1 else ''} modifié{'s' if len(updated_fields) > 1 else ''})",
            "profile": serializer.data
        })

@extend_schema(tags=["Establishment"])
class EstablishmentProfilePatchView(APIView):
    permission_classes = [IsAuthenticated, IsEstablishment]

    @extend_schema(request=UserProfileSerializer, responses=UserProfileSerializer)
    def patch(self, request):
        user = request.user
        # La permission IsEstablishment s'en charge déjà
        establishment = user.establishment
        establishment = user.establishment
        data = request.data.copy()

        # Champs utilisateur pouvant être mis à jour
        allowed_user_fields = {
            'first_name', 'last_name', 'phone_number', 'profile_picture'
        }

        # Champs établissement pouvant être mis à jour
        allowed_est_fields = {
            'name', 'type', 'address', 'description', 'main_photo', 'secondary_photos',
            'business_name', 'business_type', 'registration_number', 'tax_id',
            'opening_hours', 'services_offered', 'average_rating', 'location_coordinates',
            'website', 'social_media'
        }

        # Extraction des sous-objets du payload
        user_data = {k: v for k, v in data.items() if k in allowed_user_fields}
        est_data = data.get('establishment_profile', {})
        est_data = {k: v for k, v in est_data.items() if k in allowed_est_fields}

        # Mise à jour de l'utilisateur
        for k, v in user_data.items():
            setattr(user, k, v)
        user.save()

        # Mise à jour de l'établissement
        for k, v in est_data.items():
            setattr(establishment, k, v)
        establishment.save()

        updated_fields = list(user_data.keys()) + [f"establishment_profile.{k}" for k in est_data.keys()]
        serializer = UserProfileSerializer(request.user)
        return Response({
            "status": "success",
            "data": {
                "updated_fields": updated_fields,
                "profile_updated_at": timezone.now().isoformat()
            },
            "message": f"Profil mis à jour ({len(updated_fields)} champ{'s' if len(updated_fields) > 1 else ''} modifié{'s' if len(updated_fields) > 1 else ''})",
            "profile": serializer.data
        })

@extend_schema(tags=["Accounts"])
class UserProfileView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Accounts"], responses=UserProfileSerializer)
    def get(self, request):
        serializer = UserProfileSerializer(request.user)
        return Response({
            "status": "success",
            "message": "Profil capitaine mis à jour",
            "profile": serializer.data
        })

    @extend_schema(tags=["Accounts"], request=UserProfileSerializer, responses=UserProfileSerializer)
    def patch(self, request):
        """
        PATCH profil unique : redirige vers le PATCH spécifique selon le type d'utilisateur
        """
        user = request.user
        if hasattr(user, 'client'):
            return self._patch_client_profile(request)
        elif hasattr(user, 'captain'):
            return self._patch_captain_profile(request)
        elif hasattr(user, 'establishment'):
            return self._patch_establishment_profile(request)
        else:
            return Response({'error': 'Type de profil non supporté'}, status=status.HTTP_400_BAD_REQUEST)

    def _patch_client_profile(self, request):
        user = request.user
        client = user.client
        data = request.data.copy()
        allowed_user_fields = {'first_name', 'last_name', 'phone_number', 'profile_picture', 'username'}
        allowed_client_fields = {'date_of_birth', 'nationality', 'preferred_language', 'emergency_contact_name', 'emergency_contact_phone'}
        user_data = {k: v for k, v in data.items() if k in allowed_user_fields}
        client_data = data.get('client_profile', {})
        client_data = {k: v for k, v in client_data.items() if k in allowed_client_fields}
        for k, v in user_data.items():
            setattr(user, k, v)
        user.save()
        for k, v in client_data.items():
            setattr(client, k, v)
        client.save()
        updated_fields = []
        for k, v in user_data.items():
            updated_fields.append(k)
        for k, v in client_data.items():
            updated_fields.append(f"client_profile.{k}")
        serializer = UserProfileSerializer(request.user)
        return Response({
            "status": "success",
            "data": {
                "updated_fields": updated_fields,
                "profile_updated_at": timezone.now().isoformat()
            },
            "message": f"Profil mis à jour ({len(updated_fields)} champ{'s' if len(updated_fields) > 1 else ''} modifié{'s' if len(updated_fields) > 1 else ''})",
            "profile": serializer.data
        })

    def _patch_captain_profile(self, request):
        user = request.user
        captain = user.captain
        data = request.data.copy()
        allowed_user_fields = {'first_name', 'last_name', 'phone_number', 'profile_picture'}
        allowed_captain_fields = {'experience', 'certifications', 'availability_status', 'average_rating', 'total_trips', 'license_number', 'years_of_experience', 'rate_per_hour'}
        allowed_boat_fields = {'name', 'registration_number', 'boat_type', 'capacity', 'color', 'fuel_type', 'fuel_consumption', 'photos', 'zone_served', 'radius', 'is_available'}
        user_data = {k: v for k, v in data.items() if k in allowed_user_fields}
        captain_data = data.get('captain_profile', {})
        captain_data = {k: v for k, v in captain_data.items() if k in allowed_captain_fields}
        boat_data = data.get('boat', {})
        boat_data = {k: v for k, v in boat_data.items() if k in allowed_boat_fields}
        for k, v in user_data.items():
            setattr(user, k, v)
        user.save()
        for k, v in captain_data.items():
            setattr(captain, k, v)
        captain.save()
        boat = getattr(captain, 'boat', None)
        updated_fields = []
        for k, v in user_data.items():
            updated_fields.append(k)
        for k, v in captain_data.items():
            updated_fields.append(f"captain_profile.{k}")
        if boat_data:
            if not boat:
                updated_fields.append("boat.created")
            else:
                for k, v in boat_data.items():
                    if k in allowed_boat_fields:
                        updated_fields.append(f"boat.{k}")
        if boat and boat_data:
            for k, v in boat_data.items():
                setattr(boat, k, v)
            boat.save()
        serializer = UserProfileSerializer(request.user)
        return Response({
            "status": "success",
            "data": {
                "updated_fields": updated_fields,
                "profile_updated_at": timezone.now().isoformat()
            },
            "message": f"Profil mis à jour ({len(updated_fields)} champ{'s' if len(updated_fields) > 1 else ''} modifié{'s' if len(updated_fields) > 1 else ''})",
            "profile": serializer.data
        })

    def _patch_establishment_profile(self, request):
        user = request.user
        establishment = user.establishment
        data = request.data.copy()
        allowed_user_fields = {'phone_number'}
        allowed_est_fields = {'name', 'type', 'address', 'description', 'main_photo', 'secondary_photos', 'website', 'has_boatman'}
        user_data = {k: v for k, v in data.items() if k in allowed_user_fields}
        est_data = {k: v for k, v in data.items() if k in allowed_est_fields}
        for k, v in user_data.items():
            setattr(user, k, v)
        user.save()
        for k, v in est_data.items():
            setattr(establishment, k, v)
        establishment.save()
        updated_fields = []
        for k, v in user_data.items():
            updated_fields.append(k)
        for k, v in est_data.items():
            updated_fields.append(f"establishment.{k}")
        if est_data.get('has_boatman', False) and 'boatman' in data:
            boatman_data = data['boatman']
            # Ici, tu peux ajouter la logique pour créer ou mettre à jour le batelier et son bateau si besoin
            # ...
        serializer = UserProfileSerializer(request.user)
        return Response({
            "status": "success",
            "data": {
                "updated_fields": updated_fields,
                "profile_updated_at": timezone.now().isoformat()
            },
            "message": f"Profil mis à jour ({len(updated_fields)} champ{'s' if len(updated_fields) > 1 else ''} modifié{'s' if len(updated_fields) > 1 else ''})",
            "profile": serializer.data
        })

    def _handle_password_change(self, request):
        """Handle password change with validation"""
        new_password = request.data.get('new_password')
        confirm_password = request.data.get('confirm_password')

        # Validations
        if not new_password or not confirm_password:
            return Response({
                'status': 'error',
                'message': 'Nouveau mot de passe et confirmation requis'
            }, status=status.HTTP_400_BAD_REQUEST)

        if new_password != confirm_password:
            return Response({
                'status': 'error',
                'message': 'Les mots de passe ne correspondent pas'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Django password validation
        try:
            validate_password(new_password, request.user)
        except ValidationError as e:
            return Response({
                'status': 'error',
                'message': '; '.join(e.messages)
            }, status=status.HTTP_400_BAD_REQUEST)

        # Update password
        request.user.set_password(new_password)
        request.user.save()

        # Maintain session
        update_session_auth_hash(request, request.user)

        return Response({
            'status': 'success',
            'message': 'Mot de passe mis à jour avec succès'
        })

    def _handle_phone_update(self, request):
        """Handle phone number update with validation"""
        phone_number = request.data.get('phone_number')

        if not phone_number:
            return Response({
                'status': 'error',
                'message': 'Numéro de téléphone requis'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Phone validation (simple)
        phone_pattern = r'^\+?[1-9]\d{1,14}$'
        if not re.match(phone_pattern, phone_number):
            return Response({
                'status': 'error',
                'message': 'Format de numéro de téléphone invalide'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Update phone
        request.user.phone_number = phone_number
        request.user.save()

        return Response({
            'status': 'success',
            'message': 'Numéro de téléphone mis à jour avec succès'
        })

    def _handle_email_update(self, request):
        """Handle email update with validation"""
        email = request.data.get('email')

        if not email:
            return Response({
                'status': 'error',
                'message': 'Adresse email requise'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Email validation
        try:
            validate_email(email)
        except ValidationError:
            return Response({
                'status': 'error',
                'message': 'Format d\'email invalide'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check uniqueness
        if User.objects.filter(email=email).exclude(id=request.user.id).exists():
            return Response({
                'status': 'error',
                'message': 'Cette adresse email est déjà utilisée'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Update email
        request.user.email = email
        request.user.save()

        return Response({
            'status': 'success',
            'message': 'Email mis à jour avec succès'
        })

@extend_schema(tags=["Accounts"], responses=ClientSerializer(many=True))
class ClientListView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Accounts"], responses=ClientSerializer(many=True))
    def get(self, request):
        clients = Client.objects.all()
        serializer = ClientSerializer(clients, many=True)
        return Response(serializer.data)

@extend_schema(tags=["Accounts"], responses=CaptainSerializer(many=True))
class CaptainListView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Accounts"], responses=CaptainSerializer(many=True))
    def get(self, request):
        captains = Captain.objects.all()
        serializer = CaptainSerializer(captains, many=True)
        return Response(serializer.data)

@extend_schema(tags=["Accounts"], responses=EstablishmentSerializer(many=True))
class EstablishmentListView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Accounts"], responses=EstablishmentSerializer(many=True))
    def get(self, request):
        establishments = Establishment.objects.all()
        serializer = EstablishmentSerializer(establishments, many=True)
        return Response(serializer.data)

@extend_schema(tags=["Accounts"], responses=UserProfileSerializer)
class UserDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Accounts"], responses=UserProfileSerializer)
    def get(self, request, pk):
        user = get_object_or_404(User, pk=pk)
        serializer = UserProfileSerializer(user)
        return Response(serializer.data)

@extend_schema(tags=["Accounts"], responses=CaptainSerializer)
class CaptainDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Accounts"], responses=CaptainSerializer)
    def get(self, request, pk):
        captain = get_object_or_404(Captain, pk=pk)
        serializer = CaptainSerializer(captain)
        return Response(serializer.data)

    def patch(self, request, pk):
        captain = get_object_or_404(Captain, pk=pk)
        if captain.user != request.user and not request.user.is_staff:
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = CaptainSerializer(
            captain,
            data=request.data,
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@extend_schema(tags=["Accounts"], responses=EstablishmentSerializer)
class EstablishmentDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Accounts"], responses=EstablishmentSerializer)
    def get(self, request, pk):
        establishment = get_object_or_404(Establishment, pk=pk)
        serializer = EstablishmentSerializer(establishment)
        return Response(serializer.data)

    def patch(self, request, pk):
        establishment = get_object_or_404(Establishment, pk=pk)
        if establishment.user != request.user and not request.user.is_staff:
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = EstablishmentSerializer(
            establishment,
            data=request.data,
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
