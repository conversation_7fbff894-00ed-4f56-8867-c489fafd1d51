"""
Module de configuration des URLs de test pour l'application payments.

Ce module définit les routes URL pour les tests de paiement avec Stripe.
"""

from django.urls import path
from .views_test import (
    stripe_test_view, payment_success_view, payment_cancel_view,
    create_payment_intent_view, create_checkout_view
)

urlpatterns = [
    # Pages de test Stripe
    path('', stripe_test_view, name='stripe-test'),
    path('success/', payment_success_view, name='payment-success'),
    path('cancel/', payment_cancel_view, name='payment-cancel'),
    
    # API pour les tests Stripe
    path('create-payment-intent/', create_payment_intent_view, name='create-payment-intent'),
    path('create-checkout/', create_checkout_view, name='create-checkout'),
]
