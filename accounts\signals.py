from django.db.models.signals import post_save
from django.dispatch import receiver
from accounts.models import Captain
from boats.models import Boat
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Captain)
def create_boat_for_captain(sender, instance, created, **kwargs):
    # Vérifier si c'est une nouvelle création et si le capitaine n'a pas encore de bateaux
    if created and not instance.boats.exists():
        try:
            logger.info(f"Création automatique d'un bateau pour le capitaine {instance.user.id}")
            # Créer un bateau par défaut pour le capitaine
            Boat.objects.create(
                captain=instance,
                name="Bateau par défaut",
                registration_number=f"AUTO-{instance.user.id}",
                capacity=4,
                boat_type="CLASSIC",
                fuel_type="DIESEL",
                fuel_consumption=10.0,
                is_available=True
            )
        except Exception as e:
            logger.error(f"Erreur lors de la création automatique du bateau: {str(e)}")

