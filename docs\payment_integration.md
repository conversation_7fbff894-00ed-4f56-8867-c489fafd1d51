# Documentation d'Intégration des Paiements

## Introduction

Ce document décrit l'intégration complète du système de paiement Commodore Taxi Boat avec Stripe. Il détaille tous les endpoints disponibles, leurs paramètres et les réponses attendues.

Le système de paiement Commodore prend en charge les fonctionnalités suivantes :

- Paiements pour les courses individuelles et partagées
- Paiements pour les navettes
- Paiements pour les services de maintenance
- Paiements pour les promotions
- Portefeuille électronique pour les clients, capitaines et établissements
- Remboursements automatiques et manuels
- Gestion des pourboires
- Compensation carbone

## Configuration Stripe

Le projet utilise Stripe pour traiter les paiements. Les clés d'API sont configurées dans le fichier `.env` :

```
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_PUBLISHABLE_KEY=pk_live_51RQVnNDvUQ1WBV9hUJmQweHLv843ZA9wBH7Rtm3OOyz5OkIY5fHTlK9ma4w3hRrlV01mzLXZkly4YvRZfRuyuyqj00ZzzGPFpz
STRIPE_WEBHOOK_SECRET=whsec_BWTzOok8dhSgadWTZlT8rKXEuYGGTBOw
```

> **Important** : Pour les tests, utilisez des clés de test commençant par `sk_test_` et `pk_test_`.

## Webhooks Stripe

Les webhooks Stripe sont configurés pour recevoir les événements de paiement asynchrones. L'URL de webhook est :

```
https://api.commodore.com/api/payments/webhooks/stripe/
```

Pour les tests en local, vous pouvez utiliser Stripe CLI pour rediriger les webhooks :

```bash
stripe listen --forward-to http://localhost:8000/api/payments/webhooks/stripe/
```

## API de Paiements

### 1. Paiement d'une Course Individuelle

**Endpoint** : `POST /api/payments/rides/{ride_id}/pay/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "payment_method_id": "pm_1234567890",
  "use_wallet": false
}
```

**Réponse 200 OK** :
```json
{
  "id": "pi_1234567890",
  "amount": 50.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T13:45:30Z",
  "payment_method": {
    "id": "pm_1234567890",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242",
      "exp_month": 12,
      "exp_year": 2025
    }
  },
  "receipt_url": "https://api.commodore.com/api/payments/receipts/pi_1234567890/",
  "ride": {
    "id": 1,
    "start_location": "Vieux Port",
    "end_location": "Plage des Catalans",
    "distance": 2.5,
    "duration": 15,
    "price": 50.0
  },
  "wallet": {
    "previous_balance": 100.0,
    "current_balance": 100.0
  }
}
```

### 2. Paiement Partagé d'une Course

**Endpoint** : `POST /api/payments/rides/{ride_id}/shared_pay/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "payment_method_id": "pm_1234567890",
  "invitation_token": "inv_abcdefg"
}
```

**Réponse 200 OK** :
```json
{
  "id": "pi_1234567890",
  "amount": 25.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:05:45Z",
  "ride": {
    "id": 1,
    "total_amount": 100.0,
    "amount_paid": 75.0,
    "amount_remaining": 25.0,
    "payment_status": "PARTIALLY_PAID",
    "participants": [
      {
        "user_id": 8,
        "name": "Marie Martin",
        "amount_paid": 50.0,
        "payment_status": "PAID"
      },
      {
        "user_id": 12,
        "name": "Pierre Dupont",
        "amount_paid": 25.0,
        "payment_status": "PAID"
      },
      {
        "invitation_email": "<EMAIL>",
        "amount_paid": 0.0,
        "payment_status": "PENDING",
        "invitation_status": "SENT"
      }
    ]
  }
}
```

### 3. Paiement de Réservation de Navette

**Endpoint** : `POST /api/payments/shuttles/{shuttle_id}/pay/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "payment_method_id": "pm_1234567890",
  "seats": 2,
  "passenger_ids": ["p_1", "p_2"],
  "passenger_names": ["John Doe", "Jane Doe"],
  "special_requests": "Besoin d'assistance pour les bagages"
}
```

**Réponse 200 OK** :
```json
{
  "id": "pi_1234567890",
  "amount": 75.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:00:30Z",
  "payment_method": {
    "id": "pm_1234567890",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242",
      "exp_month": 12,
      "exp_year": 2025
    }
  },
  "receipt_url": "https://api.commodore.com/api/payments/receipts/pi_1234567890/",
  "shuttle": {
    "id": 5,
    "name": "Saint-Tropez - Pampelonne Express",
    "departure": "Port de Saint-Tropez",
    "arrival": "Plage de Pampelonne",
    "departure_time": "2025-06-01T10:00:00Z",
    "price_per_seat": 37.5,
    "total_seats": 12,
    "available_seats": 8
  },
  "seats": 2,
  "seat_numbers": ["A3", "A4"],
  "wallet": {
    "previous_balance": 100.0,
    "current_balance": 25.0
  }
}
```

### 4. Paiement de Services de Maintenance

**Endpoint** : `POST /api/payments/maintenance/{maintenance_id}/pay/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "payment_method_id": "pm_1234567890"
}
```

**Réponse 200 OK** :
```json
{
  "id": "pi_1234567890",
  "amount": 250.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:10:15Z",
  "payment_method": {
    "id": "pm_1234567890",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242",
      "exp_month": 12,
      "exp_year": 2025
    }
  },
  "maintenance": {
    "id": 3,
    "boat_id": 1,
    "boat_name": "Blue Wave",
    "type": "ROUTINE",
    "description": "Entretien moteur et coque",
    "date": "2025-05-25T09:00:00Z",
    "status": "COMPLETED"
  },
  "invoice_url": "https://api.commodore.com/api/payments/invoices/pi_1234567890/"
}
```

### 5. Paiement pour la Promotion de Services

**Endpoint** : `POST /api/payments/promotions/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "payment_method_id": "pm_1234567890",
  "promotion_type": "FEATURED_LISTING",
  "duration_days": 30,
  "target_type": "CAPTAIN",
  "target_id": 5
}
```

**Réponse 200 OK** :
```json
{
  "id": "pi_1234567890",
  "amount": 99.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:15:30Z",
  "promotion": {
    "id": 2,
    "type": "FEATURED_LISTING",
    "start_date": "2025-05-25T14:15:30Z",
    "end_date": "2025-06-24T14:15:30Z",
    "status": "ACTIVE",
    "target": {
      "type": "CAPTAIN",
      "id": 5,
      "name": "Jean Dupont"
    }
  },
  "features": [
    "Mise en avant dans les résultats de recherche",
    "Badge 'Recommandé'",
    "Visibilité dans la section 'Top capitaines'"
  ]
}
```

### 6. Gestion du Portefeuille

#### Voir le Solde

**Endpoint** : `GET /api/payments/wallet/`

**Headers** :
```
Authorization: Bearer {token}
```

**Réponse 200 OK** :
```json
{
  "balance": 100.0,
  "currency": "eur",
  "transactions": [
    {
      "id": 1,
      "type": "credit",
      "amount": 50.0,
      "description": "Remboursement trajet #123",
      "created_at": "2025-05-24T00:00:00Z"
    }
  ]
}
```

#### Ajouter des Crédits

**Endpoint** : `POST /api/payments/wallet/add_credits/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "amount": 50.0,
  "payment_method_id": "pm_1234567890"
}
```

**Réponse 200 OK** :
```json
{
  "transaction_id": "pi_1234567890",
  "new_balance": 150.0
}
```

### 7. Paiement d'une Maintenance

**Endpoint** : `POST /api/payments/maintenance/{maintenance_id}/pay/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "payment_method_id": "pm_1234567890",
  "use_wallet": false
}
```

**Réponse 200 OK** :
```json
{
  "id": "pi_1234567890",
  "amount": 150.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:30:00Z",
  "payment_method": {
    "id": "pm_1234567890",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242"
    }
  },
  "maintenance": {
    "id": 5,
    "type": "REPAIR",
    "status": "SCHEDULED"
  }
}
```

### 8. Paiement d'une Promotion

**Endpoint** : `POST /api/payments/promotions/{promotion_id}/pay/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "payment_method_id": "pm_1234567890",
  "use_wallet": false
}
```

**Réponse 200 OK** :
```json
{
  "id": "pi_1234567890",
  "amount": 75.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:30:00Z",
  "payment_method": {
    "id": "pm_1234567890",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242"
    }
  },
  "promotion": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "type": "FEATURED_LISTING",
    "status": "PENDING",
    "target_type": "CAPTAIN"
  }
}
```

### 9. Paiement d'une Course Partagée

**Endpoint** : `POST /api/payments/shared/{shared_trip_id}/pay/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "payment_method_id": "pm_1234567890",
  "use_wallet": false,
  "passenger_count": 2
}
```

**Réponse 200 OK** :
```json
{
  "id": "pi_1234567890",
  "amount": 40.0,
  "currency": "eur",
  "status": "succeeded",
  "created_at": "2025-05-25T14:30:00Z",
  "payment_method": {
    "id": "pm_1234567890",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242"
    }
  },
  "shared_trip": {
    "id": 3,
    "total_price": 120.0,
    "price_per_person": 40.0,
    "max_passengers": 3,
    "current_passengers": 2
  }
}
```

### 10. Remboursements

**Endpoint** : `POST /api/payments/transactions/{id}/refund/`

**Headers** :
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body** :
```json
{
  "amount": 50.0,
  "reason": "Annulation du trajet"
}
```

**Réponse 200 OK** :
```json
{
  "id": "re_1234567890",
  "amount": 50.0,
  "status": "succeeded",
  "created_at": "2025-05-24T00:00:00Z"
}
```

## Tester les Paiements avec Stripe

### Cartes de Test

Pour tester les paiements, vous pouvez utiliser ces cartes de test Stripe :

| Numéro de carte      | Description                           |
|----------------------|---------------------------------------|
| 4242 4242 4242 4242  | Paiement réussi                       |
| 4000 0000 0000 0002  | Refusé (fonds insuffisants)           |
| 4000 0000 0000 0027  | Refusé (carte expirée)                |
| 4000 0000 0000 3220  | 3D Secure 2 (authentification requise)|

Utilisez n'importe quelle date d'expiration future, n'importe quel CVC à 3 chiffres, et n'importe quel code postal.

### Étapes pour Tester un Paiement

1. **Initialiser un paiement depuis votre frontend** :
   ```javascript
   const stripe = Stripe('pk_test_...');
   const elements = stripe.elements();
   const card = elements.create('card');
   card.mount('#card-element');

   // Gérer la soumission du formulaire
   form.addEventListener('submit', async (event) => {
     event.preventDefault();
     const {paymentMethod, error} = await stripe.createPaymentMethod({
       type: 'card',
       card: card,
     });

     if (error) {
       // Gérer l'erreur
     } else {
       // Envoyer l'ID de la méthode de paiement à votre backend
       const response = await fetch('/api/payments/rides/1/pay/', {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
           'Authorization': 'Bearer YOUR_TOKEN'
         },
         body: JSON.stringify({
           payment_method_id: paymentMethod.id
         })
       });
       
       const result = await response.json();
       // Gérer la réponse
     }
   });
   ```

2. **Vérifier les événements de webhook** :
   - Utilisez la commande `stripe listen` pour capturer les événements en local
   - Vérifiez les logs du serveur pour confirmer que les webhooks sont bien traités

3. **Vérifier les transactions dans le dashboard Stripe** :
   - Connectez-vous à votre [dashboard Stripe](https://dashboard.stripe.com/)
   - Allez dans "Payments" pour voir les transactions
   - Vérifiez que les métadonnées sont correctement enregistrées

## Bonnes Pratiques et Résolution de Problèmes

### Sécurité
- Ne stockez jamais les données de carte complètes
- Utilisez toujours HTTPS pour les requêtes API
- Vérifiez la signature des webhooks
- Validez toutes les entrées utilisateur côté serveur

### Résolution des Problèmes Courants

1. **Webhook non reçu** :
   - Vérifiez que l'URL de webhook est accessible depuis Internet
   - Vérifiez la signature du webhook
   - Consultez les logs de webhook dans le dashboard Stripe

2. **Paiement refusé** :
   - Vérifiez le code d'erreur retourné par Stripe
   - Assurez-vous que la carte de test utilisée est valide
   - Vérifiez que les informations de facturation sont correctes

3. **Problèmes de remboursement** :
   - Les remboursements ne sont possibles que pour les paiements capturés
   - Vérifiez que le paiement n'a pas déjà été remboursé
   - Le remboursement ne peut pas dépasser le montant du paiement original
