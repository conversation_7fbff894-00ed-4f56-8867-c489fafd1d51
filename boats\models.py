from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from accounts.models import Captain, Establishment

class Boat(models.Model):
    class FuelTypes(models.TextChoices):
        DIESEL = 'DIESEL', _('Diesel')
        GASOLINE = 'GASOLINE', _('Essence')
        ELECTRIC = 'ELECTRIC', _('Électrique')
        HYBRID = 'HYBRID', _('Hybride')

    class BoatTypes(models.TextChoices):
        "Catégorie de bateau"
        BLUE = 'BLUE', _('Blue')
        CLASSIC = 'CLASSIC', _('Classic')
        LUXE = 'LUXE', _('Luxe')
        BOAT_XL = 'BOAT_XL', _('Boat XL')
        NAVETTE = 'NAVETTE', _('Navette')



        
    name = models.CharField(_('nom'), max_length=100, null=True, blank=True)
    registration_number = models.Char<PERSON>ield(_('numéro d\'immatriculation'), max_length=50, unique=True, null=True, blank=True)
    color = models.CharField(_('couleur'), max_length=50, null=True, blank=True)
    capacity = models.IntegerField(_('capacité'), null=True, blank=True)
    boat_type = models.CharField(_('type de bateau'), max_length=20, choices=BoatTypes.choices, null=True, blank=True)
    fuel_type = models.CharField(_('type de carburant'), max_length=20, choices=FuelTypes.choices, null=True, blank=True)
    fuel_consumption = models.DecimalField(_('consommation de carburant'), max_digits=5, decimal_places=2, help_text=_('L/100km'), null=True, blank=True)
    photos = models.JSONField(_('photos'), default=list, help_text=_('Liste de liens (max 4)'))
    features = models.JSONField(_('caractéristiques'), default=list, help_text=_('Liste des équipements'), null=True, blank=True)
    year = models.IntegerField(_('année de fabrication'), null=True, blank=True)
    manufacturer = models.CharField(_('fabricant'), max_length=100, null=True, blank=True)
    model = models.CharField(_('modèle'), max_length=100, null=True, blank=True)
    length = models.DecimalField(_('longueur (m)'), max_digits=5, decimal_places=2, null=True, blank=True)
    zone_served = models.CharField(_('zone desservie'), max_length=255, blank=True, help_text=_('Zone géographique desservie'))
    radius = models.PositiveIntegerField(_('rayon (km)'), default=10, validators=[MinValueValidator(1), MaxValueValidator(100)], help_text=_('Rayon de service en km (1-100)'))
    average_speed_kmh = models.DecimalField(_('vitesse moyenne (km/h)'), max_digits=5, decimal_places=2, default=33, null=True, blank=True, help_text=_('Vitesse de croisière utilisée pour estimer l\'heure d\'arrivée'))
    captain = models.ForeignKey(Captain, on_delete=models.CASCADE, related_name='boats', null=True, blank=True)
    establishment = models.ForeignKey(Establishment, on_delete=models.CASCADE, related_name='boats', null=True, blank=True)
    is_available = models.BooleanField(_('disponible'), default=True)
    last_maintenance = models.DateField(_('dernière maintenance'), null=True, blank=True)
    next_maintenance = models.DateField(_('prochaine maintenance'), null=True, blank=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)

    class Meta:
        verbose_name = _('bateau')
        verbose_name_plural = _('bateaux')

    def __str__(self):
        return f'{self.name} ({self.registration_number})'

    def clean(self):
        # Un bateau peut maintenant appartenir à un établissement ET être opéré par un capitaine
        # Cette contrainte est supprimée pour permettre le workflow des navettes
        pass

    @property
    def status(self):
        return "AVAILABLE" if self.is_available else "UNAVAILABLE"

    @property
    def location(self):
        return self.zone_served

class MaintenanceRecord(models.Model):
    class MaintenanceTypes(models.TextChoices):
        ROUTINE = 'ROUTINE', _('Maintenance de routine')
        REPAIR = 'REPAIR', _('Réparation')
        INSPECTION = 'INSPECTION', _('Inspection')
        EMERGENCY = 'EMERGENCY', _('Urgence')

    boat = models.ForeignKey(Boat, on_delete=models.CASCADE, related_name='maintenance_records')
    maintenance_type = models.CharField(_('type de maintenance'), max_length=20, choices=MaintenanceTypes.choices)
    description = models.TextField(_('description'))
    cost = models.DecimalField(_('coût'), max_digits=10, decimal_places=2)
    performed_at = models.DateTimeField(_('effectué le'))
    performed_by = models.CharField(_('effectué par'), max_length=100)
    notes = models.TextField(_('notes'), blank=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)

    class Meta:
        verbose_name = _('registre de maintenance')
        verbose_name_plural = _('registres de maintenance')
        ordering = ['-performed_at']

    def __str__(self):
        return f'{self.boat.name} - {self.maintenance_type} - {self.performed_at.date()}'
