"""
Module d'utilitaires Firebase pour l'application notifications.

Ce module contient les fonctions utilitaires pour l'intégration avec Firebase Cloud Messaging (FCM),
permettant l'envoi de notifications push aux appareils mobiles.
"""

import json
import logging
import requests
from django.conf import settings
from django.core.cache import cache
from .models import Device

# Configuration du logger
logger = logging.getLogger(__name__)

# Clé de cache pour le token d'accès Firebase
FIREBASE_TOKEN_CACHE_KEY = 'firebase_access_token'

def get_firebase_access_token():
    """
    Récupère un token d'accès Firebase à partir des identifiants de service.
    
    Cette fonction utilise le cache pour éviter de demander un nouveau token à chaque requête.
    
    Returns:
        str: Token d'accès Firebase
    """
    # Vérifier si le token est dans le cache
    token = cache.get(FIREBASE_TOKEN_CACHE_KEY)
    if token:
        return token
    
    try:
        # Charger les identifiants Firebase
        credentials_path = settings.FIREBASE_CREDENTIALS
        with open(credentials_path, 'r') as f:
            credentials = json.load(f)
        
        # Obtenir un token d'accès
        auth_url = f"https://oauth2.googleapis.com/token"
        payload = {
            'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'assertion': credentials.get('private_key')
        }
        response = requests.post(auth_url, data=payload)
        response.raise_for_status()
        
        # Extraire le token
        token_data = response.json()
        token = token_data.get('access_token')
        expires_in = token_data.get('expires_in', 3600)  # Par défaut 1 heure
        
        # Mettre en cache le token (avec une durée légèrement inférieure à l'expiration)
        cache.set(FIREBASE_TOKEN_CACHE_KEY, token, expires_in - 300)
        
        return token
    except Exception as e:
        logger.error(f"Erreur lors de l'obtention du token Firebase: {str(e)}")
        return None

def send_fcm_message(tokens, title, body, data=None):
    """
    Envoie un message FCM à un ou plusieurs appareils.
    
    Args:
        tokens (list): Liste des tokens FCM des appareils
        title (str): Titre de la notification
        body (str): Corps de la notification
        data (dict, optional): Données supplémentaires à envoyer
        
    Returns:
        dict: Résultat de l'envoi
    """
    if not tokens:
        return {'success': False, 'error': 'Aucun token fourni'}
    
    # Obtenir le token d'accès
    access_token = get_firebase_access_token()
    if not access_token:
        return {'success': False, 'error': 'Impossible d\'obtenir le token d\'accès Firebase'}
    
    # Préparer les données
    data = data or {}
    
    # Préparer le message
    message = {
        'notification': {
            'title': title,
            'body': body
        },
        'data': data,
        'tokens': tokens,  # Pour plusieurs appareils
        'android': {
            'priority': 'high',
            'notification': {
                'sound': 'default'
            }
        },
        'apns': {
            'payload': {
                'aps': {
                    'sound': 'default',
                    'badge': 1
                }
            }
        }
    }
    
    try:
        # Envoyer le message
        project_id = settings.FIREBASE_PROJECT_ID
        url = f"https://fcm.googleapis.com/v1/projects/{project_id}/messages:send"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        response = requests.post(url, headers=headers, json={'message': message})
        response.raise_for_status()
        
        return {
            'success': True,
            'response': response.json()
        }
    except requests.exceptions.HTTPError as e:
        error_info = e.response.json() if e.response.content else {'error': str(e)}
        logger.error(f"Erreur HTTP lors de l'envoi du message FCM: {error_info}")
        return {
            'success': False,
            'error': str(e),
            'details': error_info
        }
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi du message FCM: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }

def register_device(user, token, device_type, device_name=None):
    """
    Enregistre un appareil pour les notifications push.
    
    Args:
        user: Utilisateur propriétaire de l'appareil
        token (str): Token FCM de l'appareil
        device_type (str): Type d'appareil (android, ios, web)
        device_name (str, optional): Nom de l'appareil
        
    Returns:
        dict: Résultat de l'enregistrement
    """
    try:
        # Vérifier si l'appareil existe déjà
        device, created = Device.objects.update_or_create(
            user=user,
            token=token,
            defaults={
                'device_type': device_type,
                'device_name': device_name or ''
            }
        )
        
        return {
            'success': True,
            'created': created,
            'device_id': device.id
        }
    except Exception as e:
        logger.error(f"Erreur lors de l'enregistrement de l'appareil: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }

def unregister_device(user, token):
    """
    Supprime un appareil pour les notifications push.
    
    Args:
        user: Utilisateur propriétaire de l'appareil
        token (str): Token FCM de l'appareil
        
    Returns:
        dict: Résultat de la suppression
    """
    try:
        # Supprimer l'appareil
        count, _ = Device.objects.filter(user=user, token=token).delete()
        
        return {
            'success': True,
            'deleted': count > 0
        }
    except Exception as e:
        logger.error(f"Erreur lors de la suppression de l'appareil: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }
