from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Boat, MaintenanceRecord
from unfold.admin import ModelAdmin

@admin.register(Boat)
class BoatAdmin(ModelAdmin):
    list_display = ('name', 'registration_number', 'captain', 'establishment', 'is_available', 'fuel_type', 'capacity')
    list_filter = ('is_available', 'fuel_type', 'captain', 'establishment')
    search_fields = ('name', 'registration_number', 'captain__user__email', 'establishment__name')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('Informations générales'), {
            'fields': ('name', 'registration_number', 'color', 'capacity', 'photos')
        }),
        (_('Assignation'), {
            'fields': ('captain', 'establishment', 'is_available')
        }),
        (_('Carburant'), {
            'fields': ('fuel_type', 'fuel_consumption')
        }),
        (_('Maintenance'), {
            'fields': ('last_maintenance', 'next_maintenance')
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(MaintenanceRecord)
class MaintenanceRecordAdmin(ModelAdmin):
    list_display = ('boat', 'maintenance_type', 'performed_at', 'performed_by', 'cost')
    list_filter = ('maintenance_type', 'performed_at', 'boat')
    search_fields = ('boat__name', 'boat__registration_number', 'description', 'performed_by')
    date_hierarchy = 'performed_at'
    fieldsets = (
        (_('Informations générales'), {
            'fields': ('boat', 'maintenance_type', 'description')
        }),
        (_('Détails'), {
            'fields': ('cost', 'performed_at', 'performed_by')
        })
    )
