"""
Configuration complète de Swagger/OpenAPI pour Commodore.
Organisation par application avec documentation détaillée de tous les endpoints.
"""

from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes
from rest_framework import serializers
from decimal import Decimal


# ============================================================================
# CONFIGURATION GÉNÉRALE SWAGGER
# ============================================================================

SWAGGER_SETTINGS = {
    'TITLE': 'Commodore API',
    'DESCRIPTION': '''
    # 🚤 API Commodore - Plateforme de Transport Maritime

    ## 📋 Vue d'ensemble
    L'API Commodore permet de gérer une plateforme complète de transport maritime avec :
    - **Gestion des utilisateurs** (Clients, Capitaines, Établissements)
    - **Système de réservation** de courses et navettes
    - **Paiements sécurisés** avec portefeuilles virtuels
    - **Suivi en temps réel** des trajets
    - **Notifications** push et email
    - **Chat** intégré entre utilisateurs

    ## 🔐 Authentification
    L'API utilise l'authentification par token. Incluez le header :
    ```
    Authorization: Token votre_token_ici
    ```

    ## 📱 Applications
    - **Accounts** : Gestion des comptes utilisateurs
    - **Trips** : Réservation et gestion des courses
    - **Payments** : Paiements et portefeuilles
    - **Boats** : Gestion des bateaux
    - **Chat** : Messagerie instantanée
    - **Notifications** : Système de notifications

    ## 🌐 Environnements
    - **Production** : https://api.commodore.com
    - **Staging** : https://staging-api.commodore.com
    - **Development** : http://localhost:8000

    ## 📞 Support
    - Email : <EMAIL>
    - Documentation : https://docs.commodore.com
    ''',
    'VERSION': '2.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'TAGS': [
        {
            'name': '👤 Accounts',
            'description': 'Gestion des comptes utilisateurs (inscription, connexion, profils)'
        },
        {
            'name': '🚤 Trips',
            'description': 'Réservation et gestion des courses maritimes'
        },
        {
            'name': '💰 Payments',
            'description': 'Paiements, portefeuilles et transactions financières'
        },
        {
            'name': '⛵ Boats',
            'description': 'Gestion des bateaux et maintenance'
        },
        {
            'name': '💬 Chat',
            'description': 'Messagerie instantanée entre utilisateurs'
        },
        {
            'name': '🔔 Notifications',
            'description': 'Système de notifications push et email'
        },
        {
            'name': '🏢 Establishments',
            'description': 'Gestion des établissements partenaires'
        },
        {
            'name': '👨‍✈️ Captains',
            'description': 'Interface et outils pour les capitaines'
        },
        {
            'name': '🛡️ Security',
            'description': 'Endpoints de sécurité et validation'
        }
    ]
}


# ============================================================================
# PARAMÈTRES COMMUNS
# ============================================================================

COMMON_PARAMETERS = {
    'page': OpenApiParameter(
        name='page',
        type=OpenApiTypes.INT,
        location=OpenApiParameter.QUERY,
        description='Numéro de page pour la pagination',
        default=1
    ),
    'page_size': OpenApiParameter(
        name='page_size',
        type=OpenApiTypes.INT,
        location=OpenApiParameter.QUERY,
        description='Nombre d\'éléments par page (max 100)',
        default=20
    ),
    'search': OpenApiParameter(
        name='search',
        type=OpenApiTypes.STR,
        location=OpenApiParameter.QUERY,
        description='Terme de recherche'
    ),
    'ordering': OpenApiParameter(
        name='ordering',
        type=OpenApiTypes.STR,
        location=OpenApiParameter.QUERY,
        description='Champ de tri (préfixer par - pour ordre décroissant)'
    ),
    'trip_id': OpenApiParameter(
        name='trip_id',
        type=OpenApiTypes.INT,
        location=OpenApiParameter.PATH,
        description='ID unique de la course'
    ),
    'user_id': OpenApiParameter(
        name='user_id',
        type=OpenApiTypes.INT,
        location=OpenApiParameter.PATH,
        description='ID unique de l\'utilisateur'
    ),
    'boat_id': OpenApiParameter(
        name='boat_id',
        type=OpenApiTypes.INT,
        location=OpenApiParameter.PATH,
        description='ID unique du bateau'
    )
}


# ============================================================================
# EXEMPLES DE RÉPONSES
# ============================================================================

RESPONSE_EXAMPLES = {
    'success': OpenApiExample(
        'Succès',
        value={
            'status': 'success',
            'message': 'Opération réussie',
            'data': {}
        }
    ),
    'error_400': OpenApiExample(
        'Erreur de validation',
        value={
            'error': 'Données invalides',
            'details': {
                'field_name': ['Ce champ est requis.']
            }
        }
    ),
    'error_401': OpenApiExample(
        'Non autorisé',
        value={
            'error': 'Token d\'authentification requis'
        }
    ),
    'error_403': OpenApiExample(
        'Accès interdit',
        value={
            'error': 'Vous n\'avez pas les permissions nécessaires'
        }
    ),
    'error_404': OpenApiExample(
        'Non trouvé',
        value={
            'error': 'Ressource non trouvée'
        }
    ),
    'error_500': OpenApiExample(
        'Erreur serveur',
        value={
            'error': 'Erreur interne du serveur'
        }
    )
}


# ============================================================================
# SÉRIALISEURS POUR LA DOCUMENTATION
# ============================================================================

class PaginatedResponseSerializer(serializers.Serializer):
    """Réponse paginée standard"""
    count = serializers.IntegerField(help_text="Nombre total d'éléments")
    next = serializers.URLField(allow_null=True, help_text="URL de la page suivante")
    previous = serializers.URLField(allow_null=True, help_text="URL de la page précédente")
    results = serializers.ListField(help_text="Résultats de la page actuelle")


class ErrorResponseSerializer(serializers.Serializer):
    """Réponse d'erreur standard"""
    error = serializers.CharField(help_text="Message d'erreur")
    details = serializers.DictField(required=False, help_text="Détails de l'erreur")


class SuccessResponseSerializer(serializers.Serializer):
    """Réponse de succès standard"""
    status = serializers.CharField(default="success", help_text="Statut de l'opération")
    message = serializers.CharField(help_text="Message de succès")
    data = serializers.DictField(required=False, help_text="Données de réponse")


# ============================================================================
# DÉCORATEURS POUR CHAQUE APPLICATION
# ============================================================================

def accounts_schema(**kwargs):
    """Décorateur pour les endpoints Accounts"""
    return extend_schema(
        tags=['👤 Accounts'],
        **kwargs
    )


def trips_schema(**kwargs):
    """Décorateur pour les endpoints Trips"""
    return extend_schema(
        tags=['🚤 Trips'],
        **kwargs
    )


def payments_schema(**kwargs):
    """Décorateur pour les endpoints Payments"""
    return extend_schema(
        tags=['💰 Payments'],
        **kwargs
    )


def boats_schema(**kwargs):
    """Décorateur pour les endpoints Boats"""
    return extend_schema(
        tags=['⛵ Boats'],
        **kwargs
    )


def chat_schema(**kwargs):
    """Décorateur pour les endpoints Chat"""
    return extend_schema(
        tags=['💬 Chat'],
        **kwargs
    )


def notifications_schema(**kwargs):
    """Décorateur pour les endpoints Notifications"""
    return extend_schema(
        tags=['🔔 Notifications'],
        **kwargs
    )


def establishments_schema(**kwargs):
    """Décorateur pour les endpoints Establishments"""
    return extend_schema(
        tags=['🏢 Establishments'],
        **kwargs
    )


def captains_schema(**kwargs):
    """Décorateur pour les endpoints Captains"""
    return extend_schema(
        tags=['👨‍✈️ Captains'],
        **kwargs
    )


def security_schema(**kwargs):
    """Décorateur pour les endpoints Security"""
    return extend_schema(
        tags=['🛡️ Security'],
        **kwargs
    )


# ============================================================================
# CONFIGURATION DRF-SPECTACULAR
# ============================================================================

SPECTACULAR_SETTINGS = {
    'TITLE': SWAGGER_SETTINGS['TITLE'],
    'DESCRIPTION': SWAGGER_SETTINGS['DESCRIPTION'],
    'VERSION': SWAGGER_SETTINGS['VERSION'],
    'SERVE_INCLUDE_SCHEMA': False,
    'COMPONENT_SPLIT_REQUEST': True,
    'COMPONENT_NO_READ_ONLY_REQUIRED': True,
    'SCHEMA_PATH_PREFIX': '/api/',
    'SCHEMA_PATH_PREFIX_TRIM': True,
    'SERVE_PERMISSIONS': ['rest_framework.permissions.AllowAny'],
    'SERVE_AUTHENTICATION': None,
    'SWAGGER_UI_SETTINGS': {
        'deepLinking': True,
        'persistAuthorization': True,
        'displayOperationId': False,
        'defaultModelsExpandDepth': 2,
        'defaultModelExpandDepth': 2,
        'displayRequestDuration': True,
        'docExpansion': 'none',
        'filter': True,
        'showExtensions': True,
        'showCommonExtensions': True,
        'tryItOutEnabled': True
    },
    'REDOC_UI_SETTINGS': {
        'hideDownloadButton': False,
        'hideHostname': False,
        'hideLoading': False,
        'hideSchemaPattern': True,
        'expandResponses': '200,201',
        'pathInMiddlePanel': True,
        'nativeScrollbars': False,
        'theme': {
            'colors': {
                'primary': {
                    'main': '#1976d2'
                }
            },
            'typography': {
                'fontSize': '14px',
                'lineHeight': '1.5em',
                'code': {
                    'fontSize': '13px'
                }
            }
        }
    },
    'PREPROCESSING_HOOKS': [
        'commodore.swagger_config.preprocess_schema'
    ],
    'POSTPROCESSING_HOOKS': [
        'commodore.swagger_config.postprocess_schema'
    ]
}


def preprocess_schema(endpoints):
    """Préprocessing du schéma pour organiser les endpoints"""
    # Trier les endpoints par tags
    def get_sort_key(endpoint):
        try:
            path, method, callback = endpoint
            if hasattr(callback, 'cls') and hasattr(callback.cls, 'schema'):
                schema = callback.cls.schema
                if hasattr(schema, 'tags') and schema.tags:
                    return schema.tags[0] if isinstance(schema.tags, list) else str(schema.tags)
            return 'zzz_other'  # Mettre à la fin
        except:
            return 'zzz_other'

    return sorted(endpoints, key=get_sort_key)


def postprocess_schema(result, generator, request, public):
    """Post-processing du schéma pour ajouter des métadonnées"""
    # Ajouter des informations de contact et licence
    result['info']['contact'] = {
        'name': 'Support Commodore',
        'email': '<EMAIL>',
        'url': 'https://commodore.com/support'
    }

    result['info']['license'] = {
        'name': 'Propriétaire',
        'url': 'https://commodore.com/license'
    }

    # Ajouter des serveurs
    result['servers'] = [
        {
            'url': 'https://api.commodore.com',
            'description': 'Serveur de production'
        },
        {
            'url': 'https://staging-api.commodore.com',
            'description': 'Serveur de staging'
        },
        {
            'url': 'http://localhost:8000',
            'description': 'Serveur de développement'
        }
    ]

    return result
