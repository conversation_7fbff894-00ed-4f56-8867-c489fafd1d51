"""
Test des payloads complets pour les endpoints PATCH.
Vérifie que tous les champs peuvent être modifiés selon la structure documentée.
"""

import os
import sys
import django
import json
from decimal import Decimal
from datetime import datetime, date

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token

from accounts.models import Client, Captain, Establishment
from boats.models import Boat

User = get_user_model()


class CompletePayloadTester:
    """Testeur pour les payloads complets des endpoints PATCH"""
    
    def __init__(self):
        self.client = APIClient()
        self.test_results = []
        self.errors = []
        self.test_users = {}
        self.test_tokens = {}
    
    def setup_test_environment(self):
        """Créer l'environnement de test"""
        print("🔧 CONFIGURATION DE L'ENVIRONNEMENT DE TEST PAYLOADS COMPLETS")
        print("-" * 70)
        
        # Créer un client de test
        try:
            client_user = User.objects.get(email='<EMAIL>')
            client_user.delete()
        except User.DoesNotExist:
            pass
        
        client_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestClient123!',
            first_name='Client',
            last_name='PayloadTest',
            type='CLIENT'
        )
        
        Client.objects.create(user=client_user)
        client_token = Token.objects.create(user=client_user)
        
        self.test_users['client'] = client_user
        self.test_tokens['client'] = client_token.key
        
        # Créer un capitaine de test
        try:
            captain_user = User.objects.get(email='<EMAIL>')
            captain_user.delete()
        except User.DoesNotExist:
            pass
        
        captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestCaptain123!',
            first_name='Captain',
            last_name='PayloadTest',
            type='CAPTAIN'
        )
        
        Captain.objects.create(user=captain_user)
        captain_token = Token.objects.create(user=captain_user)
        
        self.test_users['captain'] = captain_user
        self.test_tokens['captain'] = captain_token.key
        
        # Créer un établissement de test
        try:
            establishment_user = User.objects.get(email='<EMAIL>')
            establishment_user.delete()
        except User.DoesNotExist:
            pass
        
        establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestEstablishment123!',
            first_name='Establishment',
            last_name='PayloadTest',
            type='ESTABLISHMENT'
        )
        
        Establishment.objects.create(
            user=establishment_user,
            name="Test Payload Establishment",
            type="HOTEL",
            address="123 Test Street"
        )
        establishment_token = Token.objects.create(user=establishment_user)
        
        self.test_users['establishment'] = establishment_user
        self.test_tokens['establishment'] = establishment_token.key
        
        print(f"✅ Client créé: {client_user.email}")
        print(f"✅ Capitaine créé: {captain_user.email}")
        print(f"✅ Établissement créé: {establishment_user.email}")
    
    def test_client_complete_payload(self):
        """Test du payload complet pour client"""
        print("\n👤 TEST: PAYLOAD COMPLET CLIENT")
        print("-" * 50)

        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.test_tokens["client"]}')

        # Payload complet client selon la documentation
        complete_payload = {
            "first_name": "Jean",
            "last_name": "Dupont",
            "client_profile": {
                "wallet_balance": "150.75",
                "date_of_birth": "1990-05-15",
                "nationality": "Française",
                "preferred_language": "fr",
                "emergency_contact_name": "Marie Dupont",
                "emergency_contact_phone": "+33687654321"
            }
        }

        try:
            response = self.client.patch('/api/profile/', complete_payload, format='json')
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.data, indent=2, ensure_ascii=False)}")

            if response.status_code == 200:
                # Vérifier que les données ont été mises à jour
                user = User.objects.get(id=self.test_users['client'].id)
                client = user.client

                assert user.first_name == "Jean"
                assert user.last_name == "Dupont"
                assert str(client.date_of_birth) == "1990-05-15"
                assert client.nationality == "Française"
                assert client.preferred_language == "fr"

                print("✅ SUCCÈS: Payload complet client traité correctement")
                self.test_results.append("Client complete payload: PASSED")
            else:
                print(f"❌ ÉCHEC: Status {response.status_code}")
                self.test_results.append("Client complete payload: FAILED")
                self.errors.append(f"Client payload failed: {response.data}")

        except Exception as e:
            print(f"❌ ERREUR: {str(e)}")
            self.test_results.append("Client complete payload: FAILED")
            self.errors.append(f"Client payload error: {str(e)}")

    def test_captain_complete_payload(self):
        """Test du payload complet pour capitaine"""
        print("\n⛵ TEST: PAYLOAD COMPLET CAPITAINE")
        print("-" * 50)

        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.test_tokens["captain"]}')

        # Payload complet capitaine selon la documentation
        complete_payload = {
            "first_name": "Pierre",
            "last_name": "Martin",
            "captain_profile": {
                "experience": "10 ans d'expérience maritime professionnelle",
                "license_number": "CAP123456",
                "years_of_experience": 10,
                "certifications": {
                    "permis_cotier": "Permis côtier",
                    "permis_hauturier": "Permis hauturier"
                },
                "specializations": {
                    "navigation_cotiere": "Navigation côtière",
                    "transport_passagers": "Transport de passagers"
                },
                "rate_per_hour": "45.00",
                "rate_per_km": "2.50",
                "availability_status": "AVAILABLE"
            }
        }

        try:
            response = self.client.patch('/api/profile/', complete_payload, format='json')
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.data, indent=2, ensure_ascii=False)}")

            if response.status_code == 200:
                # Vérifier que les données ont été mises à jour
                user = User.objects.get(id=self.test_users['captain'].id)
                captain = user.captain

                assert user.first_name == "Pierre"
                assert user.last_name == "Martin"
                assert captain.experience == "10 ans d'expérience maritime professionnelle"
                assert captain.license_number == "CAP123456"
                assert captain.years_of_experience == 10

                print("✅ SUCCÈS: Payload complet capitaine traité correctement")
                self.test_results.append("Captain complete payload: PASSED")
            else:
                print(f"❌ ÉCHEC: Status {response.status_code}")
                self.test_results.append("Captain complete payload: FAILED")
                self.errors.append(f"Captain payload failed: {response.data}")

        except Exception as e:
            print(f"❌ ERREUR: {str(e)}")
            self.test_results.append("Captain complete payload: FAILED")
            self.errors.append(f"Captain payload error: {str(e)}")

    def test_establishment_complete_payload(self):
        """Test du payload complet pour établissement"""
        print("\n🏢 TEST: PAYLOAD COMPLET ÉTABLISSEMENT")
        print("-" * 50)

        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.test_tokens["establishment"]}')

        # Payload complet établissement selon la documentation
        complete_payload = {
            "first_name": "Hôtel",
            "last_name": "Riviera",
            "establishment_profile": {
                "name": "Hôtel Riviera Updated",
                "type": "HOTEL",
                "address": "123 Avenue de la Mer, 06400 Cannes",
                "description": "Hôtel de luxe avec vue sur mer et services nautiques",
                "business_name": "Riviera Hospitality SAS",
                "business_type": "Hôtellerie",
                "website": "https://hotel-riviera.com",
                "opening_hours": {
                    "monday": "24h/24",
                    "tuesday": "24h/24"
                },
                "services_offered": {
                    "hebergement": "Hébergement",
                    "restaurant": "Restaurant",
                    "spa": "Spa"
                }
            }
        }

        try:
            response = self.client.patch('/api/profile/', complete_payload, format='json')
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.data, indent=2, ensure_ascii=False)}")

            if response.status_code == 200:
                # Vérifier que les données ont été mises à jour
                user = User.objects.get(id=self.test_users['establishment'].id)
                establishment = user.establishment

                assert user.first_name == "Hôtel"
                assert user.last_name == "Riviera"
                assert establishment.name == "Hôtel Riviera Updated"
                assert establishment.type == "HOTEL"
                assert establishment.business_name == "Riviera Hospitality SAS"

                print("✅ SUCCÈS: Payload complet établissement traité correctement")
                self.test_results.append("Establishment complete payload: PASSED")
            else:
                print(f"❌ ÉCHEC: Status {response.status_code}")
                self.test_results.append("Establishment complete payload: FAILED")
                self.errors.append(f"Establishment payload failed: {response.data}")

        except Exception as e:
            print(f"❌ ERREUR: {str(e)}")
            self.test_results.append("Establishment complete payload: FAILED")
            self.errors.append(f"Establishment payload error: {str(e)}")

    def run_all_tests(self):
        """Exécuter tous les tests de payloads complets"""
        print("🚀 DÉMARRAGE DES TESTS DE PAYLOADS COMPLETS")
        print("=" * 70)

        start_time = datetime.now()

        # Configuration
        self.setup_test_environment()

        # Tests principaux
        self.test_client_complete_payload()
        self.test_captain_complete_payload()
        self.test_establishment_complete_payload()

        end_time = datetime.now()

        # Résumé
        print("\n" + "=" * 70)
        print("📊 RÉSUMÉ DES TESTS DE PAYLOADS COMPLETS")
        print("=" * 70)

        passed_tests = [t for t in self.test_results if "PASSED" in t]
        failed_tests = [t for t in self.test_results if "FAILED" in t]

        print(f"✅ Tests réussis: {len(passed_tests)}")
        print(f"❌ Tests échoués: {len(failed_tests)}")
        print(f"⏱️  Temps d'exécution: {(end_time - start_time).total_seconds():.2f} secondes")

        if failed_tests:
            print("\n❌ TESTS ÉCHOUÉS:")
            for test in failed_tests:
                print(f"  - {test}")

        if self.errors:
            print("\n🚨 ERREURS DÉTECTÉES:")
            for error in self.errors:
                print(f"  - {error}")

        if not failed_tests and not self.errors:
            print("\n🎉 TOUS LES TESTS DE PAYLOADS COMPLETS ONT RÉUSSI!")
            print("✅ Les endpoints PATCH supportent les payloads complets")
        else:
            print("\n⚠️  ATTENTION: Des problèmes ont été détectés")
            print("❌ Vérifier les erreurs avant déploiement")

        return len(failed_tests) == 0 and len(self.errors) == 0


if __name__ == "__main__":
    tester = CompletePayloadTester()
    success = tester.run_all_tests()
    
    # Code de sortie pour les scripts automatisés
    sys.exit(0 if success else 1)
