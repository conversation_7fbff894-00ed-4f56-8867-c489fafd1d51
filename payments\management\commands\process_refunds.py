import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from payments.models import Payment
# Utiliser directement le modèle Trip de l'application trips
from trips.models import Trip
from payments.views_auto_refunds import AutoRefundViewSet

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Traite les remboursements automatiques pour les réservations annulées'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Exécute la commande sans effectuer de remboursements réels'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Nombre de jours à remonter pour traiter les réservations annulées'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        days = options['days']
        
        # Date limite pour les réservations à traiter
        cutoff_date = timezone.now() - timedelta(days=days)
        
        self.stdout.write(self.style.SUCCESS(f"Traitement des remboursements automatiques (dry-run: {dry_run})"))
        
        # Trouver toutes les réservations annulées avec des paiements non remboursés
        bookings = Trip.objects.filter(
            status='CANCELED',
            payment__status='COMPLETED',
            updated_at__gte=cutoff_date
        ).distinct()
        
        self.stdout.write(f"Nombre de réservations annulées à traiter: {bookings.count()}")
        
        results = {
            'processed': 0,
            'failed': 0,
            'skipped': 0,
            'details': []
        }
        
        # Créer une instance de AutoRefundViewSet pour utiliser ses méthodes
        refund_view = AutoRefundViewSet()
        
        for booking in bookings:
            try:
                # Trouver le paiement associé
                payment = Payment.objects.filter(booking=booking, status='COMPLETED').first()
                
                if not payment or not payment.stripe_payment_id:
                    self.stdout.write(self.style.WARNING(
                        f"Réservation {booking.id}: Paiement non trouvé ou sans ID Stripe"
                    ))
                    results['details'].append({
                        'booking_id': str(booking.id),
                        'status': 'failed',
                        'reason': 'Paiement non trouvé ou sans ID Stripe'
                    })
                    results['failed'] += 1
                    continue
                
                # Calculer le montant du remboursement
                refund_amount = refund_view._calculate_refund_amount(booking, payment)
                
                if refund_amount <= 0:
                    self.stdout.write(self.style.WARNING(
                        f"Réservation {booking.id}: Aucun remboursement dû selon la politique"
                    ))
                    results['details'].append({
                        'booking_id': str(booking.id),
                        'status': 'skipped',
                        'reason': "Aucun remboursement dû selon la politique"
                    })
                    results['skipped'] += 1
                    continue
                
                # Si c'est un dry run, ne pas effectuer le remboursement
                if dry_run:
                    self.stdout.write(self.style.SUCCESS(
                        f"Réservation {booking.id}: Remboursement de {refund_amount}€ (dry-run)"
                    ))
                    results['details'].append({
                        'booking_id': str(booking.id),
                        'status': 'dry-run',
                        'amount': refund_amount
                    })
                    results['processed'] += 1
                    continue
                
                # Préparer les métadonnées
                metadata = {
                    'payment_id': str(payment.id),
                    'booking_id': str(booking.id),
                    'reason': 'requested_by_customer',
                    'auto_refund': 'true',
                    'batch_process': 'true'
                }
                
                # Convertir le montant en centimes
                amount_cents = int(refund_amount * 100)
                
                # Créer le remboursement
                from payments.stripe_utils import create_refund
                refund = create_refund(
                    payment_intent_id=payment.stripe_payment_id,
                    amount=amount_cents,
                    reason='requested_by_customer',
                    metadata=metadata
                )
                
                if 'error' in refund:
                    self.stdout.write(self.style.ERROR(
                        f"Réservation {booking.id}: Erreur lors du remboursement: {refund['error']}"
                    ))
                    results['details'].append({
                        'booking_id': str(booking.id),
                        'status': 'failed',
                        'reason': refund['error']
                    })
                    results['failed'] += 1
                    continue
                
                # Mettre à jour le paiement
                if refund_amount < payment.amount:
                    payment.status = 'PARTIALLY_REFUNDED'
                    payment.refund_amount = refund_amount
                else:
                    payment.status = 'REFUNDED'
                    payment.refund_amount = payment.amount
                
                payment.refund_id = refund.id
                payment.refund_reason = 'requested_by_customer'
                payment.save()
                
                self.stdout.write(self.style.SUCCESS(
                    f"Réservation {booking.id}: Remboursement de {refund_amount}€ effectué (ID: {refund.id})"
                ))
                results['details'].append({
                    'booking_id': str(booking.id),
                    'status': 'success',
                    'refund_id': refund.id,
                    'amount': refund_amount
                })
                results['processed'] += 1
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Réservation {booking.id}: Erreur inattendue: {str(e)}"
                ))
                logger.error(f"Erreur lors du traitement du remboursement pour la réservation {booking.id}: {str(e)}")
                results['details'].append({
                    'booking_id': str(booking.id),
                    'status': 'failed',
                    'reason': str(e)
                })
                results['failed'] += 1
        
        # Afficher le résumé
        self.stdout.write(self.style.SUCCESS(
            f"Traitement terminé: {results['processed']} traités, {results['failed']} échoués, {results['skipped']} ignorés"
        ))
        
        return results
