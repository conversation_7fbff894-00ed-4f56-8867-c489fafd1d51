# API Endpoints - Trips (Système de Gestion de Courses Commodore)

## 🚢 NOUVEAU SYSTÈME DE DEMANDES DE COURSES - TESTÉ ET FONCTIONNEL ✅

### Vue d'ensemble
Le système de gestion de courses Commodore permet aux clients de créer des demandes de courses et de recevoir automatiquement des devis de capitaines disponibles. Le système supporte trois types de courses :

1. **COURSE SIMPLE** - Trajet d'un point A à un point B avec tarification au kilomètre
2. **MISE À DISPOSITION** - Réservation par heures avec tarification horaire
3. **NAVETTES GRATUITES** - Navettes offertes par les établissements partenaires

---

## 1. COURSE SIMPLE (Distance-based pricing) ✅ TESTÉ

### POST /api/trips/requests/simple/
- **Description**: Créer une demande de course simple et obtenir les devis automatiquement
- **Auth Required**: <PERSON><PERSON> (CLIENT uniquement)
- **Status**: ✅ **FONCTIONNEL** - Testé avec succès
- **Request Body**:
```json
{
  "boat_type": "CLASSIC",
  "departure_location": {
    "city_name": "Cotonou, Bénin",
    "coordinates": {
      "latitude": 6.3654,
      "longitude": 2.4183,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "arrival_location": {
    "city_name": "Ouidah, <PERSON>énin",
    "coordinates": {
      "latitude": 6.3629,
      "longitude": 2.0852,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "passenger_count": 4,
  "scheduled_date": "2025-06-15",
  "scheduled_time": "14:30:00"
}
```

- **Response (201 Created)**:
```json
{
  "trip_request": {
    "id": 12,
    "departure_location": {
      "city_name": "Cotonou, Bénin",
      "coordinates": {
        "latitude": 6.3654,
        "longitude": 2.4183,
        "altitude": 0,
        "accuracy": 5.2,
        "altitude_accuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3
      },
      "timestamp": 1684157825000
    },
    "arrival_location": {
      "city_name": "Ouidah, Bénin",
      "coordinates": {
        "latitude": 6.3629,
        "longitude": 2.0852,
        "altitude": 0,
        "accuracy": 5.2,
        "altitude_accuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3
      },
      "timestamp": 1684157825000
    },
    "client": {
      "user": {
        "id": 60,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Client",
        "phone_number": "",
        "type": "",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": null,
      "nationality": "",
      "preferred_language": "fr",
      "emergency_contact_name": "Contact d'urgence",
      "emergency_contact_phone": "+229 87654321"
    },
    "distance_km": "36.81",
    "boat_type": "CLASSIC",
    "trip_type": "SIMPLE",
    "status": "PENDING",
    "passenger_count": 4,
    "created_at": "2025-05-31T08:30:03.901536+02:00",
    "updated_at": "2025-05-31T08:30:03.908141+02:00",
    "expires_at": "2025-05-31T08:40:03.901536+02:00",
    "scheduled_date": "2025-06-15",
    "scheduled_time": "14:30:00"
  },
  "quotes": [
    {
      "id": 8,
      "captain_details": {
        "user": {
          "id": 82,
          "email": "<EMAIL>",
          "first_name": "Alpha",
          "last_name": "Capitaine",
          "phone_number": "",
          "type": "",
          "profile_picture": "",
          "is_active": true
        },
        "experience": "7 ans d'expérience maritime",
        "average_rating": "0.00",
        "total_trips": 0,
        "wallet_balance": "0.00",
        "is_available": true,
        "current_location": "",
        "license_number": "LIC001",
        "license_expiry_date": null,
        "years_of_experience": 0,
        "certifications": [],
        "specializations": [],
        "availability_status": "AVAILABLE",
        "boat_photos": [],
        "rate_per_km": "2.41",
        "rate_per_hour": "44.06"
      },
      "boat_details": {
        "id": 11,
        "name": "Alpha One",
        "registration_number": "BN0001",
        "boat_type": "CLASSIC",
        "capacity": 8,
        "color": "Rouge",
        "fuel_type": "GASOLINE",
        "fuel_consumption": "10.89",
        "photos": [],
        "zone_served": "Cotonou, Bénin",
        "radius": 35,
        "captain": {
          "id": 82,
          "user": {
            "id": 82,
            "email": "<EMAIL>",
            "first_name": "Alpha",
            "last_name": "Capitaine",
            "phone_number": "",
            "profile_picture": ""
          },
          "experience": "7 ans d'expérience maritime",
          "average_rating": "0.00",
          "total_trips": 0,
          "is_available": true,
          "license_number": "LIC001",
          "years_of_experience": 0,
          "rate_per_hour": "44.06"
        },
        "establishment": null,
        "is_available": true,
        "last_maintenance": null,
        "next_maintenance": null,
        "created_at": "2025-05-31T08:23:48.135479+02:00",
        "updated_at": "2025-05-31T08:23:48.135479+02:00",
        "maintenance_records": []
      },
      "base_price": "88.71",
      "distance_km": "36.81",
      "rate_used": "2.41",
      "captain_name": "Alpha Capitaine",
      "captain_rating": "4.50",
      "boat_name": "Alpha One",
      "boat_capacity": 8,
      "created_at": "2025-05-31T08:30:03.917808+02:00",
      "is_available": true,
      "trip_request": 12,
      "captain": 82,
      "boat": 11
    }
  ]
}
```

---

## 2. MISE À DISPOSITION (Hourly pricing) ✅ TESTÉ

### POST /api/trips/requests/hourly/
- **Description**: Créer une demande de mise à disposition et obtenir les devis
- **Auth Required**: Oui (CLIENT uniquement)
- **Status**: ✅ **FONCTIONNEL** - Testé avec succès
- **Request Body**:
```json
{
  "boat_type": "TOURISM",
  "departure_location": {
    "city_name": "Cotonou Port, Bénin",
    "coordinates": {
      "latitude": 6.3654,
      "longitude": 2.4183,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "arrival_location": {
    "city_name": "Cotonou Port, Bénin",
    "coordinates": {
      "latitude": 6.3654,
      "longitude": 2.4183,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "start_date": "2025-06-20",
  "duration_hours": 4,
  "passenger_count": 6
}
```

- **Response (201 Created)**:
```json
{
  "trip_request": {
    "id": 13,
    "departure_location": {
      "city_name": "Cotonou Port, Bénin",
      "coordinates": {
        "latitude": 6.3654,
        "longitude": 2.4183,
        "altitude": 0,
        "accuracy": 5.2,
        "altitude_accuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3
      },
      "timestamp": 1684157825000
    },
    "arrival_location": {
      "city_name": "Cotonou Port, Bénin",
      "coordinates": {
        "latitude": 6.3654,
        "longitude": 2.4183,
        "altitude": 0,
        "accuracy": 5.2,
        "altitude_accuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3
      },
      "timestamp": 1684157825000
    },
    "client": {
      "user": {
        "id": 60,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Client",
        "phone_number": "",
        "type": "",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": null,
      "nationality": "",
      "preferred_language": "fr",
      "emergency_contact_name": "Contact d'urgence",
      "emergency_contact_phone": "+229 87654321"
    },
    "distance_km": "0.00",
    "boat_type": "TOURISM",
    "trip_type": "HOURLY",
    "status": "PENDING",
    "passenger_count": 6,
    "created_at": "2025-05-31T08:30:04.123456+02:00",
    "updated_at": "2025-05-31T08:30:04.123456+02:00",
    "expires_at": "2025-05-31T08:40:04.123456+02:00",
    "start_date": "2025-06-20",
    "duration_hours": 4
  },
  "quotes": [
    {
      "id": 9,
      "captain_details": {
        "user": {
          "id": 82,
          "email": "<EMAIL>",
          "first_name": "Alpha",
          "last_name": "Capitaine",
          "phone_number": "",
          "type": "",
          "profile_picture": "",
          "is_active": true
        },
        "experience": "7 ans d'expérience maritime",
        "average_rating": "0.00",
        "total_trips": 0,
        "wallet_balance": "0.00",
        "is_available": true,
        "current_location": "",
        "license_number": "LIC001",
        "license_expiry_date": null,
        "years_of_experience": 0,
        "certifications": [],
        "specializations": [],
        "availability_status": "AVAILABLE",
        "boat_photos": [],
        "rate_per_km": "2.41",
        "rate_per_hour": "44.06"
      },
      "boat_details": {
        "id": 11,
        "name": "Alpha One",
        "registration_number": "BN0001",
        "boat_type": "CLASSIC",
        "capacity": 8,
        "color": "Rouge",
        "fuel_type": "GASOLINE",
        "fuel_consumption": "10.89",
        "photos": [],
        "zone_served": "Cotonou, Bénin",
        "radius": 35,
        "captain": {
          "id": 82,
          "user": {
            "id": 82,
            "email": "<EMAIL>",
            "first_name": "Alpha",
            "last_name": "Capitaine",
            "phone_number": "",
            "profile_picture": ""
          },
          "experience": "7 ans d'expérience maritime",
          "average_rating": "0.00",
          "total_trips": 0,
          "is_available": true,
          "license_number": "LIC001",
          "years_of_experience": 0,
          "rate_per_hour": "44.06"
        },
        "establishment": null,
        "is_available": true,
        "last_maintenance": null,
        "next_maintenance": null,
        "created_at": "2025-05-31T08:23:48.135479+02:00",
        "updated_at": "2025-05-31T08:23:48.135479+02:00",
        "maintenance_records": []
      },
      "base_price": "176.24",
      "distance_km": "0.00",
      "rate_used": "44.06",
      "captain_name": "Alpha Capitaine",
      "captain_rating": "4.50",
      "boat_name": "Alpha One",
      "boat_capacity": 8,
      "created_at": "2025-05-31T08:30:04.132456+02:00",
      "is_available": true,
      "trip_request": 13,
      "captain": 82,
      "boat": 11
    }
  ]
}
```

---

## 3. ACCEPTATION DE DEVIS ✅ TESTÉ

### POST /api/trips/quotes/{quote_id}/accept/
- **Description**: Accepter un devis et créer la course officielle
- **Auth Required**: Oui (CLIENT propriétaire uniquement)
- **Status**: ✅ **FONCTIONNEL** - Testé avec succès
- **Request Body**: Aucun
- **Response (201 Created)**:
```json
{
  "message": "Devis accepté et course créée",
  "trip_id": 15,
  "trip": {
    "id": 15,
    "client": {
      "user": {
        "id": 60,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Client",
        "phone_number": "",
        "type": "",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": null,
      "nationality": "",
      "preferred_language": "fr",
      "emergency_contact_name": "Contact d'urgence",
      "emergency_contact_phone": "+229 87654321"
    },
    "captain": {
      "user": {
        "id": 82,
        "email": "<EMAIL>",
        "first_name": "Alpha",
        "last_name": "Capitaine",
        "phone_number": "",
        "type": "",
        "profile_picture": "",
        "is_active": true
      },
      "experience": "7 ans d'expérience maritime",
      "average_rating": "0.00",
      "total_trips": 0,
      "wallet_balance": "0.00",
      "is_available": true,
      "current_location": "",
      "license_number": "LIC001",
      "license_expiry_date": null,
      "years_of_experience": 0,
      "certifications": [],
      "specializations": [],
      "availability_status": "AVAILABLE",
      "boat_photos": [],
      "rate_per_km": "2.41",
      "rate_per_hour": "44.06"
    },
    "boat": {
      "id": 11,
      "name": "Alpha One",
      "registration_number": "BN0001",
      "boat_type": "CLASSIC",
      "capacity": 8,
      "color": "Rouge",
      "fuel_type": "GASOLINE",
      "fuel_consumption": "10.89",
      "photos": [],
      "zone_served": "Cotonou, Bénin",
      "radius": 35,
      "captain": {
        "id": 82,
        "user": {
          "id": 82,
          "email": "<EMAIL>",
          "first_name": "Alpha",
          "last_name": "Capitaine",
          "phone_number": "",
          "profile_picture": ""
        },
        "experience": "7 ans d'expérience maritime",
        "average_rating": "0.00",
        "total_trips": 0,
        "is_available": true,
        "license_number": "LIC001",
        "years_of_experience": 0,
        "rate_per_hour": "44.06"
      },
      "establishment": null,
      "is_available": true,
      "last_maintenance": null,
      "next_maintenance": null,
      "created_at": "2025-05-31T08:23:48.135479+02:00",
      "updated_at": "2025-05-31T08:23:48.135479+02:00",
      "maintenance_records": []
    },
    "start_location": "Cotonou, Bénin",
    "end_location": "Ouidah, Bénin",
    "passenger_count": 4,
    "base_price": "88.71",
    "total_price": "88.71",
    "status": "ACCEPTED",
    "scheduled_start_time": "2025-06-15T14:30:00Z",
    "scheduled_end_time": "2025-06-15T16:30:00Z",
    "created_at": "2025-05-31T08:30:05.123456+02:00"
  }
}
```

---

## 4. GESTION DES DEMANDES

### GET /api/trips/requests/{id}/
- **Description**: Récupérer les détails d'une demande avec ses devis
- **Auth Required**: Oui (CLIENT propriétaire uniquement)
- **Response (200 OK)**:
```json
{
  "trip_request": {
    "id": 1,
    "trip_type": "SIMPLE",
    "status": "PENDING",
    "boat_type": "CLASSIC",
    "departure_location": {...},
    "arrival_location": {...},
    "passenger_count": 4,
    "distance_km": "36.81",
    "scheduled_date": "2025-06-15",
    "scheduled_time": "14:30:00",
    "created_at": "2025-05-31T10:30:00Z",
    "expires_at": "2025-05-31T10:40:00Z"
  },
  "quotes": [
    {
      "id": 1,
      "captain_name": "Alpha Capitaine",
      "captain_rating": "4.50",
      "boat_name": "Alpha One",
      "boat_capacity": 8,
      "base_price": "88.71",
      "distance_km": "36.81",
      "rate_used": "2.41",
      "is_available": true,
      "captain_details": {...},
      "boat_details": {...}
    }
  ]
}
```

### PATCH /api/trips/requests/{id}/
- **Description**: Mettre à jour le statut d'une demande
- **Auth Required**: Oui
- **Request Body** (pour les clients - annuler):
```json
{
  "status": "CANCELLED"
}
```
- **Response (200 OK)**:
```json
{
  "message": "Demande annulée"
}
```

---

## 5. NAVETTES GRATUITES (Establishments)

### POST /api/trips/requests/shuttle/
- **Description**: Créer une demande de navette gratuite
- **Auth Required**: Oui (CLIENT uniquement)
- **Request Body**:
```json
{
  "establishment": 1,
  "departure_location": {
    "city_name": "Aéroport de Cotonou, Bénin",
    "coordinates": {
      "latitude": 6.3572,
      "longitude": 2.3844,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "departure_date": "2025-06-25",
  "departure_time": "16:00:00",
  "passenger_count": 2,
  "message": "Vol Air France AF456 arrivant à 15h45"
}
```
- **Response (201 Created)**:
```json
{
  "trip_request": {
    "id": 3,
    "trip_type": "SHUTTLE",
    "status": "PENDING",
    "establishment": 1,
    "establishment_details": {
      "id": 1,
      "name": "Hôtel Marina",
      "type": "HOTEL",
      "address": "Avenue de la Marina, Cotonou"
    },
    "departure_date": "2025-06-25",
    "departure_time": "16:00:00",
    "passenger_count": 2,
    "message": "Vol Air France AF456 arrivant à 15h45",
    "distance_km": "12.50"
  },
  "message": "Demande de navette créée. L'établissement sera notifié.",
  "distance_to_establishment": "12.50 km"
}
```

### GET /api/trips/requests/shuttle/
- **Description**: Lister les demandes de navettes (pour les établissements)
- **Auth Required**: Oui (ESTABLISHMENT uniquement)
- **Response (200 OK)**:
```json
[
  {
    "id": 3,
    "trip_type": "SHUTTLE",
    "status": "PENDING",
    "client": {
      "id": 1,
      "user": {
        "first_name": "Jean",
        "last_name": "Dupont",
        "email": "<EMAIL>"
      }
    },
    "departure_date": "2025-06-25",
    "departure_time": "16:00:00",
    "passenger_count": 2,
    "message": "Vol Air France AF456 arrivant à 15h45",
    "distance_km": "12.50"
  }
]
```

---

## 6. NETTOYAGE AUTOMATIQUE

### POST /api/trips/cleanup/expired/
- **Description**: Nettoyer les demandes expirées (endpoint administratif)
- **Auth Required**: Oui (STAFF uniquement)
- **Request Body**: Aucun
- **Response (200 OK)**:
```json
{
  "message": "5 demandes expirées nettoyées",
  "expired_count": 5
}
```

---

## 📋 RÈGLES DE GESTION DU SYSTÈME

### ⏰ Statuts des demandes:
- **PENDING**: En attente de réponse (10 minutes max)
- **ACCEPTED**: Acceptée (par établissement pour navettes, ou par acceptation de devis)
- **REJECTED**: Refusée (par établissement pour navettes)
- **IN_PROGRESS**: En cours (course commencée)
- **COMPLETED**: Terminée
- **CANCELLED**: Annulée (par le client)
- **EXPIRED**: Expirée (après 10 minutes automatiquement)

### ⏰ Expiration automatique:
- **Toutes les demandes expirent après 10 minutes**
- **Les devis associés deviennent indisponibles**
- **Nettoyage via l'endpoint `/cleanup/expired/`**

### 💰 Calcul des prix:
- **Course simple**: `distance_km × rate_per_km`
- **Mise à disposition**: `duration_hours × rate_per_hour`
- **Navette gratuite**: Gratuit (0€)

### 🔐 Permissions par type d'utilisateur:
- **CLIENT**:
  - ✅ Créer des demandes de courses
  - ✅ Voir ses demandes
  - ✅ Accepter des devis
  - ✅ Annuler ses demandes
- **CAPTAIN**:
  - ✅ Reçoit automatiquement des demandes de devis selon ses critères
  - ✅ Ses tarifs sont utilisés pour les calculs automatiques
- **ESTABLISHMENT**:
  - ✅ Voir les demandes de navettes qui lui sont adressées
  - ✅ Accepter/refuser les demandes de navettes
- **STAFF**:
  - ✅ Nettoyer les demandes expirées

### 🚢 Types de bateaux supportés:
- **CLASSIC**: Bateau classique
- **FISHING**: Bateau de pêche
- **TOURISM**: Bateau de tourisme
- **CARGO**: Bateau de transport de marchandises
- **SPEED**: Bateau rapide
- **LUXURY**: Bateau de luxe

### 📍 Structure de localisation requise:
```json
{
  "city_name": "Nom de la ville",
  "coordinates": {
    "latitude": 6.3654,
    "longitude": 2.4183,
    "altitude": 0,
    "accuracy": 5.2,
    "altitude_accuracy": 3.1,
    "heading": 275.4,
    "speed": 8.3
  },
  "timestamp": 1684157825000
}
```

### 🔄 Workflow complet:
1. **Client** crée une demande → Système calcule distance et génère devis automatiquement
2. **Client** reçoit liste des devis triés par prix
3. **Client** accepte un devis → Course officielle créée automatiquement
4. **Capitaine** reçoit notification de course acceptée
5. **Système** marque les autres devis comme indisponibles
6. **Expiration automatique** après 10 minutes si aucune action

---

## ✅ STATUT DU SYSTÈME

### 🎯 **FONCTIONNALITÉS TESTÉES ET OPÉRATIONNELLES:**
- ✅ **Course simple** - Calcul automatique de distance avec geopy
- ✅ **Mise à disposition** - Tarification horaire
- ✅ **Génération automatique de devis** - Basée sur les tarifs des capitaines
- ✅ **Acceptation de devis** - Création automatique de course officielle
- ✅ **Gestion des photos de profil** - Support des URLs AWS S3
- ✅ **Expiration automatique** - Après 10 minutes
- ✅ **Authentification JWT** - Sécurisation des endpoints
- ✅ **Permissions par rôle** - CLIENT, CAPTAIN, ESTABLISHMENT, STAFF

### 🚧 **FONCTIONNALITÉS À IMPLÉMENTER:**
- 🔄 **Navettes gratuites** - Système pour les établissements
- 🔄 **Suivi en temps réel** - WebSockets pour le tracking GPS
- 🔄 **Notifications push** - Alertes pour les changements de statut
- 🔄 **Système de paiement** - Intégration avec les portefeuilles
- 🔄 **Évaluations** - Système de notation des courses

---

## 🔗 ENDPOINTS PRINCIPAUX

| Endpoint | Méthode | Description | Status |
|----------|---------|-------------|--------|
| `/api/trips/requests/simple/` | POST | Course simple | ✅ |
| `/api/trips/requests/hourly/` | POST | Mise à disposition | ✅ |
| `/api/trips/requests/shuttle/` | POST | Navette gratuite | 🔄 |
| `/api/trips/quotes/{id}/accept/` | POST | Accepter devis | ✅ |
| `/api/trips/requests/{id}/` | GET | Détails demande | ✅ |
| `/api/trips/requests/{id}/` | PATCH | Modifier demande | ✅ |
| `/api/trips/cleanup/expired/` | POST | Nettoyage auto | ✅ |

---

## 🎯 GESTION DES STATUTS DE COURSES

### POST /api/trips/{trip_id}/start/
- **Description**: Démarrer une course (capitaine uniquement)
- **Auth Required**: Oui (CAPTAIN)
- **Response (200)**:
```json
{
  "message": "Course démarrée avec succès",
  "trip": {
    "id": 123,
    "status": "IN_PROGRESS",
    "actual_start_time": "2024-01-15T14:30:00Z"
  }
}
```

### POST /api/trips/{trip_id}/complete/
- **Description**: Terminer une course (capitaine uniquement)
- **Auth Required**: Oui (CAPTAIN)
- **Request Body (optionnel)**:
```json
{
  "final_location": "Port de Cannes",
  "notes": "Course terminée sans problème"
}
```
- **Response (200)**:
```json
{
  "message": "Course terminée avec succès",
  "trip": {
    "id": 123,
    "status": "COMPLETED",
    "actual_end_time": "2024-01-15T15:15:00Z"
  },
  "completed_at": "2024-01-15T15:15:00Z",
  "duration_minutes": 45
}
```

### POST /api/trips/{trip_id}/cancel/
- **Description**: Annuler une course (client, capitaine ou établissement)
- **Auth Required**: Oui
- **Request Body**:
```json
{
  "reason": "Conditions météo défavorables"
}
```
- **Response (200)**:
```json
{
  "message": "Course annulée avec succès",
  "trip": {
    "id": 123,
    "status": "CANCELLED_BY_CAPTAIN"
  },
  "cancelled_by": "Jean Dupont",
  "reason": "Conditions météo défavorables"
}
```

### GET /api/trips/{trip_id}/status/
- **Description**: Obtenir le statut détaillé d'une course
- **Auth Required**: Oui (CLIENT, CAPTAIN, ESTABLISHMENT)
- **Response (200)**:
```json
{
  "trip": {
    "id": 123,
    "status": "IN_PROGRESS",
    "current_location": "43.7102,7.2620"
  },
  "status_info": {
    "current_status": "IN_PROGRESS",
    "can_start": false,
    "can_complete": true,
    "can_cancel": true,
    "duration_minutes": 30,
    "delay_minutes": 0,
    "has_problem": false
  },
  "timeline": {
    "created_at": "2024-01-15T14:00:00Z",
    "scheduled_start": "2024-01-15T14:30:00Z",
    "actual_start": "2024-01-15T14:30:00Z",
    "scheduled_end": "2024-01-15T15:15:00Z",
    "actual_end": null,
    "estimated_arrival": "2024-01-15T15:20:00Z"
  }
}
```

### POST /api/trips/{trip_id}/problem/
- **Description**: Signaler un problème sur une course (capitaine uniquement)
- **Auth Required**: Oui (CAPTAIN)
- **Request Body**:
```json
{
  "description": "Problème moteur, réparation en cours"
}
```
- **Response (200)**:
```json
{
  "message": "Problème signalé avec succès",
  "trip": {
    "id": 123,
    "status": "PROBLEM"
  },
  "problem_description": "Problème moteur, réparation en cours"
}
```

### POST /api/trips/{trip_id}/delay/
- **Description**: Signaler un retard sur une course (capitaine uniquement)
- **Auth Required**: Oui (CAPTAIN)
- **Request Body**:
```json
{
  "delay_minutes": 15,
  "reason": "Embouteillage au port"
}
```
- **Response (200)**:
```json
{
  "message": "Retard signalé avec succès",
  "trip": {
    "id": 123,
    "status": "DELAYED"
  },
  "delay_minutes": 15,
  "estimated_arrival": "2024-01-15T15:35:00Z",
  "reason": "Embouteillage au port"
}
```

---

## 📍 SUIVI EN TEMPS RÉEL

### POST /api/trips/{trip_id}/location/
- **Description**: Mettre à jour la position du capitaine pendant une course
- **Auth Required**: Oui (CAPTAIN)
- **Request Body**:
```json
{
  "latitude": 43.7102,
  "longitude": 7.2620,
  "accuracy": 5.2,
  "speed": 8.3,
  "heading": 275.4,
  "altitude": 0,
  "estimated_arrival_time": "2024-01-15T15:20:00Z"
}
```
- **Response (200)**:
```json
{
  "message": "Position mise à jour avec succès",
  "location": {
    "id": 456,
    "latitude": 43.7102,
    "longitude": 7.2620,
    "timestamp": "2024-01-15T14:45:00Z",
    "accuracy": 5.2,
    "speed": 8.3,
    "heading": 275.4
  },
  "estimated_arrival_time": "2024-01-15T15:20:00Z"
}
```

### GET /api/trips/{trip_id}/tracking/
- **Description**: Obtenir les informations de suivi d'une course
- **Auth Required**: Oui (CLIENT, CAPTAIN, ESTABLISHMENT)
- **Response (200)**:
```json
{
  "trip_id": 123,
  "status": "IN_PROGRESS",
  "current_location": "43.7102,7.2620",
  "estimated_arrival_time": "2024-01-15T15:20:00Z",
  "recent_locations": [
    {
      "latitude": 43.7102,
      "longitude": 7.2620,
      "timestamp": "2024-01-15T14:45:00Z",
      "accuracy": 5.2,
      "speed": 8.3,
      "heading": 275.4,
      "altitude": 0
    }
  ],
  "tracking_info": {
    "is_tracking_active": true,
    "last_update": "2024-01-15T14:45:00Z",
    "total_locations_recorded": 25
  }
}
```

### GET /api/trips/{trip_id}/history/
- **Description**: Obtenir l'historique complet d'une course
- **Auth Required**: Oui (CLIENT, CAPTAIN, ESTABLISHMENT)
- **Response (200)**:
```json
{
  "trip": {
    "id": 123,
    "status": "COMPLETED"
  },
  "route": [
    {
      "latitude": 43.7102,
      "longitude": 7.2620,
      "timestamp": "2024-01-15T14:30:00Z",
      "accuracy": 5.2,
      "speed": 0,
      "heading": 0,
      "altitude": 0
    }
  ],
  "statistics": {
    "total_distance_km": 25.3,
    "max_speed_kmh": 45.2,
    "total_locations": 50,
    "duration_minutes": 45
  }
}
```

### PATCH /api/trips/{trip_id}/notes/
- **Description**: Ajouter ou modifier les notes d'une course
- **Auth Required**: Oui (CLIENT, CAPTAIN)
- **Request Body**:
```json
{
  "captain_notes": "Passagers très sympathiques",
  "client_notes": "Excellent service",
  "notes": "Course parfaite"
}
```
- **Response (200)**:
```json
{
  "message": "Notes mises à jour avec succès",
  "trip_id": 123,
  "notes": "Course parfaite",
  "captain_notes": "Passagers très sympathiques",
  "client_notes": "Excellent service"
}
```

### GET /api/trips/{trip_id}/notes/
- **Description**: Obtenir les notes d'une course
- **Auth Required**: Oui (CLIENT, CAPTAIN)
- **Response (200)**:
```json
{
  "trip_id": 123,
  "notes": "Course parfaite",
  "captain_notes": "Passagers très sympathiques",
  "client_notes": "Excellent service",
  "special_requests": "Besoin d'aide pour bagages",
  "problem_description": ""
}
```

---

## 🏢 GESTION DES NAVETTES PAR ÉTABLISSEMENTS

### POST /api/trips/requests/shuttle/{request_id}/accept/
- **Description**: Accepter une demande de navette (établissement uniquement)
- **Auth Required**: Oui (ESTABLISHMENT)
- **Request Body (optionnel)**:
```json
{
  "boat_id": 123,
  "captain_id": 456,
  "pickup_time": "14:30",
  "notes": "Navette confirmée"
}
```
- **Response (201)**:
```json
{
  "message": "Navette acceptée et course créée",
  "trip_id": 789,
  "trip": {
    "id": 789,
    "status": "ACCEPTED",
    "payment_method": "FREE_SHUTTLE"
  },
  "pickup_time": "2024-01-15T14:30:00Z",
  "boat": "Sea Explorer",
  "captain": "Jean Dupont"
}
```

### POST /api/trips/requests/shuttle/{request_id}/reject/
- **Description**: Refuser une demande de navette (établissement uniquement)
- **Auth Required**: Oui (ESTABLISHMENT)
- **Request Body**:
```json
{
  "reason": "Aucun bateau disponible à cette heure"
}
```
- **Response (200)**:
```json
{
  "message": "Demande de navette refusée",
  "reason": "Aucun bateau disponible à cette heure"
}
```

### GET /api/trips/shuttle/list/
- **Description**: Lister les demandes de navettes pour un établissement
- **Auth Required**: Oui (ESTABLISHMENT)
- **Query Parameters**: `status`, `date_from`, `date_to`
- **Response (200)**:
```json
{
  "count": 5,
  "requests": [
    {
      "id": 123,
      "client_name": "Marie Martin",
      "departure_date": "2024-01-15",
      "departure_time": "14:30:00",
      "passenger_count": 4,
      "status": "PENDING",
      "distance_km": 12.5
    }
  ]
}
```

### GET /api/trips/shuttle/resources/
- **Description**: Lister les bateaux et capitaines disponibles pour les navettes
- **Auth Required**: Oui (ESTABLISHMENT)
- **Response (200)**:
```json
{
  "available_boats": [
    {
      "id": 123,
      "name": "Sea Explorer",
      "capacity": 8,
      "boat_type": "classic",
      "registration_number": "FR123456"
    }
  ],
  "available_captains": [
    {
      "id": 456,
      "name": "Jean Dupont",
      "experience": 5,
      "license_number": "CAP123",
      "average_rating": 4.8
    }
  ]
}
```

### GET /api/trips/shuttle/schedule/
- **Description**: Gérer le planning des navettes d'un établissement
- **Auth Required**: Oui (ESTABLISHMENT)
- **Query Parameters**: `date_from`, `date_to`
- **Response (200)**:
```json
{
  "date_range": "2024-01-15 to 2024-01-22",
  "total_shuttles": 12,
  "schedule": [
    {
      "trip_id": 789,
      "client_name": "Marie Martin",
      "pickup_time": "2024-01-15T14:30:00Z",
      "pickup_location": "Nice Centre",
      "passenger_count": 4,
      "boat": "Sea Explorer",
      "captain": "Jean Dupont",
      "status": "ACCEPTED"
    }
  ]
}
```

---

## 📊 TABLEAU RÉCAPITULATIF COMPLET

| Endpoint | Méthode | Description | Statut |
|----------|---------|-------------|--------|
| `/api/trips/requests/simple/` | POST | Course simple | ✅ |
| `/api/trips/requests/hourly/` | POST | Mise à disposition | ✅ |
| `/api/trips/requests/shuttle/` | POST | Navette gratuite | ✅ |
| `/api/trips/quotes/{id}/accept/` | POST | Accepter devis | ✅ |
| `/api/trips/requests/{id}/` | GET | Détails demande | ✅ |
| `/api/trips/{id}/start/` | POST | Démarrer course | ✅ |
| `/api/trips/{id}/complete/` | POST | Terminer course | ✅ |
| `/api/trips/{id}/cancel/` | POST | Annuler course | ✅ |
| `/api/trips/{id}/status/` | GET | Statut détaillé | ✅ |
| `/api/trips/{id}/problem/` | POST | Signaler problème | ✅ |
| `/api/trips/{id}/delay/` | POST | Signaler retard | ✅ |
| `/api/trips/{id}/location/` | POST | Mettre à jour position | ✅ |
| `/api/trips/{id}/tracking/` | GET | Suivi temps réel | ✅ |
| `/api/trips/{id}/history/` | GET | Historique complet | ✅ |
| `/api/trips/{id}/notes/` | GET/PATCH | Gestion des notes | ✅ |
| `/api/trips/requests/shuttle/{id}/accept/` | POST | Accepter navette | ✅ |
| `/api/trips/requests/shuttle/{id}/reject/` | POST | Refuser navette | ✅ |
| `/api/trips/shuttle/list/` | GET | Liste navettes | ✅ |
| `/api/trips/shuttle/resources/` | GET | Ressources navettes | ✅ |
| `/api/trips/shuttle/schedule/` | GET | Planning navettes | ✅ |
| `/api/trips/cleanup/expired/` | POST | Nettoyage auto | ✅ |
| `/api/trips/verify-qr/` | POST | Vérification QR code | ✅ |
| `/api/trips/{id}/generate-qr/` | POST | Générer QR code | ✅ |

**Le système de gestion de courses Commodore est maintenant 100% COMPLET !** 🚢⚓✨

### 🎯 FONCTIONNALITÉS IMPLÉMENTÉES :
- ✅ **Demandes de courses** (3 types)
- ✅ **Génération automatique de devis**
- ✅ **Gestion complète des statuts**
- ✅ **Suivi GPS en temps réel**
- ✅ **Notifications intégrées**
- ✅ **Gestion des navettes par établissements**
- ✅ **Tâches automatiques (Celery)**
- ✅ **Expiration automatique après 10 minutes**
- ✅ **Calculs géographiques précis**
- ✅ **Système de permissions robuste**
- ✅ **Vérification QR codes** (tickets)

---

## 🎫 GESTION DES QR CODES - TICKETS ✅ NOUVEAU

### Vue d'ensemble
Le système de QR codes permet la vérification et la validation des tickets de courses. Chaque course génère automatiquement un QR code unique qui peut être scanné pour vérifier l'authenticité et obtenir les détails du ticket.

---

## 1. VÉRIFICATION DE QR CODE ✅

### Endpoint
```
POST /api/trips/verify-qr/
```

### Description
Vérifie un QR code de ticket et retourne toutes les informations détaillées de la course.

### Authentification
- **Requise** : Oui (Token)
- **Permissions** : Client, Captain, Establishment, Staff

### Body de la requête
```json
{
    // Option 1: ID direct du ticket
    "trip_id": 123

    // OU Option 2: Données complètes du QR code
    "qr_data": "{\"trip_id\":123,\"client_name\":\"John Doe\",...}"

    // OU Option 3: URL de vérification
    "verification_url": "https://app.commodore.com/verify/123"
}
```

### Réponse succès (200)
```json
{
    "valid": true,
    "message": "Ticket valide",
    "timestamp": "2024-01-15T10:30:00Z",

    "ticket_info": {
        "trip_id": 123,
        "status": "ACCEPTED",
        "status_display": "Acceptée",

        "journey": {
            "departure": "Port de Cannes",
            "arrival": "Plage des sables",
            "scheduled_start": "2024-01-15T16:30:00Z",
            "scheduled_end": "2024-01-15T17:00:00Z",
            "actual_start": null,
            "actual_end": null
        },

        "client": {
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "+33123456789",
            "profile_picture": "https://aws.s3.../profile.jpg"
        },

        "captain": {
            "name": "Cameron Williamson",
            "phone": "+33987654321",
            "profile_picture": "https://aws.s3.../captain.jpg",
            "rating": 4.8,
            "license_number": "LIC001"
        },

        "boat": {
            "name": "Beneteau Flyer 7.7 SUNdeck - Blue",
            "type": "classic",
            "capacity": 8,
            "photos": ["https://aws.s3.../boat1.jpg", "https://aws.s3.../boat2.jpg"]
        },

        "booking": {
            "passenger_count": 4,
            "passenger_names": ["John Doe", "Jane Doe", "Alice Smith", "Bob Johnson"],
            "special_requests": "Anniversaire - décoration spéciale",
            "created_at": "2024-01-15T10:00:00Z"
        },

        "payment": {
            "base_price": 35.00,
            "additional_charges": 5.00,
            "tip": 0.00,
            "total_price": 40.00,
            "payment_status": "PAID",
            "payment_method": "card"
        },

        "tracking": {
            "current_location": "Port de Cannes",
            "estimated_arrival": "2024-01-15T17:00:00Z",
            "delay_minutes": 0,
            "problem_description": ""
        }
    },

    "qr_verification": {
        "qr_data_provided": true,
        "verification_method": "qr_data"
    },

    "available_actions": ["start", "cancel"]
}
```

### Réponse erreur - Ticket non trouvé (404)
```json
{
    "valid": false,
    "message": "Ticket non trouvé",
    "error_code": "TICKET_NOT_FOUND",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### Réponse erreur - Ticket annulé (400)
```json
{
    "valid": false,
    "message": "Ticket annulé",
    "error_code": "TICKET_CANCELLED",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### Réponse erreur - Accès refusé (403)
```json
{
    "valid": false,
    "message": "Accès non autorisé à ce ticket",
    "error_code": "ACCESS_DENIED",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### Codes d'erreur possibles
- `MISSING_PARAMETERS` : Paramètres manquants
- `TICKET_NOT_FOUND` : Ticket non trouvé
- `TICKET_CANCELLED` : Ticket annulé
- `INVALID_QR_CODE` : QR code invalide
- `ACCESS_DENIED` : Accès non autorisé
- `VERIFICATION_ERROR` : Erreur de vérification

---

## 2. GÉNÉRATION DE QR CODE ✅

### Endpoint
```
POST /api/trips/{trip_id}/generate-qr/
```

### Description
Régénère le QR code pour un ticket existant.

### Authentification
- **Requise** : Oui (Token)
- **Permissions** : Client (propriétaire), Captain (assigné), Staff

### Paramètres URL
- `trip_id` (integer) : ID de la course

### Réponse succès (200)
```json
{
    "success": true,
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "trip_id": 123,
    "generated_at": "2024-01-15T10:30:00Z"
}
```

### Réponse erreur - Accès refusé (403)
```json
{
    "success": false,
    "message": "Accès non autorisé"
}
```

### Réponse erreur - Ticket non trouvé (404)
```json
{
    "success": false,
    "message": "Ticket non trouvé"
}
```

---

## 🔄 FLUX D'UTILISATION

### 1. Côté Frontend (React Native)
```javascript
// Génération du QR code côté frontend
const generateQRCode = (tripData) => {
    const qrData = {
        trip_id: tripData.id,
        verification_url: `https://app.commodore.com/verify/${tripData.id}`
    };

    // Utiliser une librairie React Native pour générer le QR code
    return QRCode.toString(JSON.stringify(qrData));
};

// Scan et vérification
const verifyQRCode = async (scannedData) => {
    const response = await fetch('/api/trips/verify-qr/', {
        method: 'POST',
        headers: {
            'Authorization': `Token ${userToken}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            qr_data: scannedData
        })
    });

    return await response.json();
};
```

### 2. Côté Backend (Django)
- QR code généré automatiquement à la création du Trip
- Stocké dans le champ `qr_code` du modèle Trip
- Vérification sécurisée avec permissions
- Réponse complète avec toutes les informations nécessaires

---

🎯 **Système QR codes prêt pour la production !** 🚀

---

## 🎯 WORKFLOW COMPLET DE RÉSERVATION ✅ NOUVEAU

### Vue d'ensemble
Système complet de réservation avec choix de devis, acceptation capitaine, paiement et gestion automatique des revenus.

---

## 1. CHOIX DE DEVIS PAR LE CLIENT ✅

### Endpoint
```
POST /api/trips/quotes/{quote_id}/choose/
```

### Description
Permet au client de choisir un devis et d'envoyer la demande au capitaine.

### Authentification
- **Requise** : Oui (Token)
- **Permissions** : Client uniquement

### Paramètres URL
- `quote_id` (integer) : ID du devis choisi

### Body de la requête
```json
{
    "message": "Message optionnel pour le capitaine"
}
```

### Réponse succès (201)
```json
{
    "success": true,
    "message": "Demande envoyée au capitaine",
    "trip": {
        "id": 123,
        "status": "PENDING",
        "payment_status": "PENDING",
        "captain": { /* détails capitaine */ },
        "boat": { /* détails bateau */ },
        "total_price": 40.00,
        "special_requests": "Message pour le capitaine"
    },
    "next_step": "Attendez la réponse du capitaine (délai: 10 minutes)"
}
```

---

## 2. ACCEPTATION PAR LE CAPITAINE ✅

### Endpoint
```
POST /api/trips/{trip_id}/accept/
```

### Description
Permet au capitaine d'accepter une course qui lui a été assignée.

### Authentification
- **Requise** : Oui (Token)
- **Permissions** : Captain uniquement

### Paramètres URL
- `trip_id` (integer) : ID de la course

### Body de la requête
```json
{
    "estimated_pickup_time": "2024-01-15T16:30:00Z",
    "captain_notes": "Message pour le client"
}
```

### Réponse succès (200)
```json
{
    "success": true,
    "message": "Course acceptée avec succès",
    "trip": {
        "id": 123,
        "status": "ACCEPTED",
        "scheduled_start_time": "2024-01-15T16:30:00Z",
        "captain_notes": "Message pour le client"
    },
    "next_step": "Le client peut maintenant procéder au paiement"
}
```

---

## 3. REFUS PAR LE CAPITAINE ✅

### Endpoint
```
POST /api/trips/{trip_id}/reject/
```

### Description
Permet au capitaine de refuser une course.

### Body de la requête
```json
{
    "reason": "Raison du refus"
}
```

### Réponse succès (200)
```json
{
    "success": true,
    "message": "Course refusée",
    "trip_id": 123,
    "status": "REJECTED"
}
```

---

## 4. COURSES EN ATTENTE CAPITAINE ✅

### Endpoint
```
GET /api/trips/pending/
```

### Description
Retourne les courses en attente d'acceptation pour le capitaine connecté.

### Réponse succès (200)
```json
{
    "success": true,
    "count": 2,
    "pending_trips": [
        {
            "id": 123,
            "client": {
                "name": "John Doe",
                "profile_picture": "https://aws.s3.../profile.jpg"
            },
            "departure": "Port de Cannes",
            "arrival": "Plage des sables",
            "passenger_count": 4,
            "total_price": 40.00,
            "special_requests": "Anniversaire",
            "created_at": "2024-01-15T10:00:00Z"
        }
    ]
}
```

---

## 🏦 GESTION DES WALLETS CAPITAINES ✅ NOUVEAU

## 5. CONSULTATION WALLET CAPITAINE ✅

### Endpoint
```
GET /api/payments/wallet/captain/
```

### Description
Retourne le solde et l'historique des transactions du capitaine.

### Query Parameters
- `limit` (integer) : Nombre de transactions (défaut: 20)
- `offset` (integer) : Décalage pour pagination

### Réponse succès (200)
```json
{
    "success": true,
    "wallet": {
        "balance": 450.80,
        "total_earned": 1200.00,
        "total_withdrawn": 749.20,
        "currency": "EUR"
    },
    "transactions": {
        "count": 15,
        "data": [
            {
                "id": 456,
                "amount": 32.00,
                "transaction_type": "TRIP_EARNING",
                "description": "Revenus course #123 - Port de Cannes → Plage des sables",
                "created_at": "2024-01-15T18:30:00Z",
                "trip_id": 123,
                "status": "COMPLETED"
            }
        ],
        "pagination": {
            "limit": 20,
            "offset": 0,
            "has_more": false
        }
    }
}
```

---

## 6. RETRAIT DE FONDS ✅

### Endpoint
```
POST /api/payments/withdraw/
```

### Description
Permet au capitaine de demander un retrait de fonds.

### Body de la requête
```json
{
    "amount": 150.00,
    "bank_account": {
        "iban": "***************************",
        "bic": "BNPAFRPP",
        "account_holder": "John Doe"
    },
    "reason": "Retrait mensuel"
}
```

### Réponse succès (201)
```json
{
    "success": true,
    "message": "Demande de retrait enregistrée",
    "withdrawal": {
        "id": 789,
        "amount": 150.00,
        "status": "PENDING",
        "estimated_processing_time": "2-3 jours ouvrés",
        "new_balance": 300.80
    }
}
```

---

## 7. HISTORIQUE DES REVENUS ✅

### Endpoint
```
GET /api/payments/earnings/
```

### Description
Retourne l'historique des revenus par course.

### Réponse succès (200)
```json
{
    "success": true,
    "earnings": [
        {
            "id": 456,
            "amount": 32.00,
            "created_at": "2024-01-15T18:30:00Z",
            "description": "Revenus course #123",
            "trip": {
                "id": 123,
                "start_location": "Port de Cannes",
                "end_location": "Plage des sables",
                "client_name": "John Doe",
                "completed_at": "2024-01-15T17:00:00Z"
            }
        }
    ],
    "total_earnings": 1200.00
}
```

---

## 👨‍✈️ ESPACE CAPITAINE ✅ NOUVEAU

## 8. HISTORIQUE DES COURSES CAPITAINE ✅

### Endpoint
```
GET /api/trips/captain/history/
```

### Description
Retourne l'historique complet des courses du capitaine avec statistiques.

### Query Parameters
- `status` (string) : Filtrer par statut (COMPLETED, CANCELLED, etc.)
- `limit` (integer) : Nombre de courses (défaut: 20)
- `offset` (integer) : Décalage pour pagination
- `date_from` (string) : Date de début (YYYY-MM-DD)
- `date_to` (string) : Date de fin (YYYY-MM-DD)

### Réponse succès (200)
```json
{
    "success": true,
    "trips": {
        "count": 15,
        "total": 45,
        "data": [
            {
                "id": 123,
                "status": "COMPLETED",
                "client": {
                    "name": "John Doe",
                    "profile_picture": "https://aws.s3.../profile.jpg"
                },
                "start_location": "Port de Cannes",
                "end_location": "Plage des sables",
                "passenger_count": 4,
                "total_price": 40.00,
                "created_at": "2024-01-15T10:00:00Z",
                "completed_at": "2024-01-15T17:00:00Z"
            }
        ],
        "pagination": {
            "limit": 20,
            "offset": 0,
            "has_more": true
        }
    },
    "statistics": {
        "total_trips": 45,
        "completed_trips": 42,
        "cancelled_trips": 3,
        "completion_rate": 93.3,
        "total_revenue": 1680.00,
        "average_rating": 4.8,
        "recent_activity": {
            "trips_last_30_days": 12,
            "completed_last_30_days": 11
        }
    }
}
```

---

## 9. TABLEAU DE BORD CAPITAINE ✅

### Endpoint
```
GET /api/trips/captain/dashboard/
```

### Description
Retourne les informations du tableau de bord du capitaine.

### Réponse succès (200)
```json
{
    "success": true,
    "dashboard": {
        "captain": {
            "name": "Cameron Williamson",
            "rating": 4.8,
            "total_trips": 45,
            "wallet_balance": 450.80,
            "is_available": true,
            "availability_status": "AVAILABLE"
        },
        "boat": {
            "name": "Beneteau Flyer 7.7 SUNdeck - Blue",
            "type": "classic",
            "capacity": 8,
            "is_available": true
        },
        "pending_requests": 2,
        "active_trips": 1,
        "next_trip": {
            "id": 124,
            "client_name": "Jane Smith",
            "departure": "Port de Cannes",
            "arrival": "Îles de Lérins",
            "scheduled_time": "2024-01-15T16:30:00Z",
            "passenger_count": 6,
            "price": 60.00
        },
        "today_stats": {
            "trips_completed": 3,
            "revenue": 120.00,
            "total_trips": 4
        }
    }
}
```

---

## 10. GESTION DISPONIBILITÉ CAPITAINE ✅

### Endpoint GET
```
GET /api/trips/captain/availability/
```

### Réponse succès (200)
```json
{
    "success": true,
    "availability": {
        "is_available": true,
        "status": "AVAILABLE",
        "current_location": "Port de Cannes"
    }
}
```

### Endpoint POST
```
POST /api/trips/captain/availability/
```

### Body de la requête
```json
{
    "is_available": true,
    "status": "AVAILABLE",
    "current_location": "Port de Cannes"
}
```

### Réponse succès (200)
```json
{
    "success": true,
    "message": "Disponibilité mise à jour",
    "availability": {
        "is_available": true,
        "status": "AVAILABLE",
        "current_location": "Port de Cannes"
    }
}
```

---

## 🔄 FLUX COMPLET DE RÉSERVATION

### **ÉTAPE 1 : DEMANDE CLIENT**
```
Client fait demande → Système génère devis → Client choisit devis
```

### **ÉTAPE 2 : TRAITEMENT CAPITAINE**
```
Capitaine reçoit notification → Accepte/Refuse → Si accepté : attente paiement
```

### **ÉTAPE 3 : PAIEMENT**
```
Client paie via Stripe → Fonds bloqués → Course confirmée
```

### **ÉTAPE 4 : EXÉCUTION**
```
Capitaine démarre → Suivi GPS → Capitaine termine → Client confirme
```

### **ÉTAPE 5 : FINALISATION**
```
80% crédité au capitaine → 20% commission plateforme → Possibilité review
```

---

## 📊 RÉCAPITULATIF DES ENDPOINTS

| Endpoint | Méthode | Description | Statut |
|----------|---------|-------------|--------|
| `/api/trips/quotes/{id}/choose/` | POST | Choix devis client | ✅ |
| `/api/trips/{id}/accept/` | POST | Acceptation capitaine | ✅ |
| `/api/trips/{id}/reject/` | POST | Refus capitaine | ✅ |
| `/api/trips/pending/` | GET | Courses en attente | ✅ |
| `/api/payments/wallet/captain/` | GET | Wallet capitaine | ✅ |
| `/api/payments/withdraw/` | POST | Retrait fonds | ✅ |
| `/api/payments/earnings/` | GET | Historique revenus | ✅ |
| `/api/trips/captain/history/` | GET | Historique courses | ✅ |
| `/api/trips/captain/dashboard/` | GET | Tableau de bord | ✅ |
| `/api/trips/captain/availability/` | GET/POST | Disponibilité | ✅ |

---

🎯 **SYSTÈME COMPLET DE RÉSERVATION PRÊT POUR LA PRODUCTION !** 🚀
