"""
Vues pour la gestion des paiements et du portefeuille de l'établissement.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.db.models import Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from accounts.models import Establishment
from payments.models import Payment, Wallet, Transaction
from trips.models import Trip
from accounts.permissions import IsEstablishment


class EstablishmentWalletView(APIView):
    """
    Consulter le portefeuille de l'établissement.
    
    GET /api/establishments/wallet/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def get(self, request):
        """Récupérer les informations du portefeuille"""
        
        # La permission IsEstablishment s'en charge déjà
        
        # Récupérer le wallet
        wallet = getattr(request.user, 'wallet', None)
        if not wallet:
            return Response({
                'error': 'Portefeuille non trouvé'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Statistiques des 30 derniers jours
        thirty_days_ago = timezone.now() - timedelta(days=30)
        
        # Dépenses pour les navettes
        shuttle_expenses = Payment.objects.filter(
            user=request.user,
            type=Payment.PaymentType.SHUTTLE_PAYMENT,
            created_at__gte=thirty_days_ago,
            status=Payment.Status.COMPLETED
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Transactions récentes
        recent_transactions = Transaction.objects.filter(
            wallet=wallet
        ).order_by('-created_at')[:10]
        
        transactions_data = []
        for transaction in recent_transactions:
            transactions_data.append({
                'transaction_id': str(transaction.id),
                'type': transaction.get_type_display(),
                'amount': float(transaction.amount),
                'balance_after': float(transaction.balance_after),
                'description': transaction.description,
                'created_at': transaction.created_at.isoformat()
            })
        
        return Response({
            'status': 'success',
            'data': {
                'balance': float(wallet.balance),
                'currency': wallet.currency,
                'total_earned': float(wallet.total_earned),
                'total_spent': float(wallet.total_spent),
                'shuttle_expenses_30d': float(shuttle_expenses),
                'recent_transactions': transactions_data
            }
        })


class EstablishmentAddFundsView(APIView):
    """
    Ajouter des fonds au portefeuille.
    
    POST /api/establishments/wallet/add-funds/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def post(self, request):
        """Ajouter des fonds au portefeuille"""
        
        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)
        
        amount = request.data.get('amount')
        payment_method = request.data.get('payment_method', 'CARD')
        
        if not amount:
            return Response({
                'error': 'Le montant est requis'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            amount = Decimal(str(amount))
            if amount <= 0:
                return Response({
                    'error': 'Le montant doit être positif'
                }, status=status.HTTP_400_BAD_REQUEST)
        except (ValueError, TypeError):
            return Response({
                'error': 'Montant invalide'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Récupérer le wallet
        wallet = getattr(request.user, 'wallet', None)
        if not wallet:
            return Response({
                'error': 'Portefeuille non trouvé'
            }, status=status.HTTP_404_NOT_FOUND)
        
        try:
            # Ajouter les fonds
            payment = wallet.add_funds(
                amount=amount,
                payment_method=payment_method,
                description=f"Recharge du portefeuille - {amount}€"
            )
            
            return Response({
                'status': 'success',
                'message': 'Fonds ajoutés avec succès',
                'data': {
                    'new_balance': float(wallet.balance),
                    'payment_id': str(payment.id),
                    'amount_added': float(amount)
                }
            })
            
        except Exception as e:
            return Response({
                'error': f'Erreur lors de l\'ajout des fonds: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EstablishmentPaymentHistoryView(APIView):
    """
    Historique des paiements de l'établissement.
    
    GET /api/establishments/payments/history/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def get(self, request):
        """Récupérer l'historique des paiements"""
        
        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Paramètres de pagination
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))
        offset = (page - 1) * limit
        
        # Filtres
        payment_type = request.GET.get('type')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        
        # Requête de base
        payments_query = Payment.objects.filter(user=request.user)
        
        # Appliquer les filtres
        if payment_type:
            payments_query = payments_query.filter(type=payment_type)
        
        if date_from:
            try:
                date_from = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
                payments_query = payments_query.filter(created_at__gte=date_from)
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
                payments_query = payments_query.filter(created_at__lte=date_to)
            except ValueError:
                pass
        
        # Pagination
        total_count = payments_query.count()
        payments = payments_query.order_by('-created_at')[offset:offset+limit]
        
        # Sérialiser les données
        payments_data = []
        for payment in payments:
            payments_data.append({
                'payment_id': str(payment.id),
                'type': payment.get_type_display(),
                'amount': float(payment.amount),
                'status': payment.get_status_display(),
                'payment_method': payment.get_payment_method_display(),
                'description': payment.description,
                'trip_id': str(payment.trip.id) if payment.trip else None,
                'shuttle_id': str(payment.shuttle.id) if payment.shuttle else None,
                'created_at': payment.created_at.isoformat(),
                'completed_at': payment.completed_at.isoformat() if payment.completed_at else None
            })
        
        return Response({
            'status': 'success',
            'data': {
                'payments': payments_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total_count
                }
            }
        })


class EstablishmentPaymentStatsView(APIView):
    """
    Statistiques des paiements de l'établissement.
    
    GET /api/establishments/payments/stats/
    """
    permission_classes = [IsAuthenticated, IsEstablishment]

    def get(self, request):
        """Récupérer les statistiques des paiements"""
        
        if not hasattr(request.user, 'establishment'):
            return Response({
                'error': 'Accès refusé'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Période d'analyse
        period = request.GET.get('period', '30')  # 30 jours par défaut
        
        try:
            days = int(period)
            start_date = timezone.now() - timedelta(days=days)
        except ValueError:
            days = 30
            start_date = timezone.now() - timedelta(days=30)
        
        # Statistiques générales
        total_payments = Payment.objects.filter(
            user=request.user,
            created_at__gte=start_date,
            status=Payment.Status.COMPLETED
        )
        
        total_amount = total_payments.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        total_count = total_payments.count()
        
        # Répartition par type
        payment_types = total_payments.values('type').annotate(
            count=Count('id'),
            total=Sum('amount')
        )
        
        types_data = []
        for pt in payment_types:
            types_data.append({
                'type': pt['type'],
                'count': pt['count'],
                'total_amount': float(pt['total'])
            })
        
        # Évolution par jour (7 derniers jours)
        daily_stats = []
        for i in range(7):
            day = timezone.now().date() - timedelta(days=i)
            day_payments = Payment.objects.filter(
                user=request.user,
                created_at__date=day,
                status=Payment.Status.COMPLETED
            )
            
            daily_stats.append({
                'date': day.isoformat(),
                'count': day_payments.count(),
                'amount': float(day_payments.aggregate(total=Sum('amount'))['total'] or Decimal('0.00'))
            })
        
        return Response({
            'status': 'success',
            'data': {
                'period_days': days,
                'total_amount': float(total_amount),
                'total_count': total_count,
                'average_amount': float(total_amount / total_count) if total_count > 0 else 0,
                'payment_types': types_data,
                'daily_evolution': list(reversed(daily_stats))
            }
        })
