from django.core.management.base import BaseCommand
from trips.models import Shuttle

class Command(BaseCommand):
    help = "Renseigne departure_name et arrival_name à partir de route_name pour les navettes existantes."

    def handle(self, *args, **options):
        updated = 0
        for shuttle in Shuttle.objects.all():
            if (not shuttle.departure_name or not shuttle.arrival_name) and shuttle.route_name:
                # On cherche séparateur
                if '->' in shuttle.route_name:
                    dep, arr = [part.strip() for part in shuttle.route_name.split('->', 1)]
                elif '-' in shuttle.route_name:
                    dep, arr = [part.strip() for part in shuttle.route_name.split('-', 1)]
                else:
                    continue
                # on met à jour si vide
                if not shuttle.departure_name:
                    shuttle.departure_name = dep
                if not shuttle.arrival_name:
                    shuttle.arrival_name = arr
                shuttle.save(update_fields=['departure_name', 'arrival_name'])
                updated += 1
        self.stdout.write(self.style.SUCCESS(f"{updated} navettes mises à jour."))
