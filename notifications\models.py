from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from accounts.models import User

class Notification(models.Model):
    class Type(models.TextChoices):
        TRIP_REQUEST = 'TRIP_REQUEST', _('Demande de course')
        TRIP_ACCEPTED = 'TRIP_ACCEPTED', _('Course acceptée')
        TRIP_REJECTED = 'TRIP_REJECTED', _('Course refusée')
        TRIP_STARTED = 'TRIP_STARTED', _('Course démarrée')
        TRIP_COMPLETED = 'TRIP_COMPLETED', _('Course terminée')
        TRIP_CANCELLED = 'TRIP_CANCELLED', _('Course annulée')
        PAYMENT_RECEIVED = 'PAYMENT_RECEIVED', _('Paiement reçu')
        PAYMENT_FAILED = 'PAYMENT_FAILED', _('Paiement échoué')
        REFUND_PROCESSED = 'REFUND_PROCESSED', _('Remboursement traité')
        NEW_MESSAGE = 'NEW_MESSAGE', _('Nouveau message')
        SYSTEM = 'SYSTEM', _('Notification système')

    class Channel(models.TextChoices):
        PUSH = 'PUSH', _('Notification push')
        EMAIL = 'EMAIL', _('Email')
        SMS = 'SMS', _('SMS')
        IN_APP = 'IN_APP', _('Dans l\'application')

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    type = models.CharField(_('type'), max_length=50, choices=Type.choices)
    channel = models.CharField(_('canal'), max_length=20, choices=Channel.choices, default=Channel.IN_APP)
    title = models.CharField(_('titre'), max_length=255)
    message = models.TextField(_('message'))

    # Lien générique vers l'objet associé (course, paiement, etc.)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')

    # Métadonnées
    is_read = models.BooleanField(_('lu'), default=False)
    read_at = models.DateTimeField(_('lu le'), null=True, blank=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    sent_at = models.DateTimeField(_('envoyé le'), null=True, blank=True)
    delivery_status = models.CharField(_('statut d\'envoi'), max_length=20, default='PENDING')
    error_message = models.TextField(_('message d\'erreur'), blank=True)

    class Meta:
        verbose_name = _('notification')
        verbose_name_plural = _('notifications')
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.type} - {self.user.email} - {self.created_at}'

class NotificationTemplate(models.Model):
    name = models.CharField(_('nom'), max_length=100, unique=True)
    type = models.CharField(_('type'), max_length=50, choices=Notification.Type.choices)
    channel = models.CharField(_('canal'), max_length=20, choices=Notification.Channel.choices)
    subject = models.CharField(_('sujet'), max_length=255, blank=True)
    content = models.TextField(_('contenu'))
    variables = models.JSONField(_('variables'), default=list)
    is_active = models.BooleanField(_('actif'), default=True)

    # Métadonnées
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)

    class Meta:
        verbose_name = _('modèle de notification')
        verbose_name_plural = _('modèles de notification')
        unique_together = ['name', 'type', 'channel']

    def __str__(self):
        return f'{self.name} ({self.type} - {self.channel})'


class Device(models.Model):
    """Modèle pour gérer les appareils des utilisateurs pour les notifications push."""
    
    class DeviceType(models.TextChoices):
        ANDROID = 'ANDROID', _('Android')
        IOS = 'IOS', _('iOS')
        WEB = 'WEB', _('Web')
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='devices')
    device_id = models.CharField(_('identifiant de l\'appareil'), max_length=255, unique=True)
    device_type = models.CharField(_('type d\'appareil'), max_length=20, choices=DeviceType.choices)
    name = models.CharField(_('nom de l\'appareil'), max_length=255, blank=True)
    push_token = models.TextField(_('token push'), blank=True)
    is_active = models.BooleanField(_('actif'), default=True)
    last_used = models.DateTimeField(_('dernière utilisation'), auto_now=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('appareil')
        verbose_name_plural = _('appareils')
        ordering = ['-last_used']
    
    def __str__(self):
        return f'{self.user.email} - {self.device_type} - {self.name}'
