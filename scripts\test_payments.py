import requests
import json
import time
from datetime import datetime

# Configuration
API_URL = "http://localhost:8000/api"
TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzE2OTEwNDk2LCJpYXQiOjE3MTY4MjQwOTYsImp0aSI6ImQ5ZjgzZjE5YTk1YzQ1MzliZjkxZWEwZDNmNzIxMTFhIiwidXNlcl9pZCI6MX0.DEMO_TOKEN_SIMULATED"
HEADERS = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

# ID de méthode de paiement simulé pour les tests
PAYMENT_METHOD_ID = "pm_card_visa"

# Fonction pour formater les réponses JSON
def format_json(data):
    return json.dumps(data, indent=2)

# Classe de test
class PaymentTester:
    def __init__(self):
        self.transaction_ids = []
    
    def run_test(self, name, endpoint, data):
        print(f"\n=== Test: {name} ===")
        print(f"Endpoint: {endpoint}")
        print(f"Données: {format_json(data)}")
        
        try:
            response = requests.post(
                f"{API_URL}{endpoint}", 
                headers=HEADERS,
                json=data
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                print(f"Succès! Code: {response.status_code}")
                print(f"Réponse: {format_json(result)}")
                
                # Stocker l'ID de transaction pour d'éventuels remboursements
                if "id" in result:
                    self.transaction_ids.append(result["id"])
                    
                return result
            else:
                print(f"Erreur! Code: {response.status_code}")
                print(f"Réponse: {response.text}")
                return None
        except Exception as e:
            print(f"Exception: {str(e)}")
            return None
    
    def test_ride_payment(self, ride_id=1):
        """Test de paiement d'une course individuelle"""
        data = {
            "payment_method_id": PAYMENT_METHOD_ID,
            "use_wallet": False
        }
        return self.run_test(
            "Paiement de course individuelle", 
            f"/payments/rides/{ride_id}/pay/", 
            data
        )
    
    def test_shuttle_payment(self, shuttle_id=5):
        """Test de paiement d'une réservation de navette"""
        data = {
            "payment_method_id": PAYMENT_METHOD_ID,
            "seats": 2,
            "passenger_ids": ["p_1", "p_2"],
            "passenger_names": ["John Doe", "Jane Doe"],
            "special_requests": "Besoin d'assistance pour les bagages"
        }
        return self.run_test(
            "Paiement de réservation de navette", 
            f"/payments/shuttles/{shuttle_id}/pay/", 
            data
        )
    
    def test_maintenance_payment(self, maintenance_id=3):
        """Test de paiement d'un service de maintenance"""
        data = {
            "payment_method_id": PAYMENT_METHOD_ID
        }
        return self.run_test(
            "Paiement de service de maintenance", 
            f"/payments/maintenance/{maintenance_id}/pay/", 
            data
        )
    
    def test_promotion_payment(self):
        """Test de paiement pour une promotion"""
        data = {
            "payment_method_id": PAYMENT_METHOD_ID,
            "promotion_type": "FEATURED_LISTING",
            "duration_days": 30,
            "target_type": "CAPTAIN",
            "target_id": 5
        }
        return self.run_test(
            "Paiement de promotion", 
            "/payments/promotions/", 
            data
        )
    
    def test_shared_payment(self, ride_id=1, invitation_token="inv_abcdefg"):
        """Test de paiement partagé"""
        data = {
            "payment_method_id": PAYMENT_METHOD_ID,
            "invitation_token": invitation_token
        }
        return self.run_test(
            "Paiement partagé", 
            f"/payments/rides/{ride_id}/shared_pay/", 
            data
        )
    
    def test_add_wallet_credits(self):
        """Test d'ajout de crédits au portefeuille"""
        data = {
            "amount": 50.0,
            "payment_method_id": PAYMENT_METHOD_ID
        }
        return self.run_test(
            "Ajout de crédits au portefeuille", 
            "/payments/wallet/add_credits/", 
            data
        )
    
    def test_refund(self, transaction_id):
        """Test de remboursement d'une transaction"""
        data = {
            "amount": 25.0,
            "reason": "Test de remboursement"
        }
        return self.run_test(
            "Remboursement de transaction", 
            f"/payments/transactions/{transaction_id}/refund/", 
            data
        )
    
    def get_wallet_balance(self):
        """Récupère le solde du portefeuille"""
        print("\n=== Test: Consulter le solde du portefeuille ===")
        
        try:
            response = requests.get(
                f"{API_URL}/payments/wallet/", 
                headers=HEADERS
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"Succès! Code: {response.status_code}")
                print(f"Réponse: {format_json(result)}")
                return result
            else:
                print(f"Erreur! Code: {response.status_code}")
                print(f"Réponse: {response.text}")
                return None
        except Exception as e:
            print(f"Exception: {str(e)}")
            return None
    
    def run_all_tests(self):
        """Exécute tous les tests en séquence"""
        print("=== DÉBUT DES TESTS DE PAIEMENT ===")
        print(f"Date et heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"API URL: {API_URL}")
        print("="*50)
        
        # Exécuter chaque test et attendre un court instant entre les tests
        self.test_ride_payment()
        time.sleep(1)
        
        self.test_shuttle_payment()
        time.sleep(1)
        
        self.test_maintenance_payment()
        time.sleep(1)
        
        self.test_promotion_payment()
        time.sleep(1)
        
        self.test_shared_payment()
        time.sleep(1)
        
        self.test_add_wallet_credits()
        time.sleep(1)
        
        # Récupérer le solde du portefeuille
        self.get_wallet_balance()
        time.sleep(1)
        
        # Si nous avons des transactions, tester le remboursement de la première
        if self.transaction_ids:
            self.test_refund(self.transaction_ids[0])
        
        print("\n=== FIN DES TESTS DE PAIEMENT ===")
        print(f"Nombre de transactions testées: {len(self.transaction_ids)}")
        print(f"IDs de transaction: {', '.join(self.transaction_ids)}")

# Menu interactif
def show_menu():
    print("\n=== MENU DE TEST DES PAIEMENTS ===")
    print("1: Paiement d'une course individuelle")
    print("2: Paiement d'une navette")
    print("3: Paiement d'un service de maintenance")
    print("4: Paiement d'une promotion")
    print("5: Paiement partagé")
    print("6: Ajout de crédits au portefeuille")
    print("7: Consulter le solde du portefeuille")
    print("8: Remboursement d'une transaction")
    print("9: Exécuter tous les tests")
    print("0: Quitter")
    
    tester = PaymentTester()
    
    while True:
        try:
            choice = input("\nChoisissez une option: ")
            
            if choice == "1":
                ride_id = input("ID de la course (défaut: 1): ") or 1
                tester.test_ride_payment(ride_id)
            elif choice == "2":
                shuttle_id = input("ID de la navette (défaut: 5): ") or 5
                tester.test_shuttle_payment(shuttle_id)
            elif choice == "3":
                maintenance_id = input("ID de la maintenance (défaut: 3): ") or 3
                tester.test_maintenance_payment(maintenance_id)
            elif choice == "4":
                tester.test_promotion_payment()
            elif choice == "5":
                ride_id = input("ID de la course (défaut: 1): ") or 1
                invitation_token = input("Token d'invitation (défaut: inv_abcdefg): ") or "inv_abcdefg"
                tester.test_shared_payment(ride_id, invitation_token)
            elif choice == "6":
                tester.test_add_wallet_credits()
            elif choice == "7":
                tester.get_wallet_balance()
            elif choice == "8":
                if tester.transaction_ids:
                    print(f"Transactions disponibles: {', '.join(tester.transaction_ids)}")
                transaction_id = input("ID de transaction à rembourser: ")
                tester.test_refund(transaction_id)
            elif choice == "9":
                tester.run_all_tests()
            elif choice == "0":
                print("Au revoir!")
                break
            else:
                print("Option invalide. Veuillez réessayer.")
        except KeyboardInterrupt:
            print("\nAu revoir!")
            break
        except Exception as e:
            print(f"Erreur: {str(e)}")

if __name__ == "__main__":
    # Vérifier si le serveur est accessible
    try:
        response = requests.get(f"{API_URL}/health/", timeout=2)
        if response.status_code == 200:
            print(f"Serveur accessible à {API_URL}")
        else:
            print(f"Serveur accessible mais a retourné le code {response.status_code}")
    except:
        print(f"AVERTISSEMENT: Le serveur ne semble pas accessible à {API_URL}")
        if input("Continuer quand même? (o/n): ").lower() != 'o':
            exit()
    
    # Afficher le menu
    show_menu()
