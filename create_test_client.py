#!/usr/bin/env python
"""
Script pour créer un client de test
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import Client

User = get_user_model()

def create_test_client():
    """Créer un client de test"""

    email = "<EMAIL>"
    password = "password123"

    # Créer l'utilisateur
    user, created = User.objects.get_or_create(
        email=email,
        defaults={
            'first_name': 'Test',
            'last_name': 'Client',
            'type': 'CLIENT',
            'is_active': True
        }
    )

    if created:
        user.set_password(password)
        user.email_verified = True  # Marquer l'email comme vérifié
        user.save()
        print(f"✅ Utilisateur créé: {email}")
    else:
        # Mettre à jour le mot de passe au cas où
        user.set_password(password)
        user.email_verified = True  # Marquer l'email comme vérifié
        user.save()
        print(f"ℹ️ Utilisateur existe déjà: {email} (mot de passe mis à jour)")

    # Créer le profil client
    client, created = Client.objects.get_or_create(
        user=user,
        defaults={
            'emergency_contact_name': 'Contact d\'urgence',
            'emergency_contact_phone': '+229 87654321',
            'preferred_language': 'fr'
        }
    )

    if created:
        print(f"✅ Client créé: {user.first_name} {user.last_name}")
    else:
        print(f"ℹ️ Client existe déjà: {user.first_name} {user.last_name}")

    return user, client

def main():
    print("👤 Création du client de test...\n")

    user, client = create_test_client()

    print(f"\n🎉 Client prêt pour les tests !")
    print(f"   📧 Email: {user.email}")
    print(f"   🔑 Mot de passe: password123")
    print(f"   👤 Nom: {user.first_name} {user.last_name}")

if __name__ == '__main__':
    main()
