import math

def deg_to_rad(deg):
    """Convertit des degrés en radians."""
    return deg * (math.pi / 180)

def calculer_distance(localisation1, localisation2):
    """
    Calcule la distance en kilomètres entre deux points GPS 
    à l'aide de la formule de Haversine.
    """
    R = 6371  # <PERSON>on de la Terre en km

    lat1 = deg_to_rad(localisation1["coords"]["latitude"])
    lon1 = deg_to_rad(localisation1["coords"]["longitude"])
    lat2 = deg_to_rad(localisation2["coords"]["latitude"])
    lon2 = deg_to_rad(localisation2["coords"]["longitude"])

    delta_lat = lat2 - lat1
    delta_lon = lon2 - lon1

    a = math.sin(delta_lat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(delta_lon / 2) ** 2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    distance = R * c
    return distance

localisation_depart = {
    "coords": {
        "latitude": 43.7102,
        "longitude": 7.2620,
        "altitude": 0,
        "accuracy": 5.2,
        "altitudeAccuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3,
    },
    "timestamp": 1684157825000
}

localisation_arrivee = {
    "coords": {
        "latitude": 50.7102,
        "longitude": 10.2620,
        "altitude": 0,
        "accuracy": 5.2,
        "altitudeAccuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3,
    },
    "timestamp": 1684157825000
}

distance = calculer_distance(localisation_depart, localisation_arrivee)
print(f"Distance approximative sur la mer : {distance:.2f} km")
