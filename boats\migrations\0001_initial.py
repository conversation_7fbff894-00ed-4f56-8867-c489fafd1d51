# Generated by Django 4.2.8 on 2025-05-30 23:54

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Boat",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="nom"
                    ),
                ),
                (
                    "registration_number",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        unique=True,
                        verbose_name="numéro d'immatriculation",
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="couleur"
                    ),
                ),
                (
                    "capacity",
                    models.IntegerField(blank=True, null=True, verbose_name="capacité"),
                ),
                (
                    "boat_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("BLUE", "Blue"),
                            ("CLASSIC", "Classic"),
                            ("LUXE", "Luxe"),
                            ("BOAT_XL", "Boat XL"),
                            ("NAVETTE", "Navette"),
                        ],
                        max_length=20,
                        null=True,
                        verbose_name="type de bateau",
                    ),
                ),
                (
                    "fuel_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("DIESEL", "Diesel"),
                            ("GASOLINE", "Essence"),
                            ("ELECTRIC", "Électrique"),
                            ("HYBRID", "Hybride"),
                        ],
                        max_length=20,
                        null=True,
                        verbose_name="type de carburant",
                    ),
                ),
                (
                    "fuel_consumption",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="L/100km",
                        max_digits=5,
                        null=True,
                        verbose_name="consommation de carburant",
                    ),
                ),
                (
                    "photos",
                    models.JSONField(
                        default=list,
                        help_text="Liste de liens (max 4)",
                        verbose_name="photos",
                    ),
                ),
                (
                    "zone_served",
                    models.CharField(
                        blank=True,
                        help_text="Zone géographique desservie",
                        max_length=255,
                        verbose_name="zone desservie",
                    ),
                ),
                (
                    "radius",
                    models.PositiveIntegerField(
                        default=10,
                        help_text="Rayon de service en km (1-100)",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="rayon (km)",
                    ),
                ),
                (
                    "is_available",
                    models.BooleanField(default=True, verbose_name="disponible"),
                ),
                (
                    "last_maintenance",
                    models.DateField(
                        blank=True, null=True, verbose_name="dernière maintenance"
                    ),
                ),
                (
                    "next_maintenance",
                    models.DateField(
                        blank=True, null=True, verbose_name="prochaine maintenance"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                (
                    "captain",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="boat",
                        to="accounts.captain",
                    ),
                ),
                (
                    "establishment",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="boats",
                        to="accounts.establishment",
                    ),
                ),
            ],
            options={
                "verbose_name": "bateau",
                "verbose_name_plural": "bateaux",
            },
        ),
        migrations.CreateModel(
            name="MaintenanceRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "maintenance_type",
                    models.CharField(
                        choices=[
                            ("ROUTINE", "Maintenance de routine"),
                            ("REPAIR", "Réparation"),
                            ("INSPECTION", "Inspection"),
                            ("EMERGENCY", "Urgence"),
                        ],
                        max_length=20,
                        verbose_name="type de maintenance",
                    ),
                ),
                ("description", models.TextField(verbose_name="description")),
                (
                    "cost",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="coût"
                    ),
                ),
                ("performed_at", models.DateTimeField(verbose_name="effectué le")),
                (
                    "performed_by",
                    models.CharField(max_length=100, verbose_name="effectué par"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="notes")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "boat",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="maintenance_records",
                        to="boats.boat",
                    ),
                ),
            ],
            options={
                "verbose_name": "registre de maintenance",
                "verbose_name_plural": "registres de maintenance",
                "ordering": ["-performed_at"],
            },
        ),
    ]
