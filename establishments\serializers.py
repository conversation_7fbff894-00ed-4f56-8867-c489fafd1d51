"""
Sérialiseurs pour l'application establishments.
"""

from rest_framework import serializers
from accounts.models import Establishment, Captain
from boats.models import Boat
from trips.models import ShuttleTripRequest
from payments.models import Payment, Transaction


class EstablishmentDashboardSerializer(serializers.Serializer):
    """Sérialiseur pour les données du tableau de bord"""
    
    establishment_name = serializers.CharField()
    establishment_type = serializers.CharField()
    available_balance = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_shuttles = serializers.IntegerField()
    pending_requests = serializers.IntegerField()
    availability = serializers.BooleanField()
    shuttles = serializers.ListField()


class ShuttleRequestSerializer(serializers.ModelSerializer):
    """Sérialiseur pour les demandes de navettes"""
    
    client_name = serializers.CharField(source='client.user.get_full_name', read_only=True)
    client_phone = serializers.CharField(source='client.user.phone_number', read_only=True)
    departure = serializers.SerializerMethodField()
    destination = serializers.SerializerMethodField()
    
    class Meta:
        model = ShuttleTripRequest
        fields = [
            'id', 'client_name', 'client_phone', 'departure', 'destination',
            'departure_date', 'departure_time', 'passenger_count', 'message',
            'status', 'created_at'
        ]
    
    def get_departure(self, obj):
        if obj.departure_location:
            return obj.departure_location.get('city_name', '')
        return ''
    
    def get_destination(self, obj):
        if obj.arrival_location:
            return obj.arrival_location.get('city_name', '')
        return ''


class BoatmanRegistrationSerializer(serializers.Serializer):
    """Sérialiseur pour l'enregistrement d'un batelier"""
    
    email = serializers.EmailField()
    first_name = serializers.CharField(max_length=30)
    last_name = serializers.CharField(max_length=30)
    phone_number = serializers.CharField(max_length=20, required=False, allow_blank=True)
    experience = serializers.CharField(max_length=500, required=False, allow_blank=True)
    license_number = serializers.CharField(max_length=50, required=False, allow_blank=True)
    
    def validate_email(self, value):
        """Vérifier que l'email n'existe pas déjà"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("Un utilisateur avec cet email existe déjà")
        return value


class BoatmanListSerializer(serializers.ModelSerializer):
    """Sérialiseur pour la liste des bateliers"""
    
    name = serializers.CharField(source='user.get_full_name', read_only=True)
    email = serializers.CharField(source='user.email', read_only=True)
    phone = serializers.CharField(source='user.phone_number', read_only=True)
    boat = serializers.SerializerMethodField()
    registered_at = serializers.CharField(source='user.date_joined', read_only=True)
    
    # Ajouter un champ pour l'ID de l'utilisateur
    id = serializers.IntegerField(source='user.id', read_only=True)
    
    class Meta:
        model = Captain
        fields = [
            'id', 'name', 'email', 'phone', 'experience', 'license_number',
            'availability_status', 'average_rating', 'boat', 'registered_at'
        ]
    
    def get_boat(self, obj):
        """Récupérer les informations du bateau principal"""
        main_boat = obj.boats.first()
        if main_boat:
            return {
                'id': main_boat.id,
                'name': main_boat.name,
                'capacity': main_boat.capacity,
                'boat_type': main_boat.boat_type
            }
        return None


class BoatmanDetailSerializer(serializers.ModelSerializer):
    """Sérialiseur détaillé pour un batelier"""
    
    # Ajouter un champ pour l'ID de l'utilisateur
    id = serializers.IntegerField(source='user.id', read_only=True)
    name = serializers.CharField(source='user.get_full_name', read_only=True)
    email = serializers.CharField(source='user.email', read_only=True)
    phone = serializers.CharField(source='user.phone_number', read_only=True)
    boats = serializers.SerializerMethodField()
    statistics = serializers.SerializerMethodField()
    registered_at = serializers.CharField(source='user.date_joined', read_only=True)
    
    class Meta:
        model = Captain
        fields = [
            'id', 'name', 'email', 'phone', 'experience', 'license_number',
            'availability_status', 'average_rating', 'rate_per_km', 'rate_per_hour',
            'boats', 'statistics', 'registered_at'
        ]
    
    def get_boats(self, obj):
        """Récupérer tous les bateaux du capitaine"""
        boats_data = []
        for boat in obj.boats.all():
            boats_data.append({
                'id': boat.id,
                'name': boat.name,
                'capacity': boat.capacity,
                'boat_type': boat.boat_type,
                'registration_number': boat.registration_number,
                'is_available': boat.is_available
            })
        return boats_data
    
    def get_statistics(self, obj):
        """Calculer les statistiques du capitaine"""
        from trips.models import Trip
        
        total_trips = Trip.objects.filter(captain=obj).count()
        completed_trips = Trip.objects.filter(captain=obj, status='COMPLETED').count()
        
        return {
            'total_trips': total_trips,
            'completed_trips': completed_trips,
            'completion_rate': (completed_trips / total_trips * 100) if total_trips > 0 else 0
        }


class AvailableResourcesSerializer(serializers.Serializer):
    """Sérialiseur pour les ressources disponibles"""
    
    available_boats = serializers.ListField()
    available_captains = serializers.ListField()


class WalletSerializer(serializers.Serializer):
    """Sérialiseur pour le portefeuille"""
    
    balance = serializers.DecimalField(max_digits=10, decimal_places=2)
    currency = serializers.CharField()
    total_earned = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_spent = serializers.DecimalField(max_digits=10, decimal_places=2)
    shuttle_expenses_30d = serializers.DecimalField(max_digits=10, decimal_places=2)
    recent_transactions = serializers.ListField()


class AddFundsSerializer(serializers.Serializer):
    """Sérialiseur pour l'ajout de fonds"""
    
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=0.01)
    payment_method = serializers.ChoiceField(
        choices=[
            ('CARD', 'Carte bancaire'),
            ('BANK_TRANSFER', 'Virement bancaire'),
            ('PAYPAL', 'PayPal')
        ],
        default='CARD'
    )


class PaymentHistorySerializer(serializers.ModelSerializer):
    """Sérialiseur pour l'historique des paiements"""
    
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_method_display = serializers.CharField(source='get_payment_method_display', read_only=True)
    trip_id = serializers.CharField(source='trip.id', read_only=True)
    shuttle_id = serializers.CharField(source='shuttle.id', read_only=True)
    
    class Meta:
        model = Payment
        fields = [
            'id', 'type_display', 'amount', 'status_display', 'payment_method_display',
            'description', 'trip_id', 'shuttle_id', 'created_at', 'completed_at'
        ]


class PaymentStatsSerializer(serializers.Serializer):
    """Sérialiseur pour les statistiques de paiement"""
    
    period_days = serializers.IntegerField()
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_count = serializers.IntegerField()
    average_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    payment_types = serializers.ListField()
    daily_evolution = serializers.ListField()
