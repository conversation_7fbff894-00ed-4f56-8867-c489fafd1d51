#!/usr/bin/env python
"""
Test final du système de navette optimisé
"""

import os
import django
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, Client
from trips.serializers import ShuttleTripRequestSerializer
from django.utils import timezone
from datetime import date, timedelta

def test_complete_shuttle_system():
    print("🚀 Test complet du système de navette optimisé")
    print("=" * 60)
    
    # 1. Payload utilisateur (SANS arrival_location)
    user_payload = {
        "departure_location": {
            "city_name": "Port de Cannes",
            "coordinates": {
                "latitude": 43.5528,
                "longitude": 7.0174
            },
            "timestamp": timezone.now().isoformat()
        },
        "passenger_count": 2,
        "establishment": None,  # Sera rempli
        "departure_date": (date.today() + timedelta(days=1)).strftime('%Y-%m-%d'),
        "departure_time": "19:30:00",
        "message": "Réservation navette - Test final"
    }
    
    # 2. Trouver un établissement
    establishment = Establishment.objects.filter(
        longitude__isnull=False, 
        latitude__isnull=False
    ).first()
    
    if not establishment:
        print("❌ Aucun établissement avec coordonnées trouvé")
        return False
    
    user_payload['establishment'] = establishment.user.id
    
    print("📝 ÉTAPE 1: Payload client (ce que le front-end envoie)")
    print("=" * 50)
    print(json.dumps(user_payload, indent=2, default=str))
    
    # 3. Validation du sérialiseur
    print("\n🔍 ÉTAPE 2: Validation backend")
    print("=" * 30)
    
    serializer = ShuttleTripRequestSerializer(data=user_payload)
    if not serializer.is_valid():
        print(f"❌ Erreurs de validation: {serializer.errors}")
        return False
    
    print("✅ Payload valide")
    print("✅ Le système va utiliser automatiquement les coordonnées de l'établissement")
    
    # 4. Simuler la création (comme dans la vue)
    client = Client.objects.first()
    if not client:
        print("❌ Aucun client trouvé")
        return False
    
    print(f"✅ Client: {client.user.email}")
    print(f"✅ Établissement: {establishment.name}")
    print(f"   Coordonnées établissement: {establishment.latitude}, {establishment.longitude}")
    
    # 5. Créer la navette
    shuttle_request = serializer.save(client=client, establishment=establishment)
    
    # 6. Calculer la distance
    distance = shuttle_request.calculate_distance()
    print(f"✅ Distance calculée: {distance} km")
    
    # 7. Simuler la réponse complète de l'API
    departure_coords = user_payload['departure_location']['coordinates']
    establishment_coords = {
        'latitude': float(establishment.latitude),
        'longitude': float(establishment.longitude)
    }
    
    api_response = {
        'trip_request': ShuttleTripRequestSerializer(shuttle_request).data,
        'message': 'Demande de navette créée. L\'établissement sera notifié.',
        'distance_to_establishment': f"{distance} km",
        'coordinates': {
            'departure': departure_coords,
            'destination': establishment_coords
        },
        'route_info': {
            'departure_location': user_payload['departure_location']['city_name'],
            'destination_location': establishment.name,
            'distance_km': float(distance) if distance else 0
        }
    }
    
    print("\n📡 ÉTAPE 3: Réponse API complète")
    print("=" * 35)
    print("✅ Données pour le front-end:")
    print(f"   🗺️  Coordonnées départ: {api_response['coordinates']['departure']}")
    print(f"   🏢 Coordonnées destination: {api_response['coordinates']['destination']}")
    print(f"   📏 Distance: {api_response['route_info']['distance_km']} km")
    print(f"   🚩 De: {api_response['route_info']['departure_location']}")
    print(f"   🎯 Vers: {api_response['route_info']['destination_location']}")
    
    print("\n✅ RÉSUMÉ DES AMÉLIORATIONS:")
    print("=" * 40)
    print("1. ✅ Plus besoin d'arrival_location dans le payload client")
    print("2. ✅ Calcul automatique de distance avec coordonnées établissement")
    print("3. ✅ Réponse enrichie avec coordonnées pour le front-end")
    print("4. ✅ Informations de route structurées")
    print("5. ✅ Distance précise (plus d'erreur 2560 km)")
    
    return True

if __name__ == "__main__":
    success = test_complete_shuttle_system()
    print(f"\n{'🎉 SYSTÈME OPTIMISÉ AVEC SUCCÈS!' if success else '❌ ÉCHEC DU TEST'}")
    
    if success:
        print("\n🚀 Le système de navette est maintenant:")
        print("   - Plus simple pour les clients")
        print("   - Plus précis dans les calculs")
        print("   - Plus riche en données pour le front-end")
        print("   - Plus robuste contre les erreurs")
