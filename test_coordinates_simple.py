#!/usr/bin/env python
"""
Test simple des coordonnées
"""

import os
import django
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment

def test_coordinates():
    print("🧪 Test simple des coordonnées")
    print("=" * 40)
    
    # Récupérer le premier établissement
    establishment = Establishment.objects.first()
    
    if not establishment:
        print("❌ Aucun établissement trouvé")
        return False
    
    print(f"Établissement: {establishment.name}")
    print(f"Longitude actuelle: {establishment.longitude}")
    print(f"Latitude actuelle: {establishment.latitude}")
    
    # Tester l'assignation
    try:
        establishment.longitude = Decimal('7.0167')
        establishment.latitude = Decimal('43.5528')
        establishment.save()
        
        print(f"✅ Sauvegarde réussie")
        print(f"Longitude: {establishment.longitude}")
        print(f"Latitude: {establishment.latitude}")
        
        # Test refresh
        establishment.refresh_from_db()
        print(f"Après refresh - Longitude: {establishment.longitude}")
        print(f"Après refresh - Latitude: {establishment.latitude}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_coordinates()
    print(f"\n{'✅ Test réussi' if success else '❌ Test échoué'}")
