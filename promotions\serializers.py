from rest_framework import serializers
from .models import Promotion
from django.utils import timezone

class PromotionSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour le modèle Promotion.
    """
    days_remaining = serializers.SerializerMethodField()
    target_details = serializers.SerializerMethodField()
    
    class Meta:
        model = Promotion
        fields = [
            'id', 'user', 'type', 'target_type', 'target_id', 'target_details',
            'start_date', 'end_date', 'status', 'amount_paid', 'transaction_id',
            'features', 'metadata', 'created_at', 'updated_at', 'days_remaining'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at', 'days_remaining']
    
    def get_days_remaining(self, obj):
        """
        Calcule le nombre de jours restants pour la promotion.
        """
        if obj.status != 'ACTIVE':
            return 0
            
        now = timezone.now()
        if obj.end_date < now:
            return 0
            
        days_remaining = (obj.end_date - now).days
        return max(0, days_remaining)
    
    def get_target_details(self, obj):
        """
        Récupère les détails de la cible de la promotion.
        """
        # Selon le type de cible, récupérer les informations appropriées
        if obj.target_type == 'CAPTAIN':
            from accounts.models import Captain, User
            try:
                # Utiliser user_id au lieu de id car Captain utilise user comme clé primaire
                captain = Captain.objects.get(user_id=obj.target_id)
                return {
                    'id': captain.user.id,
                    'name': captain.user.get_full_name(),
                    'rating': captain.rating,
                    'profile_picture': captain.profile_picture.url if captain.profile_picture else None
                }
            except Captain.DoesNotExist:
                return None
        
        elif obj.target_type == 'BOAT':
            from boats.models import Boat
            try:
                boat = Boat.objects.get(id=obj.target_id)
                return {
                    'id': boat.id,
                    'name': boat.name,
                    'type': boat.type,
                    'image': boat.primary_image.url if hasattr(boat, 'primary_image') and boat.primary_image else None
                }
            except Boat.DoesNotExist:
                return None
        
        elif obj.target_type == 'ESTABLISHMENT':
            from accounts.models import Establishment
            try:
                establishment = Establishment.objects.get(id=obj.target_id)
                return {
                    'id': establishment.id,
                    'name': establishment.name,
                    'type': establishment.type,
                    'logo': establishment.logo.url if establishment.logo else None,
                    'address': establishment.address
                }
            except Establishment.DoesNotExist:
                return None
        
        return None
