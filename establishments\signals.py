"""
Signaux pour l'application establishments.
"""

from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model

from accounts.models import Establishment, Captain
from notifications.services import create_notification

User = get_user_model()


@receiver(post_save, sender=Captain)
def notify_establishment_on_boatman_registration(sender, instance, created, **kwargs):
    """
    Notifier l'établissement quand un nouveau batelier est enregistré.
    """
    if created and hasattr(instance, 'registered_by_establishment') and instance.registered_by_establishment is not None:
        # Si le capitaine a été enregistré par un établissement
        establishment_user = instance.registered_by_establishment.user
        
        create_notification(
            user=establishment_user,
            title="Nouveau batelier enregistré",
            message=f"Le batelier {instance.user.get_full_name()} a été enregistré avec succès",
            notification_type="BOATMAN_REGISTERED",
            related_object_id=instance.user.id
        )


@receiver(post_save, sender=User)
def setup_establishment_defaults(sender, instance, created, **kwargs):
    """
    Configurer les paramètres par défaut pour un nouvel établissement.
    """
    if created and hasattr(instance, 'establishment'):
        establishment = instance.establishment
        
        # Configurer les horaires par défaut
        if not establishment.opening_hours:
            establishment.opening_hours = {
                'monday': {'open': '08:00', 'close': '18:00', 'closed': False},
                'tuesday': {'open': '08:00', 'close': '18:00', 'closed': False},
                'wednesday': {'open': '08:00', 'close': '18:00', 'closed': False},
                'thursday': {'open': '08:00', 'close': '18:00', 'closed': False},
                'friday': {'open': '08:00', 'close': '18:00', 'closed': False},
                'saturday': {'open': '09:00', 'close': '17:00', 'closed': False},
                'sunday': {'open': '09:00', 'close': '17:00', 'closed': False}
            }
        
        # Configurer les services par défaut
        if not establishment.services_offered:
            establishment.services_offered = [
                'Navettes gratuites',
                'Transport aéroport',
                'Excursions'
            ]
        
        # Configurer les réseaux sociaux par défaut
        if not establishment.social_media:
            establishment.social_media = {
                'facebook': '',
                'instagram': '',
                'twitter': '',
                'website': ''
            }
        
        establishment.save()
