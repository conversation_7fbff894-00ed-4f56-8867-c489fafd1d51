# Documentation API RAG Commodore

Cette documentation décrit les endpoints de l'API RAG (Retrieval Augmented Generation) de Commodore, permettant d'intégrer le chatbot intelligent dans différentes applications.

## Introduction

Le système RAG (Retrieval Augmented Generation) de <PERSON> combine la recherche d'informations pertinentes dans une base de connaissances avec la génération de réponses naturelles à l'aide de l'IA. Cette approche permet de fournir des réponses précises et contextuelles aux questions des utilisateurs, tout en s'appuyant sur des informations fiables et à jour.

### Caractéristiques principales

- **Recherche sémantique** : Utilisation d'embeddings vectoriels pour trouver les informations les plus pertinentes
- **Génération de réponses naturelles** : Utilisation du modèle Gemini pour générer des réponses en langage naturel
- **Support hors ligne** : Possibilité de télécharger des données pour une utilisation hors ligne
- **Cache Redis** : Optimisation des performances grâce à la mise en cache des réponses
- **Système de feedback** : Collecte des retours utilisateurs pour améliorer la qualité des réponses

## Table des matières

1. [Authentification](#authentification)
2. [Endpoints du chatbot](#endpoints-du-chatbot)
   - [Créer une session de chat](#créer-une-session-de-chat)
   - [Envoyer un message](#envoyer-un-message)
   - [Récupérer l'historique des messages](#récupérer-lhistorique-des-messages)
3. [Endpoints de gestion des documents](#endpoints-de-gestion-des-documents)
   - [Lister les documents](#lister-les-documents)
   - [Ajouter un document](#ajouter-un-document)
   - [Mettre à jour un document](#mettre-à-jour-un-document)
4. [Endpoints de support hors ligne](#endpoints-de-support-hors-ligne)
   - [Récupérer les données hors ligne](#récupérer-les-données-hors-ligne)
   - [Récupérer les FAQ hors ligne](#récupérer-les-faq-hors-ligne)
5. [Endpoints de feedback](#endpoints-de-feedback)
   - [Soumettre un feedback](#soumettre-un-feedback)

## Authentification

L'API utilise l'authentification par token JWT. Pour obtenir un token, utilisez l'endpoint d'authentification standard de Commodore.

```
POST /api/auth/token/
```

Exemple de requête :
```json
{
  "email": "<EMAIL>",
  "password": "motdepasse"
}
```

Exemple de réponse :
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

Incluez le token d'accès dans l'en-tête Authorization de vos requêtes :
```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Endpoints du chatbot

### Créer une session de chat

Crée une nouvelle session de chat pour l'utilisateur.

```
POST /api/rag/sessions/
```

Exemple de requête :
```json
{
  "name": "Assistance réservation"
}
```

Exemple de réponse :
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "Assistance réservation",
  "created_at": "2025-05-14T12:34:56.789Z",
  "updated_at": "2025-05-14T12:34:56.789Z"
}
```

### Envoyer un message

Envoie un message au chatbot et reçoit une réponse.

```
POST /api/rag/chat/api/
```

Exemple de requête :
```json
{
  "message": "Comment réserver un bateau-taxi ?",
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "profile": "Client"
}
```

Exemple de réponse :
```json
{
  "response": "Pour réserver un bateau-taxi sur Commodore, suivez ces étapes :\n\n1. Ouvrez l'application Commodore\n2. Sélectionnez votre point de départ et d'arrivée sur la carte\n3. Choisissez l'heure de départ souhaitée\n4. Indiquez le nombre de passagers\n5. Vérifiez le prix estimé\n6. Confirmez votre réservation\n\nVous recevrez une confirmation par email avec un QR code à présenter au capitaine.",
  "sources": [
    {
      "title": "Guide de réservation",
      "section": "Réservation de bateau-taxi"
    }
  ]
}
```

### Récupérer l'historique des messages

Récupère l'historique des messages d'une session de chat.

```
GET /api/rag/sessions/{session_id}/messages/
```

Exemple de réponse :
```json
[
  {
    "id": "7f9c24e8-85bb-4fcb-a4c2-6f7a5e1234ab",
    "role": "user",
    "content": "Comment réserver un bateau-taxi ?",
    "created_at": "2025-05-14T12:35:10.123Z"
  },
  {
    "id": "9a8b7c6d-5e4f-3a2b-1c0d-9e8f7a6b5c4d",
    "role": "assistant",
    "content": "Pour réserver un bateau-taxi sur Commodore, suivez ces étapes :\n\n1. Ouvrez l'application Commodore\n2. Sélectionnez votre point de départ et d'arrivée sur la carte\n3. Choisissez l'heure de départ souhaitée\n4. Indiquez le nombre de passagers\n5. Vérifiez le prix estimé\n6. Confirmez votre réservation\n\nVous recevrez une confirmation par email avec un QR code à présenter au capitaine.",
    "created_at": "2025-05-14T12:35:11.456Z"
  }
]
```

## Endpoints de gestion des documents

### Lister les documents

Récupère la liste des documents disponibles dans la base de connaissances.

```
GET /api/rag/documents/
```

Exemple de réponse :
```json
[
  {
    "id": "1a2b3c4d-5e6f-7g8h-9i0j-1k2l3m4n5o6p",
    "title": "Guide utilisateur Commodore",
    "category": "Documentation",
    "created_at": "2025-05-01T10:00:00.000Z",
    "updated_at": "2025-05-10T15:30:00.000Z",
    "embedding_generated": true
  }
]
```

### Ajouter un document

Ajoute un nouveau document à la base de connaissances.

```
POST /api/rag/documents/
```

Exemple de requête :
```json
{
  "title": "FAQ Commodore",
  "content": "# FAQ Commodore\n\n## Comment réserver un bateau-taxi ?\nPour réserver un bateau-taxi...\n\n## Comment payer ma course ?\nVous pouvez payer votre course...",
  "category": "FAQ"
}
```

Exemple de réponse :
```json
{
  "id": "9i8h7g6f-5e4d-3c2b-1a0z-9y8x7w6v5u4t",
  "title": "FAQ Commodore",
  "category": "FAQ",
  "created_at": "2025-05-14T14:00:00.000Z",
  "updated_at": "2025-05-14T14:00:00.000Z",
  "embedding_generated": false
}
```

### Mettre à jour un document

Met à jour un document existant dans la base de connaissances.

```
PUT /api/rag/documents/{document_id}/
```

Exemple de requête :
```json
{
  "title": "FAQ Commodore - Mise à jour",
  "content": "# FAQ Commodore\n\n## Comment réserver un bateau-taxi ?\nPour réserver un bateau-taxi...\n\n## Comment payer ma course ?\nVous pouvez payer votre course...\n\n## Comment annuler ma réservation ?\nPour annuler une réservation...",
  "category": "FAQ"
}
```

Exemple de réponse :
```json
{
  "id": "9i8h7g6f-5e4d-3c2b-1a0z-9y8x7w6v5u4t",
  "title": "FAQ Commodore - Mise à jour",
  "category": "FAQ",
  "created_at": "2025-05-14T14:00:00.000Z",
  "updated_at": "2025-05-14T15:30:00.000Z",
  "embedding_generated": false
}
```

## Endpoints de support hors ligne

### Récupérer les données hors ligne

Récupère un package de données pour le support hors ligne, incluant les FAQ et les documents importants.

```
GET /api/rag/offline/data/?profile=Client
```

Exemple de réponse :
```json
{
  "faqs": {
    "client": [
      {
        "question": "Comment réserver un bateau-taxi ?",
        "response": "Pour réserver un bateau-taxi sur Commodore, ouvrez l'application, sélectionnez votre point de départ et d'arrivée, choisissez l'heure de départ, indiquez le nombre de passagers, vérifiez le prix et confirmez votre réservation. Vous recevrez une confirmation par email avec un QR code à présenter au capitaine.",
        "intent": "specific",
        "entities": ["réservation", "bateau", "taxi"],
        "profile": "Client"
      }
    ],
    "captain": [],
    "establishment": [],
    "general": []
  },
  "documents": [
    {
      "title": "Guide utilisateur Commodore",
      "content": "Extrait du guide utilisateur...",
      "category": "Documentation",
      "updated_at": "2025-05-10T15:30:00.000Z"
    }
  ],
  "version": "1.0",
  "generated_at": "2025-05-14T16:00:00.000Z",
  "expires_at": "2025-05-21T16:00:00.000Z"
}
```

### Récupérer les FAQ hors ligne

Récupère uniquement les FAQ pour le support hors ligne.

```
GET /api/rag/offline/faqs/?profile=Client&limit=5
```

Exemple de réponse :
```json
{
  "faqs": [
    {
      "question": "Comment réserver un bateau-taxi ?",
      "response": "Pour réserver un bateau-taxi sur Commodore, ouvrez l'application, sélectionnez votre point de départ et d'arrivée, choisissez l'heure de départ, indiquez le nombre de passagers, vérifiez le prix et confirmez votre réservation. Vous recevrez une confirmation par email avec un QR code à présenter au capitaine.",
      "intent": "specific",
      "entities": ["réservation", "bateau", "taxi"],
      "profile": "Client"
    },
    {
      "question": "Comment payer ma course ?",
      "response": "Vous pouvez payer votre course sur Commodore par carte bancaire (Visa, Mastercard, Apple Pay) lors de la réservation ou utiliser des crédits prépayés. Pour recharger vos crédits, allez dans votre profil > \"Mon solde\".",
      "intent": "specific",
      "entities": ["paiement", "course", "crédit"],
      "profile": "Client"
    }
  ]
}
```

## Endpoints de feedback

### Soumettre un feedback

Soumet un feedback sur une réponse du chatbot.

```
POST /api/rag/feedback/
```

Exemple de requête :
```json
{
  "message_id": "9a8b7c6d-5e4f-3a2b-1c0d-9e8f7a6b5c4d",
  "feedback_type": "positive",
  "comments": "Réponse très claire et utile !"
}
```

Exemple de réponse :
```json
{
  "id": "1q2w3e4r-5t6y-7u8i-9o0p-1a2s3d4f5g6h",
  "message_id": "9a8b7c6d-5e4f-3a2b-1c0d-9e8f7a6b5c4d",
  "feedback_type": "positive",
  "comments": "Réponse très claire et utile !",
  "created_at": "2025-05-14T17:00:00.000Z"
}
```

## Exemples d'utilisation pour les développeurs

Cette section fournit des exemples concrets d'utilisation de l'API RAG dans différents contextes de développement.

### Exemple 1: Intégration dans une application React Native

```javascript
// Fonction pour créer une session de chat
const createChatSession = async () => {
  try {
    const response = await fetch('https://api.commodore.com/api/rag/sessions/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        name: 'Session mobile'
      })
    });

    const data = await response.json();
    return data.id; // Retourne l'ID de la session
  } catch (error) {
    console.error('Erreur lors de la création de la session:', error);
    return null;
  }
};

// Fonction pour envoyer un message et recevoir une réponse
const sendMessage = async (sessionId, message, userProfile) => {
  try {
    const response = await fetch('https://api.commodore.com/api/rag/chat/api/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        session_id: sessionId,
        message: message,
        profile: userProfile
      })
    });

    return await response.json();
  } catch (error) {
    console.error('Erreur lors de l\'envoi du message:', error);
    return { error: 'Impossible de communiquer avec le serveur' };
  }
};

// Exemple d'utilisation dans un composant React Native
const ChatScreen = () => {
  const [sessionId, setSessionId] = useState(null);
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const userProfile = 'Client';

  useEffect(() => {
    // Créer une session au chargement du composant
    const initSession = async () => {
      const id = await createChatSession();
      setSessionId(id);
    };

    initSession();
  }, []);

  const handleSend = async () => {
    if (!inputText.trim() || !sessionId) return;

    // Ajouter le message de l'utilisateur à la liste
    const userMessage = {
      id: Date.now().toString(),
      content: inputText,
      role: 'user'
    };

    setMessages(prevMessages => [...prevMessages, userMessage]);
    setInputText('');

    // Envoyer le message au chatbot
    const response = await sendMessage(sessionId, inputText, userProfile);

    // Ajouter la réponse du chatbot à la liste
    const botMessage = {
      id: Date.now().toString() + '-bot',
      content: response.response || 'Désolé, je n\'ai pas pu générer une réponse.',
      role: 'assistant',
      sources: response.sources || []
    };

    setMessages(prevMessages => [...prevMessages, botMessage]);
  };

  // Rendu du composant...
};
```

### Exemple 2: Support hors ligne dans une application mobile

```javascript
// Fonction pour télécharger les données hors ligne
const downloadOfflineData = async (userProfile) => {
  try {
    const response = await fetch(`https://api.commodore.com/api/rag/offline/data/?profile=${userProfile}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    const data = await response.json();

    // Stocker les données dans le stockage local
    await AsyncStorage.setItem('offlineData', JSON.stringify(data));
    await AsyncStorage.setItem('offlineDataTimestamp', Date.now().toString());

    return data;
  } catch (error) {
    console.error('Erreur lors du téléchargement des données hors ligne:', error);
    return null;
  }
};

// Fonction pour rechercher dans les données hors ligne
const searchOfflineData = async (query, userProfile) => {
  try {
    // Récupérer les données hors ligne
    const offlineDataStr = await AsyncStorage.getItem('offlineData');
    if (!offlineDataStr) return null;

    const offlineData = JSON.parse(offlineDataStr);

    // Rechercher dans les FAQ
    const faqCategory = userProfile.toLowerCase();
    const faqs = [
      ...(offlineData.faqs[faqCategory] || []),
      ...(offlineData.faqs.general || [])
    ];

    // Fonction simple de recherche par mots-clés
    const findRelevantFaqs = (query, faqs) => {
      const keywords = query.toLowerCase().split(' ');
      return faqs.filter(faq => {
        const questionLower = faq.question.toLowerCase();
        return keywords.some(keyword => questionLower.includes(keyword));
      });
    };

    const relevantFaqs = findRelevantFaqs(query, faqs);

    if (relevantFaqs.length > 0) {
      // Retourner la FAQ la plus pertinente
      return {
        response: relevantFaqs[0].response,
        offline: true,
        sources: []
      };
    }

    // Si aucune FAQ pertinente n'est trouvée, retourner un message par défaut
    return {
      response: "Je n'ai pas trouvé de réponse à votre question en mode hors ligne. Veuillez vous connecter à Internet pour obtenir une réponse plus précise.",
      offline: true,
      sources: []
    };
  } catch (error) {
    console.error('Erreur lors de la recherche hors ligne:', error);
    return null;
  }
};

// Fonction pour envoyer un message avec support hors ligne
const sendMessageWithOfflineSupport = async (sessionId, message, userProfile) => {
  // Vérifier la connexion Internet
  const isConnected = await NetInfo.fetch().then(state => state.isConnected);

  if (isConnected) {
    // Mode en ligne: utiliser l'API
    return await sendMessage(sessionId, message, userProfile);
  } else {
    // Mode hors ligne: rechercher dans les données locales
    return await searchOfflineData(message, userProfile);
  }
};
```

### Exemple 3: Intégration dans une application web (React)

```javascript
// Fonction pour récupérer l'historique des messages
const fetchMessageHistory = async (sessionId) => {
  try {
    const response = await fetch(`https://api.commodore.com/api/rag/sessions/${sessionId}/messages/`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    return await response.json();
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique:', error);
    return [];
  }
};

// Fonction pour soumettre un feedback
const submitFeedback = async (messageId, feedbackType, comments = '') => {
  try {
    const response = await fetch('https://api.commodore.com/api/rag/feedback/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        message_id: messageId,
        feedback_type: feedbackType,
        comments: comments
      })
    });

    return await response.json();
  } catch (error) {
    console.error('Erreur lors de la soumission du feedback:', error);
    return { error: 'Impossible de soumettre le feedback' };
  }
};

// Exemple d'utilisation dans un composant React
const ChatComponent = () => {
  // États et logique similaires à l'exemple React Native
  // ...

  // Fonction pour gérer le feedback
  const handleFeedback = async (messageId, isPositive) => {
    const feedbackType = isPositive ? 'positive' : 'negative';
    let comments = '';

    if (!isPositive) {
      // Demander des commentaires pour le feedback négatif
      comments = prompt('Pourquoi cette réponse n\'était-elle pas utile ?');
    }

    await submitFeedback(messageId, feedbackType, comments);
    alert('Merci pour votre feedback !');
  };

  // Rendu du composant...
};
```
