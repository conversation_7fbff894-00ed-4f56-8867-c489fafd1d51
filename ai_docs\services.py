import os
import json
import logging
from typing import Dict, List, Optional
from django.conf import settings
from django.urls import get_resolver
from django.apps import apps
from .models import APIDocumentation, APIEndpoint

logger = logging.getLogger(__name__)

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI package not installed. Install with: pip install openai")


class APIDocumentationService:
    """
    Service pour générer automatiquement la documentation de l'API avec OpenAI
    """
    
    def __init__(self):
        self.openai_api_key = getattr(settings, 'OPENAI_API_KEY', os.getenv('OPENAI_API_KEY'))
        self.openai_model = getattr(settings, 'OPENAI_MODEL', os.getenv('OPENAI_MODEL', 'gpt-4'))
        self.enable_ai_docs = getattr(settings, 'ENABLE_AI_DOCS', os.getenv('ENABLE_AI_DOCS', 'false').lower() == 'true')
        
        if OPENAI_AVAILABLE and self.openai_api_key:
            openai.api_key = self.openai_api_key
    
    def scan_api_endpoints(self) -> List[Dict]:
        """
        Scanne automatiquement tous les endpoints de l'API Django
        """
        endpoints = []
        resolver = get_resolver()
        
        def extract_endpoints(url_patterns, prefix=''):
            for pattern in url_patterns:
                if hasattr(pattern, 'url_patterns'):
                    # C'est un include(), on descend récursivement
                    extract_endpoints(pattern.url_patterns, prefix + str(pattern.pattern))
                else:
                    # C'est un endpoint final
                    path = prefix + str(pattern.pattern)
                    if hasattr(pattern, 'callback'):
                        view = pattern.callback
                        if hasattr(view, 'view_class'):
                            # Vue basée sur une classe
                            view_class = view.view_class
                            app_name = view_class.__module__.split('.')[0]
                            
                            # Déterminer les méthodes HTTP supportées
                            methods = []
                            if hasattr(view_class, 'http_method_names'):
                                methods = [m.upper() for m in view_class.http_method_names if m != 'options']
                            else:
                                methods = ['GET']
                            
                            for method in methods:
                                endpoints.append({
                                    'path': path,
                                    'method': method,
                                    'app_name': app_name,
                                    'view_name': view_class.__name__,
                                    'description': getattr(view_class, '__doc__', '').strip() if hasattr(view_class, '__doc__') else ''
                                })
                        else:
                            # Vue basée sur une fonction
                            app_name = view.__module__.split('.')[0]
                            endpoints.append({
                                'path': path,
                                'method': 'GET',  # Par défaut
                                'app_name': app_name,
                                'view_name': view.__name__,
                                'description': getattr(view, '__doc__', '').strip() if hasattr(view, '__doc__') else ''
                            })
        
        try:
            extract_endpoints(resolver.url_patterns)
        except Exception as e:
            logger.error(f"Erreur lors du scan des endpoints: {e}")
        
        return endpoints
    
    def save_endpoints_to_db(self, endpoints: List[Dict]):
        """
        Sauvegarde les endpoints scannés en base de données
        """
        for endpoint_data in endpoints:
            endpoint, created = APIEndpoint.objects.get_or_create(
                path=endpoint_data['path'],
                method=endpoint_data['method'],
                defaults={
                    'app_name': endpoint_data['app_name'],
                    'view_name': endpoint_data['view_name'],
                    'description': endpoint_data['description']
                }
            )
            if created:
                logger.info(f"Nouvel endpoint ajouté: {endpoint.method} {endpoint.path}")
    
    def generate_api_documentation(self) -> Optional[str]:
        """
        Génère la documentation complète de l'API avec OpenAI
        """
        if not OPENAI_AVAILABLE:
            logger.error("OpenAI n'est pas disponible")
            return None
        
        if not self.openai_api_key:
            logger.error("Clé API OpenAI non configurée")
            return None
        
        if not self.enable_ai_docs:
            logger.info("Documentation AI désactivée")
            return None
        
        # Récupérer tous les endpoints
        endpoints = list(APIEndpoint.objects.all())
        
        # Préparer le contexte pour OpenAI
        endpoints_context = []
        for endpoint in endpoints:
            endpoints_context.append({
                'method': endpoint.method,
                'path': endpoint.path,
                'app': endpoint.app_name,
                'description': endpoint.description or 'Pas de description disponible'
            })
        
        # Prompt pour OpenAI
        prompt = f"""
Tu es un expert en documentation d'API. Je vais te donner la liste des endpoints d'une API Django pour une application de réservation de taxis maritimes appelée "Commodore Taxi Boat".

Voici les endpoints de l'API:
{json.dumps(endpoints_context, indent=2, ensure_ascii=False)}

Génère une documentation complète, professionnelle et attrayante en HTML qui inclut:

1. **Introduction générale** sur l'API Commodore Taxi Boat
2. **Guide de démarrage rapide** avec authentification
3. **Organisation par modules** (authentication, accounts, boats, trips, payments, etc.)
4. **Description détaillée de chaque endpoint** avec:
   - Méthode HTTP et URL
   - Description fonctionnelle
   - Paramètres attendus (déduits du contexte)
   - Exemples de réponses JSON
   - Codes d'erreur possibles
5. **Exemples d'utilisation** concrets
6. **Informations sur l'authentification** et les permissions
7. **Style CSS moderne** intégré pour une présentation professionnelle

La documentation doit être:
- **Complète et précise**
- **Facile à naviguer** avec un menu latéral
- **Visuellement attrayante** avec un design moderne
- **Pratique** avec des exemples de code
- **En français** car c'est une API française

Génère uniquement le code HTML complet avec CSS intégré, prêt à être affiché.
"""
        
        try:
            response = openai.ChatCompletion.create(
                model=self.openai_model,
                messages=[
                    {"role": "system", "content": "Tu es un expert en documentation d'API qui génère des documentations HTML complètes et professionnelles."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,
                temperature=0.3
            )
            
            documentation_html = response.choices[0].message.content
            
            # Sauvegarder en base de données
            doc = APIDocumentation.objects.create(
                content=documentation_html,
                openai_model=self.openai_model
            )
            
            logger.info(f"Documentation générée avec succès (ID: {doc.id})")
            return documentation_html
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération de la documentation: {e}")
            return None
    
    def get_latest_documentation(self) -> Optional[APIDocumentation]:
        """
        Récupère la dernière documentation générée
        """
        return APIDocumentation.objects.filter(is_active=True).first()
    
    def refresh_documentation(self) -> bool:
        """
        Rafraîchit complètement la documentation
        """
        try:
            # Scanner les endpoints
            endpoints = self.scan_api_endpoints()
            self.save_endpoints_to_db(endpoints)
            
            # Générer la nouvelle documentation
            doc_html = self.generate_api_documentation()
            
            return doc_html is not None
        except Exception as e:
            logger.error(f"Erreur lors du rafraîchissement de la documentation: {e}")
            return False
