#!/usr/bin/env python3
"""
Test simple pour vérifier les améliorations conversationnelles.
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire du projet au path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Test direct de la classe RagService sans Django
def test_intention_detection():
    """
    Teste la détection d'intentions sans initialiser Django.
    """
    print("🎯 Test de la détection d'intentions")
    print("=" * 40)
    
    # Pas besoin d'importer, on teste directement la logique
    
    # Créer une instance temporaire juste pour tester _analyze_query
    class TestRagService:
        def _analyze_query(self, query: str):
            """Copie de la méthode _analyze_query pour test"""
            query_lower = query.lower().strip()
            
            # Détection des salutations et conversations sociales
            greetings = ["salut", "bonjour", "bonsoir", "hello", "hi", "coucou", "hey"]
            farewells = ["au revoir", "bye", "à bientôt", "merci", "bonne journée", "bonne soirée"]
            social_queries = ["comment allez-vous", "comment ça va", "ça va", "comment vous allez"]
            
            # Détection des questions d'aide générale
            help_patterns = [
                "aide", "aider", "que peux-tu faire", "que pouvez-vous faire", 
                "comment m'aider", "comment puis-je", "j'ai besoin", "pouvez-vous m'aider"
            ]
            
            # Détection des problèmes et frustrations
            problem_patterns = [
                "problème", "souci", "bug", "erreur", "ne fonctionne pas", "ne marche pas",
                "j'arrive pas", "impossible", "bloqué", "coincé", "frustré", "énervé"
            ]
            
            # Détection des questions vagues nécessitant clarification
            vague_patterns = [
                "comment ça marche", "comment faire", "c'est quoi", "qu'est-ce que", 
                "expliquer", "comprendre", "savoir plus", "en savoir plus"
            ]

            # Mots-clés pour questions spécifiques
            specific_keywords = ["comment", "où", "quand", "qui", "pourquoi", "combien", "quel", "quelle", "quels", "quelles"]
            
            # Entités importantes pour Commodore (enrichies)
            entity_patterns = {
                "paiement": ["payer", "paiement", "carte", "crédit", "remboursement", "prix", "tarif", "coût", "facture", "solde"],
                "réservation": ["réserver", "réservation", "commander", "commande", "annuler", "annulation", "modifier", "changer"],
                "qr_code": ["qr", "code", "scanner", "scan", "embarquement", "embarquer"],
                "bateau": ["bateau", "taxi", "navette", "embarcation", "capitaine", "trajet", "course"],
                "compte": ["compte", "profil", "inscription", "connexion", "mot de passe", "login", "s'inscrire"],
                "support": ["support", "aide", "assistance", "contact", "service client", "problème"]
            }

            # Déterminer l'intention principale
            if any(greeting in query_lower for greeting in greetings):
                intent = "greeting"
            elif any(farewell in query_lower for farewell in farewells):
                intent = "farewell"
            elif any(social in query_lower for social in social_queries):
                intent = "social"
            elif any(help_pattern in query_lower for help_pattern in help_patterns):
                intent = "help_request"
            elif any(problem in query_lower for problem in problem_patterns):
                intent = "problem_report"
            elif any(vague in query_lower for vague in vague_patterns):
                intent = "clarification_needed"
            elif any(keyword in query_lower for keyword in specific_keywords):
                intent = "specific_question"
            elif len(query_lower.split()) <= 3:  # Questions très courtes
                intent = "clarification_needed"
            else:
                intent = "general_inquiry"

            # Extraire les entités
            entities = []
            for entity_type, patterns in entity_patterns.items():
                if any(pattern in query_lower for pattern in patterns):
                    entities.append(entity_type)
                    # Ajouter les mots spécifiques trouvés
                    for pattern in patterns:
                        if pattern in query_lower and len(pattern) > 3:
                            entities.append(pattern)

            return intent, entities
    
    test_service = TestRagService()
    
    test_messages = [
        "salut",
        "bonjour comment allez-vous",
        "j'ai un gros problème",
        "comment ça marche commodore",
        "aide-moi s'il te plaît",
        "merci beaucoup",
        "comment réserver un bateau",
        "quel est le prix",
        "au revoir",
        "ça va bien et vous",
        "je suis bloqué avec ma réservation",
        "que peux-tu faire pour moi",
        "c'est quoi le QR code",
        "hello",
        "bonne journée",
    ]
    
    print("Messages testés et leurs intentions détectées:")
    print("-" * 50)
    
    for message in test_messages:
        intent, entities = test_service._analyze_query(message)
        print(f"'{message}' → Intent: {intent}, Entités: {entities}")
    
    print("\n✅ Test de détection d'intentions terminé!")

def test_response_selection():
    """
    Teste la sélection de méthode de réponse.
    """
    print("\n🎯 Test de sélection de méthode de réponse")
    print("=" * 45)
    
    class TestResponseSelector:
        def _select_response_method(self, query: str, intent: str = None):
            """Copie de la méthode pour test"""
            query_lower = query.lower()

            # Utiliser l'intention détectée en priorité
            if intent:
                if intent == "greeting":
                    return 'greeting'
                elif intent == "farewell":
                    return 'farewell'
                elif intent == "social":
                    return 'social'
                elif intent == "help_request":
                    return 'capabilities'
                elif intent == "problem_report":
                    return 'problem_support'
                elif intent == "clarification_needed":
                    return 'clarification'

            # Vérifications spécifiques par mots-clés (fallback)
            if any(kw in query_lower for kw in ["présente", "présentation", "qui es-tu", "qui êtes-vous"]):
                return 'introduction'

            if any(kw in query_lower for kw in ["fonctionnalité", "que peux-tu faire", "aide-moi", "comment m'aider"]):
                return 'capabilities'

            if any(kw in query_lower for kw in ["qr", "qr code", "code", "scanner", "scan"]):
                return 'qr_code'

            if any(kw in query_lower for kw in ["payer", "paiement", "course", "carte", "crédit", "prépayé", "solde", "prix"]):
                return 'payment'

            if any(kw in query_lower for kw in ["réserver", "réservation", "commander", "commande", "bateau", "taxi"]):
                return 'reservation'

            if any(kw in query_lower for kw in ["annuler", "annulation", "remboursement", "remboursé", "rembourser", "annule"]):
                return 'cancellation'

            # Par défaut, utiliser le LLM
            return 'llm'
    
    selector = TestResponseSelector()
    
    test_cases = [
        ("salut", "greeting"),
        ("j'ai un problème", "problem_report"),
        ("comment ça marche", "clarification_needed"),
        ("merci", "farewell"),
        ("aide-moi", "help_request"),
        ("comment réserver", None),
        ("quel est le prix", None),
    ]
    
    print("Test de sélection de méthode:")
    print("-" * 30)
    
    for message, intent in test_cases:
        method = selector._select_response_method(message, intent)
        print(f"'{message}' (intent: {intent}) → Méthode: {method}")
    
    print("\n✅ Test de sélection de méthode terminé!")

if __name__ == "__main__":
    test_intention_detection()
    test_response_selection()
    
    print("\n🎉 Tous les tests terminés!")
    print("\n📋 Résumé des améliorations:")
    print("- ✅ Détection d'intentions conversationnelles")
    print("- ✅ Gestion des salutations et au revoir")
    print("- ✅ Détection des problèmes et demandes d'aide")
    print("- ✅ Questions vagues nécessitant clarification")
    print("- ✅ Sélection de méthode de réponse adaptée")
