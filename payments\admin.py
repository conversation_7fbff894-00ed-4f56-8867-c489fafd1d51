from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Payment, Transaction, Wallet

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """Interface d'administration pour le modèle Payment"""
    list_display = ('id', 'user', 'get_trip', 'amount', 'type', 'status', 'payment_method', 'created_at')
    list_filter = ('type', 'status', 'payment_method', 'created_at')
    search_fields = ('user__email', 'trip__id', 'shuttle__id', 'stripe_payment_id')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'created_at'

    fieldsets = (
        (_('Informations générales'), {
            'fields': ('user', 'type', 'amount', 'currency')
        }),
        (_('Statut et méthode'), {
            'fields': ('status', 'payment_method')
        }),
        (_('Relations'), {
            'fields': ('wallet', 'trip', 'shuttle')
        }),
        (_('Stripe'), {
            'fields': ('stripe_payment_id', 'stripe_payment_intent')
        }),
        (_('Métadonnées'), {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_trip(self, obj):
        """Obtenir l'ID du trajet"""
        if obj.trip:
            return f"Course #{obj.trip.id}"
        elif obj.shuttle:
            return f"Navette #{obj.shuttle.id}"
        return "Pas de trajet"
    get_trip.short_description = _('Course/Navette')
    get_trip.admin_order_field = 'trip__id'

@admin.register(Wallet)
class WalletAdmin(admin.ModelAdmin):
    """Interface d'administration pour le modèle Wallet"""
    list_display = ('id', 'user', 'type', 'balance', 'currency', 'loyalty_points', 'created_at')
    list_filter = ('type', 'currency', 'created_at')
    search_fields = ('user__email', 'stripe_customer_id')
    readonly_fields = ('created_at', 'updated_at', 'last_transaction_at')
    
    fieldsets = (
        (_('Utilisateur'), {
            'fields': ('user', 'type')
        }),
        (_('Solde et devise'), {
            'fields': ('balance', 'currency', 'loyalty_points')
        }),
        (_('Limites'), {
            'fields': ('daily_limit', 'transaction_limit')
        }),
        (_('Stripe'), {
            'fields': ('stripe_customer_id', 'default_payment_method')
        }),
        (_('Statistiques'), {
            'fields': ('total_earned', 'total_spent')
        }),
        (_('Métadonnées'), {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'updated_at', 'last_transaction_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    """Interface d'administration pour le modèle Transaction"""
    list_display = ('id', 'get_wallet', 'type', 'amount', 'balance_after', 'created_at')
    list_filter = ('type', 'created_at')
    search_fields = ('wallet__user__email', 'description', 'payment__stripe_payment_id')
    readonly_fields = ('created_at', 'balance_after')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (_('Portefeuille'), {
            'fields': ('wallet', 'type')
        }),
        (_('Montants'), {
            'fields': ('amount', 'balance_after')
        }),
        (_('Relations'), {
            'fields': ('payment', 'related_transaction')
        }),
        (_('Description'), {
            'fields': ('description',)
        }),
        (_('Métadonnées'), {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        (_('Horodatage'), {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )

    def get_wallet(self, obj):
        """Obtenir l'information du portefeuille"""
        return f"{obj.wallet.user.email} - {obj.wallet.get_type_display()}"
    get_wallet.short_description = _('Portefeuille')
    get_wallet.admin_order_field = 'wallet__user__email'
