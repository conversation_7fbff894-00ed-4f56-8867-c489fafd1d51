API Recharge du portefeuille (Stripe PaymentIntent)
====================================================

Cette API permet à un utilisateur de recharger son portefeuille via Stripe depuis l’application mobile. Elle utilise le système PaymentIntent de Stripe pour gérer le paiement côté mobile.

----------------------------------------------------

Endpoint
--------
POST /api/payments/wallet/recharge/

----------------------------------------------------

Headers requis
--------------
- Authorization: Bearer <access_token>
- Content-Type: application/json

----------------------------------------------------

Payload (body) attendu
----------------------
{
  "amount": 5000   // Montant à recharger en FCFA (ou la devise configurée)
}
- amount : Montant à recharger (entier, en centimes si Stripe est en XOF).

----------------------------------------------------

Réponse (succès)
----------------
{
  "client_secret": "pi_3RcO6ADvUQ1WBV9h2bZfIFYk_secret_wlXpRdqETtZM5ZNOergwgIugm",
  "payment_intent_id": "pi_3RcO6ADvUQ1WBV9h2bZfIFYk",
  "transaction_id": 144
}
- client_secret : À transmettre au SDK Stripe côté mobile pour déclencher l’UI de paiement.
- payment_intent_id : L’identifiant unique Stripe du PaymentIntent.
- transaction_id : L’ID interne de la transaction créée dans la base Commodore (pour suivi).

----------------------------------------------------

Flow côté mobile
----------------
1. L’utilisateur saisit le montant à recharger.
2. L’app mobile envoie une requête POST avec le montant à /api/payments/wallet/recharge/.
3. Le backend crée un PaymentIntent Stripe et une transaction interne, puis retourne client_secret, payment_intent_id, transaction_id.
4. L’app mobile utilise le client_secret avec le SDK Stripe pour afficher l’UI de paiement et finaliser la transaction.
5. Une fois le paiement validé, le backend met à jour le solde du portefeuille.

----------------------------------------------------

Cas d’erreur
------------
- Si le montant est manquant ou invalide :
  { "error": "Montant invalide" }
- Si Stripe retourne une erreur, un message d’erreur approprié est renvoyé.

----------------------------------------------------

Exemple de requête
------------------
POST /api/payments/wallet/recharge/
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJh...
Content-Type: application/json

{
  "amount": 5000
}

----------------------------------------------------

Exemple de réponse
------------------
{
  "client_secret": "pi_3RcO6ADvUQ1WBV9h2bZfIFYk_secret_wlXpRdqETtZM5ZNOergwgIugm",
  "payment_intent_id": "pi_3RcO6ADvUQ1WBV9h2bZfIFYk",
  "transaction_id": 144
}

----------------------------------------------------

Remarques
---------
- Le montant doit être strictement positif.
- Le client_secret est à utiliser UNIQUEMENT côté mobile, jamais côté serveur.
- La finalisation de la recharge (crédit effectif) dépend de la confirmation Stripe côté backend (webhook ou polling).
