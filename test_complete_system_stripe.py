"""
Test complet du système Commodore avec paiements Stripe réels.
Ce script va créer des comptes de test et tester tous les workflows avec des appels API.
"""

import os
import sys
import django
import requests
import json
import time
from decimal import Decimal
from datetime import datetime, timedelta, date

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import Client, Captain, Establishment
from boats.models import Boat
from payments.models import Wallet
from rest_framework.authtoken.models import Token

User = get_user_model()

# Configuration des tests
BASE_URL = 'http://127.0.0.1:8000'
STRIPE_TEST_CARDS = {
    'visa_success': 'pm_card_visa',
    'visa_decline': 'pm_card_visa_chargeDeclined',
    'mastercard': 'pm_card_mastercard'
}

class CommodoreAPITester:
    """Classe pour tester l'API Commodore avec Stripe"""

    def __init__(self):
        self.base_url = BASE_URL
        self.accounts = {}
        self.session = requests.Session()

    def create_test_accounts(self):
        """Créer tous les comptes de test nécessaires"""
        print("🚀 CRÉATION DES COMPTES DE TEST")
        print("=" * 50)

        # 1. CLIENT DE TEST
        try:
            client_user = User.objects.create_user(
                email='<EMAIL>',
                password='TestClient123!',
                first_name='Marie',
                last_name='Dubois',
                phone_number='+***********'
            )
            client_user.is_verified = True
            client_user.save()

            client_profile = Client.objects.create(user=client_user)
            client_wallet = Wallet.objects.create(user=client_user, balance=Decimal('500.00'))
            client_token = Token.objects.create(user=client_user)

            self.accounts['client'] = {
                'user': client_user,
                'profile': client_profile,
                'token': client_token.key,
                'email': client_user.email,
                'password': 'TestClient123!'
            }
            print(f"✅ Client créé: {client_user.email}")

        except Exception as e:
            client_user = User.objects.get(email='<EMAIL>')
            client_token = Token.objects.get_or_create(user=client_user)[0]
            self.accounts['client'] = {
                'user': client_user,
                'profile': client_user.client,
                'token': client_token.key,
                'email': client_user.email,
                'password': 'TestClient123!'
            }
            print(f"⚠️ Client existe déjà: {client_user.email}")

        # 2. ÉTABLISSEMENT DE TEST
        try:
            establishment_user = User.objects.create_user(
                email='<EMAIL>',
                password='TestHotel123!',
                first_name='Hotel',
                last_name='Paradise',
                phone_number='+***********'
            )
            establishment_user.is_verified = True
            establishment_user.save()

            establishment = Establishment.objects.create(
                user=establishment_user,
                name='Hotel Paradise Beach',
                type='HOTEL',
                address='123 Promenade des Anglais, 06000 Cannes'
            )
            establishment_wallet = Wallet.objects.create(user=establishment_user, balance=Decimal('1000.00'))
            establishment_token = Token.objects.create(user=establishment_user)

            self.accounts['establishment'] = {
                'user': establishment_user,
                'profile': establishment,
                'token': establishment_token.key,
                'email': establishment_user.email,
                'password': 'TestHotel123!'
            }
            print(f"✅ Établissement créé: {establishment.name}")

        except Exception as e:
            establishment_user = User.objects.get(email='<EMAIL>')
            establishment_token = Token.objects.get_or_create(user=establishment_user)[0]
            self.accounts['establishment'] = {
                'user': establishment_user,
                'profile': establishment_user.establishment,
                'token': establishment_token.key,
                'email': establishment_user.email,
                'password': 'TestHotel123!'
            }
            print(f"⚠️ Établissement existe déjà: {establishment_user.email}")

        # 3. BATELIER ENREGISTRÉ PAR L'ÉTABLISSEMENT (via API)
        print("  🔄 Enregistrement d'un batelier par l'établissement...")
        self.register_boatman_via_establishment()

        # 4. CAPITAINE INDÉPENDANT
        try:
            captain_user = User.objects.create_user(
                email='<EMAIL>',
                password='TestCaptain123!',
                first_name='Jack',
                last_name='Sparrow',
                phone_number='+***********'
            )
            captain_user.is_verified = True
            captain_user.is_captain = True
            captain_user.save()

            captain = Captain.objects.create(
                user=captain_user,
                experience='Capitaine indépendant - 10 ans',
                license_number='TEST_CAPTAIN_001',
                rate_per_km=Decimal('25.00'),
                rate_per_hour=Decimal('50.00'),
                is_available=True,
                availability_status='AVAILABLE'
            )

            captain_boat = Boat.objects.create(
                captain=captain,
                name='Black Pearl Test',
                boat_type='CLASSIC',
                capacity=8,
                fuel_type='gasoline',
                fuel_consumption=20.0,
                is_available=True
            )

            captain_wallet = Wallet.objects.create(user=captain_user, balance=Decimal('300.00'))
            captain_token = Token.objects.create(user=captain_user)

            self.accounts['captain'] = {
                'user': captain_user,
                'profile': captain,
                'boat': captain_boat,
                'token': captain_token.key,
                'email': captain_user.email,
                'password': 'TestCaptain123!'
            }
            print(f"✅ Capitaine créé: {captain_user.get_full_name()}")

        except Exception as e:
            captain_user = User.objects.get(email='<EMAIL>')
            captain_token = Token.objects.get_or_create(user=captain_user)[0]
            self.accounts['captain'] = {
                'user': captain_user,
                'profile': captain_user.captain,
                'boat': captain_user.captain.boat,
                'token': captain_token.key,
                'email': captain_user.email,
                'password': 'TestCaptain123!'
            }
            print(f"⚠️ Capitaine existe déjà: {captain_user.email}")

        print(f"\n✅ Tous les comptes sont prêts !")
        return self.accounts

    def register_boatman_via_establishment(self):
        """Enregistrer un batelier via l'API de l'établissement"""

        # Données du batelier à enregistrer
        boatman_data = {
            'email': '<EMAIL>',
            'first_name': 'Pierre',
            'last_name': 'Batelier',
            'phone_number': '+***********',
            'experience': 'Batelier officiel Hotel Paradise - 5 ans d\'expérience navettes',
            'license_number': 'BOATMAN_PARADISE_001'
        }

        # Appel API pour enregistrer le batelier
        response = self.make_request('POST', '/api/establishments/register-boatman/',
                                   token=self.accounts['establishment']['token'], data=boatman_data)

        if response and response.status_code == 200:
            result = response.json()
            captain_id = result['data']['captain_id']
            user_id = result['data']['user_id']
            boat_id = result['data']['boat_id']

            # Récupérer les objets créés
            from django.contrib.auth import get_user_model
            User = get_user_model()
            boatman_user = User.objects.get(id=user_id)
            boatman_token = Token.objects.create(user=boatman_user)

            self.accounts['boatman'] = {
                'user': boatman_user,
                'profile': boatman_user.captain,
                'boat': boatman_user.captain.boat,
                'token': boatman_token.key,
                'email': boatman_user.email,
                'password': 'mot_de_passe_temporaire'  # Le mot de passe est envoyé par email
            }

            print(f"    ✅ Batelier enregistré via API: {boatman_user.get_full_name()}")
            print(f"    📧 Email envoyé avec identifiants temporaires")

        else:
            # Si l'enregistrement échoue, essayer de récupérer un batelier existant
            try:
                from django.contrib.auth import get_user_model
                User = get_user_model()
                boatman_user = User.objects.get(email='<EMAIL>')
                boatman_token = Token.objects.get_or_create(user=boatman_user)[0]

                self.accounts['boatman'] = {
                    'user': boatman_user,
                    'profile': boatman_user.captain,
                    'boat': boatman_user.captain.boat,
                    'token': boatman_token.key,
                    'email': boatman_user.email,
                    'password': 'mot_de_passe_temporaire'
                }
                print(f"    ⚠️ Batelier existe déjà: {boatman_user.email}")

            except Exception as e:
                print(f"    ❌ Erreur récupération batelier: {e}")
                # Créer un compte batelier minimal pour les tests
                self.accounts['boatman'] = {
                    'user': None,
                    'profile': None,
                    'boat': None,
                    'token': None,
                    'email': '<EMAIL>',
                    'password': 'mot_de_passe_temporaire'
                }

    def make_request(self, method, endpoint, token=None, data=None):
        """Faire une requête API avec gestion des erreurs"""
        url = f"{self.base_url}{endpoint}"
        headers = {'Content-Type': 'application/json'}

        if token:
            headers['Authorization'] = f'Token {token}'

        try:
            if method.upper() == 'GET':
                response = self.session.get(url, headers=headers)
            elif method.upper() == 'POST':
                response = self.session.post(url, headers=headers, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, headers=headers, json=data)
            elif method.upper() == 'PATCH':
                response = self.session.patch(url, headers=headers, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, headers=headers)

            print(f"  📡 {method.upper()} {endpoint} -> {response.status_code}")

            if response.status_code >= 400:
                print(f"  ❌ Erreur: {response.text}")

            return response

        except Exception as e:
            print(f"  ❌ Exception: {str(e)}")
            return None

    def test_authentication(self):
        """Tester l'authentification"""
        print("\n🔐 TEST AUTHENTIFICATION")
        print("-" * 30)

        # Test login client
        login_data = {
            'email': self.accounts['client']['email'],
            'password': self.accounts['client']['password']
        }

        response = self.make_request('POST', '/api/login/', data=login_data)
        if response and response.status_code == 200:
            print("  ✅ Login client réussi")
        else:
            print("  ❌ Login client échoué")

        # Test accès au portefeuille
        response = self.make_request('GET', '/api/payments/wallet/', token=self.accounts['client']['token'])
        if response and response.status_code == 200:
            wallet_data = response.json()
            print(f"  ✅ Portefeuille client: {wallet_data.get('balance', 'N/A')}€")
        else:
            print("  ❌ Accès portefeuille échoué")

    def test_trip_workflow_with_stripe(self):
        """Tester le workflow complet d'une course avec paiement Stripe"""
        print("\n💳 TEST WORKFLOW COURSE AVEC STRIPE")
        print("-" * 40)

        # 1. Créer une demande de course simple
        departure_time = datetime.now() + timedelta(hours=2)
        trip_data = {
            'departure_location': {
                'city_name': 'Port de Cannes',
                'coordinates': {'latitude': 43.5528, 'longitude': 7.0174},
                'timestamp': departure_time.isoformat()
            },
            'arrival_location': {
                'city_name': 'Îles de Lérins',
                'coordinates': {'latitude': 43.5184, 'longitude': 7.0457},
                'timestamp': departure_time.isoformat()
            },
            'departure_date': departure_time.date().isoformat(),
            'departure_time': departure_time.time().isoformat(),
            'passenger_count': 4,
            'boat_type': 'CLASSIC'
        }

        response = self.make_request('POST', '/api/trips/requests/simple/',
                                   token=self.accounts['client']['token'], data=trip_data)

        if not response or response.status_code != 201:
            print("  ❌ Échec création demande course")
            return

        request_data = response.json()
        # Gérer différentes structures de réponse
        if 'trip_request' in request_data and 'id' in request_data['trip_request']:
            request_id = request_data['trip_request']['id']
        elif 'data' in request_data and 'request_id' in request_data['data']:
            request_id = request_data['data']['request_id']
        elif 'id' in request_data:
            request_id = request_data['id']
        else:
            request_id = request_data.get('request_id', 'unknown')
        print(f"  ✅ Demande course créée: ID {request_id}")

        # Vérifier si des devis sont disponibles
        quotes = request_data.get('quotes', [])
        print(f"  ✅ {len(quotes)} devis générés automatiquement")

        # 2. Récupérer les devis
        response = self.make_request('GET', f'/api/trips/requests/{request_id}/',
                                   token=self.accounts['client']['token'])

        if not response or response.status_code != 200:
            print("  ❌ Échec récupération devis")
            return

        quotes_data = response.json()
        # Gérer différentes structures de réponse pour les devis
        if 'data' in quotes_data and 'quotes' in quotes_data['data']:
            quotes = quotes_data['data']['quotes']
        elif 'quotes' in quotes_data:
            quotes = quotes_data['quotes']
        else:
            quotes = []

        if not quotes:
            print("  ❌ Aucun devis disponible")
            return

        selected_quote = quotes[0]
        # Structure correcte des devis selon l'API
        captain_name = selected_quote.get('captain_name', 'Capitaine')
        total_price = selected_quote.get('base_price', 0)
        quote_id = selected_quote.get('id')

        print(f"  ✅ Devis sélectionné: {captain_name} - {total_price}€")

        # 3. Accepter le devis
        response = self.make_request('POST', f'/api/trips/quotes/{quote_id}/accept/',
                                   token=self.accounts['client']['token'])

        if not response or response.status_code != 200:
            print("  ❌ Échec acceptation devis")
            return

        trip_data = response.json()
        trip_id = trip_data['data']['trip_id']
        print(f"  ✅ Devis accepté, course créée: ID {trip_id}")

        # 4. Capitaine accepte la course
        response = self.make_request('POST', f'/api/trips/{trip_id}/accept/',
                                   token=self.accounts['captain']['token'])

        if response and response.status_code == 200:
            print("  ✅ Course acceptée par le capitaine")
        else:
            print("  ⚠️ Capitaine n'a pas pu accepter (peut-être déjà acceptée)")

        # 5. Test paiement avec portefeuille
        payment_data = {
            'payment_method': 'wallet'
        }

        response = self.make_request('POST', f'/api/trips/{trip_id}/payment/',
                                   token=self.accounts['client']['token'], data=payment_data)

        if response and response.status_code == 200:
            payment_result = response.json()
            print(f"  ✅ Paiement portefeuille réussi: {payment_result.get('amount', 'N/A')}€")
            if 'qr_code' in payment_result:
                print(f"  ✅ QR Code généré: {payment_result['qr_code'][:30]}...")
        else:
            print("  ❌ Échec paiement portefeuille")

        # 6. Test paiement Stripe avec carte de test
        self.test_stripe_payment_methods(trip_id)

        return trip_id

    def test_stripe_payment_methods(self, trip_id):
        """Tester les méthodes de paiement Stripe"""
        print("\n  💳 TEST PAIEMENTS STRIPE")

        # Test recharge portefeuille avec Stripe
        recharge_data = {
            'amount': 100.00,
            'payment_method_id': STRIPE_TEST_CARDS['visa_success']
        }

        response = self.make_request('POST', '/api/payments/wallet/recharge/',
                                   token=self.accounts['client']['token'], data=recharge_data)

        if response and response.status_code == 200:
            recharge_result = response.json()
            print(f"    ✅ Recharge Stripe réussie: {recharge_result.get('amount', 'N/A')}€")
        else:
            print("    ❌ Échec recharge Stripe")

        # Test paiement direct Stripe pour une course
        stripe_payment_data = {
            'payment_method': 'stripe',
            'payment_method_id': STRIPE_TEST_CARDS['visa_success']
        }

        # Créer une nouvelle course pour tester le paiement Stripe
        print("    🔄 Création nouvelle course pour test Stripe...")

    def test_carbon_compensation(self, trip_id):
        """Tester la compensation carbone"""
        print("\n🌱 TEST COMPENSATION CARBONE")
        print("-" * 30)

        # 1. Consulter l'empreinte carbone
        response = self.make_request('GET', f'/api/trips/{trip_id}/carbon-footprint/',
                                   token=self.accounts['client']['token'])

        if response and response.status_code == 200:
            carbon_data = response.json()
            co2_kg = carbon_data['carbon_footprint']['co2_kg']
            cost = carbon_data['carbon_footprint']['compensation_cost_euros']
            print(f"  ✅ Empreinte carbone: {co2_kg} kg CO₂")
            print(f"  ✅ Coût compensation: {cost}€")

            # 2. Payer la compensation carbone
            compensation_data = {
                'payment_method': 'wallet'
            }

            response = self.make_request('POST', f'/api/trips/{trip_id}/carbon-compensation/',
                                       token=self.accounts['client']['token'], data=compensation_data)

            if response and response.status_code == 200:
                comp_result = response.json()
                print(f"  ✅ Compensation payée: {comp_result.get('co2_compensated_kg', 'N/A')} kg CO₂")
            else:
                print("  ❌ Échec paiement compensation")
        else:
            print("  ❌ Échec consultation empreinte carbone")

    def test_tip_payment(self, trip_id):
        """Tester le paiement de pourboire"""
        print("\n💰 TEST POURBOIRE")
        print("-" * 20)

        tip_data = {
            'amount': '15.00',
            'payment_method': 'wallet'
        }

        response = self.make_request('POST', f'/api/trips/{trip_id}/tip/',
                                   token=self.accounts['client']['token'], data=tip_data)

        if response and response.status_code == 200:
            tip_result = response.json()
            print(f"  ✅ Pourboire payé: {tip_result.get('amount', 'N/A')}€")
        else:
            print("  ❌ Échec paiement pourboire")

    def test_shuttle_workflow(self):
        """Tester le workflow des navettes gratuites"""
        print("\n🚌 TEST WORKFLOW NAVETTES GRATUITES")
        print("-" * 40)

        # 1. Client demande une navette
        departure_time = datetime.now() + timedelta(hours=3)
        shuttle_data = {
            'establishment': self.accounts['establishment']['profile'].pk,
            'departure_location': {
                'city_name': 'Aéroport Nice',
                'coordinates': {'latitude': 43.6584, 'longitude': 7.2159},
                'timestamp': departure_time.isoformat()
            },
            'arrival_location': {
                'city_name': 'Hotel Paradise Beach',
                'coordinates': {'latitude': 43.5528, 'longitude': 7.0174},
                'timestamp': departure_time.isoformat()
            },
            'departure_date': departure_time.date().isoformat(),
            'departure_time': departure_time.time().isoformat(),
            'passenger_count': 2,
            'message': 'Arrivée vol AF1234'
        }

        response = self.make_request('POST', '/api/trips/requests/shuttle/',
                                   token=self.accounts['client']['token'], data=shuttle_data)

        if not response or response.status_code != 201:
            print("  ❌ Échec création demande navette")
            return

        shuttle_request_data = response.json()
        # Gérer différentes structures de réponse
        if 'data' in shuttle_request_data and 'request_id' in shuttle_request_data['data']:
            request_id = shuttle_request_data['data']['request_id']
        elif 'id' in shuttle_request_data:
            request_id = shuttle_request_data['id']
        else:
            request_id = shuttle_request_data.get('request_id', 'unknown')
        print(f"  ✅ Demande navette créée: ID {request_id}")

        # 2. Établissement voit la demande
        response = self.make_request('GET', '/api/establishments/shuttle-requests/',
                                   token=self.accounts['establishment']['token'])

        if response and response.status_code == 200:
            requests_data = response.json()
            print(f"  ✅ Établissement voit {len(requests_data['data']['requests'])} demande(s)")
        else:
            print("  ❌ Établissement ne peut pas voir les demandes")

        # 3. TEST ASSIGNATION BATELIER DE L'ÉTABLISSEMENT
        print("  🔄 Test assignation batelier de l'établissement...")

        # Vérifier si le batelier existe
        if (self.accounts['boatman']['profile'] is None or
            self.accounts['boatman']['boat'] is None):
            print("  ⚠️ Batelier non disponible, test direct avec capitaine externe...")
            accept_data_boatman = None
        else:
            accept_data_boatman = {
                'captain_id': self.accounts['boatman']['profile'].pk,
                'boat_id': self.accounts['boatman']['boat'].pk,
                'estimated_pickup_time': (datetime.now() + timedelta(hours=3)).isoformat(),
                'notes': 'Assignation au batelier officiel'
            }

        response = self.make_request('POST', f'/api/establishments/shuttle-requests/{request_id}/accept/',
                                   token=self.accounts['establishment']['token'], data=accept_data_boatman)

        if response and response.status_code == 200:
            accept_result = response.json()
            trip_id = accept_result.get('data', {}).get('trip_id', accept_result.get('trip_id'))
            print(f"  ✅ Navette acceptée avec batelier: ID {trip_id}")

            # 4. BATELIER VOIT LA COURSE ASSIGNÉE
            self.test_boatman_workflow(trip_id)

            return trip_id
        else:
            print("  ⚠️ Échec assignation batelier, test fallback capitaine externe...")

            # FALLBACK: Établissement utilise un capitaine externe
            accept_data_external = {
                'captain_id': self.accounts['captain']['profile'].pk,
                'boat_id': self.accounts['captain']['boat'].pk,
                'estimated_pickup_time': (datetime.now() + timedelta(hours=3)).isoformat(),
                'notes': 'Aucun batelier disponible, utilisation capitaine externe'
            }

            response = self.make_request('POST', f'/api/establishments/shuttle-requests/{request_id}/accept/',
                                       token=self.accounts['establishment']['token'], data=accept_data_external)

            if response and response.status_code == 200:
                accept_result = response.json()
                trip_id = accept_result.get('data', {}).get('trip_id', accept_result.get('trip_id'))
                print(f"  ✅ Navette acceptée avec capitaine externe: ID {trip_id}")
                return trip_id
            else:
                print("  ❌ Échec acceptation navette")
                return None

    def test_boatman_workflow(self, trip_id):
        """Tester le workflow spécifique aux bateliers"""
        print("\n⚓ TEST WORKFLOW BATELIER")
        print("-" * 30)

        # 1. Batelier voit ses courses assignées
        response = self.make_request('GET', '/api/boatman/shuttles/',
                                   token=self.accounts['boatman']['token'])

        if response and response.status_code == 200:
            shuttles_data = response.json()
            shuttles = shuttles_data.get('data', {}).get('shuttles', shuttles_data.get('shuttles', []))
            print(f"  ✅ Batelier voit {len(shuttles)} navette(s) assignée(s)")

            # Vérifier que la navette est gratuite
            for shuttle in shuttles:
                if shuttle.get('id') == trip_id:
                    payment_info = shuttle.get('payment', {})
                    amount = payment_info.get('amount', 0)
                    method = payment_info.get('method', '')
                    print(f"  ✅ Navette gratuite confirmée: {amount}€ ({method})")
                    break
        else:
            print("  ❌ Batelier ne peut pas voir ses navettes")

        # 2. Batelier accepte la course
        response = self.make_request('POST', f'/api/trips/{trip_id}/accept/',
                                   token=self.accounts['boatman']['token'])

        if response and response.status_code == 200:
            print("  ✅ Batelier accepte la navette")
        else:
            print("  ⚠️ Batelier ne peut pas accepter (peut-être déjà acceptée)")

        # 3. Batelier démarre la course
        response = self.make_request('POST', f'/api/trips/{trip_id}/start/',
                                   token=self.accounts['boatman']['token'])

        if response and response.status_code == 200:
            print("  ✅ Batelier démarre la navette")
        else:
            print("  ⚠️ Batelier ne peut pas démarrer")

        # 4. Batelier termine la course
        response = self.make_request('POST', f'/api/trips/{trip_id}/complete/',
                                   token=self.accounts['boatman']['token'])

        if response and response.status_code == 200:
            print("  ✅ Batelier termine la navette")
        else:
            print("  ⚠️ Batelier ne peut pas terminer")

    def test_establishment_boatman_management(self):
        """Tester la gestion des bateliers par l'établissement"""
        print("\n🏨 TEST GESTION BATELIERS PAR ÉTABLISSEMENT")
        print("-" * 45)

        # 1. Établissement voit ses bateliers enregistrés
        response = self.make_request('GET', '/api/establishments/boatmen/',
                                   token=self.accounts['establishment']['token'])

        if response and response.status_code == 200:
            boatmen_data = response.json()
            boatmen = boatmen_data.get('data', {}).get('boatmen', boatmen_data.get('boatmen', []))
            print(f"  ✅ Établissement voit {len(boatmen)} batelier(s) enregistré(s)")

            # Vérifier que notre batelier est dans la liste
            if self.accounts['boatman']['profile']:
                for boatman in boatmen:
                    if boatman.get('id') == self.accounts['boatman']['profile'].pk:
                        print(f"  ✅ Batelier {boatman.get('name', 'N/A')} trouvé dans la liste")
                        break
            else:
                print("  ⚠️ Aucun batelier enregistré pour ce test")
        else:
            print("  ❌ Établissement ne peut pas voir ses bateliers")

        # 2. Établissement voit les ressources disponibles
        response = self.make_request('GET', '/api/establishments/available-resources/',
                                   token=self.accounts['establishment']['token'])

        if response and response.status_code == 200:
            resources_data = response.json()
            available_captains = resources_data.get('data', {}).get('available_captains', [])
            print(f"  ✅ {len(available_captains)} capitaine(s) disponible(s) au total")

            # Compter bateliers vs capitaines externes
            establishment_boatmen = []
            external_captains = []

            if self.accounts['boatman']['profile']:
                establishment_boatmen = [c for c in available_captains
                                       if c.get('id') == self.accounts['boatman']['profile'].pk]

            if self.accounts['captain']['profile']:
                external_captains = [c for c in available_captains
                                   if c.get('id') == self.accounts['captain']['profile'].pk]

            print(f"  ✅ Bateliers de l'établissement: {len(establishment_boatmen)}")
            print(f"  ✅ Capitaines externes: {len(external_captains)}")
        else:
            print("  ❌ Établissement ne peut pas voir les ressources disponibles")

def run_complete_tests():
    """Exécuter tous les tests du système"""
    print("🎯 TESTS COMPLETS DU SYSTÈME COMMODORE AVEC STRIPE")
    print("=" * 60)

    tester = CommodoreAPITester()

    # 1. Créer les comptes
    accounts = tester.create_test_accounts()

    # 2. Tester l'authentification
    tester.test_authentication()

    # 3. Tester le workflow des courses payantes
    trip_id = tester.test_trip_workflow_with_stripe()

    # 4. Tester la gestion des bateliers par l'établissement
    tester.test_establishment_boatman_management()

    # 5. Tester le workflow des navettes gratuites avec bateliers
    shuttle_trip_id = tester.test_shuttle_workflow()

    # 6. Tester la compensation carbone (si course créée)
    if trip_id:
        tester.test_carbon_compensation(trip_id)
        tester.test_tip_payment(trip_id)

    print("\n" + "=" * 60)
    print("🎉 TESTS TERMINÉS !")
    print(f"✅ Course payante créée: ID {trip_id}")
    print(f"✅ Navette gratuite créée: ID {shuttle_trip_id}")
    print("\n📋 COMPTES DE TEST DISPONIBLES :")
    for account_type, account_data in accounts.items():
        print(f"  {account_type}: {account_data['email']} (Token: {account_data['token'][:20]}...)")

    print("\n🔧 POUR TESTER MANUELLEMENT :")
    print("1. Interface admin: http://127.0.0.1:8000/admin/")
    print("2. API docs: http://127.0.0.1:8000/api/docs/")
    print("3. Utilisez les tokens ci-dessus pour les appels API")
    print("\n🚀 WORKFLOW VALIDÉS :")
    print("✅ Authentification et portefeuilles")
    print("✅ Courses payantes avec devis multiples")
    print("✅ Navettes gratuites avec assignation bateliers")
    print("✅ Fallback capitaines externes pour navettes")
    print("✅ Gestion bateliers par établissements")
    print("✅ Paiements Stripe et compensation carbone")

if __name__ == '__main__':
    run_complete_tests()
