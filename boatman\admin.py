"""
Configuration de l'administration Django pour l'app boatman.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from accounts.models import Captain
from trips.models import Trip
from payments.models import Payment
from unfold.admin import ModelAdmin


class CaptainAdminView(ModelAdmin):
    """Administration des capitaines (vue depuis l'app boatman)"""

    list_display = [
        'user_email', 'user_full_name', 'availability_status',
        'average_rating', 'total_trips', 'total_earnings', 'date_joined'
    ]
    list_filter = ['availability_status', 'is_available', 'user__date_joined']
    search_fields = ['user__email', 'user__first_name', 'user__last_name', 'license_number']
    readonly_fields = ['user', 'average_rating', 'total_trips', 'total_earnings']

    fieldsets = (
        ('Informations utilisateur', {
            'fields': ('user', 'user_details')
        }),
        ('Informations professionnelles', {
            'fields': ('experience', 'license_number', 'availability_status', 'is_available')
        }),
        ('Tarification', {
            'fields': ('rate_per_km', 'rate_per_hour')
        }),
        ('Statistiques', {
            'fields': ('average_rating', 'total_trips', 'total_earnings'),
            'classes': ('collapse',)
        }),
        ('Métadonnées', {
            'fields': ('metadata',),
            'classes': ('collapse',)
        })
    )

    def user_email(self, obj):
        """Afficher l'email de l'utilisateur"""
        return obj.user.email
    user_email.short_description = 'Email'
    user_email.admin_order_field = 'user__email'

    def user_full_name(self, obj):
        """Afficher le nom complet"""
        return obj.user.get_full_name()
    user_full_name.short_description = 'Nom complet'
    user_full_name.admin_order_field = 'user__first_name'

    def date_joined(self, obj):
        """Afficher la date d'inscription"""
        return obj.user.date_joined.strftime('%d/%m/%Y')
    date_joined.short_description = 'Date d\'inscription'
    date_joined.admin_order_field = 'user__date_joined'

    def total_trips(self, obj):
        """Afficher le nombre total de courses"""
        return Trip.objects.filter(captain=obj).count()
    total_trips.short_description = 'Total courses'

    def total_earnings(self, obj):
        """Afficher les gains totaux"""
        from django.db.models import Sum
        total = Payment.objects.filter(
            user=obj.user,
            type__in=[Payment.PaymentType.TRIP, Payment.PaymentType.TIP],
            status=Payment.Status.COMPLETED
        ).aggregate(total=Sum('amount'))['total'] or 0
        return f"{total}€"
    total_earnings.short_description = 'Gains totaux'

    def user_details(self, obj):
        """Afficher les détails de l'utilisateur"""
        if obj.user:
            user_url = reverse('admin:accounts_user_change', args=[obj.user.id])
            return format_html(
                '<a href="{}" target="_blank">{} ({})</a>',
                user_url,
                obj.user.get_full_name(),
                obj.user.email
            )
        return "Aucun utilisateur"
    user_details.short_description = 'Détails utilisateur'

    def get_queryset(self, request):
        """Optimiser les requêtes"""
        return super().get_queryset(request).select_related('user')


class CaptainTripInline(admin.TabularInline):
    """Inline pour afficher les courses d'un capitaine"""
    model = Trip
    fields = ['client', 'start_location', 'end_location', 'status', 'scheduled_start_time', 'total_price']
    readonly_fields = ['client', 'start_location', 'end_location', 'scheduled_start_time', 'total_price']
    extra = 0
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


# Personnalisation de l'admin pour les courses depuis l'app boatman
class BoatmanTripAdmin(ModelAdmin):
    """Administration des courses depuis la perspective batelier"""

    list_display = [
        'id', 'captain_name', 'client_name', 'start_location',
        'end_location', 'status', 'scheduled_start_time', 'total_price'
    ]
    list_filter = ['status', 'trip_type', 'scheduled_start_time', 'captain__availability_status']
    search_fields = [
        'captain__user__first_name', 'captain__user__last_name',
        'client__user__first_name', 'client__user__last_name',
        'start_location', 'end_location'
    ]
    readonly_fields = [
        'client', 'captain', 'boat', 'created_at', 'updated_at',
        'actual_start_time', 'actual_end_time', 'actual_duration'
    ]

    fieldsets = (
        ('Informations de base', {
            'fields': ('client', 'captain', 'boat', 'trip_type')
        }),
        ('Itinéraire', {
            'fields': ('start_location', 'end_location', 'distance_km')
        }),
        ('Horaires', {
            'fields': ('scheduled_start_time', 'actual_start_time', 'actual_end_time', 'estimated_duration', 'actual_duration')
        }),
        ('Passagers et prix', {
            'fields': ('passenger_count', 'base_price', 'total_price', 'payment_method', 'payment_status')
        }),
        ('Statut', {
            'fields': ('status', 'special_requests')
        }),
        ('Métadonnées', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def captain_name(self, obj):
        """Afficher le nom du capitaine"""
        return obj.captain.user.get_full_name() if obj.captain else 'Aucun'
    captain_name.short_description = 'Capitaine'
    captain_name.admin_order_field = 'captain__user__first_name'

    def client_name(self, obj):
        """Afficher le nom du client"""
        return obj.client.user.get_full_name() if obj.client else 'Aucun'
    client_name.short_description = 'Client'
    client_name.admin_order_field = 'client__user__first_name'

    def get_queryset(self, request):
        """Optimiser les requêtes"""
        return super().get_queryset(request).select_related(
            'captain__user', 'client__user', 'boat'
        )


# Note: Les modèles sont déjà enregistrés dans leurs apps respectives
# admin.site.register(Trip, BoatmanTripAdmin)


# Actions personnalisées pour les capitaines
@admin.action(description='Marquer comme disponible')
def make_available(modeladmin, request, queryset):
    """Marquer les capitaines sélectionnés comme disponibles"""
    updated = queryset.update(availability_status='AVAILABLE', is_available=True)
    modeladmin.message_user(request, f'{updated} capitaine(s) marqué(s) comme disponible(s).')


@admin.action(description='Marquer comme hors ligne')
def make_offline(modeladmin, request, queryset):
    """Marquer les capitaines sélectionnés comme hors ligne"""
    updated = queryset.update(availability_status='OFFLINE', is_available=False)
    modeladmin.message_user(request, f'{updated} capitaine(s) marqué(s) comme hors ligne.')


# Ajouter les actions à l'admin des capitaines
CaptainAdminView.actions = [make_available, make_offline]
