from rest_framework.views import APIView
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import Boat, MaintenanceRecord
from .serializers import (
    BoatSerializer, BoatListSerializer,
    BoatDetailSerializer, MaintenanceRecordSerializer
)
from drf_spectacular.utils import extend_schema, OpenApiParameter

from accounts.models import Captain, Establishment
from django.db.models import Q
from datetime import datetime, timedelta
from trips.models import Trip
import logging

logger = logging.getLogger(__name__)

@extend_schema(
    tags=["Boats"],
    responses=BoatListSerializer(many=True),
    parameters=[
        OpenApiParameter("boat_type", str, OpenApiParameter.QUERY, required=False),
        OpenApiParameter("capacity_min", int, OpenApiParameter.QUERY, required=False),
        OpenApiParameter("location", str, OpenApiParameter.QUERY, required=False),
    ],
)
class BoatListView(GenericAPIView):
    queryset = Boat.objects.all()
    serializer_class = BoatListSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(
        tags=["Boats"],
        request=None,
        responses=BoatListSerializer(many=True)
    )
    def get(self, request):
        # Filtrer selon le type d'utilisateur
        if hasattr(request.user, 'captain'):
            boats = Boat.objects.filter(captain=request.user.captain)
        elif hasattr(request.user, 'establishment'):
            est = request.user.establishment
            boats = Boat.objects.filter(Q(establishment=est) | Q(captain__registered_by_establishment=est))
        else:
            # Pour les clients, montrer tous les bateaux disponibles
            boats = Boat.objects.filter(is_available=True)

        # Appliquer les filtres
        boat_type = request.query_params.get('boat_type')
        capacity_min = request.query_params.get('capacity_min')
        location = request.query_params.get('location')

        if boat_type:
            boats = boats.filter(boat_type=boat_type)
        if capacity_min:
            boats = boats.filter(capacity__gte=capacity_min)
        if location:
            boats = boats.filter(zone_served__icontains=location)

        serializer = BoatListSerializer(boats, many=True)
        return Response(serializer.data)

    @extend_schema(
        tags=["Boats"],
        request=BoatSerializer,
        responses=BoatSerializer
    )
    def post(self, request):
        # Vérifier que l'utilisateur est un capitaine ou un établissement
        if not (hasattr(request.user, 'captain') or hasattr(request.user, 'establishment')):
            return Response(
                {'error': 'Seuls les capitaines et établissements peuvent ajouter des bateaux'},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        if hasattr(request.user, 'captain'):
            data['captain'] = request.user.captain.pk
        else:
            data['establishment'] = request.user.establishment.pk

        serializer = BoatSerializer(data=data)
        if serializer.is_valid():
            boat = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@extend_schema(tags=["Boats"])
class BoatDetailView(GenericAPIView):
    serializer_class = BoatDetailSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Boats"], responses=BoatDetailSerializer)
    def get(self, request, pk):
        boat = get_object_or_404(Boat, pk=pk)
        serializer = BoatDetailSerializer(boat)
        return Response(serializer.data)

    def patch(self, request, pk):
        boat = get_object_or_404(Boat, pk=pk)

        # Vérifier les permissions
        if not (hasattr(request.user, 'captain') and boat.captain == request.user.captain) and \
           not (hasattr(request.user, 'establishment') and boat.establishment == request.user.establishment):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = BoatSerializer(boat, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        boat = get_object_or_404(Boat, pk=pk)

        # Vérifier les permissions
        if not (hasattr(request.user, 'captain') and boat.captain == request.user.captain) and \
           not (hasattr(request.user, 'establishment') and boat.establishment == request.user.establishment):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        boat.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

@extend_schema(tags=["Boats"])
class MaintenanceRecordView(GenericAPIView):
    serializer_class = MaintenanceRecordSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Boats"], responses=MaintenanceRecordSerializer(many=True))
    def get(self, request, boat_id):
        boat = get_object_or_404(Boat, pk=boat_id)
        
        # Vérifier les permissions
        if not (hasattr(request.user, 'captain') and boat.captain == request.user.captain) and \
           not (hasattr(request.user, 'establishment') and boat.establishment == request.user.establishment):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        maintenance_records = MaintenanceRecord.objects.filter(boat=boat)
        serializer = MaintenanceRecordSerializer(maintenance_records, many=True)
        return Response(serializer.data)

    @extend_schema(tags=["Boats"], request=MaintenanceRecordSerializer, responses=MaintenanceRecordSerializer)
    def post(self, request, boat_id):
        boat = get_object_or_404(Boat, pk=boat_id)

        # Vérifier les permissions
        if not (hasattr(request.user, 'captain') and boat.captain == request.user.captain) and \
           not (hasattr(request.user, 'establishment') and boat.establishment == request.user.establishment):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        data['boat'] = boat.pk

        serializer = MaintenanceRecordSerializer(data=data)
        if serializer.is_valid():
            maintenance = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@extend_schema(tags=["Boats"])
class BoatAvailabilityView(GenericAPIView):
    serializer_class = BoatSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(
        tags=["Boats"],
        parameters=[
            OpenApiParameter("start_date", str, OpenApiParameter.QUERY, required=True),
            OpenApiParameter("end_date", str, OpenApiParameter.QUERY, required=True),
        ],
        responses=BoatSerializer
    )
    def get(self, request, pk):
        boat = get_object_or_404(Boat, pk=pk)
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        if not start_date or not end_date:
            return Response(
                {'error': 'Les dates de début et de fin sont requises'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {'error': 'Format de date invalide. Utilisez YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Vérifier les réservations existantes
        # Cette logique dépendra de votre modèle de réservation
        is_available = True  # Implémenter la logique de vérification

        return Response({
            'boat_id': boat.pk,
            'start_date': start_date,
            'end_date': end_date,
            'is_available': is_available
        })

class BoatReservationView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        boat = get_object_or_404(Boat, pk=pk)
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')

        if not start_date or not end_date:
            return Response(
                {'error': 'Les dates de début et de fin sont requises'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {'error': 'Format de date invalide. Utilisez YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Vérifier les réservations existantes
        # Cette logique dépendra de votre modèle de réservation
        is_available = True  # Implémenter la logique de vérification

        if is_available:
            # Créer une nouvelle réservation
            # Cette logique dépendra de votre modèle de réservation
            pass
            return Response({'message': 'Réservation créée avec succès'})
        else:
            return Response({'error': 'Le bateau n\'est pas disponible pour la période sélectionnée'}, status=status.HTTP_400_BAD_REQUEST)

class BoatReviewView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        boat = get_object_or_404(Boat, pk=pk)
        rating = request.data.get('rating')
        review = request.data.get('review')

        if not rating or not review:
            return Response(
                {'error': 'La note et la critique sont requises'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Enregistrer la critique
        # Cette logique dépendra de votre modèle de critique
        pass
        return Response({'message': 'Critique enregistrée avec succès'})


@extend_schema(tags=["Boats"])
class ToggleBoatAvailabilityView(GenericAPIView):
    serializer_class = BoatSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Boats"], request=None, responses=None)
    def post(self, request, pk):
        boat = get_object_or_404(Boat, pk=pk)
        
        # Vérifier les permissions
        if not (hasattr(request.user, 'captain') and boat.captain == request.user.captain) and \
           not (hasattr(request.user, 'establishment') and boat.establishment == request.user.establishment):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Basculer le statut
        if boat.status == 'AVAILABLE':
            boat.status = 'UNAVAILABLE'
        else:
            boat.status = 'AVAILABLE'
        
        boat.save()
        return Response({
            'boat_id': boat.pk,
            'status': boat.status,
            'message': f'Statut du bateau modifié en {boat.status}'
        })


class MaintenanceRecordDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        maintenance_record = get_object_or_404(MaintenanceRecord, pk=pk)
        boat = maintenance_record.boat
        
        # Vérifier les permissions
        if not (hasattr(request.user, 'captain') and boat.captain == request.user.captain) and \
           not (hasattr(request.user, 'establishment') and boat.establishment == request.user.establishment):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        serializer = MaintenanceRecordSerializer(maintenance_record)
        return Response(serializer.data)
    
    def patch(self, request, pk):
        maintenance_record = get_object_or_404(MaintenanceRecord, pk=pk)
        boat = maintenance_record.boat
        
        # Vérifier les permissions
        if not (hasattr(request.user, 'captain') and boat.captain == request.user.captain) and \
           not (hasattr(request.user, 'establishment') and boat.establishment == request.user.establishment):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        serializer = MaintenanceRecordSerializer(maintenance_record, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, pk):
        maintenance_record = get_object_or_404(MaintenanceRecord, pk=pk)
        boat = maintenance_record.boat
        
        # Vérifier les permissions
        if not (hasattr(request.user, 'captain') and boat.captain == request.user.captain) and \
           not (hasattr(request.user, 'establishment') and boat.establishment == request.user.establishment):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        maintenance_record.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


@extend_schema(
    tags=["Boats"],
    parameters=[
        OpenApiParameter("boat_type", str, OpenApiParameter.QUERY, required=False),
        OpenApiParameter("capacity_min", int, OpenApiParameter.QUERY, required=False),
        OpenApiParameter("location", str, OpenApiParameter.QUERY, required=False),
        OpenApiParameter("date_start", str, OpenApiParameter.QUERY, required=False),
        OpenApiParameter("date_end", str, OpenApiParameter.QUERY, required=False),
    ],
    responses=BoatListSerializer(many=True)
)
class AvailableBoatsView(GenericAPIView):
    queryset = Boat.objects.all()
    serializer_class = BoatListSerializer
    permission_classes = [IsAuthenticated]
    
    @extend_schema(tags=["Boats"], responses=BoatListSerializer(many=True))
    def get(self, request):
        # Récupérer tous les bateaux disponibles
        boats = Boat.objects.filter(is_available=True)
        
        # Appliquer les filtres
        boat_type = request.query_params.get('boat_type')
        capacity_min = request.query_params.get('capacity_min')
        location = request.query_params.get('location')
        date_start = request.query_params.get('date_start')
        date_end = request.query_params.get('date_end')

        if boat_type:
            boats = boats.filter(boat_type=boat_type)
        if capacity_min:
            boats = boats.filter(capacity__gte=capacity_min)
        if location:
            boats = boats.filter(zone_served__icontains=location)
        
        # Filtrer par disponibilité sur les dates
        if date_start and date_end:
            try:
                start_date = datetime.strptime(date_start, '%Y-%m-%d').date()
                end_date = datetime.strptime(date_end, '%Y-%m-%d').date()
                
                # Exclure les bateaux qui ont des réservations pendant cette période
                # Cette logique dépendra de votre modèle de réservation
                # trips = Trip.objects.filter(boat__in=boats, departure_date__lte=end_date, arrival_date__gte=start_date)
                # boats = boats.exclude(id__in=trips.values_list('boat_id', flat=True))
                
            except ValueError:
                return Response(
                    {'error': 'Format de date invalide. Utilisez YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        serializer = BoatListSerializer(boats, many=True)
        return Response(serializer.data)
