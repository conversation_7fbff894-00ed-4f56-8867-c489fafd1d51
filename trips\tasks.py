"""
Tâches asynchrones pour l'application trips
"""

from celery import shared_task
from django.utils import timezone
from .models import TripRequest, TripQuote
from .notification_services import create_trip_expiration_notification
import logging

logger = logging.getLogger(__name__)


@shared_task
def cleanup_expired_trip_requests():
    """
    Tâche périodique pour nettoyer les demandes de courses expirées
    À exécuter toutes les 5 minutes
    """
    try:
        # Trouver toutes les demandes expirées
        expired_requests = TripRequest.objects.filter(
            status=TripRequest.Status.PENDING,
            expires_at__lt=timezone.now()
        )
        
        count = 0
        for trip_request in expired_requests:
            # Marquer comme expirée
            trip_request.status = TripRequest.Status.EXPIRED
            trip_request.save()
            
            # Créer une notification pour le client
            create_trip_expiration_notification(trip_request)
            
            count += 1
        
        # Marquer les devis associés comme non disponibles
        expired_quotes_count = TripQuote.objects.filter(
            trip_request__status=TripRequest.Status.EXPIRED,
            is_available=True
        ).update(is_available=False)
        
        logger.info(f"Nettoyage automatique: {count} demandes expirées, {expired_quotes_count} devis désactivés")
        
        return {
            'expired_requests': count,
            'disabled_quotes': expired_quotes_count,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Erreur lors du nettoyage automatique: {str(e)}")
        return {'error': str(e)}


@shared_task
def send_trip_reminder_notifications():
    """
    Envoie des rappels pour les courses programmées
    À exécuter toutes les heures
    """
    try:
        from datetime import timedelta
        from .models import Trip
        from notifications.services import create_notification
        
        # Courses qui commencent dans 1 heure
        upcoming_trips = Trip.objects.filter(
            status=Trip.Status.ACCEPTED,
            scheduled_start_time__gte=timezone.now(),
            scheduled_start_time__lte=timezone.now() + timedelta(hours=1)
        )
        
        notifications_sent = 0
        
        for trip in upcoming_trips:
            # Notification pour le client
            create_notification(
                user=trip.client.user,
                notification_type='TRIP_REMINDER',
                title="Rappel de course",
                message=f"Votre course avec {trip.captain.user.get_full_name()} commence dans 1 heure.",
                related_object=trip,
                data={
                    'trip_id': trip.id,
                    'scheduled_start_time': trip.scheduled_start_time.isoformat(),
                    'captain_name': trip.captain.user.get_full_name(),
                    'boat_name': trip.boat.name if trip.boat else ''
                },
                send_email=True
            )
            
            # Notification pour le capitaine
            create_notification(
                user=trip.captain.user,
                notification_type='TRIP_REMINDER',
                title="Rappel de course",
                message=f"Votre course avec {trip.client.user.get_full_name()} commence dans 1 heure.",
                related_object=trip,
                data={
                    'trip_id': trip.id,
                    'scheduled_start_time': trip.scheduled_start_time.isoformat(),
                    'client_name': trip.client.user.get_full_name(),
                    'passenger_count': trip.passenger_count
                },
                send_email=True
            )
            
            notifications_sent += 2
        
        logger.info(f"Rappels envoyés: {notifications_sent} notifications pour {upcoming_trips.count()} courses")
        
        return {
            'trips_found': upcoming_trips.count(),
            'notifications_sent': notifications_sent,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi des rappels: {str(e)}")
        return {'error': str(e)}


@shared_task
def update_trip_statistics():
    """
    Met à jour les statistiques des capitaines
    À exécuter quotidiennement
    """
    try:
        from .models import Trip
        from accounts.models import Captain
        from django.db.models import Count, Avg
        
        updated_captains = 0
        
        # Mettre à jour les statistiques de chaque capitaine
        for captain in Captain.objects.all():
            completed_trips = Trip.objects.filter(
                captain=captain,
                status=Trip.Status.COMPLETED
            )
            
            # Compter le nombre total de courses
            total_trips = completed_trips.count()
            
            # Calculer la note moyenne (si l'app reviews est intégrée)
            # Pour l'instant, on garde la note existante
            
            # Mettre à jour
            if captain.total_trips != total_trips:
                captain.total_trips = total_trips
                captain.save()
                updated_captains += 1
        
        logger.info(f"Statistiques mises à jour pour {updated_captains} capitaines")
        
        return {
            'updated_captains': updated_captains,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des statistiques: {str(e)}")
        return {'error': str(e)}


@shared_task
def check_delayed_trips():
    """
    Vérifie les courses en retard et envoie des notifications
    À exécuter toutes les 15 minutes
    """
    try:
        from .models import Trip
        from notifications.services import create_notification
        
        # Courses qui auraient dû commencer mais ne sont pas encore démarrées
        delayed_trips = Trip.objects.filter(
            status=Trip.Status.ACCEPTED,
            scheduled_start_time__lt=timezone.now() - timezone.timedelta(minutes=15)
        )
        
        notifications_sent = 0
        
        for trip in delayed_trips:
            # Marquer comme retardée si pas déjà fait
            if trip.status != Trip.Status.DELAYED:
                trip.status = Trip.Status.DELAYED
                trip.delay_minutes = int((timezone.now() - trip.scheduled_start_time).total_seconds() / 60)
                trip.save()
                
                # Notification pour le client
                create_notification(
                    user=trip.client.user,
                    notification_type='TRIP_DELAYED',
                    title="Course retardée",
                    message=f"Votre course avec {trip.captain.user.get_full_name()} est en retard de {trip.delay_minutes} minutes.",
                    related_object=trip,
                    data={
                        'trip_id': trip.id,
                        'delay_minutes': trip.delay_minutes,
                        'captain_name': trip.captain.user.get_full_name()
                    },
                    send_email=True
                )
                
                notifications_sent += 1
        
        logger.info(f"Courses en retard détectées: {delayed_trips.count()}, notifications envoyées: {notifications_sent}")
        
        return {
            'delayed_trips': delayed_trips.count(),
            'notifications_sent': notifications_sent,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Erreur lors de la vérification des retards: {str(e)}")
        return {'error': str(e)}


@shared_task
def generate_daily_report():
    """
    Génère un rapport quotidien des activités
    À exécuter tous les jours à minuit
    """
    try:
        from .models import Trip, TripRequest
        from datetime import date, timedelta
        
        yesterday = date.today() - timedelta(days=1)
        
        # Statistiques des demandes
        requests_created = TripRequest.objects.filter(
            created_at__date=yesterday
        ).count()
        
        requests_accepted = TripRequest.objects.filter(
            status=TripRequest.Status.ACCEPTED,
            updated_at__date=yesterday
        ).count()
        
        requests_expired = TripRequest.objects.filter(
            status=TripRequest.Status.EXPIRED,
            updated_at__date=yesterday
        ).count()
        
        # Statistiques des courses
        trips_completed = Trip.objects.filter(
            status=Trip.Status.COMPLETED,
            actual_end_time__date=yesterday
        ).count()
        
        trips_cancelled = Trip.objects.filter(
            status__in=[Trip.Status.CANCELLED, Trip.Status.CANCELLED_BY_CLIENT, Trip.Status.CANCELLED_BY_CAPTAIN],
            updated_at__date=yesterday
        ).count()
        
        report = {
            'date': yesterday.isoformat(),
            'requests': {
                'created': requests_created,
                'accepted': requests_accepted,
                'expired': requests_expired
            },
            'trips': {
                'completed': trips_completed,
                'cancelled': trips_cancelled
            },
            'generated_at': timezone.now().isoformat()
        }
        
        logger.info(f"Rapport quotidien généré: {report}")
        
        return report
        
    except Exception as e:
        logger.error(f"Erreur lors de la génération du rapport: {str(e)}")
        return {'error': str(e)}
