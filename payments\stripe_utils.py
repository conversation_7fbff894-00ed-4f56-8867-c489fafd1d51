import stripe
import logging
from django.conf import settings
from django.urls import reverse
from decimal import Decimal
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

# Configure Stripe API key
stripe.api_key = settings.STRIPE_SECRET_KEY
stripe.api_version = "2023-10-16"  # Utiliser une version stable de l'API

# Configuration du logger
logger = logging.getLogger(__name__)

def create_payment_intent(amount, currency='eur', payment_method_types=None, customer=None,
                         setup_future_usage=None, metadata=None, description=None):
    """
    Crée un payment intent avec Stripe

    Args:
        amount: Montant en centimes (ex: 1000 pour 10€)
        currency: Code de devise (défaut: eur)
        payment_method_types: Liste des types de méthodes de paiement acceptées
        customer: ID du client Stripe
        setup_future_usage: Indique si la méthode de paiement doit être sauvegardée pour une utilisation future
        metadata: Métadonnées additionnelles pour le paiement
        description: Description du paiement

    Returns:
        Objet payment intent ou dictionnaire d'erreur
    """
    try:
        payment_method_types = payment_method_types or ['card']

        params = {
            'amount': amount,
            'currency': currency,
            'payment_method_types': payment_method_types,
            'metadata': metadata or {},
        }

        if customer:
            params['customer'] = customer

        if setup_future_usage:
            params['setup_future_usage'] = setup_future_usage

        if description:
            params['description'] = description

        intent = stripe.PaymentIntent.create(**params)
        logger.info(f"Payment intent créé avec succès: {intent.id}")
        return intent
    except stripe.error.CardError as e:
        # Erreur liée à la carte du client
        logger.error(f"Erreur de carte: {str(e)}")
        return {'error': _("Erreur de carte: ") + str(e.user_message)}
    except stripe.error.RateLimitError as e:
        # Trop de requêtes effectuées vers l'API Stripe
        logger.error(f"Erreur de limite de taux: {str(e)}")
        return {'error': _("Trop de requêtes. Veuillez réessayer plus tard.")}
    except stripe.error.InvalidRequestError as e:
        # Paramètres invalides fournis à l'API Stripe
        logger.error(f"Erreur de requête invalide: {str(e)}")
        return {'error': _("Paramètres de paiement invalides.")}
    except stripe.error.AuthenticationError as e:
        # Problème d'authentification avec l'API Stripe
        logger.error(f"Erreur d'authentification: {str(e)}")
        return {'error': _("Erreur d'authentification avec le système de paiement.")}
    except stripe.error.APIConnectionError as e:
        # Problème de réseau lors de la communication avec Stripe
        logger.error(f"Erreur de connexion API: {str(e)}")
        return {'error': _("Impossible de se connecter au système de paiement. Veuillez vérifier votre connexion.")}
    except stripe.error.StripeError as e:
        # Erreur générique Stripe
        logger.error(f"Erreur Stripe: {str(e)}")
        return {'error': _("Une erreur est survenue lors du traitement du paiement.")}
    except Exception as e:
        # Autre erreur non liée à Stripe
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def create_checkout_session(amount, currency='eur', product_name='Commodore Service',
                           product_description=None, customer=None, payment_method_types=None,
                           success_url=None, cancel_url=None, metadata=None, locale='fr'):
    """
    Crée une session de paiement Checkout avec Stripe

    Args:
        amount: Montant en centimes (ex: 1000 pour 10€)
        currency: Code de devise (défaut: eur)
        product_name: Nom du produit ou service
        product_description: Description du produit ou service
        customer: ID du client Stripe
        payment_method_types: Liste des types de méthodes de paiement acceptées
        success_url: URL de redirection après paiement réussi
        cancel_url: URL de redirection après annulation
        metadata: Métadonnées additionnelles pour le paiement
        locale: Langue de l'interface Checkout (défaut: fr)

    Returns:
        Objet session checkout ou dictionnaire d'erreur
    """
    try:
        payment_method_types = payment_method_types or ['card']

        line_item = {
            'price_data': {
                'currency': currency,
                'product_data': {
                    'name': product_name,
                },
                'unit_amount': amount,
            },
            'quantity': 1,
        }

        if product_description:
            line_item['price_data']['product_data']['description'] = product_description

        params = {
            'payment_method_types': payment_method_types,
            'line_items': [line_item],
            'mode': 'payment',
            'success_url': success_url or settings.STRIPE_SUCCESS_URL,
            'cancel_url': cancel_url or settings.STRIPE_CANCEL_URL,
            'metadata': metadata or {},
            'locale': locale,
        }

        if customer:
            params['customer'] = customer

        session = stripe.checkout.Session.create(**params)
        logger.info(f"Session checkout créée avec succès: {session.id}")
        return session
    except stripe.error.CardError as e:
        logger.error(f"Erreur de carte: {str(e)}")
        return {'error': _("Erreur de carte: ") + str(e.user_message)}
    except stripe.error.RateLimitError as e:
        logger.error(f"Erreur de limite de taux: {str(e)}")
        return {'error': _("Trop de requêtes. Veuillez réessayer plus tard.")}
    except stripe.error.InvalidRequestError as e:
        logger.error(f"Erreur de requête invalide: {str(e)}")
        return {'error': _("Paramètres de paiement invalides.")}
    except stripe.error.AuthenticationError as e:
        logger.error(f"Erreur d'authentification: {str(e)}")
        return {'error': _("Erreur d'authentification avec le système de paiement.")}
    except stripe.error.APIConnectionError as e:
        logger.error(f"Erreur de connexion API: {str(e)}")
        return {'error': _("Impossible de se connecter au système de paiement. Veuillez vérifier votre connexion.")}
    except stripe.error.StripeError as e:
        logger.error(f"Erreur Stripe: {str(e)}")
        return {'error': _("Une erreur est survenue lors du traitement du paiement.")}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def create_connect_account(email, country='FR', business_type='individual', business_profile=None):
    """
    Crée un compte Connect pour un capitaine

    Args:
        email: Email du capitaine
        country: Code pays (défaut: FR)
        business_type: Type d'entreprise ('individual' ou 'company')
        business_profile: Informations sur l'entreprise

    Returns:
        Objet compte Connect ou dictionnaire d'erreur
    """
    try:
        params = {
            'type': 'express',
            'country': country,
            'email': email,
            'capabilities': {
                'card_payments': {'requested': True},
                'transfers': {'requested': True},
            },
            'business_type': business_type,
        }

        if business_profile:
            params['business_profile'] = business_profile

        account = stripe.Account.create(**params)
        logger.info(f"Compte Connect créé avec succès: {account.id}")
        return account
    except stripe.error.StripeError as e:
        logger.error(f"Erreur lors de la création du compte Connect: {str(e)}")
        return {'error': _("Erreur lors de la création du compte: ") + str(e)}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def create_account_link(account_id, refresh_url, return_url, type='account_onboarding'):
    """
    Crée un lien de compte pour l'onboarding

    Args:
        account_id: ID du compte Stripe
        refresh_url: URL pour rafraîchir le lien s'il expire
        return_url: URL de retour après l'onboarding
        type: Type de lien ('account_onboarding' ou 'account_update')

    Returns:
        Objet lien de compte ou dictionnaire d'erreur
    """
    try:
        account_link = stripe.AccountLink.create(
            account=account_id,
            refresh_url=refresh_url,
            return_url=return_url,
            type=type,
        )
        logger.info(f"Lien de compte créé avec succès pour: {account_id}")
        return account_link
    except stripe.error.StripeError as e:
        logger.error(f"Erreur lors de la création du lien de compte: {str(e)}")
        return {'error': _("Erreur lors de la création du lien: ") + str(e)}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def create_transfer(amount, destination, currency='eur', source_transaction=None,
                   transfer_group=None, metadata=None, description=None):
    """
    Crée un transfert vers un compte connecté

    Args:
        amount: Montant en centimes (ex: 1000 pour 10€)
        destination: ID du compte Stripe du destinataire
        currency: Code de devise (défaut: eur)
        source_transaction: ID du payment intent à utiliser comme source
        transfer_group: Groupe de transfert pour lier plusieurs transferts
        metadata: Métadonnées additionnelles pour le transfert
        description: Description du transfert

    Returns:
        Objet transfert ou dictionnaire d'erreur
    """
    try:
        params = {
            'amount': amount,
            'currency': currency,
            'destination': destination,
            'metadata': metadata or {},
        }

        if source_transaction:
            params['source_transaction'] = source_transaction

        if transfer_group:
            params['transfer_group'] = transfer_group

        if description:
            params['description'] = description

        transfer = stripe.Transfer.create(**params)
        logger.info(f"Transfert créé avec succès: {transfer.id}")
        return transfer
    except stripe.error.StripeError as e:
        logger.error(f"Erreur lors de la création du transfert: {str(e)}")
        return {'error': _("Erreur lors du transfert: ") + str(e)}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def handle_webhook_event(payload, sig_header):
    """
    Traite les événements webhook de Stripe

    Args:
        payload: Corps de la requête (bytes)
        sig_header: En-tête de signature Stripe

    Returns:
        Objet événement ou dictionnaire d'erreur
    """
    from .models import Payment, Wallet, Transaction
    from django.db import transaction

    try:
        # Vérifier que la clé secrète de webhook est définie
        if not settings.STRIPE_WEBHOOK_SECRET:
            logger.error("La clé secrète de webhook n'est pas définie")
            return {'error': _("La clé secrète de webhook n'est pas définie")}

        if not sig_header:
            logger.error("L'en-tête de signature est manquant")
            return {'error': _("L'en-tête de signature est manquant")}

        try:
            # Construire l'événement
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
            )
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Erreur de vérification de signature: {str(e)}")
            return {'error': _("Signature invalide")}

        # Journaliser l'événement
        logger.info(f"Événement webhook reçu: {event.type} - {event.id}")

        # Traiter l'événement selon son type
        with transaction.atomic():
            if event.type == 'payment_intent.succeeded':
                # Récupérer les données du paiement
                intent = event.data.object
                payment = Payment.objects.get(stripe_payment_intent_id=intent.id)

                # Mettre à jour le statut du paiement
                payment.status = Payment.Status.COMPLETED
                payment.stripe_payment_id = intent.charges.data[0].id
                payment.completed_at = timezone.now()
                payment.save()

                # Mettre à jour le portefeuille si nécessaire
                if payment.type == Payment.PaymentType.WALLET_RECHARGE and payment.wallet:
                    payment.wallet.balance += payment.amount
                    payment.wallet.total_earned += payment.amount
                    payment.wallet.last_transaction_at = timezone.now()
                    payment.wallet.save()

                    # Créer une transaction
                    Transaction.objects.create(
                        wallet=payment.wallet,
                        payment=payment,
                        type=Transaction.TransactionType.CREDIT,
                        amount=payment.amount,
                        balance_after=payment.wallet.balance,
                        description=_('Recharge du portefeuille')
                    )

            elif event.type == 'payment_intent.payment_failed':
                # Récupérer les données du paiement
                intent = event.data.object
                payment = Payment.objects.get(stripe_payment_intent_id=intent.id)

                # Mettre à jour le statut du paiement
                payment.status = Payment.Status.FAILED
                payment.save()

            elif event.type == 'charge.refunded':
                # Récupérer les données du remboursement
                charge = event.data.object
                payment = Payment.objects.get(stripe_payment_id=charge.id)

                # Calculer le montant remboursé
                refund_amount = Decimal(str(charge.amount_refunded)) / 100

                # Mettre à jour le statut du paiement
                payment.status = Payment.Status.REFUNDED if charge.refunded else Payment.Status.PARTIALLY_REFUNDED
                payment.refund_amount = refund_amount
                payment.refunded_at = timezone.now()
                payment.save()

                # Rembourser le portefeuille si nécessaire
                if payment.wallet:
                    payment.wallet.balance += refund_amount
                    payment.wallet.save()

                    # Créer une transaction
                    Transaction.objects.create(
                        wallet=payment.wallet,
                        payment=payment,
                        type=Transaction.TransactionType.REFUND,
                        amount=refund_amount,
                        balance_after=payment.wallet.balance,
                        description=_('Remboursement')
                    )

            elif event.type == 'customer.created':
                # Mettre à jour l'ID client Stripe dans le portefeuille
                customer = event.data.object
                if customer.email:
                    try:
                        wallet = Wallet.objects.get(user__email=customer.email)
                        wallet.stripe_customer_id = customer.id
                        wallet.save()
                    except Wallet.DoesNotExist:
                        logger.warning(f"Portefeuille non trouvé pour l'email: {customer.email}")

            elif event.type == 'payment_method.attached':
                # Mettre à jour la méthode de paiement par défaut
                payment_method = event.data.object
                if payment_method.customer:
                    try:
                        wallet = Wallet.objects.get(stripe_customer_id=payment_method.customer)
                        wallet.default_payment_method = payment_method.id
                        wallet.save()
                    except Wallet.DoesNotExist:
                        logger.warning(f"Portefeuille non trouvé pour le client: {payment_method.customer}")

        return event

    except Payment.DoesNotExist:
        logger.error(f"Paiement non trouvé pour l'événement: {event.id}")
        return {'error': _("Paiement non trouvé")}
    except ValueError as e:
        logger.error(f"Payload invalide: {str(e)}")
        return {'error': _("Payload invalide")}
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Signature invalide: {str(e)}")
        return {'error': _("Signature invalide")}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {'error': _("Une erreur inattendue est survenue.")}

def create_customer(email, name=None, phone=None, metadata=None, description=None):
    """
    Crée un client Stripe

    Args:
        email: Email du client
        name: Nom du client
        phone: Numéro de téléphone du client
        metadata: Métadonnées additionnelles
        description: Description du client

    Returns:
        Objet client ou dictionnaire d'erreur
    """
    try:
        params = {
            'email': email,
            'metadata': metadata or {},
        }

        if name:
            params['name'] = name

        if phone:
            params['phone'] = phone

        if description:
            params['description'] = description

        customer = stripe.Customer.create(**params)
        logger.info(f"Client créé avec succès: {customer.id}")
        return customer
    except stripe.error.StripeError as e:
        logger.error(f"Erreur lors de la création du client: {str(e)}")
        return {'error': _("Erreur lors de la création du client: ") + str(e)}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def create_payment_method(type, card=None, billing_details=None):
    """
    Crée une méthode de paiement Stripe

    Args:
        type: Type de méthode de paiement (ex: 'card')
        card: Détails de la carte
        billing_details: Détails de facturation

    Returns:
        Objet méthode de paiement ou dictionnaire d'erreur
    """
    try:
        params = {
            'type': type,
        }

        if type == 'card' and card:
            params['card'] = card

        if billing_details:
            params['billing_details'] = billing_details

        payment_method = stripe.PaymentMethod.create(**params)
        logger.info(f"Méthode de paiement créée avec succès: {payment_method.id}")
        return payment_method
    except stripe.error.StripeError as e:
        logger.error(f"Erreur lors de la création de la méthode de paiement: {str(e)}")
        return {'error': _("Erreur lors de la création de la méthode de paiement: ") + str(e)}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def attach_payment_method(payment_method_id, customer_id):
    """
    Attache une méthode de paiement à un client

    Args:
        payment_method_id: ID de la méthode de paiement
        customer_id: ID du client

    Returns:
        Objet méthode de paiement ou dictionnaire d'erreur
    """
    try:
        payment_method = stripe.PaymentMethod.attach(
            payment_method_id,
            customer=customer_id,
        )
        logger.info(f"Méthode de paiement {payment_method_id} attachée au client {customer_id}")
        return payment_method
    except stripe.error.StripeError as e:
        logger.error(f"Erreur lors de l'attachement de la méthode de paiement: {str(e)}")
        return {'error': _("Erreur lors de l'attachement de la méthode de paiement: ") + str(e)}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def detach_payment_method(payment_method_id):
    """
    Détache une méthode de paiement d'un client

    Args:
        payment_method_id: ID de la méthode de paiement

    Returns:
        Objet méthode de paiement ou dictionnaire d'erreur
    """
    try:
        payment_method = stripe.PaymentMethod.detach(payment_method_id)
        logger.info(f"Méthode de paiement détachée avec succès: {payment_method_id}")
        return payment_method
    except stripe.error.StripeError as e:
        logger.error(f"Erreur lors du détachement de la méthode de paiement: {str(e)}")
        return {'error': _("Erreur lors du détachement de la méthode de paiement: ") + str(e)}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def list_payment_methods(customer_id, type='card'):
    """
    Liste les méthodes de paiement d'un client

    Args:
        customer_id: ID du client
        type: Type de méthode de paiement (défaut: 'card')

    Returns:
        Liste des méthodes de paiement ou dictionnaire d'erreur
    """
    try:
        payment_methods = stripe.PaymentMethod.list(
            customer=customer_id,
            type=type,
        )
        logger.info(f"Méthodes de paiement listées avec succès pour le client: {customer_id}")
        return payment_methods
    except stripe.error.StripeError as e:
        logger.error(f"Erreur lors de la liste des méthodes de paiement: {str(e)}")
        return {'error': _("Erreur lors de la liste des méthodes de paiement: ") + str(e)}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}

def create_refund(payment_intent_id=None, charge_id=None, amount=None, reason=None, metadata=None):
    """
    Crée un remboursement

    Args:
        payment_intent_id: ID du payment intent à rembourser
        charge_id: ID de la charge à rembourser
        amount: Montant à rembourser (en centimes)
        reason: Raison du remboursement ('duplicate', 'fraudulent', 'requested_by_customer')
        metadata: Métadonnées additionnelles

    Returns:
        Objet remboursement ou dictionnaire d'erreur
    """
    try:
        params = {
            'metadata': metadata or {},
        }

        if payment_intent_id:
            params['payment_intent'] = payment_intent_id
        elif charge_id:
            params['charge'] = charge_id
        else:
            return {'error': _("Vous devez fournir un payment_intent_id ou un charge_id")}

        if amount:
            params['amount'] = amount

        if reason:
            params['reason'] = reason

        refund = stripe.Refund.create(**params)
        logger.info(f"Remboursement créé avec succès: {refund.id}")
        return refund
    except stripe.error.StripeError as e:
        logger.error(f"Erreur lors de la création du remboursement: {str(e)}")
        return {'error': _("Erreur lors de la création du remboursement: ") + str(e)}
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return {'error': _("Une erreur inattendue est survenue.")}
