"""
Exceptions personnalisées pour l'application establishments.
"""

from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status


class EstablishmentException(Exception):
    """Exception de base pour l'application establishments"""
    pass


class BoatmanRegistrationError(EstablishmentException):
    """Erreur lors de l'enregistrement d'un batelier"""
    pass


class ShuttleManagementError(EstablishmentException):
    """Erreur lors de la gestion des navettes"""
    pass


class WalletError(EstablishmentException):
    """Erreur liée au portefeuille"""
    pass


class InsufficientFundsError(WalletError):
    """Fonds insuffisants dans le portefeuille"""
    pass


class EmailSendingError(EstablishmentException):
    """Erreur lors de l'envoi d'email"""
    pass


def custom_exception_handler(exc, context):
    """
    Gestionnaire d'exceptions personnalisé pour l'app establishments.
    """
    # Appeler le gestionnaire par défaut de DRF
    response = exception_handler(exc, context)
    
    if response is not None:
        # Personnaliser la réponse d'erreur
        custom_response_data = {
            'status': 'error',
            'error': response.data.get('detail', str(response.data)),
            'error_code': response.status_code,
            'timestamp': context['request'].META.get('HTTP_X_TIMESTAMP', None)
        }
        
        # Ajouter des détails spécifiques selon le type d'erreur
        if isinstance(exc, BoatmanRegistrationError):
            custom_response_data['error_type'] = 'BOATMAN_REGISTRATION_ERROR'
        elif isinstance(exc, ShuttleManagementError):
            custom_response_data['error_type'] = 'SHUTTLE_MANAGEMENT_ERROR'
        elif isinstance(exc, WalletError):
            custom_response_data['error_type'] = 'WALLET_ERROR'
        elif isinstance(exc, EmailSendingError):
            custom_response_data['error_type'] = 'EMAIL_SENDING_ERROR'
        else:
            custom_response_data['error_type'] = 'GENERAL_ERROR'
        
        response.data = custom_response_data
    
    return response


def handle_establishment_errors(view_func):
    """
    Décorateur pour gérer les erreurs spécifiques aux établissements.
    """
    def wrapper(*args, **kwargs):
        try:
            return view_func(*args, **kwargs)
        except BoatmanRegistrationError as e:
            return Response({
                'status': 'error',
                'error': str(e),
                'error_type': 'BOATMAN_REGISTRATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
        except ShuttleManagementError as e:
            return Response({
                'status': 'error',
                'error': str(e),
                'error_type': 'SHUTTLE_MANAGEMENT_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
        except WalletError as e:
            return Response({
                'status': 'error',
                'error': str(e),
                'error_type': 'WALLET_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
        except EmailSendingError as e:
            return Response({
                'status': 'error',
                'error': str(e),
                'error_type': 'EMAIL_SENDING_ERROR'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except Exception as e:
            return Response({
                'status': 'error',
                'error': 'Une erreur inattendue s\'est produite',
                'error_type': 'UNEXPECTED_ERROR'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return wrapper
