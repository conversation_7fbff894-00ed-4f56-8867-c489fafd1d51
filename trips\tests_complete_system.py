"""
Tests complets pour le système de réservation à trois types de voyages.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from decimal import Decimal
from django.utils import timezone
from datetime import timed<PERSON>ta

from accounts.models import Captain, Client, Establishment
from boats.models import Boat
from .models import Trip, TripRequest, SimpleTripRequest, HourlyTripRequest, ShuttleTripRequest, TripQuote
from payments.models import Wallet, Payment
from .carbon_calculator import CarbonFootprintCalculator
from .qr_service import generate_trip_qr_code, validate_trip_qr_code

User = get_user_model()


class CompleteTripSystemTest(APITestCase):
    """Tests du système complet de réservation"""
    
    def setUp(self):
        """Configuration initiale pour tous les tests"""
        
        # Créer un client
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='<PERSON>',
            last_name='Dubois'
        )
        self.client_profile = Client.objects.create(user=self.client_user)
        self.client_wallet = Wallet.objects.create(user=self.client_user, balance=Decimal('200.00'))
        
        # Créer un capitaine
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Captain',
            last_name='Jack',
            is_captain=True
        )
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans',
            license_number='LIC001',
            rate_per_km=Decimal('25.00'),
            rate_per_hour=Decimal('50.00'),
            is_available=True
        )
        
        # Créer un bateau
        self.boat = Boat.objects.create(
            captain=self.captain,
            name='Sea Explorer',
            boat_type='classic',
            capacity=8,
            fuel_type='gasoline',
            fuel_consumption=25.0,
            is_available=True
        )
        
        # Créer un établissement
        self.establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Hotel',
            last_name='Paradise'
        )
        self.establishment = Establishment.objects.create(
            user=self.establishment_user,
            name='Hotel Paradise Beach',
            type='HOTEL'
        )
        
        # Tokens d'authentification
        self.client_token = Token.objects.create(user=self.client_user)
        self.captain_token = Token.objects.create(user=self.captain_user)
        self.establishment_token = Token.objects.create(user=self.establishment_user)
        
        self.client_api = APIClient()
        self.client_api.credentials(HTTP_AUTHORIZATION='Token ' + self.client_token.key)
        
        self.captain_api = APIClient()
        self.captain_api.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

    def test_paid_trip_complete_workflow(self):
        """Test du workflow complet d'une course payante"""
        
        # 1. Client fait une demande de course simple
        trip_data = {
            'departure_location': {'city_name': 'Cannes Port', 'coordinates': {'lat': 43.5528, 'lng': 7.0174}},
            'arrival_location': {'city_name': 'Îles de Lérins', 'coordinates': {'lat': 43.5184, 'lng': 7.0457}},
            'departure_date': (timezone.now() + timedelta(hours=2)).date().isoformat(),
            'departure_time': (timezone.now() + timedelta(hours=2)).time().isoformat(),
            'passenger_count': 4,
            'boat_type': 'classic',
            'special_requests': 'Voyage romantique'
        }
        
        response = self.client_api.post('/api/trips/requests/simple/', trip_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        request_id = response.data['data']['request_id']
        
        # 2. Vérifier que des devis ont été générés
        response = self.client_api.get(f'/api/trips/requests/{request_id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data['data']['quotes']), 0)
        
        quote_id = response.data['data']['quotes'][0]['quote_id']
        
        # 3. Client accepte un devis
        response = self.client_api.post(f'/api/trips/quotes/{quote_id}/accept/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        trip_id = response.data['data']['trip_id']
        
        # 4. Capitaine valide la demande
        response = self.captain_api.post(f'/api/trips/{trip_id}/accept/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 5. Client paie la course via portefeuille
        payment_data = {
            'payment_method': 'wallet'
        }
        
        response = self.client_api.post(f'/api/trips/{trip_id}/payment/', payment_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['booking_confirmed'])
        self.assertIn('qr_code', response.data)
        
        qr_code = response.data['qr_code']
        
        # 6. Vérifier que le QR code est valide
        trip = Trip.objects.get(id=trip_id)
        is_valid, message = validate_trip_qr_code(trip, qr_code)
        self.assertTrue(is_valid)
        
        # 7. Capitaine démarre la course
        response = self.captain_api.post(f'/api/trips/{trip_id}/start/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 8. Capitaine termine la course
        response = self.captain_api.post(f'/api/trips/{trip_id}/complete/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 9. Client consulte l'empreinte carbone
        response = self.client_api.get(f'/api/trips/{trip_id}/carbon-footprint/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('carbon_footprint', response.data)
        self.assertIn('co2_kg', response.data['carbon_footprint'])
        
        # 10. Client paie la compensation carbone (optionnelle)
        carbon_data = {
            'payment_method': 'wallet'
        }
        
        response = self.client_api.post(f'/api/trips/{trip_id}/carbon-compensation/', carbon_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('co2_compensated_kg', response.data)
        
        # 11. Client donne un pourboire
        tip_data = {
            'amount': '10.00',
            'payment_method': 'wallet'
        }
        
        response = self.client_api.post(f'/api/trips/{trip_id}/tip/', tip_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['amount'], 10.0)

    def test_free_shuttle_workflow(self):
        """Test du workflow des navettes gratuites"""
        
        # 1. Client demande une navette gratuite
        shuttle_data = {
            'establishment_id': self.establishment.id,
            'departure_location': {'city_name': 'Aéroport Nice', 'coordinates': {'lat': 43.6584, 'lng': 7.2159}},
            'arrival_location': {'city_name': 'Hotel Paradise Beach', 'coordinates': {'lat': 43.5528, 'lng': 7.0174}},
            'departure_date': (timezone.now() + timedelta(hours=1)).date().isoformat(),
            'departure_time': (timezone.now() + timedelta(hours=1)).time().isoformat(),
            'passenger_count': 2
        }
        
        response = self.client_api.post('/api/trips/requests/shuttle/', shuttle_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        request_id = response.data['data']['request_id']
        
        # 2. Établissement accepte et assigne un capitaine
        establishment_api = APIClient()
        establishment_api.credentials(HTTP_AUTHORIZATION='Token ' + self.establishment_token.key)
        
        accept_data = {
            'captain_id': self.captain.user.id,  # Captain utilise user comme clé primaire
            'boat_id': self.boat.id,
            'estimated_pickup_time': (timezone.now() + timedelta(hours=1)).isoformat()
        }
        
        response = establishment_api.post(f'/api/establishments/shuttle-requests/{request_id}/accept/', accept_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        trip_id = response.data['data']['trip_id']
        
        # 3. Vérifier que la course est gratuite
        trip = Trip.objects.get(id=trip_id)
        self.assertEqual(trip.trip_type, 'NAVETTES_GRATUITES')
        self.assertEqual(trip.total_price, Decimal('0.00'))
        self.assertEqual(trip.payment_status, 'PAID')

    def test_hourly_trip_workflow(self):
        """Test du workflow des mises à disposition"""
        
        # 1. Client fait une demande de mise à disposition
        hourly_data = {
            'departure_location': {'city_name': 'Port de Cannes', 'coordinates': {'lat': 43.5528, 'lng': 7.0174}},
            'departure_date': (timezone.now() + timedelta(hours=3)).date().isoformat(),
            'departure_time': (timezone.now() + timedelta(hours=3)).time().isoformat(),
            'duration_hours': 4,
            'passenger_count': 6,
            'boat_type': 'classic',
            'special_requests': 'Tour des îles avec arrêts baignade'
        }
        
        response = self.client_api.post('/api/trips/requests/hourly/', hourly_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        request_id = response.data['data']['request_id']
        
        # 2. Vérifier que des devis ont été générés avec tarification horaire
        response = self.client_api.get(f'/api/trips/requests/{request_id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        quotes = response.data['data']['quotes']
        self.assertGreater(len(quotes), 0)
        
        # Vérifier que le prix est basé sur la durée
        quote = quotes[0]
        expected_price = self.captain.rate_per_hour * 4  # 4 heures
        self.assertEqual(Decimal(str(quote['total_price'])), expected_price)

    def test_carbon_footprint_calculation(self):
        """Test des calculs d'empreinte carbone"""
        
        # Créer une course terminée
        trip = Trip.objects.create(
            client=self.client_profile,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Cannes',
            end_location='Îles de Lérins',
            scheduled_start_time=timezone.now() - timedelta(hours=1),
            actual_start_time=timezone.now() - timedelta(hours=1),
            actual_end_time=timezone.now() - timedelta(minutes=45),
            estimated_duration=15,
            actual_duration=15,
            passenger_count=4,
            status='COMPLETED',
            payment_status='PAID',
            total_price=Decimal('75.00')
        )
        
        # Calculer l'empreinte carbone
        carbon_data = CarbonFootprintCalculator.calculate_trip_carbon_data(trip)
        
        self.assertIsNotNone(carbon_data)
        self.assertIn('calculation', carbon_data)
        self.assertIn('co2_kg', carbon_data['calculation'])
        self.assertIn('compensation_cost_euros', carbon_data['calculation'])
        
        # Vérifier les calculs pour un bateau essence
        calculation = carbon_data['calculation']
        self.assertEqual(calculation['fuel_type'], 'gasoline')
        self.assertGreater(calculation['co2_kg'], 0)
        self.assertGreater(calculation['compensation_cost_euros'], 0)

    def test_qr_code_security(self):
        """Test de la sécurité des QR codes"""
        
        # Créer une course payée
        trip = Trip.objects.create(
            client=self.client_profile,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Cannes',
            end_location='Îles de Lérins',
            scheduled_start_time=timezone.now() + timedelta(minutes=30),
            passenger_count=2,
            status='ACCEPTED',
            payment_status='PAID',
            total_price=Decimal('50.00')
        )
        
        # Générer un QR code
        qr_code = generate_trip_qr_code(trip)
        self.assertIsNotNone(qr_code)
        self.assertTrue(qr_code.startswith('COMMODORE_TRIP_'))
        
        # Valider le QR code correct
        is_valid, message = validate_trip_qr_code(trip, qr_code)
        self.assertTrue(is_valid)
        
        # Tester un QR code invalide
        fake_qr = 'COMMODORE_TRIP_999_fakehash'
        is_valid, message = validate_trip_qr_code(trip, fake_qr)
        self.assertFalse(is_valid)
        
        # Tester validation trop précoce
        trip.scheduled_start_time = timezone.now() + timedelta(hours=2)
        trip.save()
        
        is_valid, message = validate_trip_qr_code(trip, qr_code)
        self.assertFalse(is_valid)
        self.assertIn('trop précoce', message)

    def test_wallet_integration(self):
        """Test de l'intégration du portefeuille"""
        
        # Vérifier le solde initial
        response = self.client_api.get('/api/trips/wallet/balance/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['balance'], 200.0)
        
        # Simuler une recharge (sans vraie carte)
        # En production, ceci utiliserait Stripe
        wallet = Wallet.objects.get(user=self.client_user)
        wallet.add_funds(Decimal('50.00'), 'CARD', 'Test recharge')
        
        # Vérifier le nouveau solde
        response = self.client_api.get('/api/trips/wallet/balance/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['balance'], 250.0)

    def test_payment_types_separation(self):
        """Test de la séparation des types de paiements"""
        
        # Créer une course terminée
        trip = Trip.objects.create(
            client=self.client_profile,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Cannes',
            end_location='Îles de Lérins',
            scheduled_start_time=timezone.now() - timedelta(hours=1),
            actual_start_time=timezone.now() - timedelta(hours=1),
            actual_end_time=timezone.now() - timedelta(minutes=45),
            estimated_duration=15,
            actual_duration=15,
            passenger_count=4,
            status='COMPLETED',
            payment_status='PAID',
            total_price=Decimal('75.00')
        )
        
        # Créer différents types de paiements
        Payment.objects.create(
            user=self.client_user,
            trip=trip,
            type=Payment.PaymentType.TRIP,
            amount=Decimal('75.00'),
            status=Payment.Status.COMPLETED,
            description='Paiement course principale'
        )
        
        Payment.objects.create(
            user=self.client_user,
            trip=trip,
            type=Payment.PaymentType.CARBON_OFFSET,
            amount=Decimal('1.16'),
            status=Payment.Status.COMPLETED,
            description='Compensation carbone'
        )
        
        Payment.objects.create(
            user=self.client_user,
            trip=trip,
            type=Payment.PaymentType.TIP,
            amount=Decimal('10.00'),
            status=Payment.Status.COMPLETED,
            description='Pourboire capitaine'
        )
        
        # Vérifier le statut des paiements
        response = self.client_api.get(f'/api/trips/{trip.id}/payment/status/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        payments = response.data['payments']
        self.assertEqual(len(payments), 3)
        
        # Vérifier que tous les types sont présents
        payment_types = [p['type'] for p in payments]
        self.assertIn('TRIP', payment_types)
        self.assertIn('CARBON_OFFSET', payment_types)
        self.assertIn('TIP', payment_types)
