# Documentation Technique du Système RAG Commodore

## Introduction

Cette documentation technique détaille l'architecture, les composants et le fonctionnement du système RAG (Retrieval Augmented Generation) de Commodore. Elle est destinée aux développeurs et administrateurs système qui souhaitent comprendre, maintenir ou étendre le système.

## Architecture du système

Le système RAG de Commodore est basé sur une architecture modulaire qui combine plusieurs technologies :

1. **Django** : Framework web pour la gestion des API, des modèles de données et de l'authentification
2. **PostgreSQL** : Base de données relationnelle pour stocker les documents, chunks, sessions et messages
3. **Redis** : Système de cache pour améliorer les performances et stocker les données hors ligne
4. **FAISS** : Bibliothèque de recherche vectorielle pour la récupération efficace des chunks pertinents
5. **Gemini** : Modèle de langage de Google pour la génération de réponses et la création d'embeddings

### Diagramme de composants

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  API Django     │     │  Service RAG    │     │  Modèle Gemini  │
│                 │────▶│                 │────▶│                 │
│ (Endpoints)     │     │ (Logique métier)│     │ (LLM & Embeddings)
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                      │                        │
         │                      │                        │
         ▼                      ▼                        │
┌─────────────────┐     ┌─────────────────┐             │
│  PostgreSQL     │     │  FAISS          │◀────────────┘
│                 │     │                 │
│ (Stockage)      │     │ (Vectorstore)   │
└─────────────────┘     └─────────────────┘
         ▲                      ▲
         │                      │
         │                      │
         ▼                      │
┌─────────────────┐             │
│  Redis          │─────────────┘
│                 │
│ (Cache)         │
└─────────────────┘
```

## Composants principaux

### 1. Service RAG (`rag_service`)

Le service RAG est le cœur du système. Il gère :
- Le traitement des documents et leur découpage en chunks
- La génération d'embeddings pour les chunks
- La recherche vectorielle pour trouver les chunks pertinents
- La génération de réponses basées sur les chunks récupérés

#### Performances optimisées

Le service a été optimisé pour offrir des temps de réponse rapides (3-4 secondes) grâce à :
- Préchargement des chunks en une seule requête
- Utilisation de Redis pour le cache
- Optimisations basées sur l'intention et le profil utilisateur
- Logs de performance pour identifier les goulots d'étranglement

### 2. Modèles de données

Le système utilise plusieurs modèles Django pour stocker les données :

- **Document** : Représente un document dans la base de connaissances
- **DocumentChunk** : Représente un fragment de document avec son embedding
- **ChatSession** : Représente une session de chat avec un utilisateur
- **ChatMessage** : Représente un message dans une session de chat

### 3. Vectorstore FAISS

FAISS est utilisé pour stocker et rechercher efficacement les embeddings des chunks. Le système :
- Charge les embeddings depuis la base de données
- Crée un index FAISS pour la recherche vectorielle
- Utilise la similarité cosinus pour trouver les chunks pertinents

### 4. Cache Redis

Redis est utilisé pour :
- Mettre en cache les résultats de recherche vectorielle
- Stocker les données pour le support hors ligne
- Améliorer les performances globales du système

## Flux de traitement

### 1. Traitement des documents

1. Le document est divisé en sections basées sur le séparateur `⸻`
2. Chaque section est découpée en chunks plus petits
3. Pour chaque chunk, un embedding est généré avec Gemini
4. Les chunks et leurs embeddings sont stockés dans la base de données

### 2. Génération de réponse

1. La requête utilisateur est analysée pour détecter l'intention et les entités
2. Un embedding est généré pour la requête
3. Les chunks pertinents sont récupérés via FAISS
4. Les chunks sont triés et filtrés en fonction du profil utilisateur
5. Les chunks sont utilisés comme contexte pour générer une réponse avec Gemini
6. La réponse est post-traitée pour améliorer sa qualité

## Configuration et paramètres

### Paramètres principaux

- **GEMINI_API_KEY** : Clé API pour accéder aux modèles Gemini
- **GEMINI_CHAT_MODEL** : Modèle Gemini utilisé pour la génération de réponses (par défaut : "gemini-2.0-flash")
- **GEMINI_EMBEDDING_MODEL** : Modèle Gemini utilisé pour la génération d'embeddings (par défaut : "models/embedding-001")
- **CHUNK_SIZE** : Taille maximale des chunks (par défaut : 800 caractères)
- **CHUNK_OVERLAP** : Chevauchement entre chunks (par défaut : 150 caractères)
- **TOP_K_RETRIEVAL** : Nombre maximum de chunks à récupérer (par défaut : 5)
- **SIMILARITY_THRESHOLD** : Seuil de similarité pour filtrer les chunks (par défaut : 0.7)

### Configuration Redis

Redis doit être configuré et accessible sur le port standard (6379). La configuration est définie dans `settings.py` :

```python
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/0',
    },
    'rag': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'TIMEOUT': 86400,  # 24 heures
    },
    'offline': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/2',
        'TIMEOUT': 604800,  # 7 jours
    },
}
```

## Prompts et génération de réponses

### Prompt système

Le prompt système définit le comportement global du chatbot. Il a été optimisé pour générer des réponses structurées, informatives et personnalisées.

### Prompt de récupération

Le prompt de récupération est utilisé pour formater la requête utilisateur avec le contexte récupéré. Il a été optimisé pour mieux utiliser le contexte et générer des réponses de haute qualité.

### Post-traitement des réponses

Les réponses générées sont post-traitées pour :
- Améliorer la structure (paragraphes, listes à puces)
- Ajouter des formules de politesse
- Ajouter des mentions RGPD si pertinent
- Personnaliser en fonction du profil utilisateur
- Ajouter des conseils pratiques pour les clients

## Maintenance et dépannage

### Démarrage de Redis

Redis doit être démarré avant de lancer le serveur Django :

```bash
# Sur Windows
D:\COMMOD\redis\redis-server.exe

# Sur Linux/Mac
redis-server
```

### Vérification de l'état de Redis

Pour vérifier si Redis est accessible :

```bash
# Sur Windows
D:\COMMOD\redis\redis-cli.exe ping

# Sur Linux/Mac
redis-cli ping
```

La réponse devrait être "PONG".

### Logs et débogage

Le système utilise le module `logging` de Python pour enregistrer les événements importants. Les logs incluent :
- Temps de traitement pour chaque étape
- Erreurs et avertissements
- Informations sur les chunks récupérés et les réponses générées

### Tests de robustesse

Des scripts de test sont disponibles pour vérifier la robustesse du système :
- `test_rag_robustness.py` : Teste la robustesse face aux erreurs
- `test_gemini_integration.py` : Teste l'intégration avec Gemini
- `test_vectorstore.py` : Teste le vectorstore FAISS
- `test_redis_cache.py` : Teste les performances du cache Redis

## Optimisations futures

1. **Amélioration des embeddings** : Utiliser des modèles d'embeddings plus récents pour améliorer la qualité de la recherche
2. **Clustering des chunks** : Regrouper les chunks similaires pour améliorer la diversité des résultats
3. **Adaptation dynamique du modèle** : Sélectionner automatiquement le modèle Gemini en fonction de la complexité de la requête
4. **Parallélisation** : Traiter les documents et générer les embeddings en parallèle pour améliorer les performances
5. **Compression des embeddings** : Réduire la taille des embeddings pour améliorer les performances de recherche

## Conclusion

Le système RAG de Commodore est une solution robuste et performante pour fournir des réponses précises et personnalisées aux utilisateurs. Grâce aux optimisations récentes, il offre des temps de réponse rapides (3-4 secondes) et des réponses de haute qualité.
