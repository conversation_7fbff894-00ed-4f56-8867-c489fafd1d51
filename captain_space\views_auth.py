"""
Vues d'authentification pour l'espace capitaine mobile.

Ce module contient toutes les vues d'authentification spécifiques
à l'application mobile des capitaines.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from accounts.models import Captain
from django.core.mail import send_mail
from django.conf import settings
import random
import string


class CaptainLoginView(APIView):
    """
    Connexion pour les capitaines.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """
        POST /api/captain/login/
        
        Body: {
            "email": "<EMAIL>",
            "password": "motdepasse123"
        }
        """
        
        try:
            email = request.data.get('email')
            password = request.data.get('password')
            
            if not email or not password:
                return Response({
                    'status': 'error',
                    'message': 'Email et mot de passe requis'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Authentifier l'utilisateur
            user = authenticate(username=email, password=password)
            
            if not user:
                return Response({
                    'status': 'error',
                    'message': 'Identifiants invalides'
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # Vérifier que c'est un capitaine (double vérification)
            try:
                captain = user.captain
            except Captain.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': 'Cet utilisateur n\'est pas un capitaine'
                }, status=status.HTTP_403_FORBIDDEN)
                
            # Vérifier que l'utilisateur est bien de type CAPTAIN
            if user.type != 'CAPTAIN':
                return Response({
                    'status': 'error',
                    'message': 'Accès refusé - Ce compte n\'est pas un compte capitaine'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Générer les tokens JWT
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token
            
            return Response({
                'status': 'success',
                'data': {
                    'captain_id': str(captain.user.id),  # Utiliser user.id comme clé primaire
                    'token': str(access_token),
                    'captain_name': user.get_full_name() or user.email
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Erreur lors de la connexion: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CaptainSignupView(APIView):
    """
    Inscription pour les capitaines.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """
        POST /api/captain/signup/
        
        Body: {
            "name": "John Doe",
            "email": "<EMAIL>",
            "password": "motdepasse123"
        }
        """
        
        try:
            name = request.data.get('name')
            email = request.data.get('email')
            password = request.data.get('password')
            
            if not all([name, email, password]):
                return Response({
                    'status': 'error',
                    'message': 'Nom, email et mot de passe requis'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Vérifier si l'email existe déjà
            if User.objects.filter(email=email).exists():
                return Response({
                    'status': 'error',
                    'message': 'Cet email est déjà utilisé'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Créer l'utilisateur
            user = User.objects.create_user(
                username=email,
                email=email,
                password=password,
                first_name=name.split(' ')[0] if ' ' in name else name,
                last_name=' '.join(name.split(' ')[1:]) if ' ' in name else ''
            )
            
            # Créer le profil capitaine
            captain = Captain.objects.create(
                user=user,
                experience='',
                license_number='',
                years_of_experience=0,
                rate_per_hour=0,
                rate_per_km=0,
                is_available=True
            )
            
            # Générer les tokens JWT
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token
            
            return Response({
                'status': 'success',
                'data': {
                    'captain_id': str(captain.user.id),
                    'token': str(access_token)
                }
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Erreur lors de l\'inscription: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CaptainForgotPasswordView(APIView):
    """
    Mot de passe oublié pour les capitaines.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """
        POST /api/captain/forgot-password/
        
        Body: {
            "email": "<EMAIL>"
        }
        """
        
        try:
            email = request.data.get('email')
            
            if not email:
                return Response({
                    'status': 'error',
                    'message': 'Email requis'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                user = User.objects.get(email=email)
                captain = user.captain
            except (User.DoesNotExist, Captain.DoesNotExist):
                # Pour la sécurité, on retourne toujours success même si l'email n'existe pas
                return Response({
                    'status': 'success',
                    'message': 'Instructions de réinitialisation envoyées'
                }, status=status.HTTP_200_OK)
            
            # Générer un code de réinitialisation
            reset_code = ''.join(random.choices(string.digits, k=6))
            
            # Stocker le code (ici on pourrait utiliser Redis ou un modèle temporaire)
            # Pour la démo, on l'envoie juste par email
            
            # Envoyer l'email
            try:
                send_mail(
                    'Réinitialisation de votre mot de passe Commodore',
                    f'Votre code de réinitialisation est: {reset_code}',
                    settings.DEFAULT_FROM_EMAIL,
                    [email],
                    fail_silently=False,
                )
            except Exception:
                pass  # Ignorer les erreurs d'email pour la démo
            
            return Response({
                'status': 'success',
                'message': 'Instructions de réinitialisation envoyées'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Erreur lors de la réinitialisation: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CaptainLogoutView(APIView):
    """
    Déconnexion pour les capitaines.
    """

    def post(self, request):
        """
        POST /api/captain/logout/
        
        Body: {
            "captain_id": "123",
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        }
        """
        
        try:
            # Dans une vraie implémentation, on pourrait blacklister le token
            # Pour la démo, on retourne juste success
            
            return Response({
                'status': 'success',
                'message': 'Déconnexion réussie'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Erreur lors de la déconnexion: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
