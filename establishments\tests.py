"""
Tests pour l'application establishments.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from decimal import Decimal
from unittest.mock import patch

from accounts.models import Establishment, Captain, Client
from boats.models import Boat
from trips.models import ShuttleTripRequest
from payments.models import Wallet

User = get_user_model()


class EstablishmentDashboardTestCase(APITestCase):
    """Tests pour le tableau de bord de l'établissement"""
    
    def setUp(self):
        """Configuration initiale pour les tests"""
        # Créer un utilisateur établissement
        self.establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Hotel',
            last_name='Paradise'
        )
        
        # Créer le profil établissement
        self.establishment = Establishment.objects.create(
            user=self.establishment_user,
            name='Hotel Paradise',
            type='HOTEL',
            address='123 Beach Street',
            description='Un bel hôtel en bord de mer',
            main_photo='',
        )
        
        # Créer un token d'authentification
        self.token = Token.objects.create(user=self.establishment_user)
        
        # Configurer le client API
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

    def test_dashboard_access(self):
        """Test d'accès au tableau de bord"""
        response = self.client.get('/api/establishments/dashboard/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertIn('establishment_name', response.data['data'])
        self.assertEqual(response.data['data']['establishment_name'], 'Hotel Paradise')

    def test_dashboard_unauthorized(self):
        """Test d'accès non autorisé au tableau de bord"""
        # Créer un utilisateur client
        client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        client_token = Token.objects.create(user=client_user)
        
        # Essayer d'accéder avec un token client
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + client_token.key)
        response = self.client.get('/api/establishments/dashboard/')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class EstablishmentBoatmanRegistrationTestCase(APITestCase):
    """Tests pour l'enregistrement des bateliers"""
    
    def setUp(self):
        """Configuration initiale"""
        # Créer un utilisateur établissement
        self.establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Hotel',
            last_name='Paradise'
        )
        
        self.establishment = Establishment.objects.create(
            user=self.establishment_user,
            name='Hotel Paradise',
            type='HOTEL',
            address='123 Beach Street',
            description='Un bel hôtel',
            main_photo='',
        )
        
        self.token = Token.objects.create(user=self.establishment_user)
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

    @patch('establishments.views_boatman.send_mail')
    def test_register_boatman_success(self, mock_send_mail):
        """Test d'enregistrement réussi d'un batelier"""
        mock_send_mail.return_value = True
        
        data = {
            'email': '<EMAIL>',
            'first_name': 'Jean',
            'last_name': 'Dupont',
            'phone_number': '+33123456789',
            'experience': '5 ans d\'expérience',
            'license_number': 'LIC123'
        }
        
        response = self.client.post('/api/establishments/register-boatman/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('captain_id', response.data['data'])
        self.assertTrue(response.data['data']['email_sent'])
        
        # Vérifier que l'utilisateur a été créé
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
        
        # Vérifier que le capitaine a été créé
        captain = Captain.objects.get(user__email='<EMAIL>')
        self.assertEqual(captain.experience, '5 ans d\'expérience')
        
        # Vérifier que l'email a été envoyé
        mock_send_mail.assert_called_once()

    def test_register_boatman_duplicate_email(self):
        """Test d'enregistrement avec email déjà existant"""
        # Créer un utilisateur avec cet email
        User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        data = {
            'email': '<EMAIL>',
            'first_name': 'Jean',
            'last_name': 'Dupont',
            'phone_number': '+33123456789'
        }
        
        response = self.client.post('/api/establishments/register-boatman/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email existe déjà', response.data['error'])

    def test_register_boatman_missing_fields(self):
        """Test d'enregistrement avec champs manquants"""
        data = {
            'email': '<EMAIL>',
            'first_name': 'Jean'
            # Champs manquants : last_name, phone_number
        }
        
        response = self.client.post('/api/establishments/register-boatman/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('requis', response.data['error'])


class EstablishmentShuttleManagementTestCase(APITestCase):
    """Tests pour la gestion des navettes"""
    
    def setUp(self):
        """Configuration initiale"""
        # Créer un établissement
        self.establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.establishment = Establishment.objects.create(
            user=self.establishment_user,
            name='Hotel Paradise',
            type='HOTEL',
            address='123 Beach Street',
            description='Un bel hôtel',
            main_photo='',
        )
        
        # Créer un client
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.client_profile = Client.objects.create(user=self.client_user)
        
        # Créer un capitaine
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans',
            license_number='LIC001'
        )
        
        # Créer un bateau
        self.boat = Boat.objects.create(
            captain=self.captain,
            name='Test Boat',
            boat_type='classic',
            capacity=8
        )
        
        # Token d'authentification
        self.token = Token.objects.create(user=self.establishment_user)
        self.api_client = APIClient()
        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

    def test_shuttle_requests_list(self):
        """Test de récupération des demandes de navettes"""
        # Créer une demande de navette
        shuttle_request = ShuttleTripRequest.objects.create(
            client=self.client_profile,
            establishment=self.establishment,
            trip_type='SHUTTLE',
            departure_location={'city_name': 'Aéroport'},
            arrival_location={'city_name': 'Hotel Paradise'},
            passenger_count=2,
            departure_date='2024-06-15',
            departure_time='16:30:00',
            message='Vol Air France'
        )
        
        response = self.api_client.get('/api/establishments/shuttle-requests/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('requests', response.data['data'])
        self.assertEqual(len(response.data['data']['requests']), 1)

    def test_shuttle_accept(self):
        """Test d'acceptation d'une demande de navette"""
        # Créer une demande de navette
        shuttle_request = ShuttleTripRequest.objects.create(
            client=self.client_profile,
            establishment=self.establishment,
            trip_type='SHUTTLE',
            departure_location={'city_name': 'Aéroport'},
            arrival_location={'city_name': 'Hotel Paradise'},
            passenger_count=2,
            departure_date='2024-06-15',
            departure_time='16:30:00'
        )
        
        data = {
            'captain_id': self.captain.user.id,  # Captain utilise user comme clé primaire
            'boat_id': self.boat.id
        }
        
        response = self.api_client.post(
            f'/api/establishments/shuttle-requests/{shuttle_request.id}/accept/',
            data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('trip_id', response.data)
        
        # Vérifier que la demande a été mise à jour
        shuttle_request.refresh_from_db()
        self.assertEqual(shuttle_request.status, 'ACCEPTED')


class EstablishmentWalletTestCase(APITestCase):
    """Tests pour le portefeuille de l'établissement"""
    
    def setUp(self):
        """Configuration initiale"""
        self.establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.establishment = Establishment.objects.create(
            user=self.establishment_user,
            name='Hotel Paradise',
            type='HOTEL',
            address='123 Beach Street',
            description='Un bel hôtel',
            main_photo='',
        )
        
        self.token = Token.objects.create(user=self.establishment_user)
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

    def test_wallet_view(self):
        """Test de consultation du portefeuille"""
        response = self.client.get('/api/establishments/wallet/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('balance', response.data['data'])
        self.assertIn('currency', response.data['data'])

    def test_add_funds(self):
        """Test d'ajout de fonds"""
        data = {
            'amount': 100.00,
            'payment_method': 'CARD'
        }
        
        response = self.client.post('/api/establishments/wallet/add-funds/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('new_balance', response.data['data'])

    def test_add_funds_invalid_amount(self):
        """Test d'ajout de fonds avec montant invalide"""
        data = {
            'amount': -50.00,
            'payment_method': 'CARD'
        }
        
        response = self.client.post('/api/establishments/wallet/add-funds/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('positif', response.data['error'])
