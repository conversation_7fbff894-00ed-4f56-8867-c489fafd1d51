from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from accounts.models import User
from trips.models import Trip

class Review(models.Model):
    class ReviewType(models.TextChoices):
        TRIP = 'TRIP', _('Course')
        CAPTAIN = 'CAPTAIN', _('Capitaine')
        CLIENT = 'CLIENT', _('Client')
        BOAT = 'BOAT', _('Bateau')
        ESTABLISHMENT = 'ESTABLISHMENT', _('Établissement')

    # Informations de base
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='written_reviews')
    trip = models.ForeignKey(Trip, on_delete=models.CASCADE, related_name='reviews', null=True, blank=True)
    type = models.CharField(_('type'), max_length=20, choices=ReviewType.choices)

    # Lien générique vers l'objet évalué
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    reviewed_object = GenericForeignKey('content_type', 'object_id')

    # Contenu de l'avis
    rating = models.IntegerField(_('note'), validators=[MinValueValidator(1), MaxValueValidator(5)])
    title = models.CharField(_('titre'), max_length=255)
    comment = models.TextField(_('commentaire'))
    pros = models.TextField(_('points positifs'), blank=True)
    cons = models.TextField(_('points négatifs'), blank=True)

    # Critères spécifiques
    cleanliness_rating = models.IntegerField(_('note de propreté'), validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True)
    communication_rating = models.IntegerField(_('note de communication'), validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True)
    punctuality_rating = models.IntegerField(_('note de ponctualité'), validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True)
    value_rating = models.IntegerField(_('note qualité-prix'), validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True)

    # Métadonnées
    is_verified = models.BooleanField(_('vérifié'), default=False)
    is_public = models.BooleanField(_('public'), default=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    reported_count = models.PositiveIntegerField(_('nombre de signalements'), default=0)

    class Meta:
        verbose_name = _('avis')
        verbose_name_plural = _('avis')
        ordering = ['-created_at']
        unique_together = ['author', 'content_type', 'object_id', 'trip']

    def __str__(self):
        return f'Avis de {self.author.email} - {self.rating}/5 étoiles'

    def calculate_average_rating(self):
        ratings = [self.rating]
        if self.cleanliness_rating:
            ratings.append(self.cleanliness_rating)
        if self.communication_rating:
            ratings.append(self.communication_rating)
        if self.punctuality_rating:
            ratings.append(self.punctuality_rating)
        if self.value_rating:
            ratings.append(self.value_rating)
        return sum(ratings) / len(ratings)

class ReviewResponse(models.Model):
    review = models.ForeignKey(Review, on_delete=models.CASCADE, related_name='responses')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='review_responses')
    content = models.TextField(_('contenu'))
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)

    class Meta:
        verbose_name = _('réponse à un avis')
        verbose_name_plural = _('réponses aux avis')
        ordering = ['created_at']

    def __str__(self):
        return f'Réponse de {self.author.email} à l\'avis {self.review.id}'

class ReviewReport(models.Model):
    class ReportReason(models.TextChoices):
        INAPPROPRIATE = 'INAPPROPRIATE', _('Contenu inapproprié')
        SPAM = 'SPAM', _('Spam')
        FAKE = 'FAKE', _('Avis frauduleux')
        OFFENSIVE = 'OFFENSIVE', _('Contenu offensant')
        OTHER = 'OTHER', _('Autre')

    review = models.ForeignKey(Review, on_delete=models.CASCADE, related_name='reports')
    reporter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='review_reports')
    reason = models.CharField(_('raison'), max_length=20, choices=ReportReason.choices)
    description = models.TextField(_('description'))
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    status = models.CharField(_('statut'), max_length=20, default='PENDING')
    resolved_at = models.DateTimeField(_('résolu le'), null=True, blank=True)
    resolution_notes = models.TextField(_('notes de résolution'), blank=True)

    class Meta:
        verbose_name = _('signalement d\'avis')
        verbose_name_plural = _('signalements d\'avis')
        ordering = ['-created_at']
        unique_together = ['review', 'reporter']

    def __str__(self):
        return f'Signalement de {self.reporter.email} - Avis {self.review.id}'
