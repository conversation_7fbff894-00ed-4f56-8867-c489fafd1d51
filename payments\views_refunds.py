from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.conf import settings
import stripe
from .models import Payment
from .serializers import PaymentSerializer
from .stripe_utils import create_refund

# Configure Stripe API key
stripe.api_key = settings.STRIPE_SECRET_KEY

class RefundViewSet(viewsets.ViewSet):
    """ViewSet pour gérer les remboursements Stripe"""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['post'])
    def refund_payment(self, request):
        """Rembourse un paiement"""
        payment_id = request.data.get('payment_id')
        amount = request.data.get('amount')  # Optionnel, pour remboursement partiel
        reason = request.data.get('reason')  # Optionnel, raison du remboursement

        if not payment_id:
            return Response({'error': 'payment_id est requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            payment = Payment.objects.get(id=payment_id)

            if not payment.stripe_payment_id:
                return Response({'error': 'ce paiement n\'a pas d\'ID Stripe associé'}, status=status.HTTP_400_BAD_REQUEST)

            if payment.status == 'REFUNDED':
                return Response({'error': 'ce paiement a déjà été remboursé'}, status=status.HTTP_400_BAD_REQUEST)

            # Préparer les métadonnées
            metadata = {
                'payment_id': str(payment.id),
                'payment_type': payment.type,
            }

            if payment.booking:
                metadata['booking_id'] = str(payment.booking.id)

            # Convertir le montant en centimes si fourni
            amount_cents = None
            if amount:
                amount_cents = int(float(amount) * 100)

            # Créer le remboursement
            refund = create_refund(
                payment_intent_id=payment.stripe_payment_id,
                amount=amount_cents,
                reason=reason,
                metadata=metadata
            )

            if 'error' in refund:
                return Response({'error': refund['error']}, status=status.HTTP_400_BAD_REQUEST)

            # Mettre à jour le paiement
            if amount_cents:
                payment.status = 'PARTIALLY_REFUNDED'
                payment.refund_amount = float(amount)
            else:
                payment.status = 'REFUNDED'
                payment.refund_amount = payment.amount

            payment.refund_id = refund.id
            payment.refund_reason = reason
            payment.save()

            # Envoyer une notification de remboursement initié
            try:
                from notifications.services import create_refund_initiated_notification
                create_refund_initiated_notification(payment)
                logger.info(f"Notification de remboursement initié envoyée pour le paiement {payment.id}")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la notification de remboursement initié: {str(e)}")

            return Response({
                'status': 'remboursement effectué',
                'refund_id': refund.id,
                'payment_id': payment.id,
                'amount': amount or payment.amount
            })
        except Payment.DoesNotExist:
            return Response({'error': 'paiement non trouvé'}, status=status.HTTP_404_NOT_FOUND)
        except ValueError:
            return Response({'error': 'montant invalide'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def get_refund(self, request):
        """Récupère les détails d'un remboursement"""
        refund_id = request.query_params.get('refund_id')

        if not refund_id:
            return Response({'error': 'refund_id est requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Récupérer le remboursement depuis Stripe
            refund = stripe.Refund.retrieve(refund_id)

            # Formater la réponse
            response = {
                'id': refund.id,
                'amount': refund.amount / 100,  # Convertir en euros
                'currency': refund.currency,
                'status': refund.status,
                'created': refund.created,
                'reason': refund.reason,
                'payment_intent': refund.payment_intent,
            }

            return Response(response)
        except stripe.error.StripeError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def list_refunds(self, request):
        """Liste les remboursements pour un paiement"""
        payment_id = request.query_params.get('payment_id')

        if not payment_id:
            return Response({'error': 'payment_id est requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            payment = Payment.objects.get(id=payment_id)

            if not payment.stripe_payment_id:
                return Response({'error': 'ce paiement n\'a pas d\'ID Stripe associé'}, status=status.HTTP_400_BAD_REQUEST)

            # Récupérer les remboursements depuis Stripe
            refunds = stripe.Refund.list(payment_intent=payment.stripe_payment_id)

            # Formater la réponse
            response = []
            for refund in refunds.data:
                response.append({
                    'id': refund.id,
                    'amount': refund.amount / 100,  # Convertir en euros
                    'currency': refund.currency,
                    'status': refund.status,
                    'created': refund.created,
                    'reason': refund.reason,
                })

            return Response(response)
        except Payment.DoesNotExist:
            return Response({'error': 'paiement non trouvé'}, status=status.HTTP_404_NOT_FOUND)
        except stripe.error.StripeError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
