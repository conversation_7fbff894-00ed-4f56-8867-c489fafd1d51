# Generated by Django 4.2.8 on 2025-05-31 05:45

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("boats", "0001_initial"),
        ("accounts", "0001_initial"),
        ("trips", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="TripRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "trip_type",
                    models.CharField(
                        choices=[
                            ("SIMPLE", "Course simple"),
                            ("HOURLY", "Mise à disposition"),
                            ("SHUTTLE", "Navette gratuite"),
                        ],
                        max_length=20,
                        verbose_name="type de course",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "En attente"),
                            ("ACCEPTED", "Acceptée"),
                            ("REJECTED", "Refusée"),
                            ("IN_PROGRESS", "En cours"),
                            ("COMPLETED", "Terminée"),
                            ("CANCELLED", "Annulée"),
                            ("EXPIRED", "Expirée"),
                        ],
                        default="PENDING",
                        max_length=20,
                        verbose_name="statut",
                    ),
                ),
                (
                    "departure_location",
                    models.JSONField(
                        help_text="Structure complète avec coordonnées",
                        verbose_name="lieu de départ",
                    ),
                ),
                (
                    "arrival_location",
                    models.JSONField(
                        help_text="Structure complète avec coordonnées",
                        verbose_name="lieu d'arrivée",
                    ),
                ),
                (
                    "passenger_count",
                    models.IntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="nombre de passagers",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="expire le"
                    ),
                ),
                (
                    "distance_km",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=8,
                        null=True,
                        verbose_name="distance (km)",
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trip_requests",
                        to="accounts.client",
                    ),
                ),
            ],
            options={
                "verbose_name": "demande de course",
                "verbose_name_plural": "demandes de courses",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="HourlyTripRequest",
            fields=[
                (
                    "triprequest_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="trips.triprequest",
                    ),
                ),
                (
                    "boat_type",
                    models.CharField(
                        choices=[
                            ("BLUE", "Blue"),
                            ("CLASSIC", "Classic"),
                            ("LUXE", "Luxe"),
                            ("BOAT_XL", "Boat XL"),
                            ("NAVETTE", "Navette"),
                        ],
                        max_length=20,
                        verbose_name="type de bateau",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="date de début")),
                (
                    "duration_hours",
                    models.IntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="durée en heures",
                    ),
                ),
            ],
            options={
                "verbose_name": "mise à disposition",
                "verbose_name_plural": "mises à disposition",
            },
            bases=("trips.triprequest",),
        ),
        migrations.CreateModel(
            name="SimpleTripRequest",
            fields=[
                (
                    "triprequest_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="trips.triprequest",
                    ),
                ),
                (
                    "boat_type",
                    models.CharField(
                        choices=[
                            ("BLUE", "Blue"),
                            ("CLASSIC", "Classic"),
                            ("LUXE", "Luxe"),
                            ("BOAT_XL", "Boat XL"),
                            ("NAVETTE", "Navette"),
                        ],
                        max_length=20,
                        verbose_name="type de bateau",
                    ),
                ),
                (
                    "scheduled_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="date programmée"
                    ),
                ),
                (
                    "scheduled_time",
                    models.TimeField(
                        blank=True, null=True, verbose_name="heure programmée"
                    ),
                ),
            ],
            options={
                "verbose_name": "course simple",
                "verbose_name_plural": "courses simples",
            },
            bases=("trips.triprequest",),
        ),
        migrations.CreateModel(
            name="TripQuote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="prix de base"
                    ),
                ),
                (
                    "distance_km",
                    models.DecimalField(
                        decimal_places=2, max_digits=8, verbose_name="distance (km)"
                    ),
                ),
                (
                    "rate_used",
                    models.DecimalField(
                        decimal_places=2, max_digits=7, verbose_name="tarif utilisé"
                    ),
                ),
                (
                    "captain_name",
                    models.CharField(max_length=200, verbose_name="nom du capitaine"),
                ),
                (
                    "captain_rating",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=3,
                        verbose_name="note du capitaine",
                    ),
                ),
                (
                    "boat_name",
                    models.CharField(max_length=100, verbose_name="nom du bateau"),
                ),
                (
                    "boat_capacity",
                    models.IntegerField(verbose_name="capacité du bateau"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "is_available",
                    models.BooleanField(default=True, verbose_name="disponible"),
                ),
                (
                    "boat",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trip_quotes",
                        to="boats.boat",
                    ),
                ),
                (
                    "captain",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trip_quotes",
                        to="accounts.captain",
                    ),
                ),
                (
                    "trip_request",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quotes",
                        to="trips.triprequest",
                    ),
                ),
            ],
            options={
                "verbose_name": "devis de course",
                "verbose_name_plural": "devis de courses",
                "ordering": ["base_price"],
                "unique_together": {("trip_request", "captain")},
            },
        ),
        migrations.CreateModel(
            name="ShuttleTripRequest",
            fields=[
                (
                    "triprequest_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="trips.triprequest",
                    ),
                ),
                ("departure_date", models.DateField(verbose_name="date de départ")),
                ("departure_time", models.TimeField(verbose_name="heure de départ")),
                ("message", models.TextField(blank=True, verbose_name="message")),
                (
                    "establishment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shuttle_requests",
                        to="accounts.establishment",
                    ),
                ),
            ],
            options={
                "verbose_name": "navette gratuite",
                "verbose_name_plural": "navettes gratuites",
            },
            bases=("trips.triprequest",),
        ),
    ]
