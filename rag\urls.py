from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import DocumentViewSet, ChatSessionViewSet, ChatMessageViewSet, chat_view, chat_api
from .views_feedback import submit_feedback
from .views_offline import OfflineDataAPIView, OfflineFAQsAPIView

# Créer un routeur pour les viewsets
router = DefaultRouter()
router.register(r'documents', DocumentViewSet, basename='document')
router.register(r'sessions', ChatSessionViewSet, basename='chat-session')
router.register(r'messages', ChatMessageViewSet, basename='chat-message')

# Définir les URLs de l'application
urlpatterns = [
    path('', include(router.urls)),
    path('chat/', chat_view, name='rag-chat-view'),
    path('chat/api/', chat_api, name='rag-chat-api'),
    path('feedback/', submit_feedback, name='rag-feedback'),
    path('offline/data/', OfflineDataAPIView.as_view(), name='rag-offline-data'),
    path('offline/faqs/', OfflineFAQsAPIView.as_view(), name='rag-offline-faqs'),
]
