#!/usr/bin/env python
"""
Script de test pour vérifier l'ajout des champs longitude et latitude
"""

import os
import sys
import django
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, User
from django.contrib.auth import get_user_model

def test_establishment_coordinates():
    print("🧪 Test des coordonnées d'établissement")
    print("=" * 50)
    
    try:
        # 1. Vérifier que les champs existent dans le modèle
        print("1. Vérification des champs du modèle...")
        establishment_fields = [field.name for field in Establishment._meta.get_fields()]
        
        if 'longitude' in establishment_fields:
            print("   ✅ Champ 'longitude' trouvé")
        else:
            print("   ❌ Champ 'longitude' manquant")
            return False
            
        if 'latitude' in establishment_fields:
            print("   ✅ Champ 'latitude' trouvé")
        else:
            print("   ❌ Champ 'latitude' manquant")
            return False
        
        # 2. Tester la création d'un établissement avec coordonnées
        print("2. Test de création avec coordonnées...")
        
        # Trouver un utilisateur établissement existant ou en créer un
        establishment = Establishment.objects.first()
        if not establishment:
            print("   ⚠️  Aucun établissement trouvé pour le test")
            return True
        
        # 3. Tester la mise à jour des coordonnées
        print("3. Test de mise à jour des coordonnées...")
        
        # Coordonnées de Cannes (exemple)
        test_longitude = 7.0167
        test_latitude = 43.5528
        
        establishment.longitude = test_longitude
        establishment.latitude = test_latitude
        establishment.save()
        
        print(f"   ✅ Coordonnées sauvegardées: {establishment.longitude}, {establishment.latitude}")
        
        # 4. Vérifier la récupération
        print("4. Vérification de la récupération...")
        establishment.refresh_from_db()
        
        if establishment.longitude == test_longitude and establishment.latitude == test_latitude:
            print("   ✅ Coordonnées récupérées correctement")
        else:
            print("   ❌ Erreur lors de la récupération des coordonnées")
            return False
        
        # 5. Test avec des valeurs nulles
        print("5. Test avec valeurs nulles...")
        establishment.longitude = None
        establishment.latitude = None
        establishment.save()
        
        establishment.refresh_from_db()
        if establishment.longitude is None and establishment.latitude is None:
            print("   ✅ Valeurs nulles acceptées")
        else:
            print("   ❌ Problème avec les valeurs nulles")
            return False
        
        print("\n🎉 Tous les tests sont passés avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_establishment_info():
    """Afficher les informations des établissements existants"""
    print("\n📊 Informations des établissements:")
    print("-" * 40)
    
    establishments = Establishment.objects.all()[:3]  # Limiter à 3 pour l'affichage
    
    for est in establishments:
        print(f"ID: {est.user.id}")
        print(f"Nom: {est.name}")
        print(f"Type: {est.type}")
        print(f"Adresse: {est.address}")
        print(f"Longitude: {est.longitude}")
        print(f"Latitude: {est.latitude}")
        print(f"Location coordinates (ancien): {est.location_coordinates}")
        print("-" * 40)

if __name__ == "__main__":
    success = test_establishment_coordinates()
    show_establishment_info()
    sys.exit(0 if success else 1)
