#!/bin/bash
# Exemples de commandes curl pour tester les endpoints de paiement
# Ces commandes peuvent être exécutées individuellement dans une console

# Variables de configuration
API_URL="http://localhost:8000/api"
TOKEN="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzE2OTEwNDk2LCJpYXQiOjE3MTY4MjQwOTYsImp0aSI6ImQ5ZjgzZjE5YTk1YzQ1MzliZjkxZWEwZDNmNzIxMTFhIiwidXNlcl9pZCI6MX0.DEMO_TOKEN_SIMULATED"  # Token simulé pour les tests
PAYMENT_METHOD_ID="pm_card_visa"  # ID de méthode de paiement prédéfini de Stripe pour les tests

# 1. Paiement d'une course individuelle
echo "=== Test: Paiement d'une course individuelle ==="
curl -X POST "$API_URL/payments/rides/1/pay/" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_method_id": "'$PAYMENT_METHOD_ID'",
    "use_wallet": false
  }'
echo -e "\n\n"

# 2. Paiement d'une réservation de navette
echo "=== Test: Paiement d'une réservation de navette ==="
curl -X POST "$API_URL/payments/shuttles/5/pay/" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_method_id": "'$PAYMENT_METHOD_ID'",
    "seats": 2,
    "passenger_ids": ["p_1", "p_2"],
    "passenger_names": ["John Doe", "Jane Doe"],
    "special_requests": "Besoin d'\''assistance pour les bagages"
  }'
echo -e "\n\n"

# 3. Paiement d'un service de maintenance
echo "=== Test: Paiement d'un service de maintenance ==="
curl -X POST "$API_URL/payments/maintenance/3/pay/" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_method_id": "'$PAYMENT_METHOD_ID'"
  }'
echo -e "\n\n"

# 4. Paiement pour une promotion
echo "=== Test: Paiement d'une promotion ==="
curl -X POST "$API_URL/payments/promotions/" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_method_id": "'$PAYMENT_METHOD_ID'",
    "promotion_type": "FEATURED_LISTING",
    "duration_days": 30,
    "target_type": "CAPTAIN",
    "target_id": 5
  }'
echo -e "\n\n"

# 5. Paiement partagé
echo "=== Test: Paiement partagé d'une course ==="
curl -X POST "$API_URL/payments/rides/1/shared_pay/" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_method_id": "'$PAYMENT_METHOD_ID'",
    "invitation_token": "inv_abcdefg"
  }'
echo -e "\n\n"

# 6. Ajout de crédits au portefeuille
echo "=== Test: Ajout de crédits au portefeuille ==="
curl -X POST "$API_URL/payments/wallet/add_credits/" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 50.0,
    "payment_method_id": "'$PAYMENT_METHOD_ID'"
  }'
echo -e "\n\n"

# 7. Remboursement d'une transaction
echo "=== Test: Remboursement d'une transaction ==="
# Remplacez TRANSACTION_ID par un ID de transaction valide
TRANSACTION_ID="pi_1234567890"
curl -X POST "$API_URL/payments/transactions/$TRANSACTION_ID/refund/" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 25.0,
    "reason": "Test de remboursement"
  }'
echo -e "\n\n"

# 8. Consulter le solde du portefeuille
echo "=== Test: Consulter le solde du portefeuille ==="
curl -X GET "$API_URL/payments/wallet/" \
  -H "Authorization: Bearer $TOKEN"
echo -e "\n\n"

# 9. Consulter l'historique des transactions
echo "=== Test: Consulter l'historique des transactions ==="
curl -X GET "$API_URL/payments/transactions/" \
  -H "Authorization: Bearer $TOKEN"
echo -e "\n\n"

# 10. Webhook test avec cURL
echo "=== Test: Simuler un webhook Stripe ==="
curl -X POST "$API_URL/payments/webhooks/stripe/" \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: t=1624027147,v1=5257a869e7ecebeda32affa62cdca3fa51cad7e77a0e56ff536d0ce8e108d8bd" \
  -d '{
    "id": "evt_test_webhook",
    "object": "event",
    "api_version": "2020-08-27",
    "created": 1624027147,
    "data": {
      "object": {
        "id": "pi_test_123456789",
        "object": "payment_intent",
        "amount": 5000,
        "currency": "eur",
        "status": "succeeded",
        "payment_method": "pm_card_visa"
      }
    },
    "type": "payment_intent.succeeded"
  }'
echo -e "\n\n"
