#!/usr/bin/env python
"""
Test de la nouvelle réponse API avec coordonnées
"""

import os
import django
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, Client
from trips.serializers import ShuttleTripRequestSerializer
from django.utils import timezone
from datetime import date, timedelta

def test_new_api_response():
    print("🎯 Test de la nouvelle réponse API")
    print("=" * 50)
    
    # 1. Payload utilisateur simplifié (SANS arrival_location)
    user_payload = {
        "departure_location": {
            "city_name": "Port de Cannes",
            "coordinates": {
                "latitude": 43.5528,
                "longitude": 7.0174
            },
            "timestamp": timezone.now().isoformat()
        },
        "passenger_count": 2,
        "departure_date": (date.today() + timedelta(days=1)).strftime('%Y-%m-%d'),
        "departure_time": "19:30:00",
        "message": "Test nouvelle API"
    }
    
    # 2. Trouver établissement et client
    establishment = Establishment.objects.filter(
        longitude__isnull=False, 
        latitude__isnull=False
    ).first()
    
    client = Client.objects.first()
    
    if not establishment or not client:
        print("❌ Données manquantes")
        return False
    
    print(f"✅ Client: {client.user.email}")
    print(f"✅ Établissement: {establishment.name}")
    print(f"   Coordonnées: {establishment.latitude}, {establishment.longitude}")
    
    # 3. Créer avec le sérialiseur
    serializer = ShuttleTripRequestSerializer(data=user_payload)
    if serializer.is_valid():
        shuttle_request = serializer.save(client=client, establishment=establishment)
        print(f"✅ Navette créée avec ID: {shuttle_request.id}")
        
        # 4. Calculer distance
        distance = shuttle_request.calculate_distance()
        print(f"✅ Distance calculée: {distance} km")
        
        # 5. Simuler la réponse API complète
        departure_coords = user_payload['departure_location']['coordinates']
        establishment_coords = {
            'latitude': float(establishment.latitude),
            'longitude': float(establishment.longitude)
        }
        
        api_response = {
            'trip_request': ShuttleTripRequestSerializer(shuttle_request).data,
            'message': 'Demande de navette créée. L\'établissement sera notifié.',
            'distance_to_establishment': f"{distance} km",
            'coordinates': {
                'departure': departure_coords,
                'destination': establishment_coords
            },
            'route_info': {
                'departure_location': user_payload['departure_location']['city_name'],
                'destination_location': establishment.name,
                'distance_km': float(distance) if distance else 0
            }
        }
        
        print("\n📡 Réponse API pour le front-end:")
        print("=" * 40)
        print(f"✅ Message: {api_response['message']}")
        print(f"✅ Distance: {api_response['distance_to_establishment']}")
        print(f"✅ Coordonnées départ: {api_response['coordinates']['departure']}")
        print(f"✅ Coordonnées destination: {api_response['coordinates']['destination']}")
        print(f"✅ Route info: {api_response['route_info']}")
        
        return True
    else:
        print(f"❌ Erreurs: {serializer.errors}")
        return False

if __name__ == "__main__":
    success = test_new_api_response()
    print(f"\n{'🎉 Test réussi!' if success else '❌ Test échoué'}")
    
    if success:
        print("\n✅ RÉSUMÉ:")
        print("- Le client n'envoie plus arrival_location")
        print("- Le système utilise automatiquement les coordonnées de l'établissement")
        print("- La réponse inclut toutes les coordonnées pour le front-end")
        print("- Le calcul de distance est précis et automatique")
