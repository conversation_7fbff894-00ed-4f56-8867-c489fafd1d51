import random
from faker import Faker
from supabase import create_client, Client
from datetime import datetime, timedelta
import uuid

# Initialiser Faker et Supabase
fake = Faker()
SUPABASE_URL = "https://wmbfixptkubkfaqyfaum.supabase.co"  # Remplace par ton URL Supabase
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndtYmZpeHB0a3Via2ZhcXlmYXVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NDI1NzIsImV4cCI6MjA2NDExODU3Mn0.m-5lND8AE1Dg7G5c3C8oDNv8phdPPoIRh3-xd-hXClw"  # Remplace par ta clé API Supabase
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

# Configuration
NUM_RECORDS_PER_MODEL = 10

# Mapper les types de champs aux méthodes Faker
FIELD_MAPPINGS = {
    "string": lambda max_length: fake.text(max_nb_chars=max_length or 50)[:max_length or 50],
    "string.email": lambda _: fake.email(),
    "string.uri": lambda _: fake.url(),
    "string.date-time": lambda _: fake.date_time_this_year().isoformat(),
    "integer": lambda _: random.randint(1, 100),
    "boolean": lambda _: fake.boolean(),
    "string.decimal": lambda _: str(round(random.uniform(0, 1000), 2)),
    "enum": lambda choices: random.choice(choices)
}

def generate_data_for_model(table_name, schema, num_records, dependencies=None):
    """Génère et insère des données fictives pour une table donnée."""
    data = []
    for _ in range(num_records):
        record = {}
        for field_name, field_info in schema["properties"].items():
            if field_info.get("readOnly") or field_name in ["id", "created_at", "updated_at"]:
                continue
            field_type = field_info.get("type", "string")
            if "enum" in field_info:
                record[field_name] = FIELD_MAPPINGS["enum"](field_info["enum"])
            elif field_type == "string" and field_info.get("format") == "email":
                record[field_name] = FIELD_MAPPINGS["string.email"](None)
            elif field_type == "string" and field_info.get("format") == "uri":
                record[field_name] = FIELD_MAPPINGS["string.uri"](None)
            elif field_type == "string" and field_info.get("format") == "date-time":
                record[field_name] = FIELD_MAPPINGS["string.date-time"](None)
            elif field_type == "string" and field_info.get("pattern", "").startswith("^-?\\d{0,"):
                record[field_name] = FIELD_MAPPINGS["string.decimal"](None)
            elif field_type == "integer":
                record[field_name] = FIELD_MAPPINGS["integer"](None)
            elif field_type == "boolean":
                record[field_name] = FIELD_MAPPINGS["boolean"](None)
            elif field_type == "object":
                record[field_name] = {}  # JSON vide pour les champs comme metadata
            else:
                max_length = field_info.get("maxLength", 50)
                record[field_name] = FIELD_MAPPINGS["string"](max_length)
        data.append(record)
    
    # Insérer via Supabase
    response = supabase.table(table_name).insert(data).execute()
    return [record["id"] for record in response.data]

def populate_database():
    """Peuple toutes les tables dans l'ordre des dépendances."""
    # 1. User
    user_ids = generate_data_for_model(
        "users",
        {
            "properties": {
                "email": {"type": "string", "format": "email", "maxLength": 254},
                "first_name": {"type": "string", "maxLength": 150},
                "last_name": {"type": "string", "maxLength": 150},
                "phone_number": {"type": "string", "maxLength": 15},
                "type": {"type": "string", "enum": ["CLIENT", "CAPTAIN", "ESTABLISHMENT"]},
                "is_active": {"type": "boolean"}
            }
        },
        NUM_RECORDS_PER_MODEL * 3  # Créer assez d'utilisateurs pour chaque type
    )

    client_user_ids = user_ids[:NUM_RECORDS_PER_MODEL]
    captain_user_ids = user_ids[NUM_RECORDS_PER_MODEL:2*NUM_RECORDS_PER_MODEL]
    establishment_user_ids = user_ids[2*NUM_RECORDS_PER_MODEL:]

    # 2. Client
    client_ids = generate_data_for_model(
        "clients",
        {
            "properties": {
                "user": {"type": "integer"},
                "birth_date": {"type": "string", "format": "date-time"},
                "preferred_language": {"type": "string", "maxLength": 10},
                "emergency_contact_name": {"type": "string", "maxLength": 100},
                "emergency_contact_phone": {"type": "string", "maxLength": 15}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"user": client_user_ids}
    )

    # 3. Boat
    boat_ids = generate_data_for_model(
        "boats",
        {
            "properties": {
                "name": {"type": "string", "maxLength": 100},
                "registration_number": {"type": "string", "maxLength": 50},
                "boat_type": {"type": "string", "enum": ["BLUE", "CLASSIC", "LUXE", "BOAT_XL", "NAVETTE"]},
                "capacity": {"type": "integer"},
                "color": {"type": "string", "maxLength": 50},
                "fuel_type": {"type": "string", "enum": ["DIESEL", "GASOLINE", "ELECTRIC", "HYBRID"]},
                "fuel_consumption": {"type": "string", "pattern": "^-?\\d{0,3}(?:\\.\\d{0,2})?$"},
                "photos": {"type": "object"},
                "zone_served": {"type": "string", "maxLength": 255},
                "radius": {"type": "integer", "minimum": 1, "maximum": 100},
                "is_available": {"type": "boolean"}
            }
        },
        NUM_RECORDS_PER_MODEL
    )

    # 4. Captain
    captain_ids = generate_data_for_model(
        "captains",
        {
            "properties": {
                "user": {"type": "integer"},
                "license_number": {"type": "string", "maxLength": 50},
                "experience_years": {"type": "integer"},
                "boat": {"type": "integer"},
                "availability_status": {"type": "boolean"},
                "average_rating": {"type": "string", "pattern": "^-?\\d{0,1}(?:\\.\\d{0,2})?$"},
                "total_trips": {"type": "integer"},
                "preferred_language": {"type": "string", "maxLength": 10}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"user": captain_user_ids, "boat": boat_ids}
    )

    # 5. Establishment
    establishment_ids = generate_data_for_model(
        "establishments",
        {
            "properties": {
                "user": {"type": "integer"},
                "name": {"type": "string", "maxLength": 100},
                "type": {"type": "string", "enum": ["RESTAURANT", "HOTEL", "PRIVATE_BEACH"]},
                "address": {"type": "string", "maxLength": 255},
                "description": {"type": "string"},
                "main_photo": {"type": "string", "format": "uri"},
                "secondary_photos": {"type": "object"},
                "wallet_balance": {"type": "string", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"},
                "business_name": {"type": "string", "maxLength": 100},
                "business_type": {"type": "string", "maxLength": 50},
                "registration_number": {"type": "string", "maxLength": 50},
                "tax_id": {"type": "string", "maxLength": 50},
                "opening_hours": {"type": "object"},
                "services_offered": {"type": "object"},
                "average_rating": {"type": "string", "pattern": "^-?\\d{0,1}(?:\\.\\d{0,2})?$"},
                "location_coordinates": {"type": "string", "maxLength": 50},
                "website": {"type": "string", "format": "uri", "maxLength": 200},
                "social_media": {"type": "object"}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"user": establishment_user_ids}
    )

    # 6. Trip
    trip_ids = generate_data_for_model(
        "trips",
        {
            "properties": {
                "client": {"type": "integer"},
                "captain": {"type": "integer"},
                "boat": {"type": "integer"},
                "establishment": {"type": "integer"},
                "trip_type": {"type": "string", "enum": ["COURSE_SIMPLE", "MISE_A_DISPOSITION", "NAVETTES_GRATUITES"]},
                "start_location": {"type": "string", "maxLength": 255},
                "end_location": {"type": "string", "maxLength": 255},
                "scheduled_start_time": {"type": "string", "format": "date-time"},
                "scheduled_end_time": {"type": "string", "format": "date-time"},
                "passenger_count": {"type": "integer", "minimum": 1},
                "passenger_names": {"type": "object"},
                "special_requests": {"type": "string"},
                "base_price": {"type": "string", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"},
                "additional_charges": {"type": "string", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"},
                "tip": {"type": "string", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"},
                "payment_method": {"type": "string", "maxLength": 50},
                "cancellation_reason": {"type": "string"},
                "notes": {"type": "string"},
                "delay_minutes": {"type": "integer"},
                "problem_description": {"type": "string"},
                "captain_notes": {"type": "string"},
                "client_notes": {"type": "string"},
                "estimated_arrival_time": {"type": "string", "format": "date-time"}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"client": client_ids, "captain": captain_ids, "boat": boat_ids, "establishment": establishment_ids}
    )

    # 7. MaintenanceRecord
    generate_data_for_model(
        "maintenance_records",
        {
            "properties": {
                "boat": {"type": "integer"},
                "maintenance_type": {"type": "string", "enum": ["ROUTINE", "REPAIR", "INSPECTION", "EMERGENCY"]},
                "description": {"type": "string"},
                "cost": {"type": "string", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"},
                "performed_at": {"type": "string", "format": "date-time"},
                "performed_by": {"type": "string", "maxLength": 100},
                "notes": {"type": "string"}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"boat": boat_ids}
    )

    # 8. ChatRoom
    chat_room_ids = generate_data_for_model(
        "chat_rooms",
        {
            "properties": {
                "name": {"type": "string", "maxLength": 100},
                "type": {"type": "string", "enum": ["TRIP", "SUPPORT", "GROUP"]},
                "is_active": {"type": "boolean"}
            }
        },
        NUM_RECORDS_PER_MODEL
    )

    # 9. Message
    generate_data_for_model(
        "messages",
        {
            "properties": {
                "sender": {"type": "integer"},
                "room": {"type": "integer"},
                "type": {"type": "string", "enum": ["TEXT", "IMAGE", "LOCATION", "SYSTEM"]},
                "content": {"type": "string"},
                "attachment": {"type": "string", "format": "uri"},
                "metadata": {"type": "object"},
                "is_read": {"type": "boolean"}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"sender": user_ids, "room": chat_room_ids}
    )

    # 10. ChatbotSession
    chatbot_session_ids = generate_data_for_model(
        "chatbot_sessions",
        {
            "properties": {
                "context": {"type": "object"}
            }
        },
        NUM_RECORDS_PER_MODEL
    )

    # 11. ChatbotMessage
    generate_data_for_model(
        "chatbot_messages",
        {
            "properties": {
                "role": {"type": "string", "enum": ["USER", "ASSISTANT", "SYSTEM"]},
                "content": {"type": "string"},
                "session": {"type": "integer"}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"session": chatbot_session_ids}
    )

    # 12. Notification
    generate_data_for_model(
        "notifications",
        {
            "properties": {
                "user": {"type": "integer"},
                "type": {"type": "string", "enum": ["TRIP_REQUEST", "TRIP_ACCEPTED", "TRIP_REJECTED", "TRIP_STARTED", "TRIP_COMPLETED", "TRIP_CANCELLED", "PAYMENT_RECEIVED", "PAYMENT_FAILED", "REFUND_PROCESSED", "NEW_MESSAGE", "SYSTEM"]},
                "is_read": {"type": "boolean"}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"user": user_ids}
    )

    # 13. Review
    review_ids = generate_data_for_model(
        "reviews",
        {
            "properties": {
                "author": {"type": "integer"},
                "trip": {"type": "integer"},
                "type": {"type": "string", "enum": ["TRIP", "CAPTAIN", "CLIENT", "BOAT", "ESTABLISHMENT"]},
                "rating": {"type": "integer", "minimum": 1, "maximum": 5},
                "title": {"type": "string", "maxLength": 255},
                "comment": {"type": "string"},
                "pros": {"type": "string"},
                "cons": {"type": "string"},
                "cleanliness_rating": {"type": "integer", "minimum": 1, "maximum": 5},
                "communication_rating": {"type": "integer", "minimum": 1, "maximum": 5},
                "punctuality_rating": {"type": "integer", "minimum": 1, "maximum": 5},
                "value_rating": {"type": "integer", "minimum": 1, "maximum": 5},
                "is_public": {"type": "boolean"}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"author": user_ids, "trip": trip_ids}
    )

    # 14. ReviewResponse
    generate_data_for_model(
        "review_responses",
        {
            "properties": {
                "author": {"type": "integer"},
                "content": {"type": "string"},
                "review": {"type": "integer"}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"author": user_ids, "review": review_ids}
    )

    # 15. Device
    generate_data_for_model(
        "devices",
        {
            "properties": {
                "device_id": {"type": "string", "maxLength": 255},
                "device_type": {"type": "string", "enum": ["ANDROID", "IOS", "WEB"]},
                "name": {"type": "string", "maxLength": 255},
                "push_token": {"type": "string"},
                "is_active": {"type": "boolean"}
            }
        },
        NUM_RECORDS_PER_MODEL
    )

    # 16. Document
    document_ids = generate_data_for_model(
        "documents",
        {
            "properties": {
                "title": {"type": "string", "maxLength": 255},
                "content": {"type": "string"},
                "source": {"type": "string", "maxLength": 255},
                "url": {"type": "string", "format": "uri", "maxLength": 200},
                "category": {"type": "string", "maxLength": 100},
                "tags": {"type": "object"}
            }
        },
        NUM_RECORDS_PER_MODEL
    )

    # 17. DocumentChunk
    generate_data_for_model(
        "document_chunks",
        {
            "properties": {
                "document": {"type": "string", "format": "uuid"},
                "content": {"type": "string"},
                "chunk_index": {"type": "integer"}
            }
        },
        NUM_RECORDS_PER_MODEL,
        dependencies={"document": document_ids}
    )

if __name__ == "__main__":
    populate_database()