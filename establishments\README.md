# 🏢 ESPACE ÉTABLISSEMENT - APPLICATION DJANGO

## 📋 **DESCRIPTION**

L'application `establishments` fournit un espace complet pour la gestion des établissements partenaires (hôtels, restaurants, plages privées) sur la plateforme Commodore. Elle permet aux établissements de gérer leurs navettes gratuites, d'enregistrer des bateliers, et de gérer leurs finances.

## 🎯 **FONCTIONNALITÉS PRINCIPALES**

### **1. Tableau de bord**
- Vue d'ensemble des activités
- Statistiques des navettes
- Solde du portefeuille
- Demandes en attente

### **2. Gestion des navettes**
- Accepter/rejeter les demandes de navettes
- Assigner des capitaines et bateaux
- Suivi des navettes en cours
- Historique complet

### **3. Enregistrement des bateliers**
- Création automatique de comptes capitaines
- Génération de mots de passe temporaires
- Envoi d'emails automatiques
- Gestion des bateliers enregistrés

### **4. Gestion financière**
- Consultation du portefeuille
- Ajout de fonds
- Historique des paiements
- Statistiques financières

## 📁 **STRUCTURE DES FICHIERS**

```
establishments/
├── __init__.py
├── apps.py                 # Configuration de l'app
├── admin.py               # Interface d'administration
├── permissions.py         # Permissions personnalisées
├── signals.py            # Signaux Django
├── tests.py              # Tests unitaires
├── urls.py               # Configuration des URLs
├── serializers.py        # Sérialiseurs DRF
├── views.py              # Vues principales
├── views_shuttle.py      # Vues pour les navettes
├── views_boatman.py      # Vues pour les bateliers
├── views_payments.py     # Vues pour les paiements
├── endpoints.txt         # Documentation des endpoints
└── README.md            # Cette documentation
```

## 🔗 **ENDPOINTS DISPONIBLES**

### **Dashboard**
- `GET /api/establishments/dashboard/` - Tableau de bord

### **Navettes**
- `GET /api/establishments/shuttles/` - Liste des navettes
- `GET /api/establishments/shuttle-requests/` - Demandes en attente
- `POST /api/establishments/shuttle-requests/{id}/accept/` - Accepter
- `POST /api/establishments/shuttle-requests/{id}/reject/` - Rejeter

### **Bateliers**
- `POST /api/establishments/register-boatman/` - Enregistrer un batelier
- `GET /api/establishments/boatmen/` - Liste des bateliers
- `GET /api/establishments/boatmen/{id}/` - Détails d'un batelier

### **Portefeuille**
- `GET /api/establishments/wallet/` - Consulter le portefeuille
- `POST /api/establishments/wallet/add-funds/` - Ajouter des fonds
- `GET /api/establishments/payments/history/` - Historique
- `GET /api/establishments/payments/stats/` - Statistiques

## 🔐 **AUTHENTIFICATION ET PERMISSIONS**

### **Authentification requise**
Tous les endpoints nécessitent un token d'authentification :
```
Authorization: Token <user_token>
```

### **Permissions personnalisées**
- `IsEstablishment` : Vérifier que l'utilisateur est un établissement
- `IsEstablishmentOwner` : Vérifier la propriété
- `CanManageBoatmen` : Gérer les bateliers
- `CanManageShuttles` : Gérer les navettes

## 📧 **SYSTÈME D'EMAIL AUTOMATIQUE**

### **Enregistrement de batelier**
Lors de l'enregistrement d'un nouveau batelier :

1. **Génération automatique** d'un mot de passe temporaire (8 caractères)
2. **Envoi d'email** avec les identifiants de connexion
3. **Création automatique** d'un bateau par défaut
4. **Notification** à l'établissement

### **Template d'email**
```
Sujet: Bienvenue chez [Établissement] - Vos identifiants Commodore

Bonjour [Prénom] [Nom],

Vous avez été enregistré comme batelier pour [Établissement].

Identifiants :
- Email : [email]
- Mot de passe temporaire : [password]

Connexion : https://app.commodore.com/captain/login
```

## 🧪 **TESTS**

### **Exécuter les tests**
```bash
python manage.py test establishments
```

### **Couverture des tests**
- Tests du tableau de bord
- Tests d'enregistrement des bateliers
- Tests de gestion des navettes
- Tests du portefeuille
- Tests des permissions

## 🔧 **CONFIGURATION**

### **Settings requis**
```python
# Dans settings.py
INSTALLED_APPS = [
    # ...
    'establishments',
]

# Configuration email
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
DEFAULT_FROM_EMAIL = '<EMAIL>'
FRONTEND_URL = 'https://app.commodore.com'
```

### **URLs**
```python
# Dans commodore/urls.py
urlpatterns = [
    # ...
    path('api/establishments/', include('establishments.urls')),
]
```

## 📊 **MODÈLES UTILISÉS**

### **Modèles existants étendus**
- `accounts.Establishment` - Profil établissement
- `accounts.Captain` - Profil capitaine
- `boats.Boat` - Bateaux
- `trips.ShuttleTripRequest` - Demandes de navettes
- `payments.Wallet` - Portefeuille
- `payments.Payment` - Paiements

## 🚀 **DÉPLOIEMENT**

### **Migrations**
```bash
python manage.py makemigrations establishments
python manage.py migrate
```

### **Collecte des fichiers statiques**
```bash
python manage.py collectstatic
```

## 🐛 **DÉBOGAGE**

### **Logs**
Les logs sont configurés dans `settings.py` :
```python
LOGGING = {
    'loggers': {
        'establishments': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
        },
    },
}
```

### **Erreurs courantes**
1. **403 Forbidden** : Utilisateur non établissement
2. **404 Not Found** : Ressource inexistante
3. **400 Bad Request** : Données invalides
4. **500 Internal Server Error** : Erreur serveur

## 📞 **SUPPORT**

Pour toute question ou problème :
- Consulter la documentation dans `endpoints.txt`
- Vérifier les tests dans `tests.py`
- Examiner les logs de l'application

## 🔄 **INTÉGRATIONS**

### **Applications liées**
- `accounts` : Gestion des utilisateurs
- `boats` : Gestion des bateaux
- `trips` : Gestion des courses
- `payments` : Gestion des paiements
- `notifications` : Notifications automatiques

### **Services externes**
- **Stripe** : Paiements
- **SMTP** : Envoi d'emails
- **AWS S3** : Stockage des fichiers (production)

---

## ✅ **STATUT DE DÉVELOPPEMENT**

- ✅ **Tableau de bord** - Complet
- ✅ **Gestion navettes** - Complet
- ✅ **Enregistrement bateliers** - Complet
- ✅ **Gestion portefeuille** - Complet
- ✅ **Tests unitaires** - Complet
- ✅ **Documentation** - Complète
- ✅ **Permissions** - Complètes
- ✅ **Signaux** - Complets

**L'application est prête pour la production !** 🎉
