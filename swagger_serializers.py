"""
Sérialiseurs spécialement créés pour améliorer la documentation Swagger.
Ces sérialiseurs documentent les entrées et sorties de toutes les vues API.
"""

from rest_framework import serializers
from drf_spectacular.utils import extend_schema_field
from decimal import Decimal


# ============================================================================
# SÉRIALISEURS POUR LES PAIEMENTS
# ============================================================================

class WalletDetailResponseSerializer(serializers.Serializer):
    """Réponse pour les détails du portefeuille"""
    id = serializers.IntegerField(help_text="ID du portefeuille")
    user_id = serializers.IntegerField(help_text="ID de l'utilisateur")
    balance = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Solde actuel en euros")
    loyalty_points = serializers.IntegerField(help_text="Points de fidélité")
    total_earned = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Total gagné")
    total_spent = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Total dépensé")
    created_at = serializers.DateTimeField(help_text="Date de création")
    updated_at = serializers.DateTimeField(help_text="Dernière mise à jour")


class WalletRechargeRequestSerializer(serializers.Serializer):
    """Requête pour recharger le portefeuille"""
    amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        min_value=Decimal('1.00'),
        max_value=Decimal('1000.00'),
        help_text="Montant à ajouter (entre 1€ et 1000€)"
    )
    payment_method_id = serializers.CharField(
        max_length=255,
        help_text="ID de la méthode de paiement Stripe"
    )


class WalletRechargeResponseSerializer(serializers.Serializer):
    """Réponse pour la recharge du portefeuille"""
    status = serializers.CharField(help_text="Statut de l'opération")
    payment_id = serializers.CharField(help_text="ID du paiement")
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Montant rechargé")
    new_balance = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Nouveau solde")
    transaction_id = serializers.IntegerField(help_text="ID de la transaction")


class TipPaymentRequestSerializer(serializers.Serializer):
    """Requête pour payer un pourboire"""
    trip_id = serializers.IntegerField(help_text="ID de la course")
    amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        min_value=Decimal('0.50'),
        max_value=Decimal('100.00'),
        help_text="Montant du pourboire (entre 0.50€ et 100€)"
    )
    payment_method = serializers.ChoiceField(
        choices=['WALLET', 'CARD'],
        default='WALLET',
        help_text="Méthode de paiement"
    )


class TipPaymentResponseSerializer(serializers.Serializer):
    """Réponse pour le paiement de pourboire"""
    status = serializers.CharField(help_text="Statut du paiement")
    tip_id = serializers.IntegerField(help_text="ID du pourboire")
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Montant du pourboire")
    captain_credited = serializers.BooleanField(help_text="Capitaine crédité")
    transaction_id = serializers.IntegerField(help_text="ID de la transaction")


class CarbonOffsetRequestSerializer(serializers.Serializer):
    """Requête pour compensation carbone"""
    trip_id = serializers.IntegerField(help_text="ID de la course")
    payment_method = serializers.ChoiceField(
        choices=['WALLET', 'CARD'],
        default='WALLET',
        help_text="Méthode de paiement"
    )


class CarbonOffsetResponseSerializer(serializers.Serializer):
    """Réponse pour la compensation carbone"""
    status = serializers.CharField(help_text="Statut de l'opération")
    carbon_footprint_kg = serializers.DecimalField(max_digits=8, decimal_places=2, help_text="Empreinte carbone en kg CO2")
    compensation_amount = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Montant de compensation")
    payment_id = serializers.IntegerField(help_text="ID du paiement")
    transaction_id = serializers.IntegerField(help_text="ID de la transaction")


class RefundRequestSerializer(serializers.Serializer):
    """Requête pour remboursement"""
    payment_id = serializers.IntegerField(help_text="ID du paiement à rembourser")
    amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        required=False,
        help_text="Montant à rembourser (optionnel, remboursement total par défaut)"
    )
    reason = serializers.CharField(
        max_length=255,
        required=False,
        help_text="Raison du remboursement"
    )


class RefundResponseSerializer(serializers.Serializer):
    """Réponse pour le remboursement"""
    status = serializers.CharField(help_text="Statut du remboursement")
    refund_id = serializers.CharField(help_text="ID du remboursement Stripe")
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Montant remboursé")
    payment_id = serializers.IntegerField(help_text="ID du paiement original")


# ============================================================================
# SÉRIALISEURS POUR LES COURSES
# ============================================================================

class TripPaymentRequestSerializer(serializers.Serializer):
    """Requête pour payer une course"""
    trip_id = serializers.IntegerField(help_text="ID de la course")
    payment_method = serializers.ChoiceField(
        choices=['WALLET', 'CARD'],
        help_text="Méthode de paiement"
    )
    payment_method_id = serializers.CharField(
        max_length=255,
        required=False,
        help_text="ID de la méthode de paiement (requis pour CARD)"
    )


class TripPaymentResponseSerializer(serializers.Serializer):
    """Réponse pour le paiement de course"""
    status = serializers.CharField(help_text="Statut du paiement")
    payment_id = serializers.IntegerField(help_text="ID du paiement")
    trip_id = serializers.IntegerField(help_text="ID de la course")
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Montant payé")
    payment_method = serializers.CharField(help_text="Méthode de paiement utilisée")
    transaction_id = serializers.IntegerField(help_text="ID de la transaction")


class TripStatusUpdateRequestSerializer(serializers.Serializer):
    """Requête pour mettre à jour le statut d'une course"""
    status = serializers.ChoiceField(
        choices=[
            'PENDING', 'ACCEPTED', 'REJECTED', 'IN_PROGRESS', 
            'COMPLETED', 'CANCELLED', 'CANCELLED_BY_CLIENT', 
            'CANCELLED_BY_CAPTAIN', 'DELAYED', 'PROBLEM'
        ],
        help_text="Nouveau statut de la course"
    )
    reason = serializers.CharField(
        max_length=255,
        required=False,
        help_text="Raison du changement de statut"
    )
    notes = serializers.CharField(
        max_length=1000,
        required=False,
        help_text="Notes additionnelles"
    )


class TripStatusUpdateResponseSerializer(serializers.Serializer):
    """Réponse pour la mise à jour du statut"""
    status = serializers.CharField(help_text="Statut de l'opération")
    trip_id = serializers.IntegerField(help_text="ID de la course")
    old_status = serializers.CharField(help_text="Ancien statut")
    new_status = serializers.CharField(help_text="Nouveau statut")
    updated_at = serializers.DateTimeField(help_text="Date de mise à jour")


# ============================================================================
# SÉRIALISEURS POUR LES CAPITAINES
# ============================================================================

class CaptainDashboardResponseSerializer(serializers.Serializer):
    """Réponse pour le tableau de bord capitaine"""
    total_trips = serializers.IntegerField(help_text="Nombre total de courses")
    completed_trips = serializers.IntegerField(help_text="Courses terminées")
    pending_trips = serializers.IntegerField(help_text="Courses en attente")
    total_earnings = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Gains totaux")
    current_month_earnings = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Gains du mois")
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2, help_text="Note moyenne")
    wallet_balance = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Solde du portefeuille")
    is_available = serializers.BooleanField(help_text="Disponibilité")


class CaptainAvailabilityRequestSerializer(serializers.Serializer):
    """Requête pour changer la disponibilité"""
    is_available = serializers.BooleanField(help_text="Nouvelle disponibilité")
    reason = serializers.CharField(
        max_length=255,
        required=False,
        help_text="Raison du changement"
    )


class CaptainAvailabilityResponseSerializer(serializers.Serializer):
    """Réponse pour le changement de disponibilité"""
    status = serializers.CharField(help_text="Statut de l'opération")
    is_available = serializers.BooleanField(help_text="Nouvelle disponibilité")
    updated_at = serializers.DateTimeField(help_text="Date de mise à jour")


# ============================================================================
# SÉRIALISEURS POUR LES ÉTABLISSEMENTS
# ============================================================================

class EstablishmentWalletResponseSerializer(serializers.Serializer):
    """Réponse pour le portefeuille établissement"""
    balance = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Solde actuel")
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Revenus totaux")
    pending_payments = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Paiements en attente")
    last_payout = serializers.DecimalField(max_digits=10, decimal_places=2, help_text="Dernier versement")
    last_payout_date = serializers.DateTimeField(help_text="Date du dernier versement")


class EstablishmentAddFundsRequestSerializer(serializers.Serializer):
    """Requête pour ajouter des fonds"""
    amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        min_value=Decimal('10.00'),
        help_text="Montant à ajouter (minimum 10€)"
    )
    description = serializers.CharField(
        max_length=255,
        required=False,
        help_text="Description de l'ajout"
    )


# ============================================================================
# SÉRIALISEURS POUR LES NOTIFICATIONS
# ============================================================================

class NotificationResponseSerializer(serializers.Serializer):
    """Réponse pour les notifications"""
    id = serializers.IntegerField(help_text="ID de la notification")
    title = serializers.CharField(help_text="Titre")
    message = serializers.CharField(help_text="Message")
    type = serializers.CharField(help_text="Type de notification")
    is_read = serializers.BooleanField(help_text="Lu ou non")
    created_at = serializers.DateTimeField(help_text="Date de création")
    data = serializers.DictField(help_text="Données additionnelles")


# ============================================================================
# SÉRIALISEURS POUR LES ERREURS
# ============================================================================

class ErrorResponseSerializer(serializers.Serializer):
    """Réponse d'erreur standard"""
    error = serializers.CharField(help_text="Message d'erreur")
    code = serializers.CharField(help_text="Code d'erreur", required=False)
    details = serializers.DictField(help_text="Détails de l'erreur", required=False)


class ValidationErrorResponseSerializer(serializers.Serializer):
    """Réponse d'erreur de validation"""
    field_errors = serializers.DictField(help_text="Erreurs par champ")
    non_field_errors = serializers.ListField(
        child=serializers.CharField(),
        help_text="Erreurs générales",
        required=False
    )


# ============================================================================
# SÉRIALISEURS POUR LES RÉPONSES GÉNÉRIQUES
# ============================================================================

class SuccessResponseSerializer(serializers.Serializer):
    """Réponse de succès générique"""
    status = serializers.CharField(default="success", help_text="Statut de l'opération")
    message = serializers.CharField(help_text="Message de succès")
    data = serializers.DictField(help_text="Données de réponse", required=False)


class PaginatedResponseSerializer(serializers.Serializer):
    """Réponse paginée générique"""
    count = serializers.IntegerField(help_text="Nombre total d'éléments")
    next = serializers.URLField(help_text="URL de la page suivante", allow_null=True, max_length=1000)
    previous = serializers.URLField(help_text="URL de la page précédente", allow_null=True, max_length=1000)
    results = serializers.ListField(help_text="Résultats de la page actuelle")
