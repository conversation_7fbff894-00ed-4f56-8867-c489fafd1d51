"""
Tests d'intégration pour vérifier le bon fonctionnement entre establishments et boatman.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from decimal import Decimal
from django.utils import timezone

from accounts.models import Captain, Client, Establishment
from boats.models import Boat
from trips.models import Trip, ShuttleTripRequest
from payments.models import Wallet, Payment
from .integration_services import (
    BoatmanTripIntegrationService,
    BoatmanEstablishmentIntegrationService,
    BoatmanQRValidationService
)

User = get_user_model()


class EstablishmentBoatmanIntegrationTest(APITestCase):
    """Tests d'intégration entre establishments et boatman"""
    
    def setUp(self):
        """Configuration initiale pour les tests d'intégration"""
        
        # Créer un établissement
        self.establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Hotel',
            last_name='Paradise'
        )
        
        self.establishment = Establishment.objects.create(
            user=self.establishment_user,
            name='Hotel Paradise Beach',
            type='HOTEL',
            address='123 Promenade des Anglais, Cannes'
        )
        
        # Créer un capitaine enregistré par l'établissement
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Captain',
            last_name='Jack',
            phone_number='+33123456789',
            is_captain=True
        )
        
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans d\'expérience',
            license_number='LIC001',
            is_available=True,
            availability_status='AVAILABLE'
        )
        
        # Lier le capitaine à l'établissement
        self.captain.metadata = {'registered_by_establishment_id': self.establishment.id}
        self.captain.save()
        
        # Créer un bateau pour le capitaine
        self.boat = Boat.objects.create(
            captain=self.captain,
            name='Sea Explorer',
            boat_type='classic',
            capacity=8,
            is_available=True
        )
        
        # Créer un client
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Marie',
            last_name='Dubois'
        )
        
        self.client_profile = Client.objects.create(user=self.client_user)
        
        # Créer les portefeuilles
        self.establishment_wallet = Wallet.objects.create(
            user=self.establishment_user,
            balance=Decimal('1000.00')
        )
        
        self.captain_wallet = Wallet.objects.create(
            user=self.captain_user,
            balance=Decimal('150.00')
        )
        
        # Tokens d'authentification
        self.establishment_token = Token.objects.create(user=self.establishment_user)
        self.captain_token = Token.objects.create(user=self.captain_user)
        
        self.establishment_client = APIClient()
        self.establishment_client.credentials(HTTP_AUTHORIZATION='Token ' + self.establishment_token.key)
        
        self.captain_client = APIClient()
        self.captain_client.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

    def test_complete_shuttle_workflow(self):
        """Test du workflow complet d'une navette gratuite"""
        
        # 1. Créer une demande de navette (simulée)
        shuttle_request = ShuttleTripRequest.objects.create(
            client=self.client_profile,
            establishment=self.establishment,
            departure_location={'city_name': 'Aéroport Nice', 'coordinates': {'lat': 43.6584, 'lng': 7.2159}},
            arrival_location={'city_name': 'Hotel Paradise Beach', 'coordinates': {'lat': 43.5528, 'lng': 7.0174}},
            departure_date=timezone.now().date(),
            departure_time=timezone.now().time(),
            passenger_count=2,
            status='PENDING'
        )
        
        # 2. L'établissement accepte et assigne le capitaine
        accept_data = {
            'captain_id': self.captain.id,
            'boat_id': self.boat.id,
            'estimated_pickup_time': timezone.now().isoformat()
        }
        
        response = self.establishment_client.post(
            f'/api/establishments/shuttle-requests/{shuttle_request.id}/accept/',
            accept_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('trip_id', response.data['data'])
        
        trip_id = response.data['data']['trip_id']
        
        # 3. Vérifier que la course apparaît dans l'espace batelier
        response = self.captain_client.get('/api/boatman/shuttles/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['shuttles']), 1)
        
        shuttle = response.data['data']['shuttles'][0]
        self.assertEqual(shuttle['shuttle_id'], trip_id)
        self.assertEqual(shuttle['payment']['amount'], 0.00)
        self.assertEqual(shuttle['payment']['method'], 'FREE_SHUTTLE')
        
        # 4. Le batelier consulte les détails
        response = self.captain_client.get(f'/api/boatman/shuttle/{trip_id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['client']['name'], 'Marie Dubois')
        
        # 5. Validation du QR code
        qr_code = BoatmanQRValidationService.generate_qr_code(Trip.objects.get(id=trip_id))
        qr_data = {'qr_code': qr_code}
        
        response = self.captain_client.post(
            f'/api/boatman/shuttle/{trip_id}/validate-qr/',
            qr_data,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['data']['qr_validated'])
        
        # 6. Démarrage de la course
        trip = Trip.objects.get(id=trip_id)
        trip.scheduled_start_time = timezone.now()  # Permettre le démarrage immédiat
        trip.save()
        
        response = self.captain_client.post(f'/api/boatman/shuttle/{trip_id}/start/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['status'], 'IN_PROGRESS')
        
        # 7. Fin de la course
        response = self.captain_client.post(f'/api/boatman/shuttle/{trip_id}/end/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['status'], 'COMPLETED')
        
        # 8. Vérifier que le paiement symbolique a été créé
        payment = Payment.objects.filter(
            user=self.captain_user,
            trip_id=trip_id,
            type=Payment.PaymentType.TRIP_PAYMENT
        ).first()
        
        self.assertIsNotNone(payment)
        self.assertEqual(payment.amount, Decimal('0.00'))
        self.assertEqual(payment.status, Payment.Status.COMPLETED)

    def test_captain_dashboard_integration(self):
        """Test de l'intégration du tableau de bord batelier"""
        
        # Créer quelques courses pour le capitaine
        for i in range(3):
            Trip.objects.create(
                client=self.client_profile,
                captain=self.captain,
                boat=self.boat,
                trip_type='NAVETTES_GRATUITES',
                start_location=f'Location {i}',
                end_location=f'Destination {i}',
                scheduled_start_time=timezone.now() + timezone.timedelta(hours=i+1),
                passenger_count=2,
                status='ACCEPTED',
                total_price=Decimal('0.00')
            )
        
        # Consulter le tableau de bord
        response = self.captain_client.get('/api/boatman/dashboard/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.data['data']
        self.assertEqual(data['captain']['name'], 'Captain Jack')
        self.assertEqual(data['trip_statistics']['total_shuttles'], 3)
        self.assertEqual(len(data['upcoming_shuttles']), 3)

    def test_captain_wallet_integration(self):
        """Test de l'intégration du portefeuille batelier"""
        
        # Créer quelques paiements
        Payment.objects.create(
            user=self.captain_user,
            type=Payment.PaymentType.TIP,
            amount=Decimal('15.00'),
            status=Payment.Status.COMPLETED,
            description='Pourboire course #123'
        )
        
        # Consulter le portefeuille
        response = self.captain_client.get('/api/boatman/wallet/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.data['data']
        self.assertEqual(data['wallet']['available_balance'], 150.0)
        self.assertEqual(data['statistics']['total_tips'], 15.0)

    def test_establishment_boatman_relationship(self):
        """Test de la relation entre établissement et batelier"""
        
        # Vérifier que l'établissement peut voir ses bateliers
        response = self.establishment_client.get('/api/establishments/boatmen/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Note: Cette fonctionnalité nécessiterait une relation explicite
        # entre Captain et Establishment dans le modèle

    def test_trip_ownership_validation(self):
        """Test de la validation de propriété des courses"""
        
        # Créer une course pour un autre capitaine
        other_captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_captain=True
        )
        
        other_captain = Captain.objects.create(
            user=other_captain_user,
            experience='3 ans',
            license_number='LIC002'
        )
        
        other_trip = Trip.objects.create(
            client=self.client_profile,
            captain=other_captain,
            boat=self.boat,
            trip_type='NAVETTES_GRATUITES',
            start_location='Other Location',
            end_location='Other Destination',
            scheduled_start_time=timezone.now() + timezone.timedelta(hours=1),
            passenger_count=1,
            status='ACCEPTED'
        )
        
        # Essayer d'accéder à la course d'un autre capitaine
        response = self.captain_client.get(f'/api/boatman/shuttle/{other_trip.id}/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_integration_services(self):
        """Test des services d'intégration"""
        
        # Test du service de validation de propriété
        trip = Trip.objects.create(
            client=self.client_profile,
            captain=self.captain,
            boat=self.boat,
            trip_type='NAVETTES_GRATUITES',
            start_location='Test Location',
            end_location='Test Destination',
            scheduled_start_time=timezone.now() + timezone.timedelta(hours=1),
            passenger_count=2,
            status='ACCEPTED'
        )
        
        # Test de validation de propriété
        validated_trip = BoatmanTripIntegrationService.validate_trip_ownership(
            self.captain, trip.id
        )
        self.assertEqual(validated_trip.id, trip.id)
        
        # Test de génération QR
        qr_code = BoatmanQRValidationService.generate_qr_code(trip)
        expected_qr = f"COMMODORE_TRIP_{trip.id}_{self.client_profile.id}"
        self.assertEqual(qr_code, expected_qr)
        
        # Test de validation QR
        is_valid, message = BoatmanQRValidationService.validate_qr_code(trip, qr_code)
        self.assertTrue(is_valid)
        
        # Test de récupération de l'établissement
        establishment = BoatmanEstablishmentIntegrationService.get_captain_establishment(self.captain)
        self.assertEqual(establishment.id, self.establishment.id)


class BoatmanPermissionsTest(APITestCase):
    """Tests des permissions de l'espace batelier"""
    
    def setUp(self):
        # Créer un utilisateur non-capitaine
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.regular_token = Token.objects.create(user=self.regular_user)
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.regular_token.key)

    def test_non_captain_access_denied(self):
        """Test que les non-capitaines ne peuvent pas accéder à l'espace batelier"""
        
        endpoints = [
            '/api/boatman/dashboard/',
            '/api/boatman/shuttles/',
            '/api/boatman/profile/',
            '/api/boatman/wallet/',
        ]
        
        for endpoint in endpoints:
            response = self.client.get(endpoint)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
            self.assertIn('Compte batelier requis', response.data['error'])


class BoatmanNotificationTest(TestCase):
    """Tests des notifications automatiques"""
    
    def setUp(self):
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_captain=True
        )
        
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans',
            license_number='LIC001'
        )
        
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.client_profile = Client.objects.create(user=self.client_user)

    def test_trip_assignment_notification(self):
        """Test de notification lors de l'assignation d'une course"""
        
        # Créer une course (simule l'assignation par un établissement)
        trip = Trip.objects.create(
            client=self.client_profile,
            captain=self.captain,
            trip_type='NAVETTES_GRATUITES',
            start_location='Aéroport',
            end_location='Hotel',
            scheduled_start_time=timezone.now() + timezone.timedelta(hours=1),
            passenger_count=2,
            status='ACCEPTED'
        )
        
        # Vérifier qu'une notification a été créée (via les signaux)
        from notifications.models import Notification
        notification = Notification.objects.filter(
            user=self.captain_user,
            notification_type='TRIP_ASSIGNED'
        ).first()
        
        # Note: Ceci nécessiterait que les signaux soient correctement configurés
        # et que le modèle Notification existe
