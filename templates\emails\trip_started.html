<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Commodore - Votre course a démarré</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #0066cc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f4f4f4;
            padding: 10px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .trip-info {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .button {
            display: inline-block;
            background-color: #0066cc;
            color: white;
            padding: 10px 20px;
            margin: 10px 0;
            text-decoration: none;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Votre course a démarré !</h1>
    </div>
    
    <div class="content">
        <p>Bonjour {{ user.first_name }},</p>
        
        <p>Nous vous informons que votre course vient de démarrer.</p>
        
        <div class="trip-info">
            <h3>Détails de la course :</h3>
            {% if related_object %}
            <p><strong>Numéro de réservation :</strong> {{ related_object.id }}</p>
            <p><strong>Date :</strong> {{ related_object.scheduled_date|date:"d/m/Y" }}</p>
            <p><strong>Heure de départ :</strong> {{ related_object.start_time|time:"H:i" }}</p>
            <p><strong>Capitaine :</strong> {{ related_object.captain.user.get_full_name }}</p>
            {% else %}
            <p>Veuillez consulter l'application pour plus de détails sur votre course.</p>
            {% endif %}
        </div>
        
        <p>Vous pouvez suivre le statut de votre course en temps réel via l'application Commodore.</p>
        
        <a href="{{ site_url }}" class="button">Ouvrir l'application</a>
        
        <p>Si vous avez des questions, n'hésitez pas à contacter notre service client.</p>
        
        <p>Bon voyage !<br>
        L'équipe Commodore</p>
    </div>
    
    <div class="footer">
        <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
        <p>&copy; {% now "Y" %} Commodore - Tous droits réservés</p>
    </div>
</body>
</html>
