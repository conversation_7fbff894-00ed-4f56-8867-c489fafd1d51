"""
Vues pour les paiements post-voyage : compensation carbone et pourboires.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from decimal import Decimal

from .models import Trip
from .carbon_calculator import CarbonFootprintCalculator, CarbonCompensationService
from payments.models import Payment
from payments.services import PaymentService
from .qr_service import generate_carbon_compensation_qr, generate_tip_qr


class TripCarbonFootprintView(APIView):
    """Calcul de l'empreinte carbone d'une course terminée"""
    permission_classes = [IsAuthenticated]

    def get(self, request, trip_id):
        """Récupérer les données d'empreinte carbone pour une course"""
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur a accès à cette course
        if not (
            (hasattr(request.user, 'client') and trip.client == request.user.client) or
            (hasattr(request.user, 'captain') and trip.captain == request.user.captain)
        ):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la course est terminée
        if trip.status != Trip.Status.COMPLETED:
            return Response(
                {'error': 'La course doit être terminée pour calculer l\'empreinte carbone'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Calculer l'empreinte carbone
        carbon_data = CarbonFootprintCalculator.calculate_trip_carbon_data(trip)
        
        if not carbon_data:
            return Response(
                {'error': 'Impossible de calculer l\'empreinte carbone'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Vérifier si une compensation a déjà été payée
        existing_compensation = Payment.objects.filter(
            user=request.user,
            trip=trip,
            type=Payment.PaymentType.CARBON_OFFSET,
            status=Payment.Status.COMPLETED
        ).first()
        
        response_data = {
            'trip_id': trip.id,
            'carbon_footprint': {
                'co2_kg': carbon_data['calculation']['co2_kg'],
                'fuel_consumed_liters': carbon_data['calculation']['fuel_consumed_liters'],
                'compensation_cost_euros': carbon_data['calculation']['compensation_cost_euros'],
                'is_eco_friendly': carbon_data['calculation']['is_eco_friendly'],
                'fuel_type': carbon_data['calculation']['fuel_type']
            },
            'eco_message': carbon_data['eco_message'],
            'can_compensate': carbon_data['can_compensate'],
            'already_compensated': existing_compensation is not None,
            'compensation_payment_id': existing_compensation.id if existing_compensation else None,
            'compensation_message': f"Coût de compensation carbone: {carbon_data['calculation']['compensation_cost_euros']} euros. Déjà compensé: {'Oui' if existing_compensation else 'Non'}"
        }
        
        return Response(response_data)


class CarbonCompensationPaymentView(APIView):
    """Paiement de compensation carbone"""
    permission_classes = [IsAuthenticated]

    def post(self, request, trip_id):
        """Créer un paiement de compensation carbone"""
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur est le client de cette course
        if not (hasattr(request.user, 'client') and trip.client == request.user.client):
            return Response(
                {'error': 'Seul le client peut payer la compensation carbone'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la course est terminée
        if trip.status != Trip.Status.COMPLETED:
            return Response(
                {'error': 'La course doit être terminée pour payer la compensation carbone'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Vérifier qu'aucune compensation n'a déjà été payée
        existing_compensation = Payment.objects.filter(
            user=request.user,
            trip=trip,
            type=Payment.PaymentType.CARBON_OFFSET,
            status=Payment.Status.COMPLETED
        ).exists()
        
        if existing_compensation:
            return Response(
                {'error': 'Compensation carbone déjà payée pour cette course'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Calculer l'empreinte carbone
        carbon_data = CarbonFootprintCalculator.calculate_trip_carbon_data(trip)
        
        if not carbon_data or not carbon_data['can_compensate']:
            return Response(
                {'error': 'Compensation carbone non applicable pour cette course'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        compensation_amount = carbon_data['calculation']['compensation_cost_euros']
        payment_method = request.data.get('payment_method', 'card')
        
        try:
            # Créer le paiement de compensation
            if payment_method == 'wallet':
                # Paiement via portefeuille
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=compensation_amount,
                    payment_method_id="wallet",
                    description=f"Compensation carbone - Course #{trip.id}",
                    metadata={
                        'trip_id': trip.id,
                        'carbon_offset': True,
                        'co2_compensated_kg': str(carbon_data['calculation']['co2_kg'])
                    },
                    payment_type=Payment.PaymentType.CARBON_OFFSET
                )
            else:
                # Paiement par carte
                payment_method_id = request.data.get('payment_method_id')
                if not payment_method_id:
                    return Response(
                        {'error': 'payment_method_id requis pour le paiement par carte'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=compensation_amount,
                    payment_method_id=payment_method_id,
                    description=f"Compensation carbone - Course #{trip.id}",
                    metadata={
                        'trip_id': trip.id,
                        'carbon_offset': True,
                        'co2_compensated_kg': str(carbon_data['calculation']['co2_kg'])
                    },
                    payment_type=Payment.PaymentType.CARBON_OFFSET
                )
            
            # Générer un QR code pour la compensation
            compensation_qr = generate_carbon_compensation_qr(trip, compensation_amount)
            
            return Response({
                'message': 'Compensation carbone payée avec succès',
                'payment_id': payment_result.get('id'),
                'amount': compensation_amount,
                'co2_compensated_kg': carbon_data['calculation']['co2_kg'],
                'compensation_qr': compensation_qr,
                'payment_method': payment_method
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Erreur lors du paiement: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TipPaymentView(APIView):
    """Paiement de pourboire au capitaine"""
    permission_classes = [IsAuthenticated]

    def post(self, request, trip_id):
        """Créer un paiement de pourboire"""
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur est le client de cette course
        if not (hasattr(request.user, 'client') and trip.client == request.user.client):
            return Response(
                {'error': 'Seul le client peut donner un pourboire'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la course est terminée
        if trip.status != Trip.Status.COMPLETED:
            return Response(
                {'error': 'La course doit être terminée pour donner un pourboire'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Récupérer le montant du pourboire
        tip_amount = request.data.get('amount')
        if not tip_amount:
            return Response(
                {'error': 'Montant du pourboire requis'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            tip_amount = Decimal(str(tip_amount))
            if tip_amount <= 0:
                return Response(
                    {'error': 'Le montant du pourboire doit être positif'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return Response(
                {'error': 'Montant du pourboire invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        payment_method = request.data.get('payment_method', 'card')
        
        try:
            # Créer le paiement de pourboire
            if payment_method == 'wallet':
                # Paiement via portefeuille
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=tip_amount,
                    payment_method_id="wallet",
                    description=f"Pourboire - Course #{trip.id} - Capitaine {trip.captain.user.get_full_name()}",
                    metadata={
                        'trip_id': trip.id,
                        'captain_id': trip.captain.pk,
                        'tip_payment': True
                    },
                    payment_type=Payment.PaymentType.TIP
                )
            else:
                # Paiement par carte
                payment_method_id = request.data.get('payment_method_id')
                if not payment_method_id:
                    return Response(
                        {'error': 'payment_method_id requis pour le paiement par carte'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=tip_amount,
                    payment_method_id=payment_method_id,
                    description=f"Pourboire - Course #{trip.id} - Capitaine {trip.captain.user.get_full_name()}",
                    metadata={
                        'trip_id': trip.id,
                        'captain_id': trip.captain.pk,
                        'tip_payment': True
                    },
                    payment_type=Payment.PaymentType.TIP
                )
            
            # Mettre à jour le pourboire dans la course
            trip.tip += tip_amount
            trip.save()
            
            # Générer un QR code pour le pourboire
            tip_qr = generate_tip_qr(trip, tip_amount)
            
            return Response({
                'message': 'Pourboire payé avec succès',
                'payment_id': payment_result.get('id'),
                'amount': tip_amount,
                'captain_name': trip.captain.user.get_full_name(),
                'tip_qr': tip_qr,
                'payment_method': payment_method,
                'total_trip_tip': trip.tip
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Erreur lors du paiement: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
