import os
import sys
import django

# Initialisation Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from rag.models import Document
from rag.services import _rag_service as rag_service
from django.utils import timezone

# Chemin du dossier documentation
DOCS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'documentation')

# Parcours des fichiers .md
md_files = [f for f in os.listdir(DOCS_DIR) if f.endswith('.md')]
print(f"\n {len(md_files)} fichiers trouvés dans documentation/ : {md_files}")
print(f"\n📚 {len(md_files)} fichiers trouvés dans documentation/ : {md_files}")

for filename in md_files:
    path = os.path.join(DOCS_DIR, filename)
    title = os.path.splitext(filename)[0].replace('_', ' ').capitalize()
    with open(path, 'r', encoding='utf-8') as f:
        content = f.read()
    doc, created = Document.objects.get_or_create(
        title=title,
        defaults={
            'content': content,
            'source': f'Documentation interne - {filename}',
            'category': 'Documentation',
            'tags': ['documentation', 'commodore'],
            'created_at': timezone.now()
        }
    )
    if not created and doc.content != content:
        print(f"📝 Mise à jour du document existant : {title}")
        doc.content = content
        doc.embedding_generated = False
        doc.updated_at = timezone.now()
        doc.save()
    else:
        print(f"➕ Import de {title}")
    # Génération des embeddings
    rag_service.process_document(doc)
    print(f"   → Embeddings générés pour : {title}")

print("\n✅ Importation terminée. Tous les documents sont prêts pour le RAG.")
