import os
import django
import requests
import json
from decimal import Decimal

# Configurer l'environnement Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from payments.models import Wallet, Transaction
from trips.models import Trip, Shuttle
from boats.models import MaintenanceRecord
from django.contrib.auth import get_user_model

User = get_user_model()

# Token JWT valide pour l'utilisateur client (ID: 2)
# Le même que celui utilisé dans le script PowerShell
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4Mjk2OTg0LCJpYXQiOjE3NDgyMTA1ODQsImp0aSI6IjJfMTc0ODIxMDU4NC4zNzUyMTMiLCJ1c2VyX2lkIjoyfQ.020cRZGNI8czl5DnOnETaDGlfyV2pl0I-B_dB9C12aA"
API_URL = "http://localhost:8000/api"

def make_api_request(endpoint, method="GET", data=None):
    """Effectue une requête vers l'API"""
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    url = f"{API_URL}/{endpoint}"
    
    if method == "GET":
        response = requests.get(url, headers=headers)
    elif method == "POST":
        response = requests.post(url, headers=headers, data=json.dumps(data) if data else None)
    
    print(f"\n=== Requête {method} vers {url} ===")
    print(f"Données envoyées: {data}")
    
    try:
        result = response.json()
        print(f"Statut: {response.status_code}")
        print(f"Réponse: {json.dumps(result, indent=2)}")
        return result
    except:
        print(f"Statut: {response.status_code}")
        print(f"Réponse: {response.text}")
        return None

def get_wallet_balance():
    """Récupère le solde du portefeuille"""
    result = make_api_request("payments/wallet/")
    if result and "balance" in result:
        return result["balance"]
    return None

def test_trip_payment(trip_id=1):
    """Teste le paiement d'une course"""
    print("\n\n=== TEST PAIEMENT COURSE ===")
    
    # Vérifier que la course existe
    try:
        trip = Trip.objects.get(id=trip_id)
        print(f"Course trouvée: {trip} (prix: {trip.total_price})")
    except Trip.DoesNotExist:
        print(f"La course avec l'ID {trip_id} n'existe pas.")
        return
    
    # Vérifier le solde avant paiement
    balance_before = get_wallet_balance()
    print(f"Solde avant paiement: {balance_before}")
    
    # Effectuer le paiement
    data = {"trip_id": trip_id}
    result = make_api_request("payments/trip/", method="POST", data=data)
    
    # Vérifier le solde après paiement
    balance_after = get_wallet_balance()
    print(f"Solde après paiement: {balance_after}")
    
    # Vérifier si le paiement a réussi
    if result and "id" in result:
        print(f"Paiement réussi! ID de transaction: {result['id']}")
        print(f"Différence de solde: {float(balance_before) - float(balance_after)}")
    else:
        print("Échec du paiement.")

def test_shuttle_payment(shuttle_id=5):
    """Teste le paiement d'une navette"""
    print("\n\n=== TEST PAIEMENT NAVETTE ===")
    
    # Vérifier que la navette existe
    try:
        shuttle = Shuttle.objects.get(id=shuttle_id)
        print(f"Navette trouvée: {shuttle.route_name} (prix par personne: {shuttle.price_per_person})")
    except Shuttle.DoesNotExist:
        print(f"La navette avec l'ID {shuttle_id} n'existe pas.")
        return
    
    # Vérifier le solde avant paiement
    balance_before = get_wallet_balance()
    print(f"Solde avant paiement: {balance_before}")
    
    # Effectuer le paiement
    data = {"shuttle_id": shuttle_id}
    result = make_api_request("payments/trip/", method="POST", data=data)
    
    # Vérifier le solde après paiement
    balance_after = get_wallet_balance()
    print(f"Solde après paiement: {balance_after}")
    
    # Vérifier si le paiement a réussi
    if result and "id" in result:
        print(f"Paiement réussi! ID de transaction: {result['id']}")
        print(f"Différence de solde: {float(balance_before) - float(balance_after)}")
    else:
        print("Échec du paiement.")

def test_maintenance_payment(maintenance_id=3):
    """Teste le paiement d'une maintenance"""
    print("\n\n=== TEST PAIEMENT MAINTENANCE ===")
    
    # Vérifier que la maintenance existe
    try:
        maintenance = MaintenanceRecord.objects.get(id=maintenance_id)
        print(f"Maintenance trouvée: {maintenance.description} (coût: {maintenance.cost})")
        
        # Vérifier le propriétaire du bateau
        boat = maintenance.boat
        print(f"Bateau: {boat.name}")
        if boat.captain:
            print(f"Capitaine: {boat.captain}")
        if boat.establishment:
            print(f"Établissement: {boat.establishment}")
        
        # Vérifier l'utilisateur actuel
        user = User.objects.get(id=2)  # ID de l'utilisateur client
        print(f"Utilisateur de test: {user}")
        if hasattr(user, 'captain'):
            print(f"Capitaine associé: {user.captain}")
        if hasattr(user, 'establishment'):
            print(f"Établissement associé: {user.establishment}")
    except MaintenanceRecord.DoesNotExist:
        print(f"La maintenance avec l'ID {maintenance_id} n'existe pas.")
        return
    
    # Vérifier le solde avant paiement
    balance_before = get_wallet_balance()
    print(f"Solde avant paiement: {balance_before}")
    
    # Effectuer le paiement
    data = {"payment_method_id": "wallet"}
    result = make_api_request(f"payments/maintenance/{maintenance_id}/pay/", method="POST", data=data)
    
    # Vérifier le solde après paiement
    balance_after = get_wallet_balance()
    print(f"Solde après paiement: {balance_after}")
    
    # Vérifier si le paiement a réussi
    if result and "id" in result:
        print(f"Paiement réussi! ID de transaction: {result['id']}")
        print(f"Différence de solde: {float(balance_before) - float(balance_after)}")
    else:
        print("Échec du paiement.")

def test_all():
    """Exécute tous les tests de paiement"""
    test_trip_payment()
    test_shuttle_payment()
    test_maintenance_payment()

if __name__ == "__main__":
    test_all()
