# API Endpoints - Accounts

## 1. Gestion des Profils Utilisateurs

### 1.1. Profil Utilisateur
- **Endpoint**: GET /api/profile/
- **Description**: R<PERSON><PERSON><PERSON>rer le profil complet de l'utilisateur connecté
- **Auth Required**: <PERSON><PERSON> (JWT <PERSON>)
- **Headers**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```
- **Response (200 OK)**:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "phone_number": "+***********",
  "type": "CLIENT",
  "profile_picture": "https://example.com/profile.jpg",
  "client_profile": {
    "wallet_balance": "150.00",
    "date_of_birth": "1990-05-15",
    "nationality": "Française",
    "preferred_language": "fr",
    "emergency_contact_name": "<PERSON>",
    "emergency_contact_phone": "+***********"
  },
  "captain_profile": null,
  "establishment_profile": null
}
```

### 1.2. Modifier Profil Utilisateur
- **Endpoint**: PATCH /api/profile/
- **Description**: Modifier le profil de l'utilisateur connecté
- **Auth Required**: Oui (JWT Token)
- **Request Body (Client)**:
```json
{
  "first_name": "Jean",
  "last_name": "Dupont",
  "phone_number": "+***********",
  "profile_picture": "https://example.com/new_profile.jpg",
  "client_profile": {
    "date_of_birth": "1990-05-15",
    "nationality": "Française",
    "preferred_language": "fr",
    "emergency_contact_name": "Jane Dupont",
    "emergency_contact_phone": "+***********"
  }
}
```
- **Request Body (Captain)**:
```json
{
  "first_name": "Pierre",
  "last_name": "Martin",
  "captain_profile": {
    "experience": "10 ans d'expérience en navigation côtière",
    "license_number": "CAP123456",
    "years_of_experience": 10,
    "certifications": ["Permis côtier", "Permis hauturier"],
    "specializations": ["Navigation côtière", "Pêche en mer"],
    "rate_per_hour": "45.00"
  }
}
```
- **Request Body (Establishment)**:
```json
{
  "establishment_profile": {
    "name": "Hôtel Riviera",
    "type": "HOTEL",
    "address": "123 Boulevard de la Mer, 06400 Cannes",
    "description": "Hôtel de luxe avec accès direct à la plage",
    "opening_hours": "Réception: 24h/24",
    "services_offered": ["Hébergement", "Restaurant", "Spa"],
    "website": "https://hotel-riviera.com"
  }
}
```
- **Response (200 OK)**:
```json
{
  "message": "Profil mis à jour avec succès"
}
```

## 2. Listes d'Utilisateurs

### 2.1. Liste des Clients
- **Endpoint**: GET /api/clients/
- **Description**: Récupérer la liste des clients
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - page: Numéro de page (défaut: 1)
  - page_size: Nombre d'éléments par page (défaut: 20)
- **Response (200 OK)**:
```json
{
  "count": 150,
  "next": "http://localhost:8000/api/clients/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "user": {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "profile_picture": "https://example.com/profile1.jpg"
      },
      "wallet_balance": "150.00",
      "date_of_birth": "1990-05-15",
      "nationality": "Française"
    }
  ]
}
```

### 2.2. Liste des Capitaines
- **Endpoint**: GET /api/captains/
- **Description**: Récupérer la liste des capitaines
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - is_available: Filtrer par disponibilité (true/false)
  - experience_min: Années d'expérience minimales
  - page: Numéro de page
- **Response (200 OK)**:
```json
{
  "count": 45,
  "results": [
    {
      "id": 1,
      "user": {
        "id": 5,
        "email": "<EMAIL>",
        "first_name": "Pierre",
        "last_name": "Martin",
        "profile_picture": "https://example.com/captain1.jpg"
      },
      "experience": "10 ans d'expérience en navigation côtière",
      "average_rating": 4.8,
      "total_trips": 245,
      "is_available": true,
      "license_number": "CAP123456",
      "years_of_experience": 10,
      "rate_per_hour": "45.00"
    }
  ]
}
```

### 2.3. Liste des Établissements
- **Endpoint**: GET /api/establishments/
- **Description**: Récupérer la liste des établissements
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - type: Type d'établissement (HOTEL, RESTAURANT, MARINA, etc.)
  - location: Filtrer par localisation
  - rating_min: Note minimale
- **Response (200 OK)**:
```json
{
  "count": 25,
  "results": [
    {
      "id": 1,
      "user": {
        "id": 10,
        "email": "<EMAIL>"
      },
      "name": "Hôtel Riviera",
      "type": "HOTEL",
      "address": "123 Boulevard de la Mer, 06400 Cannes",
      "description": "Hôtel de luxe avec accès direct à la plage",
      "average_rating": 4.6,
      "main_photo": "https://example.com/hotel_main.jpg",
      "website": "https://hotel-riviera.com"
    }
  ]
}
```

## 3. Détails des Utilisateurs

### 3.1. Détails d'un Utilisateur
- **Endpoint**: GET /api/users/{id}/
- **Description**: Récupérer les détails d'un utilisateur spécifique
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "phone_number": "+***********",
  "type": "CLIENT",
  "profile_picture": "https://example.com/profile.jpg",
  "is_active": true,
  "date_joined": "2025-01-15T10:30:00Z",
  "last_login": "2025-05-30T14:20:00Z"
}
```

### 3.2. Détails d'un Capitaine
- **Endpoint**: GET /api/captains/{id}/
- **Description**: Récupérer les détails complets d'un capitaine
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "user": {
    "id": 5,
    "email": "<EMAIL>",
    "first_name": "Pierre",
    "last_name": "Martin",
    "phone_number": "+***********",
    "profile_picture": "https://example.com/captain.jpg"
  },
  "experience": "10 ans d'expérience en navigation côtière et hauturière",
  "average_rating": 4.8,
  "total_trips": 245,
  "wallet_balance": "1250.00",
  "is_available": true,
  "current_location": "Port de Cannes",
  "license_number": "CAP123456",
  "license_expiry_date": "2027-06-15",
  "years_of_experience": 10,
  "certifications": ["Permis côtier", "Permis hauturier", "Certificat de sécurité"],
  "specializations": ["Navigation côtière", "Pêche en mer", "Excursions touristiques"],
  "availability_status": "AVAILABLE",
  "rate_per_km": "2.50",
  "rate_per_hour": "45.00"
}
```

### 3.3. Détails d'un Établissement
- **Endpoint**: GET /api/establishments/{id}/
- **Description**: Récupérer les détails complets d'un établissement
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "user": {
    "id": 10,
    "email": "<EMAIL>",
    "phone_number": "+***********"
  },
  "name": "Hôtel Riviera",
  "type": "HOTEL",
  "address": "123 Boulevard de la Mer, 06400 Cannes",
  "description": "Hôtel de luxe avec accès direct à la plage privée",
  "main_photo": "https://example.com/hotel_main.jpg",
  "secondary_photos": [
    "https://example.com/hotel_1.jpg",
    "https://example.com/hotel_2.jpg"
  ],
  "wallet_balance": "5000.00",
  "business_name": "Riviera Hotels SAS",
  "business_type": "Hôtellerie",
  "registration_number": "RCS Cannes *********",
  "tax_id": "FR*********01",
  "opening_hours": "Réception: 24h/24, Restaurant: 7h-23h",
  "services_offered": ["Hébergement", "Restaurant", "Spa", "Excursions en mer"],
  "average_rating": 4.6,
  "location_coordinates": "43.5528,7.0174",
  "website": "https://hotel-riviera.com",
  "social_media": {
    "facebook": "https://facebook.com/hotelriviera",
    "instagram": "@hotelriviera"
  }
}
```

## 4. Gestion des Favoris

### 4.1. Liste des Capitaines Favoris
- **Endpoint**: GET /api/favorites/captains/
- **Description**: Récupérer la liste des capitaines favoris de l'utilisateur
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "captain": 5,
    "captain_details": {
      "user": {
        "first_name": "Jean",
        "last_name": "Dupont",
        "profile_picture": "https://example.com/captain.jpg"
      },
      "experience": "15 ans de navigation",
      "average_rating": 4.8,
      "total_trips": 237,
      "is_available": true
    },
    "notes": "Mon capitaine préféré",
    "created_at": "2025-05-25T12:30:45Z"
  }
]
```

### 4.2. Ajouter un Capitaine aux Favoris
- **Endpoint**: POST /api/favorites/captains/
- **Description**: Ajouter un capitaine à la liste des favoris
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "captain": 5,
  "notes": "Excellent capitaine pour les sorties en mer"
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "captain": 5,
  "notes": "Excellent capitaine pour les sorties en mer",
  "created_at": "2025-05-25T12:30:45Z"
}
```

### 4.3. Détails d'un Capitaine Favori
- **Endpoint**: GET /api/favorites/captains/{id}/
- **Description**: Récupérer les détails d'un capitaine favori spécifique
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "captain": 5,
  "captain_details": {
    "user": {
      "first_name": "Jean",
      "last_name": "Dupont",
      "profile_picture": "https://example.com/captain.jpg"
    },
    "experience": "15 ans de navigation",
    "average_rating": 4.8,
    "total_trips": 237,
    "is_available": true
  },
  "notes": "Mon capitaine préféré",
  "created_at": "2025-05-25T12:30:45Z"
}
```

### 4.4. Liste des Emplacements Favoris
- **Endpoint**: GET /api/favorites/locations/
- **Description**: Récupérer la liste des emplacements favoris de l'utilisateur
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "name": "Domicile",
    "address": "23 rue de la Mer, 83990 Saint-Tropez",
    "coordinates": "43.2721,6.6410",
    "notes": "Facilement accessible",
    "created_at": "2025-05-25T13:05:23Z"
  }
]
```

### 4.5. Ajouter un Emplacement aux Favoris
- **Endpoint**: POST /api/favorites/locations/
- **Description**: Ajouter un emplacement à la liste des favoris
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "name": "Bureau",
  "address": "45 avenue de la République, 83990 Saint-Tropez",
  "coordinates": "43.2721,6.6410",
  "notes": "Proche du port"
}
```
- **Response (201 Created)**:
```json
{
  "id": 2,
  "name": "Bureau",
  "address": "45 avenue de la République, 83990 Saint-Tropez",
  "coordinates": "43.2721,6.6410",
  "notes": "Proche du port",
  "created_at": "2025-05-25T13:05:23Z"
}
```

### 4.6. Détails d'un Emplacement Favori
- **Endpoint**: GET /api/favorites/locations/{id}/
- **Description**: Récupérer les détails d'un emplacement favori spécifique
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "name": "Domicile",
  "address": "23 rue de la Mer, 83990 Saint-Tropez",
  "coordinates": "43.2721,6.6410",
  "notes": "Facilement accessible",
  "created_at": "2025-05-25T13:05:23Z"
}
```