"""
Script de test pour vérifier la sécurité des portefeuilles.
Ce script teste les race conditions et la sécurité des opérations financières.
"""

import os
import sys
import django
import threading
import time
from decimal import Decimal
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
User = get_user_model()
from payments.models import Wallet, Transaction
from payments.wallet_security_service import WalletSecurityService
from payments.exceptions import InsufficientFundsError, WalletSecurityError


class WalletSecurityTester:
    """Testeur de sécurité pour les portefeuilles"""

    def __init__(self):
        self.test_results = []
        self.errors = []

    def setup_test_user(self):
        """Créer un utilisateur de test"""
        try:
            user = User.objects.get(email='<EMAIL>')
            # Supprimer l'utilisateur existant pour recommencer
            user.delete()
        except User.DoesNotExist:
            pass

        user = User.objects.create_user(
            email='<EMAIL>',
            password='TestSecurity123!',
            first_name='Test',
            last_name='Security'
        )

        # Récupérer le portefeuille créé automatiquement
        wallet = Wallet.objects.get(user=user)

        # Ajouter des fonds initiaux de manière sécurisée
        WalletSecurityService.credit_wallet_secure(
            wallet_id=wallet.id,
            amount=Decimal('1000.00'),
            description="Fonds initiaux pour test de sécurité",
            reference="INIT_SECURITY_TEST",
            user=user
        )

        return user, wallet

    def test_race_condition_protection(self):
        """Test de protection contre les race conditions"""
        print("\n🧪 TEST: Protection contre les race conditions")
        print("-" * 50)

        user, wallet = self.setup_test_user()
        wallet.refresh_from_db()  # Recharger pour avoir le bon solde
        initial_balance = wallet.balance

        print(f"Solde initial: {initial_balance}€")

        # Fonction pour effectuer un débit concurrent
        def concurrent_debit(thread_id):
            try:
                result = WalletSecurityService.debit_wallet_secure(
                    wallet_id=wallet.id,
                    amount=Decimal('100.00'),
                    description=f"Test concurrent {thread_id}",
                    reference=f"RACE_TEST_{thread_id}",
                    user=user
                )
                return {'success': True, 'thread_id': thread_id, 'result': result}
            except InsufficientFundsError as e:
                return {'success': False, 'thread_id': thread_id, 'error': str(e)}
            except Exception as e:
                return {'success': False, 'thread_id': thread_id, 'error': str(e)}

        # Lancer 15 threads simultanés pour débiter 100€ chacun
        # Avec un solde de 1000€, seulement 10 devraient réussir
        num_threads = 15

        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(concurrent_debit, i) for i in range(num_threads)]
            results = [future.result() for future in as_completed(futures)]

        # Analyser les résultats
        successful_debits = [r for r in results if r['success']]
        failed_debits = [r for r in results if not r['success']]

        print(f"Débits réussis: {len(successful_debits)}")
        print(f"Débits échoués: {len(failed_debits)}")

        # Vérifier le solde final
        wallet.refresh_from_db()
        expected_balance = initial_balance - (len(successful_debits) * Decimal('100.00'))

        print(f"Solde final: {wallet.balance}€")
        print(f"Solde attendu: {expected_balance}€")

        # Vérifications
        if wallet.balance == expected_balance:
            print("✅ SUCCÈS: Le solde est cohérent")
            self.test_results.append("Race condition protection: PASSED")
        else:
            print("❌ ÉCHEC: Le solde n'est pas cohérent")
            self.test_results.append("Race condition protection: FAILED")
            self.errors.append(f"Solde incohérent: {wallet.balance}€ vs {expected_balance}€")

        if wallet.balance >= 0:
            print("✅ SUCCÈS: Aucun solde négatif")
            self.test_results.append("Negative balance prevention: PASSED")
        else:
            print("❌ ÉCHEC: Solde négatif détecté")
            self.test_results.append("Negative balance prevention: FAILED")
            self.errors.append(f"Solde négatif: {wallet.balance}€")

        # Vérifier que les échecs sont dus à un solde insuffisant
        insufficient_funds_errors = [r for r in failed_debits if "Solde insuffisant" in r.get('error', '')]
        if len(insufficient_funds_errors) == len(failed_debits):
            print("✅ SUCCÈS: Tous les échecs sont dus à un solde insuffisant")
            self.test_results.append("Proper error handling: PASSED")
        else:
            print("❌ ÉCHEC: Certains échecs ne sont pas dus à un solde insuffisant")
            self.test_results.append("Proper error handling: FAILED")
            for r in failed_debits:
                if "Solde insuffisant" not in r.get('error', ''):
                    self.errors.append(f"Erreur inattendue: {r.get('error', 'Unknown')}")

    def test_amount_validation(self):
        """Test de validation des montants"""
        print("\n🧪 TEST: Validation des montants")
        print("-" * 40)

        user, wallet = self.setup_test_user()

        # Test des montants invalides
        invalid_amounts = [
            Decimal('-10.00'),  # Négatif
            Decimal('0.00'),    # Zéro
            Decimal('100000.00'),  # Trop élevé
            "invalid",          # Non numérique
            None,               # None
        ]

        for amount in invalid_amounts:
            try:
                WalletSecurityService.debit_wallet_secure(
                    wallet_id=wallet.id,
                    amount=amount,
                    description="Test montant invalide",
                    reference="INVALID_AMOUNT_TEST",
                    user=user
                )
                print(f"❌ ÉCHEC: Montant invalide accepté: {amount}")
                self.test_results.append(f"Amount validation ({amount}): FAILED")
                self.errors.append(f"Montant invalide accepté: {amount}")
            except (ValueError, TypeError) as e:
                print(f"✅ SUCCÈS: Montant invalide rejeté: {amount} - {str(e)}")
                self.test_results.append(f"Amount validation ({amount}): PASSED")
            except Exception as e:
                # Pour les erreurs de conversion Decimal, c'est normal
                if "ConversionSyntax" in str(type(e)) or "InvalidOperation" in str(type(e)):
                    print(f"✅ SUCCÈS: Montant invalide rejeté: {amount} - Erreur de conversion")
                    self.test_results.append(f"Amount validation ({amount}): PASSED")
                else:
                    print(f"⚠️  ERREUR: Exception inattendue pour {amount}: {str(e)}")
                    self.errors.append(f"Exception inattendue pour {amount}: {str(e)}")

    def test_transaction_atomicity(self):
        """Test de l'atomicité des transactions"""
        print("\n🧪 TEST: Atomicité des transactions")
        print("-" * 40)

        user, wallet = self.setup_test_user()
        wallet.refresh_from_db()  # Recharger pour avoir le bon solde
        initial_balance = wallet.balance
        initial_transaction_count = Transaction.objects.filter(wallet=wallet).count()

        print(f"Solde initial: {initial_balance}€")
        print(f"Transactions initiales: {initial_transaction_count}")

        # Test d'un débit réussi
        try:
            result = WalletSecurityService.debit_wallet_secure(
                wallet_id=wallet.id,
                amount=Decimal('50.00'),
                description="Test atomicité",
                reference="ATOMICITY_TEST",
                user=user
            )

            wallet.refresh_from_db()
            final_transaction_count = Transaction.objects.filter(wallet=wallet).count()

            if (wallet.balance == initial_balance - Decimal('50.00') and
                final_transaction_count == initial_transaction_count + 1):
                print("✅ SUCCÈS: Transaction atomique réussie")
                self.test_results.append("Transaction atomicity: PASSED")
            else:
                print("❌ ÉCHEC: Transaction non atomique")
                self.test_results.append("Transaction atomicity: FAILED")
                self.errors.append("Transaction non atomique lors du succès")

        except Exception as e:
            print(f"❌ ÉCHEC: Exception lors du test d'atomicité: {str(e)}")
            self.test_results.append("Transaction atomicity: FAILED")
            self.errors.append(f"Exception lors du test d'atomicité: {str(e)}")

    def test_concurrent_credit_debit(self):
        """Test de crédits et débits simultanés"""
        print("\n🧪 TEST: Crédits et débits simultanés")
        print("-" * 45)

        user, wallet = self.setup_test_user()
        wallet.refresh_from_db()  # Recharger pour avoir le bon solde
        initial_balance = wallet.balance

        print(f"Solde initial: {initial_balance}€")

        def concurrent_operation(operation_type, thread_id):
            try:
                if operation_type == 'credit':
                    result = WalletSecurityService.credit_wallet_secure(
                        wallet_id=wallet.id,
                        amount=Decimal('25.00'),
                        description=f"Test crédit concurrent {thread_id}",
                        reference=f"CONCURRENT_CREDIT_{thread_id}",
                        user=user
                    )
                else:  # debit
                    result = WalletSecurityService.debit_wallet_secure(
                        wallet_id=wallet.id,
                        amount=Decimal('25.00'),
                        description=f"Test débit concurrent {thread_id}",
                        reference=f"CONCURRENT_DEBIT_{thread_id}",
                        user=user
                    )
                return {'success': True, 'type': operation_type, 'thread_id': thread_id}
            except Exception as e:
                return {'success': False, 'type': operation_type, 'thread_id': thread_id, 'error': str(e)}

        # Lancer 10 crédits et 10 débits simultanés
        operations = []
        for i in range(10):
            operations.append(('credit', i))
            operations.append(('debit', i + 10))

        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(concurrent_operation, op_type, thread_id)
                      for op_type, thread_id in operations]
            results = [future.result() for future in as_completed(futures)]

        # Analyser les résultats
        successful_credits = [r for r in results if r['success'] and r['type'] == 'credit']
        successful_debits = [r for r in results if r['success'] and r['type'] == 'debit']
        failed_operations = [r for r in results if not r['success']]

        print(f"Crédits réussis: {len(successful_credits)}")
        print(f"Débits réussis: {len(successful_debits)}")
        print(f"Opérations échouées: {len(failed_operations)}")

        # Vérifier le solde final
        wallet.refresh_from_db()
        expected_balance = (initial_balance +
                          (len(successful_credits) * Decimal('25.00')) -
                          (len(successful_debits) * Decimal('25.00')))

        print(f"Solde final: {wallet.balance}€")
        print(f"Solde attendu: {expected_balance}€")

        if wallet.balance == expected_balance:
            print("✅ SUCCÈS: Opérations concurrentes cohérentes")
            self.test_results.append("Concurrent operations: PASSED")
        else:
            print("❌ ÉCHEC: Incohérence dans les opérations concurrentes")
            self.test_results.append("Concurrent operations: FAILED")
            self.errors.append(f"Solde incohérent après opérations concurrentes: {wallet.balance}€ vs {expected_balance}€")

    def run_all_tests(self):
        """Exécuter tous les tests de sécurité"""
        print("🚀 DÉMARRAGE DES TESTS DE SÉCURITÉ DES PORTEFEUILLES")
        print("=" * 60)

        start_time = time.time()

        # Exécuter tous les tests
        self.test_race_condition_protection()
        self.test_amount_validation()
        self.test_transaction_atomicity()
        self.test_concurrent_credit_debit()

        end_time = time.time()

        # Afficher le résumé
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DES TESTS DE SÉCURITÉ")
        print("=" * 60)

        passed_tests = [t for t in self.test_results if "PASSED" in t]
        failed_tests = [t for t in self.test_results if "FAILED" in t]

        print(f"✅ Tests réussis: {len(passed_tests)}")
        print(f"❌ Tests échoués: {len(failed_tests)}")
        print(f"⏱️  Temps d'exécution: {end_time - start_time:.2f} secondes")

        if failed_tests:
            print("\n❌ TESTS ÉCHOUÉS:")
            for test in failed_tests:
                print(f"  - {test}")

        if self.errors:
            print("\n🚨 ERREURS DÉTECTÉES:")
            for error in self.errors:
                print(f"  - {error}")

        if not failed_tests and not self.errors:
            print("\n🎉 TOUS LES TESTS DE SÉCURITÉ ONT RÉUSSI!")
            print("✅ Le système de portefeuilles est sécurisé pour la production")
        else:
            print("\n⚠️  ATTENTION: Des problèmes de sécurité ont été détectés")
            print("❌ NE PAS DÉPLOYER EN PRODUCTION sans corriger ces problèmes")

        return len(failed_tests) == 0 and len(self.errors) == 0


if __name__ == "__main__":
    tester = WalletSecurityTester()
    success = tester.run_all_tests()

    # Code de sortie pour les scripts automatisés
    sys.exit(0 if success else 1)
