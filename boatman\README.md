# 🚤 ESPACE BATELIER - APPLICATION DJANGO

## 📋 **DESCRIPTION**

L'application `boatman` fournit un espace complet pour les bateliers (capitaines) sur la plateforme Commodore. Elle permet aux bateliers de gérer leurs courses assignées par les établissements, consulter leurs gains, et gérer leur profil professionnel.

## 🎯 **FONCTIONNALITÉS PRINCIPALES**

### **1. Authentification sécurisée**
- Connexion avec email/mot de passe
- Récupération de mot de passe par email/SMS
- Changement de mot de passe sécurisé
- Gestion des tokens d'authentification

### **2. Tableau de bord complet**
- Vue d'ensemble des activités
- Statistiques financières
- Courses à venir
- Actions rapides

### **3. Gestion des courses**
- Liste des courses assignées
- Détails complets de chaque course
- Validation QR code client
- Démarrage et fin de course
- Suivi en temps réel

### **4. Gestion du profil**
- Informations personnelles
- Informations professionnelles
- Gestion du bateau
- Préférences de contact

### **5. Portefeuille et paiements**
- Consultation du solde
- Historique des transactions
- Retrait de fonds
- Gestion des méthodes de paiement

## 📁 **STRUCTURE DES FICHIERS**

```
boatman/
├── __init__.py
├── apps.py                 # Configuration de l'app
├── permissions.py          # Permissions personnalisées
├── signals.py             # Signaux Django
├── tests.py               # Tests unitaires
├── urls.py                # Configuration des URLs
├── views_auth.py          # Vues d'authentification
├── views_dashboard.py     # Vues du tableau de bord
├── views_shuttles.py      # Vues de gestion des courses
├── views_profile.py       # Vues de gestion du profil
├── views_wallet.py        # Vues du portefeuille
├── endpoints.txt          # Documentation des endpoints
└── README.md             # Cette documentation
```

## 🔗 **ENDPOINTS DISPONIBLES**

### **Authentification**
- `POST /api/boatman/login/` - Connexion
- `POST /api/boatman/forgot-password/` - Mot de passe oublié
- `POST /api/boatman/verify-code/` - Vérification code
- `POST /api/boatman/change-password/` - Changement mot de passe

### **Tableau de bord**
- `GET /api/boatman/dashboard/` - Tableau de bord
- `GET /api/boatman/availability/` - Statut disponibilité
- `POST /api/boatman/availability/` - Changer disponibilité

### **Courses**
- `GET /api/boatman/shuttles/` - Liste des courses
- `GET /api/boatman/shuttle/{id}/` - Détails course
- `POST /api/boatman/shuttle/{id}/validate-qr/` - Validation QR
- `POST /api/boatman/shuttle/{id}/start/` - Démarrer course
- `POST /api/boatman/shuttle/{id}/end/` - Terminer course
- `GET /api/boatman/shuttle/{id}/track/` - Suivre course

### **Profil**
- `GET /api/boatman/profile/` - Consulter profil
- `PATCH /api/boatman/profile/` - Modifier profil
- `GET /api/boatman/boat/` - Informations bateau
- `PATCH /api/boatman/boat/` - Modifier bateau

### **Portefeuille**
- `GET /api/boatman/wallet/` - Consulter portefeuille
- `POST /api/boatman/wallet/withdraw/` - Retirer fonds
- `GET /api/boatman/payment-methods/` - Méthodes paiement
- `POST /api/boatman/payment-methods/` - Ajouter méthode

## 🔐 **AUTHENTIFICATION ET PERMISSIONS**

### **Authentification requise**
Tous les endpoints (sauf login et forgot-password) nécessitent un token :
```
Authorization: Token <user_token>
```

### **Permissions personnalisées**
- `IsCaptain` : Vérifier que l'utilisateur est un capitaine
- `IsCaptainOwner` : Vérifier la propriété des ressources
- `CanManageTrips` : Gérer les courses
- `CanAccessWallet` : Accéder au portefeuille
- `CanStartTrip` : Démarrer une course
- `CanCompleteTrip` : Terminer une course

## 🔄 **INTÉGRATION AVEC LES AUTRES SYSTÈMES**

### **Flux de données principal**
1. **Établissement** reçoit une demande de navette gratuite
2. **Établissement** accepte et assigne un batelier
3. **Système** crée un `Trip` avec le batelier assigné
4. **Batelier** voit la course dans son espace
5. **Batelier** gère la course (validation QR, start, end)

### **Relations critiques**
- `Trip.captain` : Batelier assigné par l'établissement
- `Trip.trip_type` : `'NAVETTES_GRATUITES'` pour navettes
- `Trip.total_price` : `0.00` pour navettes gratuites
- `Trip.payment_status` : `'PAID'` (gratuit)

### **Applications liées**
- `establishments` : Assignation des bateliers
- `trips` : Gestion des courses
- `payments` : Portefeuille et transactions
- `notifications` : Notifications automatiques

## 🧪 **TESTS**

### **Exécuter les tests**
```bash
python manage.py test boatman
```

### **Couverture des tests**
- Tests d'authentification (login, forgot password, change password)
- Tests du tableau de bord
- Tests de gestion des courses
- Tests du portefeuille
- Tests de gestion du profil
- Tests des permissions

## 🔧 **CONFIGURATION**

### **Settings requis**
```python
# Dans settings.py
INSTALLED_APPS = [
    # ...
    'boatman',
]

# Configuration email
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
DEFAULT_FROM_EMAIL = '<EMAIL>'
FRONTEND_URL = 'https://app.commodore.com'
```

### **URLs**
```python
# Dans commodore/urls.py
urlpatterns = [
    # ...
    path('api/boatman/', include('boatman.urls')),
]
```

## 📊 **SIGNAUX AUTOMATIQUES**

### **Signaux implémentés**
- Création automatique du portefeuille pour nouveaux capitaines
- Notifications automatiques pour les mises à jour de courses
- Notifications de paiements reçus
- Suivi des changements de disponibilité

### **Fonctions utilitaires**
- `send_trip_reminder_notifications()` : Rappels de courses
- `send_daily_summary_notifications()` : Résumé quotidien

## 🚀 **DÉPLOIEMENT**

### **Migrations**
```bash
python manage.py makemigrations boatman
python manage.py migrate
```

### **Variables d'environnement**
```bash
# Email configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-password

# Frontend URL
FRONTEND_URL=https://app.commodore.com
```

## 🐛 **DÉBOGAGE**

### **Logs**
Les logs sont configurés dans `settings.py` :
```python
LOGGING = {
    'loggers': {
        'boatman': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
        },
    },
}
```

### **Erreurs courantes**
1. **403 Forbidden** : Utilisateur non capitaine
2. **404 Not Found** : Course non assignée au batelier
3. **400 Bad Request** : Action impossible (ex: démarrer course terminée)
4. **401 Unauthorized** : Token invalide ou expiré

## 📞 **SUPPORT**

Pour toute question ou problème :
- Consulter la documentation dans `endpoints.txt`
- Vérifier les tests dans `tests.py`
- Examiner les logs de l'application

## ✅ **STATUT DE DÉVELOPPEMENT**

- ✅ **Authentification** - Complet
- ✅ **Tableau de bord** - Complet
- ✅ **Gestion courses** - Complet
- ✅ **Gestion profil** - Complet
- ✅ **Portefeuille** - Complet
- ✅ **Tests unitaires** - Complet
- ✅ **Documentation** - Complète
- ✅ **Intégration** - Complète
- ✅ **Permissions** - Complètes
- ✅ **Signaux** - Complets

**L'application est prête pour la production !** 🎉
