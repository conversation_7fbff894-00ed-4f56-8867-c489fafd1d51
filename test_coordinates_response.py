#!/usr/bin/env python
"""
Test de la réponse avec coordonnées pour le front-end
"""

import os
import django
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, Client
from trips.models import ShuttleTripRequest
from trips.serializers import ShuttleTripRequestSerializer
from django.utils import timezone
from datetime import date, timedelta

def test_response_coordinates():
    print("🗺️  Test des coordonnées dans la réponse")
    print("=" * 50)
    
    # 1. Trouver un établissement avec coordonnées
    establishment = Establishment.objects.filter(
        longitude__isnull=False, 
        latitude__isnull=False
    ).first()
    
    if not establishment:
        print("❌ Aucun établissement avec coordonnées trouvé")
        return False
    
    print(f"✅ Établissement: {establishment.name}")
    print(f"   Coordonnées: {establishment.latitude}, {establishment.longitude}")
    
    # 2. Trouver un client
    client = Client.objects.first()
    if not client:
        print("❌ Aucun client trouvé")
        return False
    
    print(f"✅ Client: {client.user.email}")
    
    # 3. Créer une navette
    departure_location = {
        "city_name": "Port de Cannes",
        "coordinates": {
            "latitude": 43.5528,
            "longitude": 7.0174
        },
        "timestamp": timezone.now().isoformat()
    }
    
    shuttle = ShuttleTripRequest.objects.create(
        client=client,
        establishment=establishment,
        departure_location=departure_location,
        arrival_location={},  # Vide car on utilise les coordonnées de l'établissement
        passenger_count=2,
        departure_date=date.today() + timedelta(days=1),
        departure_time="19:30:00",
        message="Test coordonnées réponse"
    )
    
    # 4. Calculer la distance
    shuttle.calculate_distance()
    
    # 5. Sérialiser la réponse
    serializer = ShuttleTripRequestSerializer(shuttle)
    response_data = serializer.data
    
    print("\n📍 Coordonnées dans la réponse:")
    print(f"   Départ (client): {departure_location['coordinates']}")
    
    establishment_details = response_data.get('establishment_details', {})
    if establishment_details.get('coordinates'):
        print(f"   Destination (établissement): {establishment_details['coordinates']}")
    
    # 6. Simuler la réponse complète de la vue
    departure_coords = departure_location.get('coordinates', {})
    establishment_coords = {
        'latitude': float(establishment.latitude) if establishment.latitude else None,
        'longitude': float(establishment.longitude) if establishment.longitude else None
    }
    
    complete_response = {
        'trip_request': response_data,
        'message': 'Demande de navette créée. L\'établissement sera notifié.',
        'distance_to_establishment': f"{shuttle.distance_km} km",
        'coordinates': {
            'departure': departure_coords,
            'destination': establishment_coords
        },
        'route_info': {
            'departure_location': departure_location.get('city_name', 'Point de départ'),
            'destination_location': establishment.name,
            'distance_km': float(shuttle.distance_km) if shuttle.distance_km else 0
        }
    }
    
    print("\n🎯 Réponse complète pour le front-end:")
    print("   ✅ Coordonnées de départ:", complete_response['coordinates']['departure'])
    print("   ✅ Coordonnées de destination:", complete_response['coordinates']['destination'])
    print("   ✅ Informations de route:", complete_response['route_info'])
    
    print(f"\n📊 Distance calculée: {complete_response['distance_to_establishment']}")
    
    return True

if __name__ == "__main__":
    success = test_response_coordinates()
    print(f"\n{'🎉 Test réussi!' if success else '❌ Test échoué'}")
