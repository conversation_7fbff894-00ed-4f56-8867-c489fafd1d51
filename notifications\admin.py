from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Notification, NotificationTemplate, Device

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('user', 'type', 'channel', 'title', 'is_read', 'delivery_status',
                   'created_at')
    list_filter = ('type', 'channel', 'is_read', 'delivery_status', 'created_at')
    search_fields = ('user__email', 'title', 'message')
    readonly_fields = ('created_at', 'sent_at', 'read_at')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (_('Destinataire'), {
            'fields': ('user',)
        }),
        (_('Contenu'), {
            'fields': ('type', 'channel', 'title', 'message')
        }),
        (_('Objet associé'), {
            'fields': ('content_type', 'object_id')
        }),
        (_('Statut'), {
            'fields': ('is_read', 'read_at', 'delivery_status', 'error_message')
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'sent_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'type', 'channel', 'is_active', 'created_at')
    list_filter = ('type', 'channel', 'is_active')
    search_fields = ('name', 'subject', 'content')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('Identification'), {
            'fields': ('name', 'type', 'channel')
        }),
        (_('Contenu'), {
            'fields': ('subject', 'content', 'variables')
        }),
        (_('Statut'), {
            'fields': ('is_active',)
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(Device)
class DeviceAdmin(admin.ModelAdmin):
    list_display = ('user', 'device_type', 'name', 'is_active', 'last_used')
    list_filter = ('device_type', 'is_active', 'created_at')
    search_fields = ('user__email', 'device_id', 'name')
    readonly_fields = ('created_at', 'last_used')
    
    fieldsets = (
        (_('Utilisateur'), {
            'fields': ('user',)
        }),
        (_('Appareil'), {
            'fields': ('device_id', 'device_type', 'name')
        }),
        (_('Push'), {
            'fields': ('push_token',)
        }),
        (_('Statut'), {
            'fields': ('is_active',)
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'last_used'),
            'classes': ('collapse',)
        })
    )
