from django.core.management.base import BaseCommand
from payments.models import Payment
from django.utils import timezone
from datetime import timedelta

class Command(BaseCommand):
    help = 'Liste les paiements en attente'

    def add_arguments(self, parser):
        parser.add_argument('--days', type=int, default=30, help='Nombre de jours à remonter')
        parser.add_argument('--status', type=str, default='PENDING', help='Statut des paiements à lister')
        parser.add_argument('--all', action='store_true', help='Lister tous les paiements')

    def handle(self, *args, **options):
        days = options['days']
        status = options['status']
        list_all = options['all']

        if list_all:
            payments = Payment.objects.all().order_by('-created_at')
        else:
            since_date = timezone.now() - timedelta(days=days)
            if status == 'ALL':
                payments = Payment.objects.filter(created_at__gte=since_date).order_by('-created_at')
            else:
                payments = Payment.objects.filter(status=status, created_at__gte=since_date).order_by('-created_at')

        if not payments:
            self.stdout.write(self.style.WARNING(f'Aucun paiement trouvé avec les critères spécifiés'))
            return

        # Afficher les paiements
        self.stdout.write(self.style.SUCCESS(f'Liste des paiements ({len(payments)} trouvés):'))
        self.stdout.write('-' * 100)
        self.stdout.write(f'{"ID":<36} | {"Montant":<10} | {"Type":<15} | {"Statut":<20} | {"Date de création":<20} | {"ID Stripe":<30}')
        self.stdout.write('-' * 100)

        for payment in payments:
            self.stdout.write(f'{str(payment.id):<36} | {payment.amount:<10.2f} | {payment.type:<15} | {payment.status:<20} | {payment.created_at.strftime("%d/%m/%Y %H:%M"):<20} | {payment.stripe_payment_id or "":<30}')

        self.stdout.write('-' * 100)
