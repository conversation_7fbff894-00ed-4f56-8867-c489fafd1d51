"""
Tests pour les sérialiseurs de l'application payments.

Ce module contient des tests unitaires pour vérifier que les sérialiseurs
de l'application payments fonctionnent correctement et renvoient toutes les
informations nécessaires pour l'intégration frontend.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from rest_framework.test import APIRequestFactory
from accounts.models import User, Client as Passenger, Captain
from payments.views import CreditWallet  # Utiliser notre classe d'adaptation

# Adaptations pour les modèles qui n'existent pas dans la structure actuelle
class ImpactStatistics:
    pass

class AvailabilityCalendar:
    pass
from boats.models import Boat
# Utiliser directement le modèle Trip de l'application trips
from trips.models import Trip
from payments.models import Payment, Transaction
from payments.serializers import PaymentSerializer, TransactionSerializer

User = get_user_model()

class PaymentSerializerTest(TestCase):
    """Tests pour le sérialiseur PaymentSerializer"""

    def setUp(self):
        """Initialisation des données de test"""
        # Créer un passager
        self.passenger_user = User.objects.create_user(
            username='passenger',
            email='<EMAIL>',
            password='passengerpass123'
        )

        # Créer les statistiques d'impact pour le passager
        self.impact_stats = ImpactStatistics.objects.create()

        self.passenger = Passenger.objects.create(
            user=self.passenger_user,
            impact_statistics=self.impact_stats
        )

        # Créer un capitaine et un bateau
        self.captain_user = User.objects.create_user(
            username='captain',
            email='<EMAIL>',
            password='captainpass123'
        )

        # Créer un calendrier de disponibilité pour le capitaine
        self.availability = AvailabilityCalendar.objects.create()

        self.captain = Captain.objects.create(
            user=self.captain_user,
            availability=self.availability,
            status='ACTIVE'
        )

        self.boat = Boat.objects.create(
            name='Test Boat',
            type='YACHT',
            capacity=10,
            captain=self.captain,
            current_location=Point(43.296482, 5.369780)
        )

        # Créer une réservation
        self.booking = Trip.objects.create(
            passenger=self.passenger,
            boat=self.boat,
            start_location=Point(43.296482, 5.369780),
            end_location=Point(43.306482, 5.379780),
            trip_type='LAND_TO_LAND',
            booking_type='IMMEDIATE',
            duration=30,
            estimated_price=50.0,
            status='PENDING'
        )

        # Créer un paiement
        self.payment = Payment.objects.create(
            booking=self.booking,
            amount=50.0,
            type='TRIP',
            status='COMPLETED',
            stripe_payment_id='pi_123456',
            stripe_customer_id='cus_123456',
            stripe_payment_method_id='pm_123456',
            stripe_payment_method_type='card',
            receipt_url='https://receipt.url',
            metadata={'test': 'true'}
        )

        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')
        self.context = {'request': self.request}
        self.serializer = PaymentSerializer(
            instance=self.payment,
            context=self.context
        )

    def test_contains_expected_fields(self):
        """Vérifie que le sérialiseur contient tous les champs attendus"""
        data = self.serializer.data
        self.assertIn('id', data)
        self.assertIn('booking', data)
        self.assertIn('booking_details', data)
        self.assertIn('amount', data)
        self.assertIn('type', data)
        self.assertIn('status', data)
        self.assertIn('stripe_payment_id', data)
        self.assertIn('stripe_customer_id', data)
        self.assertIn('stripe_payment_method_id', data)
        self.assertIn('stripe_payment_method_type', data)
        self.assertIn('receipt_url', data)
        self.assertIn('refund_id', data)
        self.assertIn('refund_amount', data)
        self.assertIn('refund_reason', data)
        self.assertIn('metadata', data)
        self.assertIn('created_at', data)

    def test_booking_details_field(self):
        """Vérifie que le champ booking_details est correctement calculé"""
        data = self.serializer.data
        self.assertEqual(data['booking_details']['id'], self.booking.id)
        self.assertEqual(data['booking_details']['status'], 'PENDING')
        self.assertEqual(data['booking_details']['estimated_price'], 50.0)
        self.assertEqual(data['booking_details']['passenger'], self.passenger.id)
        self.assertEqual(data['booking_details']['boat'], self.boat.id)

    def test_refund_fields(self):
        """Vérifie que les champs de remboursement sont correctement gérés"""
        # Par défaut, pas de remboursement
        data = self.serializer.data
        self.assertIsNone(data['refund_id'])
        self.assertIsNone(data['refund_amount'])
        self.assertIsNone(data['refund_reason'])

        # Ajouter un remboursement
        self.payment.refund_id = 're_123456'
        self.payment.refund_amount = 25.0
        self.payment.refund_reason = 'customer_requested'
        self.payment.status = 'PARTIALLY_REFUNDED'
        self.payment.save()

        serializer = PaymentSerializer(
            instance=self.payment,
            context=self.context
        )
        data = serializer.data
        self.assertEqual(data['refund_id'], 're_123456')
        self.assertEqual(data['refund_amount'], 25.0)
        self.assertEqual(data['refund_reason'], 'customer_requested')
        self.assertEqual(data['status'], 'PARTIALLY_REFUNDED')

class TransactionSerializerTest(TestCase):
    """Tests pour le sérialiseur TransactionSerializer"""

    def setUp(self):
        """Initialisation des données de test"""
        # Créer un utilisateur et un portefeuille
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.wallet = CreditWallet.objects.create(
            balance=100.0
        )

        # Créer une transaction
        self.transaction = Transaction.objects.create(
            wallet=self.wallet,
            amount=50.0,
            type='PURCHASE'
        )

        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')
        self.context = {'request': self.request}
        self.serializer = TransactionSerializer(
            instance=self.transaction,
            context=self.context
        )

    def test_contains_expected_fields(self):
        """Vérifie que le sérialiseur contient tous les champs attendus"""
        data = self.serializer.data
        self.assertIn('id', data)
        self.assertIn('wallet', data)
        self.assertIn('wallet_details', data)
        self.assertIn('amount', data)
        self.assertIn('type', data)
        self.assertIn('timestamp', data)

    def test_wallet_details_field(self):
        """Vérifie que le champ wallet_details est correctement calculé"""
        data = self.serializer.data
        self.assertEqual(data['wallet_details']['id'], self.wallet.id)
        self.assertEqual(data['wallet_details']['balance'], 100.0)
