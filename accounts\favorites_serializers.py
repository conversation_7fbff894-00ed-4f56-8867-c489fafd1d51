from rest_framework import serializers
from .favorites import FavoriteLocation, FavoriteCaptain
from .serializers import UserSerializer, CaptainSerializer

class FavoriteLocationSerializer(serializers.ModelSerializer):
    """Sérialiseur pour le modèle FavoriteLocation."""
    
    class Meta:
        model = FavoriteLocation
        fields = ['id', 'name', 'address', 'coordinates', 'notes', 'created_at']
        read_only_fields = ['id', 'created_at']
    
    def create(self, validated_data):
        user = self.context['request'].user
        return FavoriteLocation.objects.create(user=user, **validated_data)

class FavoriteCaptainSerializer(serializers.ModelSerializer):
    """Sérialiseur pour le modèle FavoriteCaptain."""
    
    captain_details = CaptainSerializer(source='captain', read_only=True)
    
    class Meta:
        model = FavoriteCaptain
        fields = ['id', 'captain', 'captain_details', 'notes', 'created_at']
        read_only_fields = ['id', 'created_at']
    
    def create(self, validated_data):
        user = self.context['request'].user
        return FavoriteCaptain.objects.create(user=user, **validated_data)
