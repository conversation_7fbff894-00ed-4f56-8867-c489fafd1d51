"""
Management command pour configurer les tâches périodiques des courses
"""

from django.core.management.base import BaseCommand
from django_celery_beat.models import PeriodicTask, IntervalSchedule, CrontabSchedule
import json


class Command(BaseCommand):
    help = 'Configure les tâches périodiques pour l\'application trips'

    def handle(self, *args, **options):
        self.stdout.write('Configuration des tâches périodiques...')

        # Tâche de nettoyage des demandes expirées (toutes les 5 minutes)
        interval_5min, created = IntervalSchedule.objects.get_or_create(
            every=5,
            period=IntervalSchedule.MINUTES,
        )
        
        cleanup_task, created = PeriodicTask.objects.get_or_create(
            name='Nettoyage des demandes expirées',
            defaults={
                'interval': interval_5min,
                'task': 'trips.tasks.cleanup_expired_trip_requests',
                'enabled': True,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Tâche de nettoyage créée'))
        else:
            self.stdout.write('✓ Tâche de nettoyage déjà configurée')

        # Tâche de rappels (toutes les heures)
        interval_1hour, created = IntervalSchedule.objects.get_or_create(
            every=1,
            period=IntervalSchedule.HOURS,
        )
        
        reminder_task, created = PeriodicTask.objects.get_or_create(
            name='Rappels de courses',
            defaults={
                'interval': interval_1hour,
                'task': 'trips.tasks.send_trip_reminder_notifications',
                'enabled': True,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Tâche de rappels créée'))
        else:
            self.stdout.write('✓ Tâche de rappels déjà configurée')

        # Tâche de vérification des retards (toutes les 15 minutes)
        interval_15min, created = IntervalSchedule.objects.get_or_create(
            every=15,
            period=IntervalSchedule.MINUTES,
        )
        
        delay_task, created = PeriodicTask.objects.get_or_create(
            name='Vérification des retards',
            defaults={
                'interval': interval_15min,
                'task': 'trips.tasks.check_delayed_trips',
                'enabled': True,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Tâche de vérification des retards créée'))
        else:
            self.stdout.write('✓ Tâche de vérification des retards déjà configurée')

        # Tâche de mise à jour des statistiques (quotidienne à 2h du matin)
        crontab_daily, created = CrontabSchedule.objects.get_or_create(
            minute=0,
            hour=2,
            day_of_week='*',
            day_of_month='*',
            month_of_year='*',
        )
        
        stats_task, created = PeriodicTask.objects.get_or_create(
            name='Mise à jour des statistiques',
            defaults={
                'crontab': crontab_daily,
                'task': 'trips.tasks.update_trip_statistics',
                'enabled': True,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Tâche de statistiques créée'))
        else:
            self.stdout.write('✓ Tâche de statistiques déjà configurée')

        # Tâche de rapport quotidien (tous les jours à minuit)
        crontab_midnight, created = CrontabSchedule.objects.get_or_create(
            minute=0,
            hour=0,
            day_of_week='*',
            day_of_month='*',
            month_of_year='*',
        )
        
        report_task, created = PeriodicTask.objects.get_or_create(
            name='Rapport quotidien',
            defaults={
                'crontab': crontab_midnight,
                'task': 'trips.tasks.generate_daily_report',
                'enabled': True,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Tâche de rapport quotidien créée'))
        else:
            self.stdout.write('✓ Tâche de rapport quotidien déjà configurée')

        self.stdout.write(
            self.style.SUCCESS('Configuration des tâches périodiques terminée !')
        )
