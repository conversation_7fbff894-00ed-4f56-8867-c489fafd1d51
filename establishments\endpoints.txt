# 🏢 DOCUMENTATION COMPLÈTE - ESPACE ÉTABLISSEMENT

## 📋 **INFORMATIONS GÉNÉRALES**

### **Base URL**
```
https://api.commodore.com/api/establishments/
```

### **Authentification**
Tous les endpoints nécessitent un token d'authentification dans le header :
```
Authorization: Token <user_token>
Content-Type: application/json
```

### **Format des réponses**
Toutes les réponses suivent le format standardisé :
```json
{
    "status": "success|error",
    "data": {...},
    "message": "Message descriptif",
    "timestamp": "2024-06-15T10:30:00Z"
}
```

---

## 📊 **1. TABLEAU DE BORD**

### **GET /api/establishments/dashboard/**
Récupérer les données complètes du tableau de bord de l'établissement.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "establishment_name": "Hotel Paradise Beach",
        "establishment_type": "Hôtel",
        "establishment_id": 42,
        "available_balance": 2847.50,
        "total_shuttles": 127,
        "pending_requests": 8,
        "availability": true,
        "statistics": {
            "shuttles_today": 5,
            "shuttles_this_week": 23,
            "shuttles_this_month": 89,
            "total_boatmen": 12,
            "active_boatmen": 8,
            "average_rating": 4.7
        },
        "recent_shuttles": [
            {
                "shuttle_id": "shlt_789123",
                "date": "2024-06-15T16:30:00Z",
                "status": "À venir",
                "departure": "Aéroport Nice Côte d'Azur",
                "destination": "Hotel Paradise Beach",
                "client": {
                    "name": "John Doe",
                    "phone": "+33123456789",
                    "email": "<EMAIL>"
                },
                "captain": {
                    "name": "Captain Jack",
                    "phone": "+33987654321",
                    "boat": "Sea Explorer"
                },
                "passengers": 3,
                "amount": 0.00,
                "estimated_duration": 25,
                "created_at": "2024-06-15T14:20:00Z"
            },
            {
                "shuttle_id": "shlt_789124",
                "date": "2024-06-15T18:00:00Z",
                "status": "En cours",
                "departure": "Hotel Paradise Beach",
                "destination": "Port de Cannes",
                "client": {
                    "name": "Marie Dubois",
                    "phone": "+33234567890",
                    "email": "<EMAIL>"
                },
                "captain": {
                    "name": "Captain Pierre",
                    "phone": "+33876543210",
                    "boat": "Ocean Dream"
                },
                "passengers": 2,
                "amount": 0.00,
                "estimated_duration": 30,
                "created_at": "2024-06-15T17:45:00Z"
            }
        ],
        "financial_summary": {
            "expenses_this_month": 450.00,
            "savings_from_shuttles": 2340.00,
            "average_cost_per_shuttle": 15.50
        }
    },
    "message": "Données du tableau de bord récupérées avec succès",
    "timestamp": "2024-06-15T10:30:00Z"
}
```

**Réponse erreur - Accès refusé (403) :**
```json
{
    "status": "error",
    "error": "Accès refusé - Utilisateur non autorisé",
    "error_code": 4001,
    "message": "Seuls les établissements peuvent accéder à cette ressource",
    "timestamp": "2024-06-15T10:30:00Z"
}
```

**Réponse erreur - Non authentifié (401) :**
```json
{
    "status": "error",
    "error": "Token d'authentification invalide ou manquant",
    "error_code": 401,
    "message": "Veuillez vous connecter pour accéder à cette ressource",
    "timestamp": "2024-06-15T10:30:00Z"
}
```

---

## 🔄 **2. GESTION DE LA DISPONIBILITÉ**

### **POST /api/establishments/toggle-availability/**
Basculer la disponibilité de l'établissement pour accepter de nouvelles demandes de navettes.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Données à envoyer :**
```json
{
    "availability": true
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "availability": true,
        "updated_at": "2024-06-15T10:35:00Z"
    },
    "message": "Disponibilité mise à jour avec succès",
    "timestamp": "2024-06-15T10:35:00Z"
}
```

**Exemple - Désactiver la disponibilité :**
```json
{
    "availability": false
}
```

**Réponse - Disponibilité désactivée :**
```json
{
    "status": "success",
    "data": {
        "availability": false,
        "updated_at": "2024-06-15T10:36:00Z",
        "pending_requests_count": 3,
        "warning": "3 demandes en attente seront automatiquement rejetées"
    },
    "message": "Établissement mis hors ligne",
    "timestamp": "2024-06-15T10:36:00Z"
}
```

**Réponse erreur - Données invalides (400) :**
```json
{
    "status": "error",
    "error": "Le paramètre availability est requis",
    "error_code": 400,
    "message": "Veuillez fournir un statut de disponibilité valide (true/false)",
    "timestamp": "2024-06-15T10:35:00Z"
}
```

---

## 🚐 **3. GESTION DES NAVETTES**

### **GET /api/establishments/shuttles/**
Récupérer toutes les navettes de l'établissement avec filtres avancés et pagination.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Paramètres URL :**
- `filter` : "En attente", "À venir", "En cours", "Terminé", "Annulé"
- `page` : Numéro de page (défaut: 1)
- `limit` : Nombre d'éléments par page (défaut: 20, max: 100)
- `date_from` : Date de début (format: YYYY-MM-DD)
- `date_to` : Date de fin (format: YYYY-MM-DD)
- `search` : Recherche par nom client ou destination

**Exemples d'URLs :**
```
GET /api/establishments/shuttles/
GET /api/establishments/shuttles/?filter=En attente&page=1&limit=10
GET /api/establishments/shuttles/?date_from=2024-06-01&date_to=2024-06-30
GET /api/establishments/shuttles/?search=aéroport&filter=Terminé
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "shuttles": [
            {
                "shuttle_id": "shlt_789123",
                "date": "2024-06-15",
                "time": "16:30",
                "status": "En attente",
                "departure": {
                    "name": "Aéroport Nice Côte d'Azur",
                    "coordinates": {
                        "latitude": 43.6584,
                        "longitude": 7.2159
                    },
                    "address": "Terminal 1, Nice"
                },
                "destination": {
                    "name": "Hotel Paradise Beach",
                    "coordinates": {
                        "latitude": 43.5528,
                        "longitude": 7.0174
                    },
                    "address": "123 Promenade des Anglais, Cannes"
                },
                "client": {
                    "name": "John Doe",
                    "phone": "+33123456789",
                    "email": "<EMAIL>",
                    "profile_picture": "https://aws.s3.com/profiles/john_doe.jpg"
                },
                "captain": {
                    "id": 42,
                    "name": "Captain Jack",
                    "phone": "+33987654321",
                    "rating": 4.8,
                    "boat": {
                        "name": "Sea Explorer",
                        "capacity": 8,
                        "type": "classic"
                    }
                },
                "passengers": 3,
                "amount": 0.00,
                "estimated_duration": 25,
                "distance_km": 12.5,
                "special_requests": "Vol Air France AF1234, arrivée 16h15",
                "created_at": "2024-06-15T14:20:00Z",
                "updated_at": "2024-06-15T14:25:00Z"
            },
            {
                "shuttle_id": "shlt_789124",
                "date": "2024-06-15",
                "time": "18:00",
                "status": "À venir",
                "departure": {
                    "name": "Hotel Paradise Beach",
                    "coordinates": {
                        "latitude": 43.5528,
                        "longitude": 7.0174
                    },
                    "address": "123 Promenade des Anglais, Cannes"
                },
                "destination": {
                    "name": "Port de Cannes",
                    "coordinates": {
                        "latitude": 43.5511,
                        "longitude": 7.0128
                    },
                    "address": "Quai Saint-Pierre, Cannes"
                },
                "client": {
                    "name": "Marie Dubois",
                    "phone": "+33234567890",
                    "email": "<EMAIL>",
                    "profile_picture": "https://aws.s3.com/profiles/marie_dubois.jpg"
                },
                "captain": {
                    "id": 38,
                    "name": "Captain Pierre",
                    "phone": "+33876543210",
                    "rating": 4.6,
                    "boat": {
                        "name": "Ocean Dream",
                        "capacity": 6,
                        "type": "speedboat"
                    }
                },
                "passengers": 2,
                "amount": 0.00,
                "estimated_duration": 15,
                "distance_km": 3.2,
                "special_requests": "",
                "created_at": "2024-06-15T17:45:00Z",
                "updated_at": "2024-06-15T17:50:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 10,
            "total": 47,
            "total_pages": 5,
            "has_next": true,
            "has_previous": false
        },
        "filters_applied": {
            "status_filter": "En attente",
            "date_range": null,
            "search_term": null
        },
        "summary": {
            "total_shuttles": 47,
            "pending": 8,
            "in_progress": 3,
            "completed": 34,
            "cancelled": 2
        }
    },
    "message": "Navettes récupérées avec succès",
    "timestamp": "2024-06-15T10:40:00Z"
}
```

**Réponse - Aucune navette trouvée (200) :**
```json
{
    "status": "success",
    "data": {
        "shuttles": [],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 0,
            "total_pages": 0,
            "has_next": false,
            "has_previous": false
        },
        "filters_applied": {
            "status_filter": "En cours",
            "date_range": null,
            "search_term": null
        },
        "summary": {
            "total_shuttles": 0,
            "pending": 0,
            "in_progress": 0,
            "completed": 0,
            "cancelled": 0
        }
    },
    "message": "Aucune navette trouvée avec les critères spécifiés",
    "timestamp": "2024-06-15T10:40:00Z"
}
```

### **GET /api/establishments/shuttle-requests/**
Récupérer toutes les demandes de navettes en attente pour l'établissement.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Paramètres URL optionnels :**
- `priority` : "high", "normal", "low" (filtrer par priorité)
- `date` : "today", "tomorrow", "week" (filtrer par période)

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "requests": [
            {
                "request_id": "req_456789",
                "client": {
                    "name": "John Doe",
                    "phone": "+33123456789",
                    "email": "<EMAIL>",
                    "profile_picture": "https://aws.s3.com/profiles/john_doe.jpg",
                    "rating": 4.2,
                    "total_trips": 15
                },
                "departure": {
                    "name": "Aéroport Nice Côte d'Azur",
                    "coordinates": {
                        "latitude": 43.6584,
                        "longitude": 7.2159
                    },
                    "address": "Terminal 1, Nice",
                    "timestamp": "2024-06-15T16:15:00Z"
                },
                "destination": {
                    "name": "Hotel Paradise Beach",
                    "coordinates": {
                        "latitude": 43.5528,
                        "longitude": 7.0174
                    },
                    "address": "123 Promenade des Anglais, Cannes",
                    "timestamp": "2024-06-15T16:45:00Z"
                },
                "date": "2024-06-15",
                "time": "16:30",
                "passengers": 3,
                "estimated_duration": 25,
                "distance_km": 12.5,
                "priority": "high",
                "message": "Vol Air France AF1234, arrivée 16h15. Famille avec enfant en bas âge.",
                "special_requirements": [
                    "Siège bébé requis",
                    "Bagages volumineux"
                ],
                "created_at": "2024-06-15T14:20:00Z",
                "expires_at": "2024-06-15T14:30:00Z",
                "time_remaining": "8 minutes"
            },
            {
                "request_id": "req_456790",
                "client": {
                    "name": "Marie Dubois",
                    "phone": "+33234567890",
                    "email": "<EMAIL>",
                    "profile_picture": "https://aws.s3.com/profiles/marie_dubois.jpg",
                    "rating": 4.8,
                    "total_trips": 32
                },
                "departure": {
                    "name": "Hotel Paradise Beach",
                    "coordinates": {
                        "latitude": 43.5528,
                        "longitude": 7.0174
                    },
                    "address": "123 Promenade des Anglais, Cannes",
                    "timestamp": "2024-06-15T18:00:00Z"
                },
                "destination": {
                    "name": "Gare SNCF Cannes",
                    "coordinates": {
                        "latitude": 43.5522,
                        "longitude": 7.0158
                    },
                    "address": "1 Place de la Gare, Cannes",
                    "timestamp": "2024-06-15T18:15:00Z"
                },
                "date": "2024-06-15",
                "time": "18:00",
                "passengers": 1,
                "estimated_duration": 10,
                "distance_km": 2.1,
                "priority": "normal",
                "message": "Train TGV 18h45 pour Paris",
                "special_requirements": [],
                "created_at": "2024-06-15T17:45:00Z",
                "expires_at": "2024-06-15T17:55:00Z",
                "time_remaining": "2 minutes"
            }
        ],
        "summary": {
            "total_pending": 2,
            "high_priority": 1,
            "normal_priority": 1,
            "low_priority": 0,
            "expiring_soon": 2
        }
    },
    "message": "Demandes de navettes récupérées avec succès",
    "timestamp": "2024-06-15T17:53:00Z"
}
```

**Réponse - Aucune demande (200) :**
```json
{
    "status": "success",
    "data": {
        "requests": [],
        "summary": {
            "total_pending": 0,
            "high_priority": 0,
            "normal_priority": 0,
            "low_priority": 0,
            "expiring_soon": 0
        }
    },
    "message": "Aucune demande de navette en attente",
    "timestamp": "2024-06-15T17:53:00Z"
}
```

### **POST /api/establishments/shuttle-requests/{request_id}/accept/**
Accepter une demande de navette et assigner un capitaine avec son bateau.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Paramètres URL :**
- `request_id` : ID de la demande de navette (requis)

**Données à envoyer :**
```json
{
    "captain_id": 42,
    "boat_id": 156,
    "estimated_pickup_time": "2024-06-15T16:25:00Z",
    "notes": "Capitaine Jack confirmé, bateau Sea Explorer prêt"
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "trip_id": "trip_789123",
        "shuttle_request_id": "req_456789",
        "assignment": {
            "captain": {
                "id": 42,
                "name": "Captain Jack",
                "phone": "+33987654321",
                "rating": 4.8,
                "profile_picture": "https://aws.s3.com/profiles/captain_jack.jpg"
            },
            "boat": {
                "id": 156,
                "name": "Sea Explorer",
                "capacity": 8,
                "type": "classic",
                "registration": "FR123456",
                "photos": [
                    "https://aws.s3.com/boats/sea_explorer_1.jpg",
                    "https://aws.s3.com/boats/sea_explorer_2.jpg"
                ]
            },
            "estimated_pickup_time": "2024-06-15T16:25:00Z",
            "estimated_arrival_time": "2024-06-15T16:50:00Z"
        },
        "client_notification_sent": true,
        "captain_notification_sent": true,
        "tracking_url": "https://app.commodore.com/track/trip_789123"
    },
    "message": "Demande acceptée et course créée avec succès",
    "timestamp": "2024-06-15T15:30:00Z"
}
```

**Réponse erreur - Demande non trouvée (404) :**
```json
{
    "status": "error",
    "error": "Demande non trouvée ou déjà traitée",
    "error_code": 404,
    "message": "La demande de navette spécifiée n'existe pas ou a déjà été acceptée/rejetée",
    "timestamp": "2024-06-15T15:30:00Z"
}
```

**Réponse erreur - Capitaine/Bateau indisponible (400) :**
```json
{
    "status": "error",
    "error": "Ressources non disponibles",
    "error_code": 4003,
    "details": {
        "captain_available": false,
        "boat_available": true,
        "captain_current_status": "EN_COURSE",
        "next_available_time": "2024-06-15T17:30:00Z"
    },
    "message": "Le capitaine sélectionné n'est pas disponible à cette heure",
    "timestamp": "2024-06-15T15:30:00Z"
}
```

### **POST /api/establishments/shuttle-requests/{request_id}/reject/**
Rejeter une demande de navette avec une raison spécifique.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Paramètres URL :**
- `request_id` : ID de la demande de navette (requis)

**Données à envoyer :**
```json
{
    "reason": "Aucun bateau disponible à cette heure",
    "alternative_suggestions": [
        {
            "time": "2024-06-15T17:00:00Z",
            "message": "Navette disponible à 17h00"
        },
        {
            "time": "2024-06-15T18:30:00Z",
            "message": "Navette disponible à 18h30"
        }
    ],
    "offer_alternative": true
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "shuttle_request_id": "req_456789",
        "rejection_reason": "Aucun bateau disponible à cette heure",
        "alternative_suggestions": [
            {
                "time": "2024-06-15T17:00:00Z",
                "message": "Navette disponible à 17h00"
            },
            {
                "time": "2024-06-15T18:30:00Z",
                "message": "Navette disponible à 18h30"
            }
        ],
        "client_notification_sent": true,
        "rejection_timestamp": "2024-06-15T15:35:00Z"
    },
    "message": "Demande rejetée avec alternatives proposées",
    "timestamp": "2024-06-15T15:35:00Z"
}
```

**Réponse - Rejet simple sans alternatives :**
```json
{
    "status": "success",
    "data": {
        "shuttle_request_id": "req_456789",
        "rejection_reason": "Conditions météorologiques défavorables",
        "alternative_suggestions": [],
        "client_notification_sent": true,
        "rejection_timestamp": "2024-06-15T15:35:00Z"
    },
    "message": "Demande rejetée",
    "timestamp": "2024-06-15T15:35:00Z"
}
```

### **GET /api/establishments/available-resources/**
Récupérer toutes les ressources disponibles (bateaux et capitaines) pour assigner aux navettes.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Paramètres URL optionnels :**
- `time` : Heure souhaitée (format ISO) pour vérifier la disponibilité
- `capacity_min` : Capacité minimale requise
- `boat_type` : Type de bateau souhaité ("classic", "speedboat", "yacht", "catamaran")

**Exemple :** `/api/establishments/available-resources/?time=2024-06-15T16:30:00Z&capacity_min=4`

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "available_boats": [
            {
                "id": 156,
                "name": "Sea Explorer",
                "capacity": 8,
                "boat_type": "classic",
                "registration_number": "FR123456",
                "captain": {
                    "id": 42,
                    "name": "Captain Jack",
                    "rating": 4.8
                },
                "photos": [
                    "https://aws.s3.com/boats/sea_explorer_1.jpg",
                    "https://aws.s3.com/boats/sea_explorer_2.jpg"
                ],
                "features": [
                    "Climatisation",
                    "Système audio",
                    "Gilets de sauvetage",
                    "Trousse de secours"
                ],
                "current_location": {
                    "latitude": 43.5528,
                    "longitude": 7.0174,
                    "address": "Port de Cannes"
                },
                "availability_until": "2024-06-15T20:00:00Z"
            },
            {
                "id": 157,
                "name": "Ocean Dream",
                "capacity": 6,
                "boat_type": "speedboat",
                "registration_number": "FR789012",
                "captain": {
                    "id": 38,
                    "name": "Captain Pierre",
                    "rating": 4.6
                },
                "photos": [
                    "https://aws.s3.com/boats/ocean_dream_1.jpg"
                ],
                "features": [
                    "Haute vitesse",
                    "Système GPS",
                    "Gilets de sauvetage"
                ],
                "current_location": {
                    "latitude": 43.5511,
                    "longitude": 7.0128,
                    "address": "Marina Cannes"
                },
                "availability_until": "2024-06-15T19:30:00Z"
            }
        ],
        "available_captains": [
            {
                "id": 42,
                "name": "Captain Jack",
                "experience": "8 ans d'expérience en navigation côtière",
                "license_number": "LIC001",
                "average_rating": 4.8,
                "total_trips": 342,
                "languages": ["Français", "Anglais", "Italien"],
                "specialties": ["Navigation nocturne", "Excursions touristiques"],
                "profile_picture": "https://aws.s3.com/profiles/captain_jack.jpg",
                "phone": "+33987654321",
                "current_status": "AVAILABLE",
                "next_unavailable": "2024-06-15T20:00:00Z"
            },
            {
                "id": 38,
                "name": "Captain Pierre",
                "experience": "5 ans d'expérience",
                "license_number": "LIC002",
                "average_rating": 4.6,
                "total_trips": 198,
                "languages": ["Français", "Anglais"],
                "specialties": ["Navigation rapide", "Transport aéroport"],
                "profile_picture": "https://aws.s3.com/profiles/captain_pierre.jpg",
                "phone": "+33876543210",
                "current_status": "AVAILABLE",
                "next_unavailable": "2024-06-15T19:30:00Z"
            }
        ],
        "summary": {
            "total_boats_available": 2,
            "total_captains_available": 2,
            "max_capacity_available": 8,
            "boat_types_available": ["classic", "speedboat"]
        }
    },
    "message": "Ressources disponibles récupérées avec succès",
    "timestamp": "2024-06-15T15:40:00Z"
}
```

**Réponse - Aucune ressource disponible (200) :**
```json
{
    "status": "success",
    "data": {
        "available_boats": [],
        "available_captains": [],
        "summary": {
            "total_boats_available": 0,
            "total_captains_available": 0,
            "max_capacity_available": 0,
            "boat_types_available": []
        },
        "next_availability": {
            "time": "2024-06-15T18:00:00Z",
            "boats_count": 3,
            "captains_count": 4
        }
    },
    "message": "Aucune ressource disponible pour le créneau demandé",
    "timestamp": "2024-06-15T15:40:00Z"
}
```

---

## 👨‍⚓ **4. GESTION DES BATELIERS**

### **POST /api/establishments/register-boatman/**
Enregistrer un nouveau batelier avec génération automatique de mot de passe et envoi d'email.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Données à envoyer :**
```json
{
    "email": "<EMAIL>",
    "first_name": "Jean",
    "last_name": "Dupont",
    "phone_number": "+33123456789",
    "experience": "5 ans d'expérience en navigation côtière et plaisance",
    "license_number": "LIC123456",
    "boat_preferences": {
        "preferred_type": "classic",
        "max_capacity": 8,
        "special_equipment": ["Système audio", "Climatisation"]
    },
    "availability": {
        "days": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"],
        "hours": {
            "start": "08:00",
            "end": "20:00"
        }
    },
    "emergency_contact": {
        "name": "Marie Dupont",
        "phone": "+33987654321",
        "relationship": "Épouse"
    }
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "captain": {
            "id": 89,
            "user_id": 234,
            "name": "Jean Dupont",
            "email": "<EMAIL>",
            "phone": "+33123456789",
            "experience": "5 ans d'expérience en navigation côtière et plaisance",
            "license_number": "LIC123456",
            "profile_picture": null,
            "availability_status": "AVAILABLE",
            "registration_date": "2024-06-15T16:00:00Z"
        },
        "boat": {
            "id": 178,
            "name": "Bateau de Jean Dupont",
            "capacity": 6,
            "boat_type": "classic",
            "registration_number": null,
            "is_available": true,
            "zone_served": "Zone par défaut",
            "radius": 20
        },
        "credentials": {
            "email": "<EMAIL>",
            "temporary_password": "Kj8mN2pQ",
            "login_url": "https://app.commodore.com/captain/login",
            "password_expires_at": "2024-06-22T16:00:00Z"
        },
        "email_notification": {
            "sent": true,
            "sent_at": "2024-06-15T16:00:05Z",
            "recipient": "<EMAIL>",
            "subject": "Bienvenue chez Hotel Paradise Beach - Vos identifiants Commodore"
        },
        "next_steps": [
            "Le batelier doit se connecter avec ses identifiants temporaires",
            "Changement de mot de passe obligatoire à la première connexion",
            "Configuration du profil et ajout de photos",
            "Validation des informations du bateau"
        ]
    },
    "message": "Batelier enregistré avec succès et email envoyé",
    "timestamp": "2024-06-15T16:00:00Z"
}
```

**Réponse erreur - Email déjà existant (400) :**
```json
{
    "status": "error",
    "error": "Un utilisateur avec cet email existe déjà",
    "error_code": 4002,
    "details": {
        "field": "email",
        "value": "<EMAIL>",
        "existing_user_type": "client"
    },
    "message": "Veuillez utiliser une adresse email différente",
    "timestamp": "2024-06-15T16:00:00Z"
}
```

**Réponse erreur - Données manquantes (400) :**
```json
{
    "status": "error",
    "error": "Les champs email, first_name, last_name et phone_number sont requis",
    "error_code": 400,
    "details": {
        "missing_fields": ["last_name", "phone_number"],
        "provided_fields": ["email", "first_name"]
    },
    "message": "Veuillez fournir tous les champs obligatoires",
    "timestamp": "2024-06-15T16:00:00Z"
}
```

**Réponse erreur - Échec envoi email (500) :**
```json
{
    "status": "error",
    "error": "Batelier créé mais échec de l'envoi d'email",
    "error_code": 5001,
    "data": {
        "captain_id": 89,
        "user_id": 234,
        "temporary_password": "Kj8mN2pQ",
        "email_error": "SMTP server connection failed"
    },
    "message": "Le batelier a été créé mais l'email n'a pas pu être envoyé. Veuillez lui communiquer ses identifiants manuellement.",
    "timestamp": "2024-06-15T16:00:00Z"
}
```

### **GET /api/establishments/boatmen/**
Lister tous les bateliers enregistrés.

**Réponse :**
```json
{
    "status": "success",
    "data": {
        "boatmen": [
            {
                "captain_id": 789,
                "name": "Jean Dupont",
                "email": "<EMAIL>",
                "phone": "+33123456789",
                "experience": "3 ans d'expérience",
                "license_number": "LIC123",
                "availability_status": "AVAILABLE",
                "average_rating": 4.5,
                "boat": {
                    "id": 456,
                    "name": "Bateau de Jean Dupont",
                    "capacity": 6,
                    "boat_type": "classic"
                },
                "registered_at": "2024-06-01T10:00:00Z"
            }
        ],
        "total_count": 1
    }
}
```

### **GET /api/establishments/boatmen/{captain_id}/**
Voir les détails d'un batelier spécifique.

**Réponse :**
```json
{
    "status": "success",
    "data": {
        "captain_id": 789,
        "name": "Jean Dupont",
        "email": "<EMAIL>",
        "phone": "+33123456789",
        "experience": "3 ans d'expérience",
        "license_number": "LIC123",
        "availability_status": "AVAILABLE",
        "average_rating": 4.5,
        "rate_per_km": 25.00,
        "rate_per_hour": 50.00,
        "boats": [
            {
                "id": 456,
                "name": "Bateau de Jean Dupont",
                "capacity": 6,
                "boat_type": "classic",
                "registration_number": "FR789012",
                "is_available": true
            }
        ],
        "statistics": {
            "total_trips": 15,
            "completed_trips": 14,
            "completion_rate": 93.33
        },
        "registered_at": "2024-06-01T10:00:00Z"
    }
}
```

---

## 💳 **5. GESTION DU PORTEFEUILLE**

### **GET /api/establishments/wallet/**
Consulter le portefeuille de l'établissement.

**Réponse :**
```json
{
    "status": "success",
    "data": {
        "balance": 1250.50,
        "currency": "EUR",
        "total_earned": 5000.00,
        "total_spent": 3749.50,
        "shuttle_expenses_30d": 450.00,
        "recent_transactions": [
            {
                "transaction_id": "123",
                "type": "Débit",
                "amount": 25.00,
                "balance_after": 1250.50,
                "description": "Paiement navette",
                "created_at": "2024-06-14T15:30:00Z"
            }
        ]
    }
}
```

### **POST /api/establishments/wallet/add-funds/**
Ajouter des fonds au portefeuille.

**Données à envoyer :**
```json
{
    "amount": 500.00,
    "payment_method": "CARD"
}
```

**Réponse :**
```json
{
    "status": "success",
    "message": "Fonds ajoutés avec succès",
    "data": {
        "new_balance": 1750.50,
        "payment_id": "pay_123456",
        "amount_added": 500.00
    }
}
```

---

## 📊 **6. HISTORIQUE ET STATISTIQUES**

### **GET /api/establishments/payments/history/**
Historique des paiements avec filtres et pagination.

**Paramètres URL :**
- `type` : Type de paiement
- `date_from` : Date de début (ISO format)
- `date_to` : Date de fin (ISO format)
- `page` : Numéro de page
- `limit` : Nombre d'éléments par page

**Réponse :**
```json
{
    "status": "success",
    "data": {
        "payments": [
            {
                "payment_id": "pay_123",
                "type": "Paiement navette",
                "amount": 25.00,
                "status": "Terminé",
                "payment_method": "Portefeuille",
                "description": "Navette aéroport",
                "trip_id": "trip_456",
                "shuttle_id": null,
                "created_at": "2024-06-14T15:30:00Z",
                "completed_at": "2024-06-14T15:30:05Z"
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 50
        }
    }
}
```

### **GET /api/establishments/payments/stats/**
Statistiques des paiements.

**Paramètres URL :**
- `period` : Nombre de jours (défaut: 30)

**Réponse :**
```json
{
    "status": "success",
    "data": {
        "period_days": 30,
        "total_amount": 1500.00,
        "total_count": 25,
        "average_amount": 60.00,
        "payment_types": [
            {
                "type": "SHUTTLE_PAYMENT",
                "count": 20,
                "total_amount": 1200.00
            }
        ],
        "daily_evolution": [
            {
                "date": "2024-06-14",
                "count": 3,
                "amount": 150.00
            }
        ]
    }
}
```

---

## 🚨 **CODES D'ERREUR**

- `400` : Données invalides
- `401` : Non authentifié
- `403` : Accès refusé (pas un établissement)
- `404` : Ressource non trouvée
- `500` : Erreur serveur

---

## 📧 **SYSTÈME D'EMAIL AUTOMATIQUE**

Lors de l'enregistrement d'un batelier, un email automatique est envoyé avec :
- Email de connexion
- Mot de passe temporaire généré (8 caractères alphanumériques)
- Lien vers l'espace batelier
- Instructions pour changer le mot de passe

**Template d'email :**
```
Sujet: Bienvenue chez [Nom Établissement] - Vos identifiants Commodore

Bonjour [Prénom] [Nom],

Vous avez été enregistré comme batelier pour [Nom Établissement] sur la plateforme Commodore.

Vos identifiants de connexion :
- Email : [email]
- Mot de passe temporaire : [password]

Veuillez vous connecter et changer votre mot de passe lors de votre première connexion.

URL de connexion : https://app.commodore.com/captain/login
```
