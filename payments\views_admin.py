from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated  # Temporairement modifié pour les tests
from .models import Wallet
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class AdminWalletAddFundsView(APIView):
    """Vue pour permettre aux administrateurs d'ajouter des fonds directement à un portefeuille."""
    permission_classes = [IsAuthenticated]  # Temporairement modifié pour les tests
    
    def post(self, request):
        """
        Ajoute des fonds au portefeuille d'un utilisateur.
        
        Parameters:
            user_id (int): ID de l'utilisateur
            amount (decimal): Montant à ajouter
            
        Returns:
            Response: Détails du portefeuille mis à jour
        """
        user_id = request.data.get('user_id')
        amount = request.data.get('amount')
        
        if not user_id or not amount:
            return Response(
                {"error": "Les paramètres user_id et amount sont requis."},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            amount = float(amount)
            if amount <= 0:
                return Response(
                    {"error": "Le montant doit être positif."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return Response(
                {"error": "Le montant doit être un nombre valide."},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            user = get_object_or_404(User, id=user_id)
            wallet, created = Wallet.objects.get_or_create(user=user)
            
            # Ajouter les fonds au portefeuille
            previous_balance = wallet.balance
            wallet.balance += amount
            wallet.save()
            
            logger.info(f"Fonds ajoutés par admin: user_id={user_id}, amount={amount}, previous={previous_balance}, new={wallet.balance}")
            
            return Response({
                "wallet_id": wallet.id,
                "user_id": user.id,
                "previous_balance": previous_balance,
                "added_amount": amount,
                "new_balance": wallet.balance,
                "currency": "EUR"
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Erreur lors de l'ajout de fonds: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
