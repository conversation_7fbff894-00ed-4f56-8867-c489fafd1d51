from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
import json
import stripe
from django.conf import settings
from .models import Payment
from .stripe_utils import create_payment_intent, create_checkout_session

# Configurer Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY

def stripe_test_view(request):
    """Vue pour la page de test de l'intégration Stripe"""
    return render(request, 'stripe_test.html')

@csrf_exempt
def create_payment_intent_view(request):
    """Vue pour créer un Payment Intent"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        data = json.loads(request.body)
        amount = data.get('amount')
        currency = data.get('currency', 'eur')
        description = data.get('description', 'Test de paiement Commodore')
        
        if not amount:
            return JsonResponse({'error': 'Le montant est requis'}, status=400)
        
        # Créer un payment intent
        intent = create_payment_intent(
            amount=amount,
            currency=currency,
            payment_method_types=['card'],
            metadata={'test': 'true'},
            description=description
        )
        
        if 'error' in intent:
            return JsonResponse({'error': intent['error']}, status=400)
        
        # Créer un enregistrement de paiement
        payment = Payment.objects.create(
            amount=amount / 100,  # Convertir les centimes en euros
            type='TRIP',
            status='PENDING',
            stripe_payment_id=intent.id,
            metadata={
                'test': 'true',
                'description': description
            }
        )
        
        return JsonResponse({
            'client_secret': intent.client_secret,
            'payment_id': str(payment.id)
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
def create_checkout_view(request):
    """Vue pour créer une session Checkout"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        data = json.loads(request.body)
        amount = data.get('amount')
        product_name = data.get('product_name', 'Service Commodore')
        product_description = data.get('product_description')
        success_url = data.get('success_url', settings.STRIPE_SUCCESS_URL)
        cancel_url = data.get('cancel_url', settings.STRIPE_CANCEL_URL)
        
        if not amount:
            return JsonResponse({'error': 'Le montant est requis'}, status=400)
        
        # Créer une session Checkout
        session = create_checkout_session(
            amount=amount,
            currency='eur',
            product_name=product_name,
            product_description=product_description,
            success_url=success_url,
            cancel_url=cancel_url,
            metadata={'test': 'true'}
        )
        
        if 'error' in session:
            return JsonResponse({'error': session['error']}, status=400)
        
        # Créer un enregistrement de paiement
        payment = Payment.objects.create(
            amount=amount / 100,  # Convertir les centimes en euros
            type='TRIP',
            status='PENDING',
            metadata={
                'checkout_session_id': session.id,
                'test': 'true',
                'product_name': product_name,
                'product_description': product_description
            }
        )
        
        return JsonResponse({
            'session_id': session.id,
            'payment_id': str(payment.id),
            'checkout_url': session.url
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

def payment_success_view(request):
    """Vue pour la page de succès de paiement"""
    session_id = request.GET.get('session_id')
    return render(request, 'payment_success.html', {'session_id': session_id})

def payment_cancel_view(request):
    """Vue pour la page d'annulation de paiement"""
    return render(request, 'payment_cancel.html')
