"""
Script pour réactiver django-allauth après la migration
Ce script va:
1. Restaurer le fichier settings.py original
2. Appliquer les migrations de django-allauth
"""
import os
import sys
import shutil

# Restaurer le fichier settings.py original
settings_path = 'D:\\commodore\\commodore\\settings.py'
backup_path = settings_path + '.bak'

if os.path.exists(backup_path):
    shutil.copy(backup_path, settings_path)
    print("✅ Fichier settings.py original restauré")
    os.remove(backup_path)
else:
    print("❌ Fichier de sauvegarde non trouvé")

print("\nMaintenant, exécutez:")
print("python manage.py migrate")
print("\nLa migration devrait maintenant fonctionner sans erreur de colonne en double.")
