"""
Tests complets pour les deux endpoints PATCH de gestion des profils.
1. PATCH /api/profile/ - Profils clients, capitaines, établissements
2. PATCH /api/boatman/profile/ - Profil batelier

ENDPOINTS ANALYSÉS:
- PATCH /api/profile/ (accounts.views.UserProfileView.patch)
- PATCH /api/boatman/profile/ (boatman.views_profile.BoatmanProfileView.patch)
"""

import os
import sys
import django
import json
from decimal import Decimal
from datetime import datetime, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token

from accounts.models import Client, Captain, Establishment
from boats.models import Boat

User = get_user_model()


class PatchEndpointsTestSuite:
    """Suite de tests complète pour les endpoints PATCH"""

    def __init__(self):
        self.client = APIClient()
        self.test_results = []
        self.errors = []
        self.test_users = {}
        self.test_tokens = {}

    def setup_test_environment(self):
        """Créer l'environnement de test complet"""
        print("🔧 CONFIGURATION DE L'ENVIRONNEMENT DE TEST")
        print("-" * 60)

        # 1. Créer un client de test
        try:
            client_user = User.objects.get(email='<EMAIL>')
            client_user.delete()
        except User.DoesNotExist:
            pass

        client_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestClient123!',
            first_name='Client',
            last_name='PatchTest',
            phone_number='+***********',
            type='CLIENT'
        )

        client_profile = Client.objects.create(
            user=client_user,
            date_of_birth='1990-05-15',
            nationality='Française',
            preferred_language='fr'
        )

        client_token = Token.objects.create(user=client_user)

        self.test_users['client'] = client_user
        self.test_tokens['client'] = client_token.key

        print(f"✅ Client créé: {client_user.email}")

        # 2. Créer un capitaine de test
        try:
            captain_user = User.objects.get(email='<EMAIL>')
            captain_user.delete()
        except User.DoesNotExist:
            pass

        captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestCaptain123!',
            first_name='Captain',
            last_name='PatchTest',
            phone_number='+33987654321',
            type='CAPTAIN'
        )

        captain_profile = Captain.objects.create(
            user=captain_user,
            experience='5 ans d\'expérience',
            license_number='CAP123456',
            years_of_experience=5,
            rate_per_hour=Decimal('45.00'),
            rate_per_km=Decimal('2.50')
        )

        captain_token = Token.objects.create(user=captain_user)

        self.test_users['captain'] = captain_user
        self.test_tokens['captain'] = captain_token.key

        print(f"✅ Capitaine créé: {captain_user.email}")

        # 3. Créer un établissement de test
        try:
            establishment_user = User.objects.get(email='<EMAIL>')
            establishment_user.delete()
        except User.DoesNotExist:
            pass

        establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestEstablishment123!',
            first_name='Establishment',
            last_name='PatchTest',
            phone_number='+33555666777',
            type='ESTABLISHMENT'
        )

        establishment_profile = Establishment.objects.create(
            user=establishment_user,
            name='Hôtel Test Patch',
            type='HOTEL',
            address='123 Rue de Test, 06400 Cannes',
            description='Hôtel de test pour les endpoints PATCH'
        )

        establishment_token = Token.objects.create(user=establishment_user)

        self.test_users['establishment'] = establishment_user
        self.test_tokens['establishment'] = establishment_token.key

        print(f"✅ Établissement créé: {establishment_user.email}")

        # 4. Créer un batelier de test (capitaine avec bateau)
        try:
            boatman_user = User.objects.get(email='<EMAIL>')
            boatman_user.delete()
        except User.DoesNotExist:
            pass

        boatman_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestBoatman123!',
            first_name='Boatman',
            last_name='PatchTest',
            phone_number='+33444555666',
            type='CAPTAIN'
        )

        boatman_profile = Captain.objects.create(
            user=boatman_user,
            experience='8 ans d\'expérience maritime',
            license_number='BOAT123456',
            years_of_experience=8,
            rate_per_hour=Decimal('50.00'),
            rate_per_km=Decimal('3.00')
        )

        # Créer un bateau pour le batelier (vérifier s'il existe déjà)
        try:
            boat = Boat.objects.get(captain=boatman_profile)
            boat.delete()
        except Boat.DoesNotExist:
            pass

        boat = Boat.objects.create(
            name='Bateau Test Patch',
            registration_number='TEST_PATCH_001',
            boat_type='CLASSIC',
            capacity=8,
            captain=boatman_profile,
            fuel_type='GASOLINE',
            fuel_consumption=Decimal('12.5'),
            zone_served='Zone Test',
            radius=30
        )

        boatman_token = Token.objects.create(user=boatman_user)

        self.test_users['boatman'] = boatman_user
        self.test_tokens['boatman'] = boatman_token.key

        print(f"✅ Batelier créé: {boatman_user.email}")
        print(f"✅ Bateau créé: {boat.name}")

    def test_client_profile_patch(self):
        """Test PATCH /api/profile/ pour un client"""
        print("\n👤 TEST: PATCH /api/profile/ - Client")
        print("-" * 50)

        # Configuration de l'authentification
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.test_tokens["client"]}')



        try:
            # Test 1: Mise à jour du téléphone (traité séparément)
            phone_data = {"phone_number": "+33111222333"}
            response = self.client.patch('/api/profile/', phone_data, format='json')
            print(f"Test téléphone - Status: {response.status_code}")

            if response.status_code != 200:
                raise Exception(f"Phone update failed: {response.data}")

            # Test 2: Mise à jour du profil client (sans téléphone)
            profile_data = {
                "first_name": "ClientUpdated",
                "last_name": "PatchTestUpdated",
                "client_profile": {
                    "date_of_birth": "1992-08-20",
                    "nationality": "Italienne",
                    "preferred_language": "it",
                    "emergency_contact_name": "Emergency Contact",
                    "emergency_contact_phone": "+33999888777"
                }
            }

            response = self.client.patch('/api/profile/', profile_data, format='json')
            print(f"Test profil - Status: {response.status_code}")
            print(f"Response: {json.dumps(response.data, indent=2, ensure_ascii=False)}")

            if response.status_code == 200:
                # Vérifier que les données ont été mises à jour
                user = User.objects.get(id=self.test_users['client'].id)
                client = user.client

                assert user.first_name == "ClientUpdated"
                assert user.last_name == "PatchTestUpdated"
                assert user.phone_number == "+33111222333"
                assert str(client.date_of_birth) == "1992-08-20"
                assert client.nationality == "Italienne"

                print("✅ SUCCÈS: Profil client mis à jour correctement")
                self.test_results.append("Client profile PATCH: PASSED")
            else:
                print(f"❌ ÉCHEC: Status {response.status_code}")
                self.test_results.append("Client profile PATCH: FAILED")
                self.errors.append(f"Client PATCH failed: {response.data}")

        except Exception as e:
            print(f"❌ ERREUR: {str(e)}")
            self.test_results.append("Client profile PATCH: FAILED")
            self.errors.append(f"Client PATCH error: {str(e)}")

    def test_captain_profile_patch(self):
        """Test PATCH /api/profile/ pour un capitaine"""
        print("\n⛵ TEST: PATCH /api/profile/ - Capitaine")
        print("-" * 50)

        # Configuration de l'authentification
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.test_tokens["captain"]}')

        # Données de test pour capitaine
        patch_data = {
            "first_name": "CaptainUpdated",
            "last_name": "PatchTestUpdated",
            "phone_number": "+33222333444",
            "captain_profile": {
                "experience": "10 ans d'expérience maritime professionnelle",
                "license_number": "CAP789012",
                "years_of_experience": 10,
                "certifications": ["Permis côtier", "Permis hauturier", "Certificat de sécurité"],
                "specializations": ["Navigation côtière", "Pêche en mer", "Transport passagers"],
                "rate_per_hour": "55.00"
            }
        }

        try:
            response = self.client.patch('/api/profile/', patch_data, format='json')

            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.data, indent=2, ensure_ascii=False)}")

            if response.status_code == 200:
                # Vérifier que les données ont été mises à jour
                user = User.objects.get(id=self.test_users['captain'].id)
                captain = user.captain

                assert user.first_name == "CaptainUpdated"
                assert user.last_name == "PatchTestUpdated"
                assert user.phone_number == "+33222333444"
                assert captain.experience == "10 ans d'expérience maritime professionnelle"
                assert captain.license_number == "CAP789012"
                assert captain.years_of_experience == 10
                assert captain.rate_per_hour == Decimal('55.00')

                print("✅ SUCCÈS: Profil capitaine mis à jour correctement")
                self.test_results.append("Captain profile PATCH: PASSED")
            else:
                print(f"❌ ÉCHEC: Status {response.status_code}")
                self.test_results.append("Captain profile PATCH: FAILED")
                self.errors.append(f"Captain PATCH failed: {response.data}")

        except Exception as e:
            print(f"❌ ERREUR: {str(e)}")
            self.test_results.append("Captain profile PATCH: FAILED")
            self.errors.append(f"Captain PATCH error: {str(e)}")

    def test_establishment_profile_patch(self):
        """Test PATCH /api/profile/ pour un établissement"""
        print("\n🏢 TEST: PATCH /api/profile/ - Établissement")
        print("-" * 50)

        # Configuration de l'authentification
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.test_tokens["establishment"]}')

        # Données de test pour établissement
        patch_data = {
            "first_name": "EstablishmentUpdated",
            "last_name": "PatchTestUpdated",
            "phone_number": "+33333444555",
            "establishment_profile": {
                "name": "Hôtel Riviera Updated",
                "type": "RESTAURANT",
                "address": "456 Avenue de la Mer, 06400 Cannes",
                "description": "Restaurant de luxe avec vue sur mer - Mis à jour",
                "opening_hours": "Ouvert tous les jours de 12h à 23h",
                "services_offered": ["Restaurant", "Bar", "Terrasse", "Événements privés"],
                "website": "https://hotel-riviera-updated.com"
            }
        }

        try:
            response = self.client.patch('/api/profile/', patch_data, format='json')

            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.data, indent=2, ensure_ascii=False)}")

            if response.status_code == 200:
                # Vérifier que les données ont été mises à jour
                user = User.objects.get(id=self.test_users['establishment'].id)
                establishment = user.establishment

                assert user.first_name == "EstablishmentUpdated"
                assert user.last_name == "PatchTestUpdated"
                assert user.phone_number == "+33333444555"
                assert establishment.name == "Hôtel Riviera Updated"
                assert establishment.type == "RESTAURANT"
                assert establishment.address == "456 Avenue de la Mer, 06400 Cannes"

                print("✅ SUCCÈS: Profil établissement mis à jour correctement")
                self.test_results.append("Establishment profile PATCH: PASSED")
            else:
                print(f"❌ ÉCHEC: Status {response.status_code}")
                self.test_results.append("Establishment profile PATCH: FAILED")
                self.errors.append(f"Establishment PATCH failed: {response.data}")

        except Exception as e:
            print(f"❌ ERREUR: {str(e)}")
            self.test_results.append("Establishment profile PATCH: FAILED")
            self.errors.append(f"Establishment PATCH error: {str(e)}")

    def test_boatman_profile_patch(self):
        """Test PATCH /api/boatman/profile/ pour un batelier"""
        print("\n🚤 TEST: PATCH /api/boatman/profile/ - Batelier")
        print("-" * 50)

        # Configuration de l'authentification
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.test_tokens["boatman"]}')

        # Données de test pour batelier
        patch_data = {
            "first_name": "BoatmanUpdated",
            "last_name": "PatchTestUpdated",
            "phone_number": "+33444555666",
            "experience": "12 ans d'expérience en navigation côtière et transport de passagers",
            "contact_preferences": {
                "email_notifications": False,
                "sms_notifications": True,
                "push_notifications": True
            }
        }

        try:
            response = self.client.patch('/api/boatman/profile/', patch_data, format='json')

            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.data, indent=2, ensure_ascii=False)}")

            if response.status_code == 200:
                # Vérifier que les données ont été mises à jour
                user = User.objects.get(id=self.test_users['boatman'].id)
                captain = user.captain

                assert user.first_name == "BoatmanUpdated"
                assert user.last_name == "PatchTestUpdated"
                assert user.phone_number == "+33444555666"
                assert captain.experience == "12 ans d'expérience en navigation côtière et transport de passagers"

                print("✅ SUCCÈS: Profil batelier mis à jour correctement")
                self.test_results.append("Boatman profile PATCH: PASSED")
            else:
                print(f"❌ ÉCHEC: Status {response.status_code}")
                self.test_results.append("Boatman profile PATCH: FAILED")
                self.errors.append(f"Boatman PATCH failed: {response.data}")

        except Exception as e:
            print(f"❌ ERREUR: {str(e)}")
            self.test_results.append("Boatman profile PATCH: FAILED")
            self.errors.append(f"Boatman PATCH error: {str(e)}")

    def run_all_tests(self):
        """Exécuter tous les tests des endpoints PATCH"""
        print("🚀 DÉMARRAGE DES TESTS COMPLETS DES ENDPOINTS PATCH")
        print("=" * 70)

        start_time = datetime.now()

        # Configuration
        self.setup_test_environment()

        # Tests principaux
        self.test_client_profile_patch()
        self.test_captain_profile_patch()
        self.test_establishment_profile_patch()
        self.test_boatman_profile_patch()

        end_time = datetime.now()

        # Résumé
        print("\n" + "=" * 70)
        print("📊 RÉSUMÉ DES TESTS DES ENDPOINTS PATCH")
        print("=" * 70)

        passed_tests = [t for t in self.test_results if "PASSED" in t]
        failed_tests = [t for t in self.test_results if "FAILED" in t]

        print(f"✅ Tests réussis: {len(passed_tests)}")
        print(f"❌ Tests échoués: {len(failed_tests)}")
        print(f"⏱️  Temps d'exécution: {(end_time - start_time).total_seconds():.2f} secondes")

        if failed_tests:
            print("\n❌ TESTS ÉCHOUÉS:")
            for test in failed_tests:
                print(f"  - {test}")

        if self.errors:
            print("\n🚨 ERREURS DÉTECTÉES:")
            for error in self.errors:
                print(f"  - {error}")

        if not failed_tests and not self.errors:
            print("\n🎉 TOUS LES TESTS DES ENDPOINTS PATCH ONT RÉUSSI!")
            print("✅ Les endpoints PATCH fonctionnent correctement")
        else:
            print("\n⚠️  ATTENTION: Des problèmes ont été détectés")
            print("❌ Vérifier les erreurs avant déploiement")

        return len(failed_tests) == 0 and len(self.errors) == 0


if __name__ == "__main__":
    tester = PatchEndpointsTestSuite()
    success = tester.run_all_tests()

    # Code de sortie pour les scripts automatisés
    sys.exit(0 if success else 1)
