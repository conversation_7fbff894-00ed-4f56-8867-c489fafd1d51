from django.test import TestCase
from unittest.mock import patch, MagicMock
from .stripe_utils import create_payment_intent, create_checkout_session

class StripeUtilsTestCase(TestCase):
    """Tests pour les fonctions utilitaires Stripe"""

    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent(self, mock_create):
        """Test de la création d'un payment intent"""
        # Simuler la réponse de Stripe
        mock_create.return_value = MagicMock(
            id='pi_test123',
            client_secret='pi_test123_secret',
            amount=1000,
            currency='eur',
            status='requires_payment_method',
            metadata={}
        )
        
        # Test avec les paramètres de base
        result = create_payment_intent(amount=1000)
        
        mock_create.assert_called_once()
        self.assertEqual(result.id, 'pi_test123')
        self.assertEqual(result.client_secret, 'pi_test123_secret')
    
    @patch('stripe.checkout.Session.create')
    def test_create_checkout_session(self, mock_create):
        """Test de la création d'une session checkout"""
        # Simuler la réponse de Stripe
        mock_create.return_value = MagicMock(
            id='cs_test123',
            url='https://checkout.stripe.com/test',
            payment_intent='pi_test123',
            amount_total=1000,
            currency='eur',
            status='open',
            metadata={}
        )
        
        # Test avec les paramètres de base
        result = create_checkout_session(amount=1000)
        
        mock_create.assert_called_once()
        self.assertEqual(result.id, 'cs_test123')
        self.assertEqual(result.url, 'https://checkout.stripe.com/test')
