from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from django.utils import timezone

from .models import ChatbotMessage
from rag.models_feedback import ChatFeedback

@api_view(['POST'])
@authentication_classes([TokenAuthentication, SessionAuthentication])
@permission_classes([IsAuthenticated])
def submit_feedback(request):
    """
    Endpoint pour soumettre un feedback sur une réponse du chatbot.
    Permet aux utilisateurs d'évaluer la qualité des réponses pour amélioration future.
    """
    # Récupérer les données de la requête
    message_id = request.data.get('message_id')
    rating = request.data.get('rating')  # Note de 1 à 5
    feedback_text = request.data.get('feedback', '').strip()
    
    # Vérifier les données
    if not message_id:
        return Response({
            'error': 'ID de message requis'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    if not rating or not isinstance(rating, int) or rating < 1 or rating > 5:
        return Response({
            'error': 'Note valide requise (1-5)'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Récupérer le message
    try:
        message = ChatbotMessage.objects.get(id=message_id)
        
        # Vérifier que le message appartient à une session de l'utilisateur
        if message.session.user != request.user:
            return Response({
                'error': 'Non autorisé à évaluer ce message'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Vérifier que c'est un message de l'assistant (pas un message utilisateur)
        if message.role != ChatbotMessage.Role.ASSISTANT:
            return Response({
                'error': 'Seules les réponses du chatbot peuvent être évaluées'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except ChatbotMessage.DoesNotExist:
        return Response({
            'error': 'Message introuvable'
        }, status=status.HTTP_404_NOT_FOUND)
    
    # Créer ou mettre à jour le feedback
    feedback, created = ChatFeedback.objects.update_or_create(
        user=request.user,
        message=message,
        defaults={
            'rating': rating,
            'feedback': feedback_text,
            'created_at': timezone.now()
        }
    )
    
    # Retourner la réponse
    return Response({
        'status': 'success',
        'message': 'Feedback enregistré avec succès',
        'feedback_id': feedback.id
    })

@api_view(['GET'])
@authentication_classes([TokenAuthentication, SessionAuthentication])
@permission_classes([IsAuthenticated])
def get_feedback_stats(request):
    """
    Récupère des statistiques sur les feedbacks pour les administrateurs.
    """
    # Vérifier que l'utilisateur est un administrateur
    if not request.user.is_staff:
        return Response({
            'error': 'Accès réservé aux administrateurs'
        }, status=status.HTTP_403_FORBIDDEN)
    
    # Calculer les statistiques
    total_feedback = ChatFeedback.objects.count()
    average_rating = ChatFeedback.objects.filter(rating__gt=0).aggregate(avg=models.Avg('rating'))['avg'] or 0
    
    # Répartition des notes
    ratings_distribution = {}
    for i in range(1, 6):
        ratings_distribution[i] = ChatFeedback.objects.filter(rating=i).count()
    
    # Retourner les statistiques
    return Response({
        'total_feedback': total_feedback,
        'average_rating': round(average_rating, 2),
        'ratings_distribution': ratings_distribution,
        'recent_feedback': ChatFeedback.objects.order_by('-created_at')[:10].values(
            'id', 'rating', 'feedback', 'created_at', 'user__email', 'message__content'
        )
    })
