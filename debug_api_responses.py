"""
Script pour déboguer les réponses API et comprendre leur structure exacte.
"""

import os
import django
import requests
import json
from datetime import datetime, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token

User = get_user_model()

def debug_api_responses():
    """Déboguer les réponses API pour comprendre leur structure"""
    
    print("🔍 DEBUG DES RÉPONSES API")
    print("=" * 40)
    
    # Récupérer un token existant
    try:
        client_user = User.objects.get(email='<EMAIL>')
        client_token = Token.objects.get_or_create(user=client_user)[0]
        token = client_token.key
        print(f"✅ Token client récupéré: {token[:20]}...")
    except:
        print("❌ Impossible de récupérer le token client")
        return
    
    base_url = 'http://127.0.0.1:8000'
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # 1. TEST CRÉATION DEMANDE COURSE
    print("\n1. 📡 TEST CRÉATION DEMANDE COURSE")
    departure_time = datetime.now() + timedelta(hours=2)
    trip_data = {
        'departure_location': {
            'city_name': 'Port de Cannes',
            'coordinates': {'latitude': 43.5528, 'longitude': 7.0174},
            'timestamp': departure_time.isoformat()
        },
        'arrival_location': {
            'city_name': 'Îles de Lérins',
            'coordinates': {'latitude': 43.5184, 'longitude': 7.0457},
            'timestamp': departure_time.isoformat()
        },
        'departure_date': departure_time.date().isoformat(),
        'departure_time': departure_time.time().isoformat(),
        'passenger_count': 4,
        'boat_type': 'CLASSIC'
    }
    
    response = requests.post(f'{base_url}/api/trips/requests/simple/', 
                           headers=headers, json=trip_data)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 201:
        data = response.json()
        print("Structure de réponse création:")
        print(json.dumps(data, indent=2, ensure_ascii=False))
        
        # Extraire l'ID de la demande
        if 'trip_request' in data and 'id' in data['trip_request']:
            request_id = data['trip_request']['id']
        elif 'id' in data:
            request_id = data['id']
        else:
            request_id = None
            
        print(f"\nID de la demande: {request_id}")
        
        # 2. TEST RÉCUPÉRATION DEVIS
        if request_id:
            print(f"\n2. 📡 TEST RÉCUPÉRATION DEVIS pour ID {request_id}")
            response = requests.get(f'{base_url}/api/trips/requests/{request_id}/', 
                                  headers=headers)
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print("Structure de réponse devis:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # Analyser la structure des devis
                if 'quotes' in data:
                    quotes = data['quotes']
                    print(f"\n✅ {len(quotes)} devis trouvés")
                    if quotes:
                        print("Structure du premier devis:")
                        print(json.dumps(quotes[0], indent=2, ensure_ascii=False))
                else:
                    print("❌ Aucun champ 'quotes' trouvé")
            else:
                print(f"❌ Erreur: {response.text}")
    else:
        print(f"❌ Erreur création: {response.text}")
    
    # 3. TEST PORTEFEUILLE
    print(f"\n3. 📡 TEST PORTEFEUILLE")
    response = requests.get(f'{base_url}/api/payments/wallet/', headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print("Structure portefeuille:")
        print(json.dumps(data, indent=2, ensure_ascii=False))
    else:
        print(f"❌ Erreur: {response.text}")

if __name__ == '__main__':
    debug_api_responses()
