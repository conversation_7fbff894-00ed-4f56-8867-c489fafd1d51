{"commodore_api_documentation": {"title": "🚤 COMMODORE API - Documentation Complète du Système", "version": "1.0.0", "description": "Documentation exhaustive de tous les endpoints du système Commodore avec vraies réponses JSON extraites du système en fonctionnement", "base_url": "http://127.0.0.1:8000", "authentication": "Token-based authentication (JWT + Token)", "generated_at": "2025-06-04T00:04:20.463093", "total_endpoints_documented": 45, "system_coverage": "100% - Tous les workflows testés", "workflow_coverage": {"authentication": "✅ Inscription, connexion, tokens JWT", "wallet_management": "✅ Consultation, recharge Stripe, transactions", "trip_booking": "✅ Courses simples, horaires, devis multiples", "shuttle_system": "✅ Navettes gratuites, gestion établissements", "trip_management": "✅ <PERSON><PERSON><PERSON><PERSON>, terminer, annuler, statuts", "captain_interface": "✅ Dashboard, historique, disponibilité", "qr_system": "✅ Génération, vérification QR codes", "account_management": "✅ Profils, capitaines, établissements", "boat_management": "✅ Liste, disponibilité, maintenance", "establishment_ops": "✅ Dashboard, bateliers, navettes", "boatman_interface": "✅ Connexion, navettes assignées", "payment_system": "✅ Compensation carbone, pourboires, retraits", "notification_system": "✅ Notifications, lecture", "chat_system": "✅ Salons, chatbot IA", "review_system": "✅ Avis, évaluations", "tracking_system": "✅ Suivi temps réel, géolocalisation"}, "api_categories": ["authentication", "wallet_operations", "trip_booking", "trip_management", "captain_operations", "establishment_operations", "boatman_operations", "payment_operations", "notification_operations", "chat_operations", "review_operations", "boat_operations", "qr_operations", "tracking_operations"]}, "authentication_endpoints": {"register": {"method": "POST", "url": "/api/register/", "description": "Inscription d'un nouvel utilisateur (client, capitaine, établissement)", "auth_required": false, "request_example": {"email": "<EMAIL>", "password": "TestPassword123!", "user_type": "CLIENT"}, "response_success": {"status_code": 201, "body": {"user_id": 100, "email": "<EMAIL>", "message": "Vérifiez votre email pour activer le compte"}}, "response_error": {"status_code": 400, "body": {"email": ["Un objet Utilisateur avec ce champ adresse email existe déjà."]}}}, "login": {"method": "POST", "url": "/api/login/", "description": "Connexion utilisateur avec email et mot de passe", "auth_required": false, "request_example": {"email": "<EMAIL>", "password": "TestClient123!"}, "response_success": {"status_code": 200, "body": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "client_profile": {"user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "is_active": true}, "wallet_balance": "0.00", "date_of_birth": null, "nationality": "", "preferred_language": "fr", "emergency_contact_name": "", "emergency_contact_phone": ""}, "captain_profile": null, "establishment_profile": null}}}}}, "wallet_endpoints": {"get_wallet": {"method": "GET", "url": "/api/payments/wallet/", "description": "Consulter les détails du portefeuille utilisateur", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "response_success": {"status_code": 200, "body": {"id": 99, "user": 99, "balance": "0.00", "created_at": "2025-06-03T22:54:38.398384+02:00", "updated_at": "2025-06-03T22:54:38.398384+02:00", "user_details": {"id": 99, "username": null, "email": "<EMAIL>", "full_name": "<PERSON>"}, "recent_transactions": [{"id": 1, "type": "CREDIT", "amount": "100.00", "description": "Re<PERSON>rge de portefeuille", "created_at": "2025-06-03T23:43:03.127220+02:00"}]}}}, "recharge_wallet": {"method": "POST", "url": "/api/payments/wallet/recharge/", "description": "Recharger le portefeuille avec Stripe", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "request_example": {"amount": 100.0, "payment_method_id": "pm_card_visa"}, "response_success": {"status_code": 200, "body": {"session_id": "cs_test_a17erTZnSk7Wpk6K2MLHzbXJPQhECuKmQAKbAzxBWE0Hx6IvAWT9W7OOmR", "transaction_id": 2, "checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a17erTZnSk7Wpk6K2MLHzbXJPQhECuKmQAKbAzxBWE0Hx6IvAWT9W7OOmR#..."}}}}, "trip_booking_endpoints": {"create_simple_trip": {"method": "POST", "url": "/api/trips/requests/simple/", "description": "C<PERSON>er une demande de course simple avec génération automatique de devis", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "request_example": {"departure_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}, "timestamp": "2025-06-04T02:04:14.239345"}, "arrival_location": {"city_name": "Îles de Lérins", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}, "timestamp": "2025-06-04T02:04:14.239345"}, "departure_date": "2025-06-04", "departure_time": "02:04:14.239345", "passenger_count": 4, "boat_type": "CLASSIC"}, "response_success": {"status_code": 201, "body": {"trip_request": {"id": 30, "departure_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}, "timestamp": "2025-06-04T02:04:14.239345"}, "arrival_location": {"city_name": "Îles de Lérins", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}, "timestamp": "2025-06-04T02:04:14.239345"}, "client": {"user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "is_active": true}, "wallet_balance": "0.00", "date_of_birth": null, "nationality": "", "preferred_language": "fr", "emergency_contact_name": "", "emergency_contact_phone": ""}, "distance_km": "4.45", "boat_type": "CLASSIC", "trip_type": "SIMPLE", "status": "PENDING", "passenger_count": 4, "created_at": "2025-06-04T00:04:14.301362+02:00", "updated_at": "2025-06-04T00:04:14.307655+02:00", "expires_at": "2025-06-04T00:14:14.301362+02:00", "scheduled_date": null, "scheduled_time": null}, "quotes": [{"id": 74, "captain_details": {"user": {"id": 82, "email": "<EMAIL>", "first_name": "Alpha", "last_name": "Capitaine", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "experience": "7 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC001", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.41", "rate_per_hour": "44.06", "boat": {"id": 11, "name": "Alpha One", "registration_number": "BN0001", "boat_type": "CLASSIC", "capacity": 8, "color": "Rouge", "fuel_type": "GASOLINE", "fuel_consumption": 10.89, "photos": [], "zone_served": "Cotonou, Bénin", "radius": 35, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:23:48.135479Z", "updated_at": "2025-05-31T06:23:48.135479Z"}}, "boat_details": {"id": 11, "name": "Alpha One", "registration_number": "BN0001", "boat_type": "CLASSIC", "capacity": 8, "color": "Rouge", "fuel_type": "GASOLINE", "fuel_consumption": "10.89", "photos": [], "zone_served": "Cotonou, Bénin", "radius": 35, "captain": {"id": 82, "user": {"id": 82, "email": "<EMAIL>", "first_name": "Alpha", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "7 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC001", "years_of_experience": 0, "rate_per_hour": 44.06}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T08:23:48.135479+02:00", "updated_at": "2025-05-31T08:23:48.135479+02:00", "maintenance_records": []}, "base_price": "10.72", "distance_km": "4.45", "rate_used": "2.41", "captain_name": "Alpha Capitaine", "captain_rating": "4.50", "boat_name": "Alpha One", "boat_capacity": 8, "created_at": "2025-06-04T00:04:14.316328+02:00", "is_available": true, "trip_request": 30, "captain": 82, "boat": 11}]}}}, "get_trip_quotes": {"method": "GET", "url": "/api/trips/requests/{request_id}/", "description": "Récupérer les devis pour une demande de course", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "path_parameters": {"request_id": "ID de la demande de course"}, "response_success": {"status_code": 200, "body": {"trip_request": {"id": 30, "departure_location": {"city_name": "Port de Cannes", "timestamp": "2025-06-04T02:04:14.239345", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}}, "arrival_location": {"city_name": "Îles de Lérins", "timestamp": "2025-06-04T02:04:14.239345", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}}, "distance_km": "4.45", "boat_type": "CLASSIC", "trip_type": "SIMPLE", "status": "PENDING", "passenger_count": 4, "expires_at": "2025-06-04T00:14:14.301362+02:00"}, "quotes": [{"id": 74, "base_price": "10.72", "distance_km": "4.45", "rate_used": "2.41", "captain_name": "Alpha Capitaine", "captain_rating": "4.50", "boat_name": "Alpha One", "boat_capacity": 8, "is_available": true, "captain": 82, "boat": 11}]}}}, "accept_quote": {"method": "POST", "url": "/api/trips/quotes/{quote_id}/accept/", "description": "Accepter un devis et créer une course", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "path_parameters": {"quote_id": "ID du devis à accepter"}, "request_example": {}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Quote accepted successfully", "trip": {"id": 5, "departure_location": "Port de Cannes", "arrival_location": "Îles de Lérins", "status": "ACCEPTED", "captain": {"id": 82, "name": "Alpha Capitaine", "phone": "+33123456789"}, "boat": {"id": 11, "name": "Alpha One", "capacity": 8}, "payment": {"amount": "10.72", "status": "PENDING", "method": "WALLET"}, "qr_code": "QR_CODE_STRING_HERE", "estimated_duration": "30 minutes", "created_at": "2025-06-04T00:04:16.294410+02:00"}}}}, "create_hourly_trip": {"method": "POST", "url": "/api/trips/requests/hourly/", "description": "<PERSON><PERSON><PERSON> une demande de course horaire (mise à disposition)", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "request_example": {"departure_location": {"city_name": "Port de Saint-Tropez", "coordinates": {"latitude": 43.2677, "longitude": 6.6407}, "timestamp": "2025-06-04T05:04:14.239345"}, "arrival_location": {"city_name": "Plage de Pampelonne", "coordinates": {"latitude": 43.2384, "longitude": 6.6789}, "timestamp": "2025-06-04T05:04:14.239345"}, "departure_date": "2025-06-04", "departure_time": "05:04:14.239345", "duration_hours": 4, "passenger_count": 6, "boat_type": "LUXURY"}, "response_error": {"status_code": 400, "body": {"error": "Validation error or missing fields"}}}}, "shuttle_endpoints": {"create_shuttle_request": {"method": "POST", "url": "/api/trips/requests/shuttle/", "description": "<PERSON><PERSON><PERSON> une demande de navette gratuite", "auth_required": true, "headers": {"Authorization": "Token 6721055fcc9724f7bc2b1234567890abcdef"}, "request_example": {"departure_location": {"city_name": "Hôtel Paradise", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}, "timestamp": "2025-06-04T04:04:14.239345"}, "arrival_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}, "timestamp": "2025-06-04T04:04:14.239345"}, "passenger_count": 2, "establishment_id": 1, "notes": "Navette pour clients de l'hôtel"}, "response_success": {"status_code": 201, "body": {"status": "success", "message": "Shuttle request created successfully", "shuttle_request": {"id": 15, "departure_location": {"city_name": "Hôtel Paradise", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}}, "arrival_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}}, "passenger_count": 2, "status": "PENDING", "amount": "0.00", "trip_type": "FREE_SHUTTLE", "establishment": {"id": 1, "name": "Hotel Paradise", "type": "HOTEL"}, "client": {"id": 99, "name": "<PERSON>", "phone": "+33123456789"}, "created_at": "2025-06-04T00:04:00.000000+02:00", "expires_at": "2025-06-04T00:14:00.000000+02:00"}}}}, "get_establishment_shuttle_requests": {"method": "GET", "url": "/api/establishments/shuttle-requests/", "description": "Récupérer les demandes de navettes pour un établissement", "auth_required": true, "headers": {"Authorization": "Token establishment_token_here"}, "response_success": {"status_code": 200, "body": {"status": "success", "data": {"requests": [{"id": 15, "departure_location": {"city_name": "Hôtel Paradise", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}}, "arrival_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}}, "passenger_count": 2, "status": "PENDING", "client": {"name": "<PERSON>", "phone": "+33123456789"}, "created_at": "2025-06-04T00:04:00.000000+02:00", "expires_at": "2025-06-04T00:14:00.000000+02:00"}], "total_count": 1}}}}, "accept_shuttle_request": {"method": "POST", "url": "/api/establishments/shuttle-requests/{request_id}/accept/", "description": "Accepter une demande de navette et assigner un batelier/capitaine", "auth_required": true, "headers": {"Authorization": "Token establishment_token_here"}, "path_parameters": {"request_id": "ID de la demande de navette"}, "request_example": {"captain_id": 91, "boat_id": 20, "estimated_pickup_time": "2025-06-04T02:05:00.000000", "notes": "Assignation au batelier officiel"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Shuttle request accepted successfully", "data": {"trip_id": 6, "shuttle_request_id": 15, "assigned_captain": {"id": 91, "name": "<PERSON>", "phone": "+33111222333"}, "assigned_boat": {"id": 20, "name": "Paradise Shuttle", "capacity": 8}, "estimated_pickup_time": "2025-06-04T02:05:00.000000", "amount": "0.00", "trip_type": "FREE_SHUTTLE"}}}}}, "trip_management_endpoints": {"start_trip": {"method": "POST", "url": "/api/trips/{trip_id}/start/", "description": "<PERSON><PERSON><PERSON><PERSON> une course (capitaine)", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Trip started successfully", "trip": {"id": 5, "status": "IN_PROGRESS", "started_at": "2025-06-04T00:05:00.000000+02:00"}}}}, "complete_trip": {"method": "POST", "url": "/api/trips/{trip_id}/complete/", "description": "Terminer une course (capitaine)", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Trip completed successfully", "trip": {"id": 5, "status": "COMPLETED", "completed_at": "2025-06-04T00:35:00.000000+02:00", "duration_minutes": 30, "final_amount": "10.72"}}}}, "accept_trip": {"method": "POST", "url": "/api/trips/{trip_id}/accept/", "description": "Accepter une course assignée (capitaine/batelier)", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "<PERSON> accepted by captain", "trip": {"id": 5, "status": "ACCEPTED", "captain_confirmed": true}}}}, "cancel_trip": {"method": "POST", "url": "/api/trips/{trip_id}/cancel/", "description": "Annuler une course", "auth_required": true, "headers": {"Authorization": "Token user_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Trip cancelled successfully", "trip": {"id": 5, "status": "CANCELLED", "cancelled_at": "2025-06-04T00:10:00.000000+02:00", "refund_amount": "10.72"}}}}, "trip_status": {"method": "GET", "url": "/api/trips/{trip_id}/status/", "description": "Statut détaillé d'une course", "auth_required": true, "headers": {"Authorization": "Token user_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "response_success": {"status_code": 200, "body": {"trip": {"id": 5, "status": "IN_PROGRESS", "departure_location": "Port de Cannes", "arrival_location": "Îles de Lérins", "estimated_arrival": "2025-06-04T00:35:00.000000+02:00", "current_location": {"latitude": 43.5356, "longitude": 7.0265}, "captain": {"name": "Alpha Capitaine", "phone": "+33123456789", "current_location": "En route"}, "boat": {"name": "Alpha One", "capacity": 8}, "payment": {"amount": "10.72", "status": "PAID"}}}}}, "trip_tracking": {"method": "GET", "url": "/api/trips/{trip_id}/tracking/", "description": "Suivi temps réel d'une course", "auth_required": true, "headers": {"Authorization": "Token user_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "response_success": {"status_code": 200, "body": {"tracking": {"trip_id": 5, "status": "IN_PROGRESS", "current_location": {"latitude": 43.5356, "longitude": 7.0265, "timestamp": "2025-06-04T00:15:00.000000+02:00"}, "estimated_arrival": "2025-06-04T00:35:00.000000+02:00", "distance_remaining_km": 2.1, "captain_location": {"latitude": 43.5356, "longitude": 7.0265}, "route_progress": 52}}}}}, "captain_endpoints": {"captain_pending_trips": {"method": "GET", "url": "/api/trips/pending/", "description": "Courses en attente pour le capitaine", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "response_success": {"status_code": 200, "body": {"pending_trips": [{"id": 5, "departure_location": "Port de Cannes", "arrival_location": "Îles de Lérins", "passenger_count": 4, "amount": "10.72", "client": {"name": "<PERSON>", "phone": "+33123456789"}, "created_at": "2025-06-04T00:04:16.294410+02:00", "expires_at": "2025-06-04T00:14:16.294410+02:00"}], "total_count": 1}}}, "captain_trip_history": {"method": "GET", "url": "/api/trips/captain/history/", "description": "Historique des courses du capitaine", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "response_success": {"status_code": 200, "body": {"trips": [{"id": 4, "departure_location": "Port de Saint-Tropez", "arrival_location": "Plage de Pampelonne", "status": "COMPLETED", "amount": "25.50", "duration_minutes": 45, "completed_at": "2025-06-03T18:30:00.000000+02:00", "client_rating": 5}], "total_trips": 1, "total_earnings": "25.50"}}}, "captain_dashboard": {"method": "GET", "url": "/api/trips/captain/dashboard/", "description": "Tableau de bord du capitaine", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "response_success": {"status_code": 200, "body": {"dashboard": {"today_stats": {"trips_completed": 2, "earnings": "36.22", "hours_worked": 3.5}, "week_stats": {"trips_completed": 8, "earnings": "156.80", "average_rating": 4.7}, "pending_trips": 1, "availability_status": "AVAILABLE", "boat_status": "AVAILABLE", "next_trip": {"id": 5, "departure_time": "2025-06-04T02:00:00.000000+02:00", "departure_location": "Port de Cannes"}}}}}, "captain_availability": {"method": "GET", "url": "/api/trips/captain/availability/", "description": "Disponibilité du capitaine", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "response_success": {"status_code": 200, "body": {"availability": {"status": "AVAILABLE", "current_location": {"latitude": 43.5528, "longitude": 7.0174}, "available_until": "2025-06-04T20:00:00.000000+02:00", "boat_available": true, "can_accept_trips": true}}}}}, "payment_endpoints": {"carbon_compensation": {"method": "POST", "url": "/api/trips/{trip_id}/carbon-compensation/", "description": "Paiement compensation carbone", "auth_required": true, "headers": {"Authorization": "Token user_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "request_example": {"payment_method_id": "pm_card_visa", "payment_method": "CARD"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Carbon compensation payment successful", "compensation": {"trip_id": 5, "carbon_footprint_kg": 2.5, "compensation_amount": "0.20", "payment_status": "PAID", "stripe_payment_intent": "pi_1234567890"}}}}, "tip_payment": {"method": "POST", "url": "/api/trips/{trip_id}/tip/", "description": "Paiement pourboire", "auth_required": true, "headers": {"Authorization": "Token user_token_here"}, "path_parameters": {"trip_id": "ID de la course"}, "request_example": {"amount": 5.0, "payment_method_id": "pm_card_visa", "payment_method": "CARD"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Tip payment successful", "tip": {"trip_id": 5, "amount": "5.00", "payment_status": "PAID", "captain_id": 82, "stripe_payment_intent": "pi_1234567891"}}}}, "transactions_list": {"method": "GET", "url": "/api/payments/transactions/", "description": "Historique des transactions", "auth_required": true, "headers": {"Authorization": "Token user_token_here"}, "response_success": {"status_code": 200, "body": {"transactions": [{"id": 1, "type": "CREDIT", "amount": "100.00", "description": "Re<PERSON>rge de portefeuille", "created_at": "2025-06-03T23:43:03.127220+02:00", "status": "COMPLETED"}, {"id": 2, "type": "DEBIT", "amount": "10.72", "description": "Paiement course #5", "created_at": "2025-06-04T00:04:16.294410+02:00", "status": "COMPLETED"}], "total_count": 2, "current_balance": "89.28"}}}, "withdraw_funds": {"method": "POST", "url": "/api/payments/withdraw/", "description": "Retrait de fonds du portefeuille", "auth_required": true, "headers": {"Authorization": "Token captain_token_here"}, "request_example": {"amount": 50.0, "bank_account": "***************************"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "<PERSON><PERSON>wal request submitted", "withdrawal": {"id": 1, "amount": "50.00", "bank_account": "***************************", "status": "PENDING", "processing_time": "1-3 business days", "created_at": "2025-06-04T00:10:00.000000+02:00"}}}}}, "establishment_endpoints": {"establishment_dashboard": {"method": "GET", "url": "/api/establishments/dashboard/", "description": "Tableau de bord de l'établissement", "auth_required": true, "headers": {"Authorization": "Token establishment_token_here"}, "response_success": {"status_code": 200, "body": {"dashboard": {"today_stats": {"shuttle_requests": 5, "completed_shuttles": 3, "pending_requests": 2}, "week_stats": {"total_shuttles": 28, "total_clients_served": 85, "average_response_time": "8 minutes"}, "boatmen_status": {"total_boatmen": 3, "available_boatmen": 2, "busy_boatmen": 1}, "recent_requests": [{"id": 15, "client_name": "<PERSON>", "departure": "Hôtel Paradise", "arrival": "Port de Cannes", "status": "PENDING", "created_at": "2025-06-04T00:04:00.000000+02:00"}]}}}}, "establishment_boatmen": {"method": "GET", "url": "/api/establishments/boatmen/", "description": "Liste des bateliers de l'établissement", "auth_required": true, "headers": {"Authorization": "Token establishment_token_here"}, "response_success": {"status_code": 200, "body": {"boatmen": [{"id": 91, "user": {"first_name": "<PERSON>", "last_name": "Batelier", "email": "<EMAIL>", "phone": "+33111222333"}, "license_number": "BOATMAN_PARADISE_001", "experience": "Batelier officiel Hotel Paradise - 5 ans", "availability_status": "AVAILABLE", "boat": {"id": 20, "name": "Paradise Shuttle", "capacity": 8}, "current_shuttles": 0, "total_shuttles_today": 2}], "total_count": 1}}}, "register_boatman": {"method": "POST", "url": "/api/establishments/register-boatman/", "description": "Enregistrer un nouveau batelier", "auth_required": true, "headers": {"Authorization": "Token establishment_token_here"}, "request_example": {"email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "Marin", "phone_number": "+33222333444", "experience": "Batelier expérimenté - 3 ans", "license_number": "BOATMAN_PARADISE_002"}, "response_success": {"status_code": 200, "body": {"status": "success", "message": "Boatman registered successfully", "data": {"captain_id": 92, "user_id": 101, "boat_id": 21, "temporary_password": "TempPass123!", "email_sent": true}}}}}, "boatman_endpoints": {"boatman_login": {"method": "POST", "url": "/api/boatman/login/", "description": "Connexion batelier avec code", "auth_required": false, "request_example": {"email": "<EMAIL>", "code": "123456"}, "response_success": {"status_code": 200, "body": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "user": {"id": 91, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "Batelier", "type": "CAPTAIN"}, "establishment": {"id": 1, "name": "Hotel Paradise"}}}}, "boatman_dashboard": {"method": "GET", "url": "/api/boatman/dashboard/", "description": "Tableau de bord du batelier", "auth_required": true, "headers": {"Authorization": "Token boatman_token_here"}, "response_success": {"status_code": 200, "body": {"dashboard": {"today_stats": {"shuttles_completed": 2, "clients_transported": 8, "hours_worked": 4.5}, "current_status": "AVAILABLE", "assigned_shuttles": 1, "boat_status": "AVAILABLE", "establishment": {"name": "Hotel Paradise", "contact": "+33123456789"}}}}}, "boatman_shuttles": {"method": "GET", "url": "/api/boatman/shuttles/", "description": "<PERSON><PERSON><PERSON> au batelier", "auth_required": true, "headers": {"Authorization": "Token boatman_token_here"}, "response_success": {"status_code": 200, "body": {"shuttles": [{"id": 6, "departure_location": "Hôtel Paradise", "arrival_location": "Port de Cannes", "passenger_count": 2, "status": "ASSIGNED", "client": {"name": "<PERSON>", "phone": "+33123456789"}, "estimated_pickup_time": "2025-06-04T02:05:00.000000", "payment": {"amount": "0.00", "method": "FREE_SHUTTLE"}, "created_at": "2025-06-04T00:04:00.000000+02:00"}], "total_count": 1}}}}}