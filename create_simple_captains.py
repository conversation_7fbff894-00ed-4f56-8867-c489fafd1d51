#!/usr/bin/env python
"""
Script simple pour créer quelques capitaines avec tarifs
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import Captain
from boats.models import Boat
from decimal import Decimal
import random

User = get_user_model()

def clean_database():
    """Nettoyer complètement la base"""
    print("🧹 Nettoyage de la base de données...")

    # Supprimer tous les bateaux
    Boat.objects.all().delete()
    print("   - Bateaux supprimés")

    # Supprimer tous les capitaines
    Captain.objects.all().delete()
    print("   - Capitaines supprimés")

    # Supprimer les utilisateurs capitaines
    captain_emails = [
        '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'
    ]

    for email in captain_emails:
        try:
            user = User.objects.get(email=email)
            user.delete()
            print(f"   - Utilisateur {email} supprimé")
        except User.DoesNotExist:
            pass

    print("✅ Base nettoyée\n")

def create_simple_captains():
    """Créer 5 capitaines simples avec tarifs"""
    print("🚢 Création de 5 capitaines...")

    captains_data = [
        {"name": "Capitaine Alpha", "email": "<EMAIL>", "boat_name": "Alpha One", "boat_type": "CLASSIC"},
        {"name": "Capitaine Beta", "email": "<EMAIL>", "boat_name": "Beta Two", "boat_type": "LUXE"},
        {"name": "Capitaine Gamma", "email": "<EMAIL>", "boat_name": "Gamma Three", "boat_type": "BLUE"},
        {"name": "Capitaine Delta", "email": "<EMAIL>", "boat_name": "Delta Four", "boat_type": "BOAT_XL"},
        {"name": "Capitaine Echo", "email": "<EMAIL>", "boat_name": "Echo Five", "boat_type": "NAVETTE"},
    ]

    created_captains = []

    for i, captain_data in enumerate(captains_data, 1):
        print(f"\n🔄 Création de {captain_data['name']}...")

        try:
            # Créer l'utilisateur
            user = User.objects.create_user(
                email=captain_data["email"],
                password="captain123",
                first_name=captain_data["name"].split()[1],
                last_name=captain_data["name"].split()[0],
                is_active=True,
                email_verified=True
            )
            print(f"   ✅ Utilisateur créé: {user.email}")

            # Créer le profil capitaine avec tarifs
            rate_km = Decimal(f"{random.uniform(1.5, 2.8):.2f}")
            rate_hour = Decimal(f"{random.uniform(25.0, 45.0):.2f}")

            captain = Captain.objects.create(
                user=user,
                license_number=f"LIC{i:03d}",
                experience=f"{random.randint(3, 12)} ans d'expérience maritime",
                rate_per_km=rate_km,
                rate_per_hour=rate_hour,
                is_available=True
            )
            print(f"   ✅ Capitaine créé avec tarifs: {rate_km}€/km, {rate_hour}€/h")

            # Le signal a automatiquement créé un bateau vide, on le met à jour
            boat = captain.boat  # Récupérer le bateau créé par le signal
            boat.name = captain_data["boat_name"]
            boat.registration_number = f"BN{i:04d}"
            boat.color = random.choice(["Blanc", "Bleu", "Rouge", "Vert"])
            boat.capacity = random.randint(6, 10)
            boat.boat_type = captain_data["boat_type"]
            boat.fuel_type = random.choice(["DIESEL", "GASOLINE"])
            boat.fuel_consumption = Decimal(f"{random.uniform(10.0, 18.0):.2f}")
            boat.is_available = True
            boat.zone_served = "Cotonou, Bénin"
            boat.radius = random.randint(25, 40)
            boat.save()
            print(f"   ✅ Bateau mis à jour: {captain_data['boat_name']} ({captain_data['boat_type']})")

            created_captains.append(captain)
            print(f"✅ {captain_data['name']} créé avec succès!")

        except Exception as e:
            print(f"❌ Erreur lors de la création de {captain_data['name']}: {e}")
            import traceback
            traceback.print_exc()

    print(f"\n🎉 {len(created_captains)} capitaines créés avec succès!")
    return created_captains

def main():
    print("🚀 Création de capitaines simples pour Commodore")
    print("=" * 50)

    # Nettoyer la base
    clean_database()

    # Créer les capitaines
    captains = create_simple_captains()

    print("\n📊 Résumé:")
    print(f"   - {len(captains)} capitaines créés avec tarifs")
    print("\n🎯 Données de connexion:")
    print("   Capitaines: <EMAIL> / captain123 (cap1 à cap8)")
    print("   Client de test: <EMAIL> / client123")
    print("\n✨ Prêt pour tester le système de devis automatiques!")

if __name__ == "__main__":
    main()
