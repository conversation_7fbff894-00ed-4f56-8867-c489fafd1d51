"""
Services d'intégration pour l'application boatman.
Gère l'intégration avec les autres applications du système.
"""

from django.utils import timezone
from django.db.models import Q
from decimal import Decimal

from accounts.models import Captain, Establishment
from trips.models import Trip, ShuttleTripRequest
from payments.models import Payment, Wallet
from notifications.services import create_notification


class BoatmanTripIntegrationService:
    """Service d'intégration pour les courses des bateliers"""
    
    @staticmethod
    def get_captain_assigned_trips(captain, status_filter=None, search=None):
        """
        Récupérer les courses assignées à un capitaine par les établissements.
        
        Args:
            captain: Instance du capitaine
            status_filter: Filtre de statut optionnel
            search: Terme de recherche optionnel
            
        Returns:
            QuerySet: Courses filtrées
        """
        # Récupérer uniquement les courses assignées au capitaine
        queryset = Trip.objects.filter(
            captain=captain
        ).select_related('client__user', 'boat').order_by('-scheduled_start_time')
        
        # Filtrer par statut
        if status_filter and status_filter != 'all':
            status_mapping = {
                'À venir': ['PENDING', 'ACCEPTED'],
                'En cours': ['IN_PROGRESS'],
                'Terminées': ['COMPLETED'],
                'Annulées': ['CANCELLED']
            }
            if status_filter in status_mapping:
                queryset = queryset.filter(status__in=status_mapping[status_filter])
        
        # Recherche
        if search:
            queryset = queryset.filter(
                Q(client__user__first_name__icontains=search) |
                Q(client__user__last_name__icontains=search) |
                Q(start_location__icontains=search) |
                Q(end_location__icontains=search)
            )
        
        return queryset
    
    @staticmethod
    def validate_trip_ownership(captain, trip_id):
        """
        Valider qu'une course appartient bien au capitaine.
        
        Args:
            captain: Instance du capitaine
            trip_id: ID de la course
            
        Returns:
            Trip: Instance de la course si valide
            
        Raises:
            Trip.DoesNotExist: Si la course n'existe pas ou n'appartient pas au capitaine
        """
        return Trip.objects.get(id=trip_id, captain=captain)
    
    @staticmethod
    def can_start_trip(trip):
        """
        Vérifier si une course peut être démarrée.
        
        Args:
            trip: Instance de la course
            
        Returns:
            tuple: (can_start: bool, reason: str)
        """
        if trip.status != 'ACCEPTED':
            return False, f"Impossible de démarrer une course avec le statut: {trip.status}"
        
        # Vérifier l'heure (ne peut pas démarrer trop tôt)
        if trip.scheduled_start_time > timezone.now() + timezone.timedelta(minutes=30):
            return False, "Impossible de démarrer la course plus de 30 minutes avant l'heure prévue"
        
        # Vérifier la disponibilité du capitaine
        if trip.captain.availability_status != 'AVAILABLE':
            return False, f"Capitaine non disponible: {trip.captain.availability_status}"
        
        return True, "Course peut être démarrée"
    
    @staticmethod
    def can_complete_trip(trip):
        """
        Vérifier si une course peut être terminée.
        
        Args:
            trip: Instance de la course
            
        Returns:
            tuple: (can_complete: bool, reason: str)
        """
        if trip.status != 'IN_PROGRESS':
            return False, f"Impossible de terminer une course avec le statut: {trip.status}"
        
        return True, "Course peut être terminée"
    
    @staticmethod
    def start_trip(trip):
        """
        Démarrer une course avec toutes les vérifications nécessaires.
        
        Args:
            trip: Instance de la course
            
        Returns:
            dict: Résultat de l'opération
        """
        can_start, reason = BoatmanTripIntegrationService.can_start_trip(trip)
        if not can_start:
            return {'success': False, 'error': reason}
        
        # Démarrer la course
        trip.status = 'IN_PROGRESS'
        trip.actual_start_time = timezone.now()
        trip.save()
        
        # Notifier le client
        if trip.client:
            create_notification(
                user=trip.client.user,
                title="Course démarrée",
                message=f"Votre course vers {trip.end_location} a commencé",
                notification_type="TRIP_STARTED",
                related_object_id=trip.id
            )
        
        return {
            'success': True,
            'trip_id': trip.id,
            'started_at': trip.actual_start_time,
            'estimated_arrival': trip.actual_start_time + timezone.timedelta(minutes=trip.estimated_duration or 30)
        }
    
    @staticmethod
    def complete_trip(trip):
        """
        Terminer une course avec toutes les actions nécessaires.
        
        Args:
            trip: Instance de la course
            
        Returns:
            dict: Résultat de l'opération
        """
        can_complete, reason = BoatmanTripIntegrationService.can_complete_trip(trip)
        if not can_complete:
            return {'success': False, 'error': reason}
        
        # Terminer la course
        trip.status = 'COMPLETED'
        trip.actual_end_time = timezone.now()
        
        # Calculer la durée réelle
        if trip.actual_start_time:
            actual_duration = (trip.actual_end_time - trip.actual_start_time).total_seconds() / 60
            trip.actual_duration = int(actual_duration)
        
        trip.save()
        
        # Notifier le client
        if trip.client:
            create_notification(
                user=trip.client.user,
                title="Course terminée",
                message=f"Vous êtes arrivé à {trip.end_location}. Merci d'avoir utilisé Commodore !",
                notification_type="TRIP_COMPLETED",
                related_object_id=trip.id
            )
        
        # Créer le paiement pour le capitaine (même si c'est gratuit, pour les statistiques)
        if trip.trip_type == 'NAVETTES_GRATUITES':
            # Pour les navettes gratuites, créer un paiement symbolique
            Payment.objects.create(
                user=trip.captain.user,
                trip=trip,
                type=Payment.PaymentType.TRIP,
                amount=Decimal('0.00'),  # Gratuit
                status=Payment.Status.COMPLETED,
                payment_method='FREE_SHUTTLE',
                description=f"Navette gratuite - {trip.start_location} vers {trip.end_location}"
            )
        
        return {
            'success': True,
            'trip_id': trip.id,
            'completed_at': trip.actual_end_time,
            'actual_duration': trip.actual_duration
        }


class BoatmanEstablishmentIntegrationService:
    """Service d'intégration avec les établissements"""
    
    @staticmethod
    def get_captain_establishment(captain):
        """
        Récupérer l'établissement qui a enregistré le capitaine.
        
        Args:
            captain: Instance du capitaine
            
        Returns:
            Establishment: Établissement ou None
        """
        # Vérifier si le capitaine a été enregistré par un établissement
        if hasattr(captain, 'registered_by_establishment'):
            return captain.registered_by_establishment
        
        # Sinon, chercher dans les métadonnées
        metadata = getattr(captain, 'metadata', {}) or {}
        establishment_id = metadata.get('registered_by_establishment_id')
        
        if establishment_id:
            try:
                return Establishment.objects.get(id=establishment_id)
            except Establishment.DoesNotExist:
                pass
        
        return None
    
    @staticmethod
    def notify_establishment_trip_update(trip, event_type):
        """
        Notifier l'établissement des mises à jour de courses.
        
        Args:
            trip: Instance de la course
            event_type: Type d'événement ('started', 'completed', 'cancelled')
        """
        establishment = BoatmanEstablishmentIntegrationService.get_captain_establishment(trip.captain)
        
        if not establishment:
            return
        
        messages = {
            'started': f"Course démarrée par {trip.captain.user.get_full_name()} vers {trip.end_location}",
            'completed': f"Course terminée par {trip.captain.user.get_full_name()} - {trip.end_location}",
            'cancelled': f"Course annulée par {trip.captain.user.get_full_name()} - {trip.end_location}"
        }
        
        titles = {
            'started': "Course démarrée",
            'completed': "Course terminée",
            'cancelled': "Course annulée"
        }
        
        if event_type in messages:
            create_notification(
                user=establishment.user,
                title=titles[event_type],
                message=messages[event_type],
                notification_type=f"TRIP_{event_type.upper()}",
                related_object_id=trip.id
            )


class BoatmanWalletIntegrationService:
    """Service d'intégration avec le système de portefeuille"""
    
    @staticmethod
    def ensure_captain_wallet(captain):
        """
        S'assurer qu'un capitaine a un portefeuille.
        
        Args:
            captain: Instance du capitaine
            
        Returns:
            Wallet: Instance du portefeuille
        """
        wallet, created = Wallet.objects.get_or_create(user=captain.user)
        return wallet
    
    @staticmethod
    def get_captain_earnings_stats(captain, days=30):
        """
        Récupérer les statistiques de gains d'un capitaine.
        
        Args:
            captain: Instance du capitaine
            days: Nombre de jours pour les statistiques
            
        Returns:
            dict: Statistiques des gains
        """
        from datetime import timedelta
        from django.db.models import Sum
        
        start_date = timezone.now() - timedelta(days=days)
        
        # Gains totaux
        total_earnings = Payment.objects.filter(
            user=captain.user,
            type__in=[Payment.PaymentType.TRIP, Payment.PaymentType.TIP],
            status=Payment.Status.COMPLETED
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Gains de la période
        period_earnings = Payment.objects.filter(
            user=captain.user,
            type__in=[Payment.PaymentType.TRIP, Payment.PaymentType.TIP],
            status=Payment.Status.COMPLETED,
            created_at__gte=start_date
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Pourboires totaux
        total_tips = Payment.objects.filter(
            user=captain.user,
            type=Payment.PaymentType.TIP,
            status=Payment.Status.COMPLETED
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Nombre de courses payées
        paid_trips = Payment.objects.filter(
            user=captain.user,
            type=Payment.PaymentType.TRIP,
            status=Payment.Status.COMPLETED
        ).count()
        
        return {
            'total_earnings': total_earnings,
            'period_earnings': period_earnings,
            'total_tips': total_tips,
            'paid_trips': paid_trips,
            'average_per_trip': total_earnings / paid_trips if paid_trips > 0 else Decimal('0.00')
        }


class BoatmanQRValidationService:
    """Service de validation des QR codes"""
    
    @staticmethod
    def generate_qr_code(trip):
        """
        Générer un QR code pour une course.
        
        Args:
            trip: Instance de la course
            
        Returns:
            str: QR code généré
        """
        return f"COMMODORE_TRIP_{trip.id}_{trip.client.id if trip.client else 'UNKNOWN'}"
    
    @staticmethod
    def validate_qr_code(trip, provided_qr):
        """
        Valider un QR code pour une course.
        
        Args:
            trip: Instance de la course
            provided_qr: QR code fourni
            
        Returns:
            tuple: (is_valid: bool, message: str)
        """
        expected_qr = BoatmanQRValidationService.generate_qr_code(trip)
        
        if provided_qr != expected_qr:
            return False, "QR code invalide"
        
        if trip.status != 'ACCEPTED':
            return False, f"Impossible de valider une course avec le statut: {trip.status}"
        
        return True, "QR code validé, embarquement confirmé"
