# Spécifications pour l'Espace Batelier

Ce document détaille les spécifications backend pour l'espace **Batelier** d'une application de gestion de navettes maritimes, basé sur le mockup fourni (`Accueil bateliers.pdf`). Les fonctionnalités d'authentification, de gestion des profils, et de paiement ne sont pas encore implémentées pour l'espace Batelier et doivent être intégrées comme c'est faire pour les autres utilisateur dans l'application **Account** existante, utilisée pour l'espace Établissement. L'enregistrement des bateliers tu dois verifier dans l'application **Establishment** est effectué par un établissement via l'espace Établissement, et la able batelier doit etre   ajouté à l'application **Account** aec tout c'est champs et un type d'utilisateur Batelier doit être ajouté avec un rôle `boatman`. Un mot de passe temporaire est généré, et un email est envoyé avec les informations de connexion. Il n'y a pas de processus d'inscription pour le batelier. Ce document inclut les champs, payloads JSON, actions, et user stories pour toutes les fonctionnalités.

## 1. Contexte et Fonctionnalités Existantes

### Fonctionnalités dans l'application Account (pour l'espace Établissement)
- **Authentification** :
  - Connexion : `POST /login` (email/mot de passe).
  - Inscription : `POST /signup` (email/mot de passe).
  - Mot de passe oublié : `POST /forgot-password`, envoi de code via `POST /send-verification`, vérification via `POST /verify-code`.
  - Changement de mot de passe : `POST /change-password`.
- **Gestion des profils** :
  - Consultation et création : `GET /establishment/profile`, `POST /establishment/profile`.
  - Modification : `PATCH /establishment/update`.
- **Paiements** :
  - Champs : Propriétaire, numéro de carte, date d'expiration, CVV, total à payer, options (crédit/carte).
  - Actions : `POST /payment-method`, `POST /process-payment`.
- Ces fonctionnalités doivent être adaptées pour l'espace Batelier et intégrées dans l'application **Account**.

## 2. Enregistrement du Batelier

### Logique
- L'enregistrement est effectué par un établissement via l'espace Établissement.
- Le système génère un mot de passe temporaire (8 caractères alphanumériques, ex. `X7k4mP9v`).
- Un email est envoyé au batelier avec :
  - Nom d'utilisateur : email du batelier
  - Mot de passe temporaire
  - Lien vers l'espace batelier (ex. `https://app.example.com/boatman/login`)
- Le batelier est ajouté à l'application **Account** avec un rôle `boatman`.
- Le batelier peut se connecter et changer son mot de passe après la première connexion.

### Champs
- Nom du capitaine : `string` (ex. "John Smith")
- Email : `string` (ex. "<EMAIL>")
- Nom du bateau : `string` (ex. "Beneteau Flyer 7.7 SUNdeck")
- Matricule : `string` (ex. "MA-345-CM")
- Capacité : `integer` (1-25, ex. 25)
- Type de carburant : `string` (essence, diesel, électrique, hybride)
- Consommation de carburant : `number` (ex. 12.0 L/h)

### Actions et Payloads JSON

#### 1. Enregistrer un batelier
**Requête :** `POST /establishment/boatman/register`  
*Note : Effectué par l'établissement via l'espace Établissement.*
```json
{
  "establishment_id": "string",
  "captain": {
    "name": "string",
    "email": "string"
  },
  "boat": {
    "name": "string",
    "registration": "string",
    "capacity": "integer",
    "fuel_type": "string",
    "fuel_consumption": "number"
  }
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Batelier enregistré, email avec mot de passe envoyé",
  "captain_id": "string"
}
```

## 3. Authentification (Pages 14, 24, 10-13)

### Champs
- Email : `string` (ex. "<EMAIL>")
- Mot de passe : `string`
- Code de vérification : `string` (4 chiffres, ex. "1572")
- Contact : `string` (email ou téléphone, ex. "ja**<EMAIL>" ou "+33***5678")
- Conditions : `boolean` (acceptation des termes)

### Actions et Payloads JSON

#### 1. Connexion
**Requête :** `POST /boatman/login`
```json
{
  "email": "string",
  "password": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "captain_id": "string",
    "token": "string",
    "name": "string"
  }
}
```

#### 2. Mot de passe oublié
**Requête :** `POST /boatman/forgot-password`
```json
{
  "contact": "string (email ou téléphone)"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Code de vérification envoyé"
}
```

#### 3. Envoyer le code de vérification
**Requête :** `POST /boatman/send-verification`
```json
{
  "captain_id": "string",
  "contact_method": "string (email, phone)",
  "contact_value": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Code de vérification envoyé"
}
```

#### 4. Vérifier le code
**Requête :** `POST /boatman/verify-code`
```json
{
  "captain_id": "string",
  "code": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Code vérifié"
}
```

## 4. Gestion des Profils (Pages 6-7, 22-23)

### Champs
- Email : `string` (ex. "<EMAIL>")
- Téléphone : `string` (ex. "+33612345678")
- Nom du capitaine : `string` (ex. "Jacob Jones")
- Bateau :
  - Nom : `string` (ex. "Beneteau Flyer 7.7 SUNdeck")
  - Matricule : `string` (ex. "MA-345-CM")
  - Capacité : `integer` (ex. 25)
  - Type de carburant : `string` (ex. "essence")
  - Consommation : `number` (ex. 12.0 L/h)

### Actions et Payloads JSON

#### 1. Consulter le profil
**Requête :** `GET /boatman/profile`
```json
{
  "captain_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "captain_id": "string",
    "name": "string",
    "email": "string",
    "phone": "string",
    "boat": {
      "name": "string",
      "registration": "string",
      "capacity": "integer",
      "fuel_type": "string",
      "fuel_consumption": "number"
    }
  }
}
```

#### 2. Mettre à jour le profil
**Requête :** `PATCH /boatman/profile`
```json
{
  "captain_id": "string",
  "email": "string",
  "phone": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Profil mis à jour"
}
```

## 5. Changement de Mot de Passe (Pages 2-5)

### Champs
- Ancien mot de passe : `string`
- Nouveau mot de passe : `string` (min. 8 chars, majuscule, minuscule, caractère spécial)
- Confirmer le mot de passe : `string`

### Actions et Payloads JSON

#### 1. Changer le mot de passe
**Requête :** `POST /boatman/change-password`
```json
{
  "captain_id": "string",
  "old_password": "string",
  "new_password": "string",
  "confirm_password": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Mot de passe mis à jour"
}
```

## 6. Paiements (Pages 8-9, 27-28)

### Champs
- Propriétaire : `string` (ex. "Jacob Jones")
- Numéro de carte : `string` (ex. "****************")
- Date d'expiration : `string` (ex. "11/26")
- CVV : `string` (ex. "231")
- Total à payer : `number` (ex. 25.00)
- Montant à retirer : `number` (ex. 25.00)

### Actions et Payloads JSON

#### 1. Ajouter/Mettre à jour la méthode de paiement
**Requête :** `POST /boatman/payment-method`
```json
{
  "captain_id": "string",
  "cardholder": "string",
  "card_number": "string",
  "expiry_date": "string (MM/YY)",
  "cvv": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Méthode de paiement mise à jour"
}
```

#### 2. Retirer des revenus
**Requête :** `POST /boatman/wallet/withdraw`
```json
{
  "captain_id": "string",
  "amount": "number"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Retrait effectué, fonds en cours de transfert",
  "new_balance": "number"
}
```

## 7. Tableau de Bord (Page 1)

### Champs
- Solde disponible : `number` (ex. 25.00)
- Courses réalisées : `integer` (ex. 12)
- Pourboire total généré : `number` (ex. 120.00)
- Courses à venir :
  - `shuttle_id` : `string`
  - `date` : `string` (ISO 8601, ex. "2024-05-18T14:00:00Z")
  - `destination` : `string` (ex. "Plage de la Promenade")
  - `passengers` : `integer` (ex. 4)
  - `client` : `string` (ex. "Jacob28")

### Actions et Payloads JSON

#### 1. Récupérer les données du tableau de bord
**Requête :** `GET /boatman/dashboard`
```json
{
  "captain_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "available_balance": "number",
    "total_shuttles": "integer",
    "total_tips": "number",
    "upcoming_shuttles": [
      {
        "shuttle_id": "string",
        "date": "string",
        "destination": "string",
        "passengers": "integer",
        "client": "string"
      }
    ]
  }
}
```

## 8. Gestion des Courses (Pages 16-18)

### Champs
- Filtres : `string` (À venir, En cours, Terminées, Annulées)
- Courses :
  - `shuttle_id` : `string`
  - `client` : `string` (ex. "WaveRiders")
  - `date` : `string` (ISO 8601, ex. "2024-05-23T11:00:00Z")
  - `destination` : `string` (ex. "Plage de la Petite Afrique")
  - `passengers` : `integer` (ex. 4)

### Actions et Payloads JSON

#### 1. Récupérer la liste des courses
**Requête :** `GET /boatman/shuttles`
```json
{
  "captain_id": "string",
  "filter": "string (optionnel, ex. À venir)",
  "page": "integer",
  "limit": "integer"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "shuttles": [
      {
        "shuttle_id": "string",
        "client": "string",
        "date": "string",
        "destination": "string",
        "passengers": "integer",
        "status": "string"
      }
    ],
    "pagination": {
      "page": "integer",
      "limit": "integer",
      "total": "integer"
    }
  }
}
```

## 9. Détails de la Course (Pages 19-21)

### Champs
- Localisation : `string` (ex. "Clinton", non interactive)
- Statut : `string` (À venir, En cours, Terminé)
- Date : `string` (ISO 8601, ex. "2024-05-17T16:30:00Z")
- Départ : `string` (ex. "Port de Cannes")
- Passagers : `integer` (ex. 5)
- Client : `string` (ex. "Jacob28")
- Pourboire : `number` (ex. 5.00, si terminé)

### Actions et Payloads JSON

#### 1. Récupérer les détails de la course
**Requête :** `GET /boatman/shuttle/:shuttle_id`
```json
{
  "captain_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "shuttle_id": "string",
    "status": "string",
    "date": "string",
    "departure": "string",
    "passengers": "integer",
    "client": "string",
    "tip": "number (optionnel)"
  }
}
```

#### 2. Démarrer la course
**Requête :** `POST /boatman/shuttle/:shuttle_id/start`
```json
{
  "captain_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Course démarrée"
}
```

#### 3. Terminer la course
**Requête :** `POST /boatman/shuttle/:shuttle_id/end`
```json
{
  "captain_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "Course terminée"
}
```

## 10. Suivi de la Course (Pages 15, 31)

### Champs
- Localisation actuelle : `string` (ex. "Possession")
- Distance : `number` (ex. 0 km)
- Destination : `string` (ex. "Plage de la Petite Afrique")
- Passagers : `integer` (ex. 4)
- Client : `string` (ex. "WaveRiders")

### Actions et Payloads JSON

#### 1. Suivre la course
**Requête :** `GET /boatman/shuttle/:shuttle_id/track`
```json
{
  "captain_id": "string",
  "shuttle_id": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "data": {
    "shuttle_id": "string",
    "current_location": "string",
    "distance": "number",
    "destination": "string",
    "passengers": "integer",
    "client": "string"
  }
}
```

## 11. Scanner le QR Code (Page 30)

### Logique
- Le batelier scanne un QR code unique lié à la réservation pour valider l'embarquement et démarrer la course.
- Le QR code contient l'identifiant de la course (`shuttle_id`).

### Actions et Payloads JSON

#### 1. Valider le QR code
**Requête :** `POST /boatman/shuttle/:shuttle_id/validate-qr`
```json
{
  "captain_id": "string",
  "shuttle_id": "string",
  "qr_code": "string"
}
```
**Réponse :**
```json
{
  "status": "success",
  "message": "QR code validé, embarquement confirmé"
}
```

## 12. Portefeuille (Page 27)

### Champs
- Solde disponible : `number` (ex. 25.35)
- Historique des opérations :
  - Type : `string` (Retrait, Pourboire)
  - Montant : `number` (ex. -25.00, *****)
  - Date : `string` (ISO 8601, ex. "2024-05-17T23T16:30:00Z")
  - Détails : `string` (ex. "Port de Cannes, 4 passengers, Jacob28")

### Actions and Payloads JSON

#### 1. Consulter le portefeuille
**Requête :** `GET /boatman/wallet`
```json
{
  "captain_id": "string",
  "page": "integer",
  "limit": "integer"
}
```
**Réponse :**
```json
{
  {
  "status": "success",
  "data": {
    "available_balance": "number",
    "transactions": [
      {
        "type": "string",
        "amount": "number",
        "date": "string",
        "details": "string"
      }
    ],
    "pagination": {
      "page": "integer",
      "limit": "integer",
      "total": "integer"
    }
  }
}
}
```

##  User Stories

1. **Enregistrement par un établissement**
   - **Description** : En tant que gestionnaire d'un établissement, je veux enregistrer un batelier pour qu'il puisse gérer les navettes de mon établissement.
   - **Critères d'acceptation** :
     - Saisir le nom, l'email, le nom du bateau, le matricule, la capacité, le type de carburant, et la consommation.
     - Générer un mot de passe temporaire et envoyer un email au batelier avec ses informations de connexion.
     - Ajouter le batelier à l'application **Account** avec un rôle `boatman`.
     - Afficher un message de succès après l'enregistrement.

2. **Connexion**
   - **Description** : En tant que batelier, je veux me connecter à mon espace avec mon email et mon mot de passe temporaire pour accéder à mes fonctionnalités.
   - **Critères d'acceptation** :
     - Accepter l'email et le mot de passe.
     - Fournir une option "Mot de passe oublié".
     - Rediriger vers le tableau de bord après une connexion réussie.

3. **Mot de passe oublié**
   - **Description** : En tant que batelier, je veux réinitialiser mon mot de passe si je l'oublie pour récupérer l'accès à mon compte.
   - **Critères d'acceptation** :
     - Permettre la sélection de l'email ou du téléphone pour recevoir un code de vérification.
     - Envoyer un code de vérification à la méthode choisie.
     - Rediriger vers la page de vérification du code.

4. **Vérifier l'identité**
   - **Description** : En tant que batelier, je veux vérifier mon identité avec un code envoyé à mon email ou téléphone pour sécuriser mon compte.
   - **Critères d'acceptation** :
     - Envoyer un code de vérification à la méthode de contact choisie.
     - Permettre le renvoi du code.
     - Valider le code saisi (4 chiffres).
     - Afficher un message de succès après vérification.

5. **Changer le mot de passe**
   - **Description** : En tant que batelier, je veux changer mon mot de passe pour sécuriser mon compte.
   - **Critères d'acceptation** :
     - Exiger un mot de passe d'au moins 8 caractères avec majuscule, minuscule, et caractère spécial.
     - Confirmer que les mots de passe correspondent.
     - Afficher un message de succès après la mise à jour.

6. **Gérer le profil**
   - **Description** : En tant que batelier, je veux consulter et mettre à jour mon profil (email, téléphone) pour maintenir mes informations à jour.
   - **Critères d'acceptation** :
     - Afficher le nom, l'email, le téléphone, et les détails du bateau.
     - Permettre de modifier l'email ou le téléphone.
     - Afficher un message de succès après la mise à jour.

7. **Gérer les informations de paiement**
   - **Description** : En tant que batelier, je veux ajouter ou mettre à jour ma méthode de paiement pour effectuer des retraits.
   - **Critères d'acceptation** :
     - Accepter le nom du titulaire, le numéro de carte, la date d'expiration, et le CVV.
     - Afficher un message de succès après la mise à jour.

8. **Vue d'ensemble du tableau de bord**
   - **Description** : En tant que batelier, je veux voir mon tableau de bord pour consulter mon solde disponible, le nombre de courses réalisées, le pourboire total généré, et mes courses à venir.
   - **Critères d'acceptation** :
     - Afficher le solde disponible, le nombre de courses réalisées, et le pourboire total.
     - Montrer une liste des courses à venir avec date, destination, nombre de passagers, et client.
     - Permettre de naviguer vers la liste complète des courses.

9. **Gérer les courses**
   - **Description** : En tant que batelier, je veux gérer mes courses pour voir celles qui me sont assignées, filtrées par statut (À venir, En cours, Terminées, Annulées).
   - **Critères d'acceptation** :
     - Afficher une liste des courses avec client, date, destination, passagers, et statut.
     - Permettre le filtrage par statut.
     - Fournir une option pour voir les détails de chaque course.

10. **Consulter les détails d'une course**
    - **Description** : En tant que batelier, je veux consulter les détails d'une course pour voir le statut, la date, le départ, le nombre de passagers, le client, et le pourboire (si applicable).
    - **Critères d'acceptation** :
      - Afficher le statut, la date, le départ, les passagers, le client, et le pourboire (si terminé).
      - Permettre de démarrer ou terminer la course si le statut le permet.

11. **Démarrer une course**
    - **Description** : En tant que batelier, je veux démarrer une course assignée pour commencer le transport des passagers.
    - **Critères d'acceptation** :
      - Afficher un bouton "Démarrer la course" pour les courses à venir.
      - Valider le démarrage avec un QR code scanné.
      - Afficher un message de confirmation après le démarrage.

12. **Terminer une course**
    - **Description** : En tant que batelier, je veux terminer une course en cours pour indiquer que les passagers sont arrivés à destination.
    - **Critères d'acceptation** :
      - Afficher un bouton "Terminer la course" pour les courses en cours.
      - Afficher un message de confirmation après la fin de la course.
      - Mettre à jour le statut de la course à "Terminé".

13. **Suivre une course**
    - **Description** : En tant que batelier, je veux suivre une course en cours pour voir ma position actuelle, la distance restante, la destination, les passagers, et le client.
    - **Critères d'acceptation** :
      - Afficher la localisation actuelle, la distance, la destination, les passagers, et le client.
      - Mettre à jour les informations en temps réel.

14. **Scanner le QR code**
    - **Description** : En tant que batelier, je veux scanner le QR code du client pour valider l'embarquement et démarrer la course.
    - **Critères d'acceptation** :
      - Permettre de scanner un QR code unique lié à la réservation.
      - Valider le QR code et confirmer l'embarquement.
      - Afficher un message de succès après validation.

15. **Consulter le portefeuille**
    - **Description** : En tant que batelier, je veux consulter mon portefeuille pour voir mon solde disponible et l'historique de mes transactions (retraits, pourboires).
    - **Critères d'acceptation** :
      - Afficher le solde disponible.
      - Montrer l'historique des transactions avec type, montant, date, et détails.
      - Supporter la pagination pour l'historique.

16. **Retirer des revenus**
    - **Description** : En tant que batelier, je veux retirer mes revenus vers mon compte bancaire.
    - **Critères d'acceptation** :
      - Permettre de saisir un montant à retirer.
      - Vérifier que le solde est suffisant.
      - Afficher un message de confirmation avec le nouveau solde.
      - Indiquer que les fonds seront transférés dans les délais habituels.