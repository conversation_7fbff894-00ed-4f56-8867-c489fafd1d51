import requests
import json

FIGMA_FILE_KEY = "eGk8lVZLfiEVHMDUgGdGB9"
FIGMA_API_TOKEN = "*********************************************"

headers = {
    "X-Figma-Token": FIGMA_API_TOKEN
}

def extract_text_nodes(node, prefix=""):
    strings = {}
    if "name" in node and node["type"] == "TEXT" and "characters" in node:
        key = prefix + node["name"].lower().replace(" ", "_")
        strings[key] = node["characters"]

    if "children" in node:
        for child in node["children"]:
            child_prefix = f"{prefix}{node.get('name', '').lower().replace(' ', '_')}."
            strings.update(extract_text_nodes(child, prefix=child_prefix))

    return strings

# Fetch file JSON
res = requests.get(f"https://api.figma.com/v1/files/{FIGMA_FILE_KEY}", headers=headers)
data = res.json()

# Extract text recursively
text_nodes = extract_text_nodes(data["document"])

# Export to JSON file
with open("locales/fr.json", "w", encoding="utf-8") as f:
    json.dump(text_nodes, f, indent=2, ensure_ascii=False)

print("✅ Texte exporté vers locales/fr.json")
