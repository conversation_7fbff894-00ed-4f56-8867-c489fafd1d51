{"api_documentation": {"title": "Commodore API - Documentation Complète des Endpoints", "version": "1.0.0", "description": "Documentation extraite des VRAIES réponses API du système", "base_url": "http://127.0.0.1:8000", "authentication": "Token-based authentication", "generated_at": "2025-06-04T00:04:20.463093", "total_endpoints_tested": 45}, "endpoints": {"register": {"endpoint": "POST /api/register/", "description": "Inscription d'un nouvel utilisateur", "request_body": {"email": "<EMAIL>", "password": "TestPassword123!", "name": "Test User", "user_type": "CLIENT"}, "response": {"status_code": 400, "headers": {"Date": "Tue, 03 Jun 2025 22:04:09 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "50d4c2a042dc46b6b98d6f16d31c5f04", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=140.07700001820922;desc=\"Elapsed time\", SQLPanel_sql_time;dur=3.346100013004616;desc=\"SQL 2 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "77", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"email": ["Un objet Utilisateur avec ce champ adresse email existe déjà."]}, "text": null}}, "login": {"endpoint": "POST /api/login/", "description": "Connexion utilisateur", "request_body": {"email": "<EMAIL>", "password": "TestClient123!"}, "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:10 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "b7eb048fc8404491ab2be915129320f1", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=406.29069998976775;desc=\"Elapsed time\", SQLPanel_sql_time;dur=6.113600014941767;desc=\"SQL 5 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "1043", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4OTkxODUwLCJpYXQiOjE3NDg5ODgyNTAsImp0aSI6IjNjNmE1ZjFhYjBhMDRlZGU4NzY5MzU1YmVjNmY0NmNhIiwidXNlcl9pZCI6OTl9.p7H74a-Cisb2m9H5luQmB3y7ALcEVgjlBx43fGLej-o", "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTA3NDY1MCwiaWF0IjoxNzQ4OTg4MjUwLCJqdGkiOiI3OWYyMDdlNWU2ZTU0MTMyOGNkYjZkZmI4ZTBkNzBjOCIsInVzZXJfaWQiOjk5fQ.4SD6sJwkffKYchGAUopg8MrMBuIRWbukbKqOc4uEpm0", "user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "client_profile": {"user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "is_active": true}, "wallet_balance": "0.00", "date_of_birth": null, "nationality": "", "preferred_language": "fr", "emergency_contact_name": "", "emergency_contact_phone": ""}, "captain_profile": null, "establishment_profile": null}}, "text": null}}, "wallet_detail": {"endpoint": "GET /api/payments/wallet/", "description": "Consulter les détails du portefeuille", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:10 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "8abe24195e61447fa9366750b80954c4", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=120.4223999811802;desc=\"Elapsed time\", SQLPanel_sql_time;dur=6.379400001605973;desc=\"SQL 4 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "392", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"id": 99, "user": 99, "balance": "0.00", "created_at": "2025-06-03T22:54:38.398384+02:00", "updated_at": "2025-06-03T22:54:38.398384+02:00", "user_details": {"id": 99, "username": null, "email": "<EMAIL>", "full_name": "<PERSON>"}, "recent_transactions": [{"id": 1, "type": "CREDIT", "amount": "100.00", "description": "Re<PERSON>rge de portefeuille", "created_at": "2025-06-03T23:43:03.127220+02:00"}]}, "text": null}}, "wallet_recharge": {"endpoint": "POST /api/payments/wallet/recharge/", "description": "Recharger le portefeuille avec Stripe", "request_body": {"amount": 100.0, "payment_method_id": "pm_card_visa"}, "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:14 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "e15e13d517504cba9cbda614c1e83171", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=3905.458900000667;desc=\"Elapsed time\", SQLPanel_sql_time;dur=6.94870000006631;desc=\"SQL 3 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "503", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"session_id": "cs_test_a17erTZnSk7Wpk6K2MLHzbXJPQhECuKmQAKbAzxBWE0Hx6IvAWT9W7OOmR", "transaction_id": 2, "checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a17erTZnSk7Wpk6K2MLHzbXJPQhECuKmQAKbAzxBWE0Hx6IvAWT9W7OOmR#fidpamZkaWAnPydjdycpJ2R1bE5gfCc%2FJ3VuWnFgdnFaMDRXVnVBcUEzRGJ2MGBGRkhwfUI3VEBSbHFJaTZfaz00NVNdSXd9V2NgXDxOUkloTWlNR3xqaTBpdkxQZ2BdNmZDNzxoSWpPUWhPSU5OZnVLQlE2aWo1ZG01NUNmPHNyYkxpJyknY3dqaFZgd3Ngdyc%2FcXdwYCknaWR8anBxUXx1YCc%2FJ3Zsa2JpYFpscWBoJyknYGtkZ2lgVWlkZmBtamlhYHd2Jz9xd3BgeCUl"}, "text": null}}, "create_simple_trip": {"endpoint": "POST /api/trips/requests/simple/", "description": "<PERSON><PERSON><PERSON> une demande de course simple", "request_body": {"departure_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}, "timestamp": "2025-06-04T02:04:14.239345"}, "arrival_location": {"city_name": "Îles de Lérins", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}, "timestamp": "2025-06-04T02:04:14.239345"}, "departure_date": "2025-06-04", "departure_time": "02:04:14.239345", "passenger_count": 4, "boat_type": "CLASSIC"}, "response": {"status_code": 201, "headers": {"Date": "Tue, 03 Jun 2025 22:04:14 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "b1db323407f040539782a0891ae5b572", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=230.87329999543726;desc=\"Elapsed time\", SQLPanel_sql_time;dur=27.94159998302348;desc=\"SQL 32 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "12663", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"trip_request": {"id": 30, "departure_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}, "timestamp": "2025-06-04T02:04:14.239345"}, "arrival_location": {"city_name": "Îles de Lérins", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}, "timestamp": "2025-06-04T02:04:14.239345"}, "client": {"user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "is_active": true}, "wallet_balance": "0.00", "date_of_birth": null, "nationality": "", "preferred_language": "fr", "emergency_contact_name": "", "emergency_contact_phone": ""}, "distance_km": "4.45", "boat_type": "CLASSIC", "trip_type": "SIMPLE", "status": "PENDING", "passenger_count": 4, "created_at": "2025-06-04T00:04:14.301362+02:00", "updated_at": "2025-06-04T00:04:14.307655+02:00", "expires_at": "2025-06-04T00:14:14.301362+02:00", "scheduled_date": null, "scheduled_time": null}, "quotes": [{"id": 74, "captain_details": {"user": {"id": 82, "email": "<EMAIL>", "first_name": "Alpha", "last_name": "Capitaine", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "experience": "7 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC001", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.41", "rate_per_hour": "44.06", "boat": {"id": 11, "name": "Alpha One", "registration_number": "BN0001", "boat_type": "CLASSIC", "capacity": 8, "color": "Rouge", "fuel_type": "GASOLINE", "fuel_consumption": 10.89, "photos": [], "zone_served": "Cotonou, Bénin", "radius": 35, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:23:48.135479Z", "updated_at": "2025-05-31T06:23:48.135479Z"}}, "boat_details": {"id": 11, "name": "Alpha One", "registration_number": "BN0001", "boat_type": "CLASSIC", "capacity": 8, "color": "Rouge", "fuel_type": "GASOLINE", "fuel_consumption": "10.89", "photos": [], "zone_served": "Cotonou, Bénin", "radius": 35, "captain": {"id": 82, "user": {"id": 82, "email": "<EMAIL>", "first_name": "Alpha", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "7 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC001", "years_of_experience": 0, "rate_per_hour": 44.06}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T08:23:48.135479+02:00", "updated_at": "2025-05-31T08:23:48.135479+02:00", "maintenance_records": []}, "base_price": "10.72", "distance_km": "4.45", "rate_used": "2.41", "captain_name": "Alpha Capitaine", "captain_rating": "4.50", "boat_name": "Alpha One", "boat_capacity": 8, "created_at": "2025-06-04T00:04:14.316328+02:00", "is_available": true, "trip_request": 30, "captain": 82, "boat": 11}, {"id": 75, "captain_details": {"user": {"id": 90, "email": "<EMAIL>", "first_name": "Epsilon", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "4.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC090", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.20", "rate_per_hour": "40.00", "boat": {"id": 19, "name": "Chaparral 21 H2O", "registration_number": null, "boat_type": "CLASSIC", "capacity": 9, "color": "<PERSON>ert", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:59:46.608590Z", "updated_at": "2025-05-31T07:05:44.776794Z"}}, "boat_details": {"id": 19, "name": "Chaparral 21 H2O", "registration_number": null, "boat_type": "CLASSIC", "capacity": 9, "color": "<PERSON>ert", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 90, "user": {"id": 90, "email": "<EMAIL>", "first_name": "Epsilon", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "4.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC090", "years_of_experience": 0, "rate_per_hour": 40.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T08:59:46.608590+02:00", "updated_at": "2025-05-31T09:05:44.776794+02:00", "maintenance_records": []}, "base_price": "9.79", "distance_km": "4.45", "rate_used": "2.20", "captain_name": "Epsilon Capitaine", "captain_rating": "4.50", "boat_name": "Chaparral 21 H2O", "boat_capacity": 9, "created_at": "2025-06-04T00:04:14.327169+02:00", "is_available": true, "trip_request": 30, "captain": 90, "boat": 19}, {"id": 76, "captain_details": {"user": {"id": 91, "email": "<EMAIL>", "first_name": "Zeta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "2.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC091", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.20", "rate_per_hour": "25.00", "boat": {"id": 20, "name": "Classic Explorer", "registration_number": null, "boat_type": "CLASSIC", "capacity": 6, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:12.925554Z", "updated_at": "2025-05-31T07:08:12.930519Z"}}, "boat_details": {"id": 20, "name": "Classic Explorer", "registration_number": null, "boat_type": "CLASSIC", "capacity": 6, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 91, "user": {"id": 91, "email": "<EMAIL>", "first_name": "Zeta", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "2.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC091", "years_of_experience": 0, "rate_per_hour": 25.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:12.925554+02:00", "updated_at": "2025-05-31T09:08:12.930519+02:00", "maintenance_records": []}, "base_price": "5.34", "distance_km": "4.45", "rate_used": "1.20", "captain_name": "Zeta Capitaine", "captain_rating": "4.50", "boat_name": "Classic Explorer", "boat_capacity": 6, "created_at": "2025-06-04T00:04:14.334197+02:00", "is_available": true, "trip_request": 30, "captain": 91, "boat": 20}, {"id": 77, "captain_details": {"user": {"id": 92, "email": "<EMAIL>", "first_name": "Eta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "3.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC092", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.60", "rate_per_hour": "32.00", "boat": {"id": 21, "name": "Ocean Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 8, "color": "Bleu", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:13.191593Z", "updated_at": "2025-05-31T07:08:13.191593Z"}}, "boat_details": {"id": 21, "name": "Ocean Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 8, "color": "Bleu", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 92, "user": {"id": 92, "email": "<EMAIL>", "first_name": "Eta", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "3.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC092", "years_of_experience": 0, "rate_per_hour": 32.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:13.191593+02:00", "updated_at": "2025-05-31T09:08:13.191593+02:00", "maintenance_records": []}, "base_price": "7.12", "distance_km": "4.45", "rate_used": "1.60", "captain_name": "Eta Capitaine", "captain_rating": "4.50", "boat_name": "Ocean Classic", "boat_capacity": 8, "created_at": "2025-06-04T00:04:14.341871+02:00", "is_available": true, "trip_request": 30, "captain": 92, "boat": 21}, {"id": 78, "captain_details": {"user": {"id": 93, "email": "<EMAIL>", "first_name": "Theta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "3.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC093", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.90", "rate_per_hour": "38.00", "boat": {"id": 22, "name": "Classic Voyager", "registration_number": null, "boat_type": "CLASSIC", "capacity": 10, "color": "Rouge", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:13.451820Z", "updated_at": "2025-05-31T07:08:13.455558Z"}}, "boat_details": {"id": 22, "name": "Classic Voyager", "registration_number": null, "boat_type": "CLASSIC", "capacity": 10, "color": "Rouge", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 93, "user": {"id": 93, "email": "<EMAIL>", "first_name": "Theta", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "3.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC093", "years_of_experience": 0, "rate_per_hour": 38.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:13.451820+02:00", "updated_at": "2025-05-31T09:08:13.455558+02:00", "maintenance_records": []}, "base_price": "8.46", "distance_km": "4.45", "rate_used": "1.90", "captain_name": "Theta Capitaine", "captain_rating": "4.50", "boat_name": "Classic Voyager", "boat_capacity": 10, "created_at": "2025-06-04T00:04:14.348658+02:00", "is_available": true, "trip_request": 30, "captain": 93, "boat": 22}, {"id": 79, "captain_details": {"user": {"id": 94, "email": "<EMAIL>", "first_name": "Iota", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "4.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC094", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.25", "rate_per_hour": "45.00", "boat": {"id": 23, "name": "Premium Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 12, "color": "Noir", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:13.725335Z", "updated_at": "2025-05-31T07:08:13.730280Z"}}, "boat_details": {"id": 23, "name": "Premium Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 12, "color": "Noir", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 94, "user": {"id": 94, "email": "<EMAIL>", "first_name": "Iota", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "4.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC094", "years_of_experience": 0, "rate_per_hour": 45.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:13.725335+02:00", "updated_at": "2025-05-31T09:08:13.730280+02:00", "maintenance_records": []}, "base_price": "10.01", "distance_km": "4.45", "rate_used": "2.25", "captain_name": "Iota Capitaine", "captain_rating": "4.50", "boat_name": "Premium Classic", "boat_capacity": 12, "created_at": "2025-06-04T00:04:14.355766+02:00", "is_available": true, "trip_request": 30, "captain": 94, "boat": 23}]}, "text": null}}, "get_trip_quotes": {"endpoint": "GET /api/trips/requests/30/", "description": "Récupérer les devis pour une demande", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:14 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, PATCH, HEAD, OPTIONS", "djdt-store-id": "f518bcc03ccb42c69abcdb20e3a7fd73", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=243.13160000019707;desc=\"Elapsed time\", SQLPanel_sql_time;dur=32.59839987731539;desc=\"SQL 50 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "12663", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"trip_request": {"id": 30, "departure_location": {"city_name": "Port de Cannes", "timestamp": "2025-06-04T02:04:14.239345", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}}, "arrival_location": {"city_name": "Îles de Lérins", "timestamp": "2025-06-04T02:04:14.239345", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}}, "client": {"user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "is_active": true}, "wallet_balance": "0.00", "date_of_birth": null, "nationality": "", "preferred_language": "fr", "emergency_contact_name": "", "emergency_contact_phone": ""}, "distance_km": "4.45", "boat_type": "CLASSIC", "trip_type": "SIMPLE", "status": "PENDING", "passenger_count": 4, "created_at": "2025-06-04T00:04:14.301362+02:00", "updated_at": "2025-06-04T00:04:14.307655+02:00", "expires_at": "2025-06-04T00:14:14.301362+02:00", "scheduled_date": null, "scheduled_time": null}, "quotes": [{"id": 76, "captain_details": {"user": {"id": 91, "email": "<EMAIL>", "first_name": "Zeta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "2.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC091", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.20", "rate_per_hour": "25.00", "boat": {"id": 20, "name": "Classic Explorer", "registration_number": null, "boat_type": "CLASSIC", "capacity": 6, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:12.925554Z", "updated_at": "2025-05-31T07:08:12.930519Z"}}, "boat_details": {"id": 20, "name": "Classic Explorer", "registration_number": null, "boat_type": "CLASSIC", "capacity": 6, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 91, "user": {"id": 91, "email": "<EMAIL>", "first_name": "Zeta", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "2.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC091", "years_of_experience": 0, "rate_per_hour": 25.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:12.925554+02:00", "updated_at": "2025-05-31T09:08:12.930519+02:00", "maintenance_records": []}, "base_price": "5.34", "distance_km": "4.45", "rate_used": "1.20", "captain_name": "Zeta Capitaine", "captain_rating": "4.50", "boat_name": "Classic Explorer", "boat_capacity": 6, "created_at": "2025-06-04T00:04:14.334197+02:00", "is_available": true, "trip_request": 30, "captain": 91, "boat": 20}, {"id": 77, "captain_details": {"user": {"id": 92, "email": "<EMAIL>", "first_name": "Eta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "3.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC092", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.60", "rate_per_hour": "32.00", "boat": {"id": 21, "name": "Ocean Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 8, "color": "Bleu", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:13.191593Z", "updated_at": "2025-05-31T07:08:13.191593Z"}}, "boat_details": {"id": 21, "name": "Ocean Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 8, "color": "Bleu", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 92, "user": {"id": 92, "email": "<EMAIL>", "first_name": "Eta", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "3.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC092", "years_of_experience": 0, "rate_per_hour": 32.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:13.191593+02:00", "updated_at": "2025-05-31T09:08:13.191593+02:00", "maintenance_records": []}, "base_price": "7.12", "distance_km": "4.45", "rate_used": "1.60", "captain_name": "Eta Capitaine", "captain_rating": "4.50", "boat_name": "Ocean Classic", "boat_capacity": 8, "created_at": "2025-06-04T00:04:14.341871+02:00", "is_available": true, "trip_request": 30, "captain": 92, "boat": 21}, {"id": 78, "captain_details": {"user": {"id": 93, "email": "<EMAIL>", "first_name": "Theta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "3.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC093", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.90", "rate_per_hour": "38.00", "boat": {"id": 22, "name": "Classic Voyager", "registration_number": null, "boat_type": "CLASSIC", "capacity": 10, "color": "Rouge", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:13.451820Z", "updated_at": "2025-05-31T07:08:13.455558Z"}}, "boat_details": {"id": 22, "name": "Classic Voyager", "registration_number": null, "boat_type": "CLASSIC", "capacity": 10, "color": "Rouge", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 93, "user": {"id": 93, "email": "<EMAIL>", "first_name": "Theta", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "3.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC093", "years_of_experience": 0, "rate_per_hour": 38.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:13.451820+02:00", "updated_at": "2025-05-31T09:08:13.455558+02:00", "maintenance_records": []}, "base_price": "8.46", "distance_km": "4.45", "rate_used": "1.90", "captain_name": "Theta Capitaine", "captain_rating": "4.50", "boat_name": "Classic Voyager", "boat_capacity": 10, "created_at": "2025-06-04T00:04:14.348658+02:00", "is_available": true, "trip_request": 30, "captain": 93, "boat": 22}, {"id": 75, "captain_details": {"user": {"id": 90, "email": "<EMAIL>", "first_name": "Epsilon", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "4.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC090", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.20", "rate_per_hour": "40.00", "boat": {"id": 19, "name": "Chaparral 21 H2O", "registration_number": null, "boat_type": "CLASSIC", "capacity": 9, "color": "<PERSON>ert", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:59:46.608590Z", "updated_at": "2025-05-31T07:05:44.776794Z"}}, "boat_details": {"id": 19, "name": "Chaparral 21 H2O", "registration_number": null, "boat_type": "CLASSIC", "capacity": 9, "color": "<PERSON>ert", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 90, "user": {"id": 90, "email": "<EMAIL>", "first_name": "Epsilon", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "4.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC090", "years_of_experience": 0, "rate_per_hour": 40.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T08:59:46.608590+02:00", "updated_at": "2025-05-31T09:05:44.776794+02:00", "maintenance_records": []}, "base_price": "9.79", "distance_km": "4.45", "rate_used": "2.20", "captain_name": "Epsilon Capitaine", "captain_rating": "4.50", "boat_name": "Chaparral 21 H2O", "boat_capacity": 9, "created_at": "2025-06-04T00:04:14.327169+02:00", "is_available": true, "trip_request": 30, "captain": 90, "boat": 19}, {"id": 79, "captain_details": {"user": {"id": 94, "email": "<EMAIL>", "first_name": "Iota", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "4.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC094", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.25", "rate_per_hour": "45.00", "boat": {"id": 23, "name": "Premium Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 12, "color": "Noir", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:13.725335Z", "updated_at": "2025-05-31T07:08:13.730280Z"}}, "boat_details": {"id": 23, "name": "Premium Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 12, "color": "Noir", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 94, "user": {"id": 94, "email": "<EMAIL>", "first_name": "Iota", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "4.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC094", "years_of_experience": 0, "rate_per_hour": 45.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:13.725335+02:00", "updated_at": "2025-05-31T09:08:13.730280+02:00", "maintenance_records": []}, "base_price": "10.01", "distance_km": "4.45", "rate_used": "2.25", "captain_name": "Iota Capitaine", "captain_rating": "4.50", "boat_name": "Premium Classic", "boat_capacity": 12, "created_at": "2025-06-04T00:04:14.355766+02:00", "is_available": true, "trip_request": 30, "captain": 94, "boat": 23}, {"id": 74, "captain_details": {"user": {"id": 82, "email": "<EMAIL>", "first_name": "Alpha", "last_name": "Capitaine", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "experience": "7 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC001", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.41", "rate_per_hour": "44.06", "boat": {"id": 11, "name": "Alpha One", "registration_number": "BN0001", "boat_type": "CLASSIC", "capacity": 8, "color": "Rouge", "fuel_type": "GASOLINE", "fuel_consumption": 10.89, "photos": [], "zone_served": "Cotonou, Bénin", "radius": 35, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:23:48.135479Z", "updated_at": "2025-05-31T06:23:48.135479Z"}}, "boat_details": {"id": 11, "name": "Alpha One", "registration_number": "BN0001", "boat_type": "CLASSIC", "capacity": 8, "color": "Rouge", "fuel_type": "GASOLINE", "fuel_consumption": "10.89", "photos": [], "zone_served": "Cotonou, Bénin", "radius": 35, "captain": {"id": 82, "user": {"id": 82, "email": "<EMAIL>", "first_name": "Alpha", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "7 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC001", "years_of_experience": 0, "rate_per_hour": 44.06}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T08:23:48.135479+02:00", "updated_at": "2025-05-31T08:23:48.135479+02:00", "maintenance_records": []}, "base_price": "10.72", "distance_km": "4.45", "rate_used": "2.41", "captain_name": "Alpha Capitaine", "captain_rating": "4.50", "boat_name": "Alpha One", "boat_capacity": 8, "created_at": "2025-06-04T00:04:14.316328+02:00", "is_available": true, "trip_request": 30, "captain": 82, "boat": 11}]}, "text": null}}, "accept_quote": {"endpoint": "POST /api/trips/quotes/74/accept/", "description": "Accepter un devis et créer une course", "response": {"error": "Expecting value: line 1 column 1 (char 0)", "status_code": null}}, "create_hourly_trip": {"endpoint": "POST /api/trips/requests/hourly/", "description": "<PERSON><PERSON><PERSON> une demande de course horaire (mise à disposition)", "request_body": {"departure_location": {"city_name": "Port de Saint-Tropez", "coordinates": {"latitude": 43.2677, "longitude": 6.6407}, "timestamp": "2025-06-04T03:04:15.026239"}, "arrival_location": {"city_name": "Plage de Pampelonne", "coordinates": {"latitude": 43.2384, "longitude": 6.6789}, "timestamp": "2025-06-04T03:04:15.026239"}, "departure_date": "2025-06-04", "departure_time": "03:04:15.026239", "duration_hours": 4, "passenger_count": 6, "boat_type": "LUXURY"}, "response": {"status_code": 400, "headers": {"Date": "Tue, 03 Jun 2025 22:04:15 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "7fac462e2461472ebf01397691c42c7a", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=94.43590001319535;desc=\"Elapsed time\", SQLPanel_sql_time;dur=3.4503999922890216;desc=\"SQL 2 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "102", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"boat_type": ["« LUXURY » n'est pas un choix valide."], "start_date": ["Ce champ est obligatoire."]}, "text": null}}, "create_shuttle_request": {"endpoint": "POST /api/trips/requests/shuttle/", "description": "<PERSON><PERSON><PERSON> une demande de navette gratuite", "request_body": {"departure_location": {"city_name": "Hôtel Paradise", "coordinates": {"latitude": 43.5528, "longitude": 7.0174}, "timestamp": "2025-06-04T02:04:15.129910"}, "arrival_location": {"city_name": "Port de Cannes", "coordinates": {"latitude": 43.5184, "longitude": 7.0457}, "timestamp": "2025-06-04T02:04:15.129910"}, "passenger_count": 2, "establishment_id": 1, "notes": "Navette pour clients de l'hôtel"}, "response": {"status_code": 400, "headers": {"Date": "Tue, 03 Jun 2025 22:04:15 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, POST, HEAD, OPTIONS", "djdt-store-id": "618ca279221448e3985ce7f7c24ed1cd", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=108.49600000074133;desc=\"Elapsed time\", SQLPanel_sql_time;dur=4.174800007604063;desc=\"SQL 2 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "141", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"establishment": ["Ce champ est obligatoire."], "departure_date": ["Ce champ est obligatoire."], "departure_time": ["Ce champ est obligatoire."]}, "text": null}}, "get_shuttle_requests": {"endpoint": "GET /api/establishments/shuttle-requests/", "description": "Récupérer les demandes de navettes (établissement)", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:15 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "714887bb78904554aa00aa8e4ae0a826", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=117.25479998858646;desc=\"Elapsed time\", SQLPanel_sql_time;dur=8.091700059594586;desc=\"SQL 7 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "592", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"status": "success", "data": {"requests": [{"request_id": "28", "client_name": "<PERSON>", "client_phone": "+33123456789", "departure": "Aéroport Nice", "destination": "Hotel Paradise Beach", "date": "2025-06-04", "time": "02:32", "passengers": 2, "message": "Arrivée vol AF1234", "created_at": "2025-06-03T21:32:17.363055+00:00"}, {"request_id": "22", "client_name": "<PERSON>", "client_phone": "+33123456789", "departure": "Aéroport Nice", "destination": "Hotel Paradise Beach", "date": "2025-06-04", "time": "01:58", "passengers": 2, "message": "Arrivée vol AF1234", "created_at": "2025-06-03T20:58:35.171376+00:00"}]}}, "text": null}}, "start_trip": {"endpoint": "POST /api/trips/{trip_id}/start/", "description": "<PERSON><PERSON><PERSON><PERSON> une course (capitaine)", "response": {"status_code": 405, "headers": {"Date": "Tue, 03 Jun 2025 22:04:15 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "PATCH, OPTIONS", "djdt-store-id": "dfee2a01ebd9428e8104983983a3f77d", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=98.86090000509284;desc=\"Elapsed time\", SQLPanel_sql_time;dur=2.783500007353723;desc=\"SQL 1 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "50", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"detail": "Méthode « POST » non autorisée."}, "text": null}}, "complete_trip": {"endpoint": "POST /api/trips/{trip_id}/complete/", "description": "Terminer une course (capitaine)", "response": {"status_code": 405, "headers": {"Date": "Tue, 03 Jun 2025 22:04:15 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "PATCH, OPTIONS", "djdt-store-id": "aec0256295544e8686a1279e75b70e35", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=86.21490001678467;desc=\"Elapsed time\", SQLPanel_sql_time;dur=3.2848000118974596;desc=\"SQL 1 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "50", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"detail": "Méthode « POST » non autorisée."}, "text": null}}, "accept_trip": {"endpoint": "POST /api/trips/{trip_id}/accept/", "description": "Accepter une course assignée", "response": {"status_code": 403, "headers": {"Date": "Tue, 03 Jun 2025 22:04:15 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "38b46b3288d74227a0be757c93ea5f1b", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=119.35769999399781;desc=\"Elapsed time\", SQLPanel_sql_time;dur=5.738600011682138;desc=\"SQL 4 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "50", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "Cette course ne vous est pas assignée"}, "text": null}}, "cancel_trip": {"endpoint": "POST /api/trips/{trip_id}/cancel/", "description": "Annuler une course", "response": {"status_code": 405, "headers": {"Date": "Tue, 03 Jun 2025 22:04:15 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "PATCH, OPTIONS", "djdt-store-id": "e0c76f806fc3416fa6253d002a1e0f56", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=87.74849999463186;desc=\"Elapsed time\", SQLPanel_sql_time;dur=2.3959999962244183;desc=\"SQL 1 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "50", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"detail": "Méthode « POST » non autorisée."}, "text": null}}, "trip_status": {"endpoint": "GET /api/trips/{trip_id}/status/", "description": "Statut détaillé d'une course", "response": {"status_code": 403, "headers": {"Date": "Tue, 03 Jun 2025 22:04:15 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "e99c8c59781d436fb53cf7cb695b097a", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=112.54640002152883;desc=\"Elapsed time\", SQLPanel_sql_time;dur=7.186500006355345;desc=\"SQL 5 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "26", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "<PERSON><PERSON>ès refusé"}, "text": null}}, "trip_tracking": {"endpoint": "GET /api/trips/{trip_id}/tracking/", "description": "Suivi temps réel d'une course", "response": {"status_code": 403, "headers": {"Date": "Tue, 03 Jun 2025 22:04:16 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "1d861d1172164813853006f3de717f30", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=108.22970001026988;desc=\"Elapsed time\", SQLPanel_sql_time;dur=7.215800054837018;desc=\"SQL 5 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "26", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "<PERSON><PERSON>ès refusé"}, "text": null}}, "captain_pending_trips": {"endpoint": "GET /api/trips/pending/", "description": "Courses en attente pour le capitaine", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:16 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "be890a9378264ecf8e0b2985225b56fe", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=111.58960001193918;desc=\"Elapsed time\", SQLPanel_sql_time;dur=8.571600017603487;desc=\"SQL 4 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "45", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"success": true, "count": 0, "pending_trips": []}, "text": null}}, "captain_trip_history": {"endpoint": "GET /api/trips/captain/history/", "description": "Historique des courses du capitaine", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:16 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "9b2e63c658684efba248201f72a7c012", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=209.24500000546686;desc=\"Elapsed time\", SQLPanel_sql_time;dur=28.762599977198988;desc=\"SQL 19 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "3261", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"success": true, "trips": {"count": 1, "total": 1, "data": [{"id": 5, "client": {"user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "is_active": true}, "wallet_balance": "0.00", "date_of_birth": null, "nationality": "", "preferred_language": "fr", "emergency_contact_name": "", "emergency_contact_phone": ""}, "captain": {"user": {"id": 91, "email": "<EMAIL>", "first_name": "Zeta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "2.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC091", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.20", "rate_per_hour": "25.00", "boat": {"id": 20, "name": "Classic Explorer", "registration_number": null, "boat_type": "CLASSIC", "capacity": 6, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:12.925554Z", "updated_at": "2025-05-31T07:08:12.930519Z"}}, "boat": {"id": 20, "name": "Classic Explorer", "registration_number": null, "boat_type": "CLASSIC", "capacity": 6, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "captain": {"id": 91, "user": {"id": 91, "email": "<EMAIL>", "first_name": "Zeta", "last_name": "Capitaine", "phone_number": "", "profile_picture": ""}, "experience": "2.0 ans d'expérience maritime", "average_rating": 0.0, "total_trips": 0, "is_available": true, "license_number": "LIC091", "years_of_experience": 0, "rate_per_hour": 25.0}, "establishment": null, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T09:08:12.925554+02:00", "updated_at": "2025-05-31T09:08:12.930519+02:00", "maintenance_records": []}, "establishment": null, "captain_profile_picture": null, "captain_full_name": "Zeta Capitaine", "boat_photos": [], "qr_code": "", "trip_type": "COURSE_SIMPLE", "start_location": "Port de Cannes", "end_location": "Îles de Lérins", "scheduled_start_time": "2025-06-04T00:02:16.293407+02:00", "scheduled_end_time": "2025-06-04T02:02:16.293407+02:00", "actual_start_time": null, "actual_end_time": null, "estimated_duration": null, "actual_duration": null, "distance_km": null, "passenger_count": 4, "passenger_names": [], "special_requests": "", "status": "ACCEPTED", "current_location": "", "tracking_data": [], "base_price": "5.34", "additional_charges": "0.00", "tip": "0.00", "total_price": "5.34", "payment_status": "PENDING", "payment_method": "", "created_at": "2025-06-03T23:32:16.294410+02:00", "updated_at": "2025-06-03T23:32:16.294410+02:00", "cancellation_reason": "", "notes": "", "delay_minutes": 0, "problem_description": "", "captain_notes": "", "client_notes": "", "estimated_arrival_time": null, "cancelled_by": null}], "pagination": {"limit": 20, "offset": 0, "has_more": false}}, "statistics": {"total_trips": 1, "completed_trips": 0, "cancelled_trips": 0, "completion_rate": 0.0, "total_revenue": 0.0, "average_rating": 0.0, "recent_activity": {"trips_last_30_days": 1, "completed_last_30_days": 0}}}, "text": null}}, "captain_dashboard": {"endpoint": "GET /api/trips/captain/dashboard/", "description": "Tableau de bord du capitaine", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:16 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "10a6eef5e8a147f5aaa716a2b856fdb9", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=173.12390002189204;desc=\"Elapsed time\", SQLPanel_sql_time;dur=19.49909992981702;desc=\"SQL 11 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "547", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"success": true, "dashboard": {"captain": {"name": "Zeta Capitaine", "rating": 0.0, "total_trips": 0, "wallet_balance": 0.0, "is_available": true, "availability_status": "AVAILABLE"}, "boat": {"name": "Classic Explorer", "type": "CLASSIC", "capacity": 6, "is_available": true}, "pending_requests": 0, "active_trips": 1, "next_trip": {"id": 5, "client_name": "<PERSON>", "departure": "Port de Cannes", "arrival": "Îles de Lérins", "scheduled_time": "2025-06-03T22:02:16.293407Z", "passenger_count": 4, "price": 5.34}, "today_stats": {"trips_completed": 0, "revenue": 0.0, "total_trips": 1}}}, "text": null}}, "captain_availability": {"endpoint": "GET /api/trips/captain/availability/", "description": "Disponibilité du capitaine", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:16 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, POST, HEAD, OPTIONS", "djdt-store-id": "fac46b05f4b64542992ff4a1cb9a6992", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=100.9726999909617;desc=\"Elapsed time\", SQLPanel_sql_time;dur=7.210799987660721;desc=\"SQL 2 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "96", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"success": true, "availability": {"is_available": true, "status": "AVAILABLE", "current_location": ""}}, "text": null}}, "captain_wallet": {"endpoint": "GET /api/payments/wallet/captain/", "description": "Portefeuille du capitaine", "response": {"status_code": 500, "headers": {"Date": "Tue, 03 Jun 2025 22:04:16 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "4730c26d4f1242ceac3eaf45d02c1fea", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=107.35209999256767;desc=\"Elapsed time\", SQLPanel_sql_time;dur=4.9294999917037785;desc=\"SQL 2 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "283", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "Erreur lors de la récupération du wallet: Cannot resolve keyword 'user' into field. Choices are: amount, balance_after, created_at, description, id, linked_transactions, metadata, payment, payment_id, related_transaction, related_transaction_id, type, wallet, wallet_id"}, "text": null}}, "withdraw_funds": {"endpoint": "POST /api/payments/withdraw/", "description": "Retrait de fonds du portefeuille", "request_body": {"amount": 50.0, "bank_account": "***************************"}, "response": {"status_code": 400, "headers": {"Date": "Tue, 03 Jun 2025 22:04:16 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "b6848c6c4e594f52adcbfb504edeb102", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=95.*************;desc=\"Elapsed time\", SQLPanel_sql_time;dur=4.****************;desc=\"SQL 2 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "56", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "Solde insuffisant. Solde disponible: 0.00€"}, "text": null}}, "trip_earnings": {"endpoint": "GET /api/payments/earnings/", "description": "Historique des revenus de courses", "response": {"status_code": 500, "headers": {"Date": "Tue, 03 Jun 2025 22:04:17 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "5c43a29ff25944e0a62121efddad1112", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=95.6855000113137;desc=\"Elapsed time\", SQLPanel_sql_time;dur=3.559400007361546;desc=\"SQL 2 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "297", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "Erreur lors de la récupération des revenus: Cannot resolve keyword 'transaction_type' into field. Choices are: amount, balance_after, created_at, description, id, linked_transactions, metadata, payment, payment_id, related_transaction, related_transaction_id, type, wallet, wallet_id"}, "text": null}}, "verify_qr": {"endpoint": "POST /api/trips/verify-qr/", "description": "Vérification d'un QR code", "request_body": {"qr_code": "QR_TEST_123456", "trip_id": 1}, "response": {"status_code": 403, "headers": {"Date": "Tue, 03 Jun 2025 22:04:17 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "388a3f6aa0fb48fb8245ff3eef546237", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=122.07710000802763;desc=\"Elapsed time\", SQLPanel_sql_time;dur=7.341000018641353;desc=\"SQL 6 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "137", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"valid": false, "message": "Accès non autorisé à ce ticket", "error_code": "ACCESS_DENIED", "timestamp": "2025-06-03T22:04:17.098644+00:00"}, "text": null}}, "generate_qr": {"endpoint": "POST /api/trips/{trip_id}/generate-qr/", "description": "Générer un QR code pour une course", "response": {"status_code": 403, "headers": {"Date": "Tue, 03 Jun 2025 22:04:17 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "874e024ac26a4e01bf682d3d982c9932", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=104.33729999931529;desc=\"Elapsed time\", SQLPanel_sql_time;dur=9.173700003884733;desc=\"SQL 5 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "50", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"success": false, "message": "Accès non autorisé"}, "text": null}}, "user_profile": {"endpoint": "GET /api/profile/", "description": "Récupérer le profil utilisateur", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:17 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, PATCH, HEAD, OPTIONS", "djdt-store-id": "80367d10991f462ea97f95f0119c5400", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=107.00640000868589;desc=\"Elapsed time\", SQLPanel_sql_time;dur=5.6059999915305525;desc=\"SQL 4 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "537", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "client_profile": {"user": {"id": 99, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "+33123456789", "type": "", "profile_picture": "", "is_active": true}, "wallet_balance": "0.00", "date_of_birth": null, "nationality": "", "preferred_language": "fr", "emergency_contact_name": "", "emergency_contact_phone": ""}, "captain_profile": null, "establishment_profile": null}, "text": null}}, "captains_list": {"endpoint": "GET /api/captains/", "description": "Liste des capitaines disponibles", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:17 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "d8dfe3bd73234d04ad618e4c73b907e9", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=317.92100000893697;desc=\"Elapsed time\", SQLPanel_sql_time;dur=22.699199995258823;desc=\"SQL 32 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "13389", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": [{"user": {"id": 87, "email": "<EMAIL>", "first_name": "Beta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "3.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC087", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.80", "rate_per_hour": "35.00", "boat": {"id": 16, "name": "Sea Ray SLX 400", "registration_number": null, "boat_type": "LUXE", "capacity": 10, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:59:45.558102Z", "updated_at": "2025-05-31T07:05:44.744322Z"}}, {"user": {"id": 88, "email": "<EMAIL>", "first_name": "Gamma", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "2.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC088", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.50", "rate_per_hour": "28.00", "boat": {"id": 17, "name": "Beneteau Flyer 7.7 SUNdeck", "registration_number": null, "boat_type": "BLUE", "capacity": 12, "color": "Bleu", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:59:45.922167Z", "updated_at": "2025-05-31T07:05:44.758638Z"}}, {"user": {"id": 89, "email": "<EMAIL>", "first_name": "Delta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "5.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC089", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "3.00", "rate_per_hour": "50.00", "boat": {"id": 18, "name": "Yamaha 242X E-Series", "registration_number": null, "boat_type": "BOAT_XL", "capacity": 8, "color": "Rouge", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:59:46.318620Z", "updated_at": "2025-05-31T07:05:44.767247Z"}}, {"user": {"id": 90, "email": "<EMAIL>", "first_name": "Epsilon", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "4.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC090", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.20", "rate_per_hour": "40.00", "boat": {"id": 19, "name": "Chaparral 21 H2O", "registration_number": null, "boat_type": "CLASSIC", "capacity": 9, "color": "<PERSON>ert", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:59:46.608590Z", "updated_at": "2025-05-31T07:05:44.776794Z"}}, {"user": {"id": 91, "email": "<EMAIL>", "first_name": "Zeta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "2.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC091", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.20", "rate_per_hour": "25.00", "boat": {"id": 20, "name": "Classic Explorer", "registration_number": null, "boat_type": "CLASSIC", "capacity": 6, "color": "<PERSON>", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:12.925554Z", "updated_at": "2025-05-31T07:08:12.930519Z"}}, {"user": {"id": 92, "email": "<EMAIL>", "first_name": "Eta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "3.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC092", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.60", "rate_per_hour": "32.00", "boat": {"id": 21, "name": "Ocean Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 8, "color": "Bleu", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:13.191593Z", "updated_at": "2025-05-31T07:08:13.191593Z"}}, {"user": {"id": 93, "email": "<EMAIL>", "first_name": "Theta", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "3.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC093", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.90", "rate_per_hour": "38.00", "boat": {"id": 22, "name": "Classic Voyager", "registration_number": null, "boat_type": "CLASSIC", "capacity": 10, "color": "Rouge", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:13.451820Z", "updated_at": "2025-05-31T07:08:13.455558Z"}}, {"user": {"id": 94, "email": "<EMAIL>", "first_name": "Iota", "last_name": "Capitaine", "phone_number": "", "type": "CAPTAIN", "profile_picture": "", "is_active": true}, "experience": "4.0 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC094", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.25", "rate_per_hour": "45.00", "boat": {"id": 23, "name": "Premium Classic", "registration_number": null, "boat_type": "CLASSIC", "capacity": 12, "color": "Noir", "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T07:08:13.725335Z", "updated_at": "2025-05-31T07:08:13.730280Z"}}, {"user": {"id": 98, "email": "<EMAIL>", "first_name": "Captain", "last_name": "Test", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "experience": "5 ans", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "TEST001", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": null, "rate_per_hour": null, "boat": {"id": 24, "name": null, "registration_number": null, "boat_type": null, "capacity": null, "color": null, "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-06-03T18:31:48.859004Z", "updated_at": "2025-06-03T18:31:48.859004Z"}}, {"user": {"id": 101, "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "Sparrow", "phone_number": "+33777888999", "type": "", "profile_picture": "", "is_active": true}, "experience": "Capitaine de test - 10 ans", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "TEST_CAPTAIN_001", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "25.00", "rate_per_hour": "50.00", "boat": {"id": 26, "name": null, "registration_number": null, "boat_type": null, "capacity": null, "color": null, "fuel_type": null, "fuel_consumption": null, "photos": [], "zone_served": "", "radius": 10, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-06-03T20:54:39.021828Z", "updated_at": "2025-06-03T20:54:39.021828Z"}}, {"user": {"id": 82, "email": "<EMAIL>", "first_name": "Alpha", "last_name": "Capitaine", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "experience": "7 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC001", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.41", "rate_per_hour": "44.06", "boat": {"id": 11, "name": "Alpha One", "registration_number": "BN0001", "boat_type": "CLASSIC", "capacity": 8, "color": "Rouge", "fuel_type": "GASOLINE", "fuel_consumption": 10.89, "photos": [], "zone_served": "Cotonou, Bénin", "radius": 35, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:23:48.135479Z", "updated_at": "2025-05-31T06:23:48.135479Z"}}, {"user": {"id": 83, "email": "<EMAIL>", "first_name": "Beta", "last_name": "Capitaine", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "experience": "5 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC002", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.84", "rate_per_hour": "34.32", "boat": {"id": 12, "name": "Beta Two", "registration_number": "BN0002", "boat_type": "LUXE", "capacity": 9, "color": "<PERSON>", "fuel_type": "GASOLINE", "fuel_consumption": 12.57, "photos": [], "zone_served": "Cotonou, Bénin", "radius": 33, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:23:48.340066Z", "updated_at": "2025-05-31T06:23:48.341722Z"}}, {"user": {"id": 84, "email": "<EMAIL>", "first_name": "Gamma", "last_name": "Capitaine", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "experience": "8 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC003", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.49", "rate_per_hour": "34.29", "boat": {"id": 13, "name": "Gamma Three", "registration_number": "BN0003", "boat_type": "BLUE", "capacity": 7, "color": "<PERSON>ert", "fuel_type": "DIESEL", "fuel_consumption": 15.43, "photos": [], "zone_served": "Cotonou, Bénin", "radius": 40, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:23:48.541442Z", "updated_at": "2025-05-31T06:23:48.541442Z"}}, {"user": {"id": 85, "email": "<EMAIL>", "first_name": "Delta", "last_name": "Capitaine", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "experience": "11 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC004", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "1.90", "rate_per_hour": "37.52", "boat": {"id": 14, "name": "Delta Four", "registration_number": "BN0004", "boat_type": "BOAT_XL", "capacity": 6, "color": "<PERSON>ert", "fuel_type": "GASOLINE", "fuel_consumption": 16.43, "photos": [], "zone_served": "Cotonou, Bénin", "radius": 39, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:23:48.743442Z", "updated_at": "2025-05-31T06:23:48.743442Z"}}, {"user": {"id": 86, "email": "<EMAIL>", "first_name": "Echo", "last_name": "Capitaine", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "experience": "7 ans d'expérience maritime", "average_rating": "0.00", "total_trips": 0, "wallet_balance": "0.00", "is_available": true, "current_location": "", "license_number": "LIC005", "license_expiry_date": null, "years_of_experience": 0, "certifications": [], "specializations": [], "availability_status": "AVAILABLE", "boat_photos": [], "rate_per_km": "2.69", "rate_per_hour": "39.08", "boat": {"id": 15, "name": "Echo Five", "registration_number": "BN0005", "boat_type": "NAVETTE", "capacity": 7, "color": "<PERSON>", "fuel_type": "GASOLINE", "fuel_consumption": 11.02, "photos": [], "zone_served": "Cotonou, Bénin", "radius": 40, "is_available": true, "last_maintenance": null, "next_maintenance": null, "created_at": "2025-05-31T06:23:48.951258Z", "updated_at": "2025-05-31T06:23:48.951258Z"}}], "text": null}}, "establishments_list": {"endpoint": "GET /api/establishments/", "description": "Liste des établissements", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:17 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "ed6cfca6b3af43d9b9f811f7e92488ff", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=198.69020002079196;desc=\"Elapsed time\", SQLPanel_sql_time;dur=10.117000027094036;desc=\"SQL 14 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "7146", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": [{"user": {"id": 50, "email": "<EMAIL>", "first_name": "Hôtel Marina", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Hôtel Marina", "type": "HOTEL", "address": "Avenue de la Marina, Cotonou", "description": "Établissement de qualité situé à Avenue de la Marina, Cotonou", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 51, "email": "<EMAIL>", "first_name": "Restaurant Le Phare", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Restaurant Le Phare", "type": "RESTAURANT", "address": "Boulevard de la Plage, Cotonou", "description": "Établissement de qualité situé à Boulevard de la Plage, Cotonou", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 52, "email": "<EMAIL>", "first_name": "Club Nautique Bénin", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Club Nautique Bénin", "type": "CLUB", "address": "Port de Cotonou, Bénin", "description": "Établissement de qualité situé à Port de Cotonou, Bénin", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 53, "email": "<EMAIL>", "first_name": "Resort Tropical Paradise", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Resort Tropical Paradise", "type": "HOTEL", "address": "Plage de Fidjrossè, Cotonou", "description": "Établissement de qualité situé à Plage de Fidjrossè, Cotonou", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 54, "email": "<EMAIL>", "first_name": "Bar-Restaurant Ocean View", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Bar-Restaurant Ocean View", "type": "RESTAURANT", "address": "<PERSON><PERSON><PERSON> de Cotonou", "description": "Établissement de qualité situé à Corniche de Cotonou", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 55, "email": "<EMAIL>", "first_name": "Hôtel Riviera Palace", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Hôtel Riviera Palace", "type": "HOTEL", "address": "Quartier <PERSON>, Cotonou", "description": "Établissement de qualité situé à Quartier Haie Vive, Cotonou", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 56, "email": "<EMAIL>", "first_name": "Centre de Loisirs Aquatique", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Centre de Loisirs Aquatique", "type": "CLUB", "address": "Lac Nokoué, Cotonou", "description": "Établissement de qualité situé à Lac Nokoué, Cotonou", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 57, "email": "<EMAIL>", "first_name": "Restaurant Chez Maman", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Restaurant Chez Maman", "type": "RESTAURANT", "address": "<PERSON><PERSON>, Cotonou", "description": "Établissement de qualité situé à <PERSON>, Cotonou", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 58, "email": "<EMAIL>", "first_name": "Hôtel Golden Tulip", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Hôtel Golden Tulip", "type": "HOTEL", "address": "Avenue Clozel, Cotonou", "description": "Établissement de qualité situé à Avenue Clozel, Cotonou", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 59, "email": "<EMAIL>", "first_name": "Club de Voile Béninois", "last_name": "Manager", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Club de Voile Béninois", "type": "CLUB", "address": "Port de Plaisance, Cotonou", "description": "Établissement de qualité situé à Port de Plaisance, Cotonou", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 97, "email": "<EMAIL>", "first_name": "Hotel", "last_name": "Test", "phone_number": "", "type": "", "profile_picture": "", "is_active": true}, "name": "Hotel Test", "type": "HOTEL", "address": "", "description": "", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}, {"user": {"id": 100, "email": "<EMAIL>", "first_name": "Hotel", "last_name": "Paradise", "phone_number": "+***********", "type": "", "profile_picture": "", "is_active": true}, "name": "Hotel Paradise Beach", "type": "HOTEL", "address": "123 Promenade des Anglais, 06000 Cannes", "description": "", "main_photo": null, "secondary_photos": [], "wallet_balance": "0.00", "business_name": "", "business_type": "", "registration_number": "", "tax_id": "", "opening_hours": {}, "services_offered": [], "average_rating": "0.00", "location_coordinates": "", "website": "", "social_media": {}}], "text": null}}, "boats_list": {"endpoint": "GET /api/boats/", "description": "Liste des bateaux disponibles", "response": {"error": "Expecting value: line 1 column 1 (char 0)", "status_code": null}}, "available_boats": {"endpoint": "GET /api/boats/available/", "description": "Bateaux disponibles pour réservation", "response": {"error": "Expecting value: line 1 column 1 (char 0)", "status_code": null}}, "establishment_dashboard": {"endpoint": "GET /api/establishments/dashboard/", "description": "Tableau de bord de l'établissement", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:18 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "05c43549929a435bba244fe849246108", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=155.99800000200048;desc=\"Elapsed time\", SQLPanel_sql_time;dur=10.512799985008314;desc=\"SQL 8 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "373", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"status": "success", "data": {"establishment_name": "Hotel Paradise Beach", "establishment_type": "Hôtel", "available_balance": 0.0, "total_shuttles": 1, "pending_requests": 2, "availability": true, "shuttles": [{"shuttle_id": "4", "date": "2025-06-03T18:34:02.438825+00:00", "status": "À venir", "departure": "Aéroport Nice", "destination": "Hotel Test", "client": "Test Client", "amount": 0.0}]}}, "text": null}}, "establishment_wallet": {"endpoint": "GET /api/establishments/wallet/", "description": "Portefeuille de l'établissement", "response": {"error": "Expecting value: line 1 column 1 (char 0)", "status_code": null}}, "establishment_boatmen": {"endpoint": "GET /api/establishments/boatmen/", "description": "Liste des bateliers de l'établissement", "response": {"error": "Expecting value: line 1 column 1 (char 0)", "status_code": null}}, "boatman_login": {"endpoint": "POST /api/boatman/login/", "description": "Connexion batelier avec code", "request_body": {"email": "<EMAIL>", "code": "123456"}, "response": {"status_code": 400, "headers": {"Date": "Tue, 03 Jun 2025 22:04:18 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "565d94e13c21433fbe21212da08a78a3", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=89.89099998143502;desc=\"Elapsed time\", SQLPanel_sql_time;dur=2.3856000043451786;desc=\"SQL 1 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "74", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"status": "error", "error": "Email et mot de passe requis", "error_code": 400}, "text": null}}, "boatman_dashboard": {"endpoint": "GET /api/boatman/dashboard/", "description": "Tableau de bord du batelier", "response": {"error": "Expecting value: line 1 column 1 (char 0)", "status_code": null}}, "boatman_shuttles": {"endpoint": "GET /api/boatman/shuttles/", "description": "<PERSON><PERSON><PERSON> au batelier", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:19 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "d8d252a9f17a475a86bffed8e9b0b264", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=280.68029999849387;desc=\"Elapsed time\", SQLPanel_sql_time;dur=10.512800014112145;desc=\"SQL 9 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "1053", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"status": "success", "data": {"shuttles": [{"shuttle_id": "5", "client": "<PERSON>", "client_phone": "+33123456789", "date": "2025-06-03T22:02:16.293407+00:00", "departure": "Port de Cannes", "destination": "Îles de Lérins", "passengers": 4, "status": "ACCEPTED", "status_display": "À venir", "estimated_duration": 30, "distance_km": 0.0, "boat": {"name": "Classic Explorer", "capacity": 6}, "payment": {"amount": 5.34, "status": "PENDING", "method": ""}, "special_requests": "", "created_at": "2025-06-03T21:32:16.294410+00:00", "can_start": true, "can_complete": false}], "pagination": {"page": 1, "limit": 20, "total": 1, "total_pages": 1, "has_next": false, "has_previous": false}, "filters": {"current_filter": "all", "search_term": "", "available_filters": [{"value": "all", "label": "Toutes", "count": 1}, {"value": "À venir", "label": "À venir", "count": 1}, {"value": "En cours", "label": "En cours", "count": 0}, {"value": "Terminées", "label": "Terminées", "count": 0}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "count": 0}]}}, "message": "Courses récupérées avec succès", "timestamp": "2025-06-03T22:04:19.064372+00:00"}, "text": null}}, "transactions_list": {"endpoint": "GET /api/payments/transactions/", "description": "Historique des transactions", "response": {"error": "Expecting value: line 1 column 1 (char 0)", "status_code": null}}, "carbon_compensation": {"endpoint": "POST /api/trips/{trip_id}/carbon-compensation/", "description": "Paiement compensation carbone", "request_body": {"payment_method_id": "pm_card_visa", "payment_method": "CARD"}, "response": {"status_code": 403, "headers": {"Date": "Tue, 03 Jun 2025 22:04:19 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "f64c906e03184b249e500254af512465", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=134.07400000141934;desc=\"Elapsed time\", SQLPanel_sql_time;dur=6.240099988644943;desc=\"SQL 4 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "61", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "Seul le client peut payer la compensation carbone"}, "text": null}}, "tip_payment": {"endpoint": "POST /api/trips/{trip_id}/tip/", "description": "Paiement pourboire", "request_body": {"amount": 5.0, "payment_method_id": "pm_card_visa", "payment_method": "CARD"}, "response": {"status_code": 403, "headers": {"Date": "Tue, 03 Jun 2025 22:04:19 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "POST, OPTIONS", "djdt-store-id": "c62e642d5af34b04b6dc731bbeb3b8b9", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=118.60690001049079;desc=\"Elapsed time\", SQLPanel_sql_time;dur=5.644500022754073;desc=\"SQL 4 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "51", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "Seul le client peut donner un pourboire"}, "text": null}}, "notifications_list": {"endpoint": "GET /api/notifications/", "description": "Liste des notifications utilisateur", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:19 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, HEAD, OPTIONS", "djdt-store-id": "75a7a6814faf462484eb8bf4e56630fa", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=110.62950000632554;desc=\"Elapsed time\", SQLPanel_sql_time;dur=4.783899988979101;desc=\"SQL 1 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "135", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"notifications": "http://127.0.0.1:8000/api/notifications/notifications/", "devices": "http://127.0.0.1:8000/api/notifications/devices/"}, "text": null}}, "mark_notification_read": {"endpoint": "POST /api/notifications/{id}/mark-read/", "description": "Marquer une notification comme lue", "response": {"error": "Expecting value: line 1 column 1 (char 0)", "status_code": null}}, "chat_rooms": {"endpoint": "GET /api/chat/rooms/", "description": "Liste des salons de chat", "response": {"status_code": 200, "headers": {"Date": "Tue, 03 Jun 2025 22:04:20 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, POST, HEAD, OPTIONS", "djdt-store-id": "fff6fdef65c0491da677f9333014ac85", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=228.03509997902438;desc=\"Elapsed time\", SQLPanel_sql_time;dur=10.09409999824129;desc=\"SQL 2 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "2", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": [], "text": null}}, "chatbot_message": {"endpoint": "POST /api/chat/chatbot/message/", "description": "Envoyer un message au chatbot", "request_body": {"message": "Bonjour, comment réserver une course ?", "session_id": "test-session-123"}, "response": {"error": "Expecting value: line 1 column 1 (char 0)", "status_code": null}}, "create_review": {"endpoint": "POST /api/reviews/", "description": "<PERSON><PERSON><PERSON> un avis sur une course", "request_body": {"trip_id": 1, "rating": 5, "comment": "Excellent service, capitaine très professionnel !", "captain_rating": 5, "boat_rating": 4}, "response": {"status_code": 400, "headers": {"Date": "Tue, 03 Jun 2025 22:04:20 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, POST, HEAD, OPTIONS", "djdt-store-id": "adee5ad8797e40a18c61a2f825dc373c", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=106.61300001083873;desc=\"Elapsed time\", SQLPanel_sql_time;dur=5.433800019090995;desc=\"SQL 2 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "42", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "Type d'objet évalué invalide"}, "text": null}}, "reviews_list": {"endpoint": "GET /api/reviews/", "description": "Liste des avis", "response": {"status_code": 400, "headers": {"Date": "Tue, 03 Jun 2025 22:04:20 GMT", "Server": "WSGIServer/0.2 CPython/3.11.3", "Content-Type": "application/json", "Vary": "Accept, origin, <PERSON><PERSON>", "Allow": "GET, POST, HEAD, OPTIONS", "djdt-store-id": "9d1d4760d83f4da888f1b3069a2164cc", "Server-Timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=97.08979999413714;desc=\"Elapsed time\", SQLPanel_sql_time;dur=2.4815000069793314;desc=\"SQL 1 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "X-Frame-Options": "DENY", "Content-Length": "68", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "json": {"error": "Les paramètres content_type_id et object_id sont requis"}, "text": null}}}}