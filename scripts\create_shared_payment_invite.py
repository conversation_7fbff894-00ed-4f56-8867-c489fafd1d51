# Script pour générer une nouvelle invitation de paiement partagé pour les tests automatisés
# Usage: ce script retourne le token d'invitation à utiliser dans le test de paiement partagé
import requests
import os

API_URL = os.getenv("API_URL", "http://localhost:8000/api")
TOKEN = os.getenv("TOKEN") or "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4Mjk2OTg0LCJpYXQiOjE3NDgyMTA1ODg0LCJ1c2VyX2lkIjoyfQ.020cRZGNI8czl5DnOnETaDGlfyV2pl0I-B_dB9C12aA"
TRIP_ID = int(os.getenv("TRIP_ID", "1"))

headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

body = {
    "trip_id": TRIP_ID,
    "amount": 25.0
}

response = requests.post(f"{API_URL}/payments/trips/{TRIP_ID}/create_shared_invite/", json=body, headers=headers)

if response.status_code == 200:
    data = response.json()
    print(data["invitation_token"])
else:
    print("ERROR", response.status_code, response.text)
