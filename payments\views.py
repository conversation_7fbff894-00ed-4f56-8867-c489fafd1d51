from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.conf import settings
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .models import Payment, Transaction
from .serializers import PaymentSerializer, TransactionSerializer
from .stripe_utils import (
    create_payment_intent, create_checkout_session, create_connect_account,
    create_account_link, create_transfer, handle_webhook_event
)
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter
# Importer les modèles réels du projet Commodore
from accounts.models import User, Captain, Client, Establishment
from trips.models import Trip
import logging
from .adapters import CreditWallet, get_trip_from_booking_id

logger = logging.getLogger(__name__)

@extend_schema_view(
    list=extend_schema(tags=["Payments"], responses=PaymentSerializer(many=True)),
    retrieve=extend_schema(tags=["Payments"], responses=PaymentSerializer),
    create=extend_schema(tags=["Payments"], request=PaymentSerializer, responses=PaymentSerializer),
    update=extend_schema(tags=["Payments"], request=PaymentSerializer, responses=PaymentSerializer),
    partial_update=extend_schema(tags=["Payments"], request=PaymentSerializer, responses=PaymentSerializer),
    destroy=extend_schema(tags=["Payments"], responses=None),
)
class PaymentViewSet(viewsets.ModelViewSet):
    """ViewSet for the Payment model"""
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [permissions.AllowAny]  # Permettre l'accès à tous pour les tests

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=True, methods=['post'])
    def process_payment(self, request, pk=None):
        """Traite un paiement en utilisant Stripe"""
        payment = self.get_object()
        amount = request.data.get('amount')
        payment_method_id = request.data.get('payment_method_id')
        customer_id = request.data.get('customer_id')
        save_payment_method = request.data.get('save_payment_method', False)
        description = request.data.get('description')

        if not amount:
            return Response({'error': 'Le montant est requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Convertir le montant en centimes pour Stripe
            amount_cents = int(float(amount) * 100)

            # Créer les métadonnées pour le paiement
            metadata = {
                'payment_id': str(payment.id),
                'payment_type': payment.type,
            }

            if payment.booking:
                metadata['booking_id'] = str(payment.booking.id)

            # Paramètres pour le payment intent
            params = {
                'amount': amount_cents,
                'currency': 'eur',
                'metadata': metadata,
                'description': description
            }

            # Si un ID de méthode de paiement est fourni
            if payment_method_id:
                params['payment_method_types'] = ['card']  # Ou autre selon le type de méthode

            # Si un ID client est fourni
            if customer_id:
                params['customer'] = customer_id

                # Si l'utilisateur souhaite sauvegarder la méthode de paiement
                if save_payment_method:
                    params['setup_future_usage'] = 'off_session'

            # Créer un payment intent
            intent = create_payment_intent(**params)

            if 'error' in intent:
                return Response({'error': intent['error']}, status=status.HTTP_400_BAD_REQUEST)

            # Mettre à jour le paiement avec l'ID Stripe
            payment.stripe_payment_id = intent.id
            payment.amount = float(amount)
            payment.status = 'PENDING'

            # Stocker les informations supplémentaires
            if customer_id:
                payment.stripe_customer_id = customer_id

            if payment_method_id:
                payment.stripe_payment_method_id = payment_method_id

            # Stocker les métadonnées
            payment.metadata = metadata

            payment.save()

            response_data = {
                'client_secret': intent.client_secret,
                'payment_id': payment.id,
                'payment_intent_id': intent.id
            }

            # Ajouter l'URL de confirmation si disponible
            if hasattr(intent, 'next_action') and intent.next_action:
                response_data['next_action'] = intent.next_action

            return Response(response_data)
        except ValueError:
            return Response({'error': 'Montant invalide'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=True, methods=['post'])
    def refund_payment(self, request, pk=None):
        """Rembourse un paiement en utilisant Stripe"""
        payment = self.get_object()
        amount = request.data.get('amount')  # Optionnel, pour remboursement partiel
        reason = request.data.get('reason')  # Optionnel, raison du remboursement

        if not payment.stripe_payment_id:
            return Response({'error': 'Aucun ID de paiement Stripe trouvé'}, status=status.HTTP_400_BAD_REQUEST)

        if payment.status == 'REFUNDED':
            return Response({'error': 'Ce paiement a déjà été remboursé'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Préparer les métadonnées
            metadata = {
                'payment_id': str(payment.id),
                'payment_type': payment.type,
            }

            if payment.booking:
                metadata['booking_id'] = str(payment.booking.id)

            # Convertir le montant en centimes si fourni
            amount_cents = None
            if amount:
                amount_cents = int(float(amount) * 100)

            # Créer le remboursement
            from .stripe_utils import create_refund
            refund = create_refund(
                payment_intent_id=payment.stripe_payment_id,
                amount=amount_cents,
                reason=reason,
                metadata=metadata
            )

            if 'error' in refund:
                return Response({'error': refund['error']}, status=status.HTTP_400_BAD_REQUEST)

            # Mettre à jour le statut du paiement
            if amount_cents:
                payment.status = 'PARTIALLY_REFUNDED'
                payment.refund_amount = float(amount)
            else:
                payment.status = 'REFUNDED'
                payment.refund_amount = payment.amount

            payment.refund_id = refund.id
            payment.refund_reason = reason
            payment.save()

            return Response({
                'status': 'Paiement remboursé',
                'refund_id': refund.id,
                'payment_id': payment.id,
                'amount': amount or payment.amount
            })
        except ValueError:
            return Response({'error': 'Montant invalide'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=False, methods=['post'])
    def create_checkout(self, request):
        """Crée une session de paiement Stripe Checkout"""
        amount = request.data.get('amount')
        booking_id = request.data.get('booking_id')
        payment_type = request.data.get('type', 'TRIP')
        success_url = request.data.get('success_url')
        cancel_url = request.data.get('cancel_url')
        product_name = request.data.get('product_name', 'Service Commodore')
        product_description = request.data.get('product_description')
        customer_id = request.data.get('customer_id')
        payment_method_types = request.data.get('payment_method_types')
        locale = request.data.get('locale', 'fr')

        if not amount:
            return Response({'error': 'Le montant est requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Convertir le montant en centimes pour Stripe
            amount_cents = int(float(amount) * 100)

            # Créer les métadonnées pour la session de paiement
            metadata = {
                'payment_type': payment_type,
            }

            if booking_id:
                try:
                    booking = Trip.objects.get(id=booking_id)
                    metadata['booking_id'] = str(booking.id)
                except Trip.DoesNotExist:
                    return Response({'error': 'Réservation non trouvée'}, status=status.HTTP_404_NOT_FOUND)

            # Créer une session de paiement
            from .stripe_utils import create_checkout_session
            session = create_checkout_session(
                amount=amount_cents,
                currency='eur',
                product_name=product_name,
                product_description=product_description,
                customer=customer_id,
                payment_method_types=payment_method_types,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata=metadata,
                locale=locale
            )

            if 'error' in session:
                return Response({'error': session['error']}, status=status.HTTP_400_BAD_REQUEST)

            # Créer un enregistrement de paiement
            payment_data = {
                'amount': float(amount),
                'type': payment_type,
                'status': 'PENDING',
                'metadata': {
                    'checkout_session_id': session.id,
                    **metadata
                }
            }

            if booking_id:
                payment_data['booking'] = booking

            if customer_id:
                payment_data['stripe_customer_id'] = customer_id

            payment = Payment.objects.create(**payment_data)

            # Mettre à jour les métadonnées avec l'ID du paiement
            metadata['payment_id'] = str(payment.id)
            stripe.checkout.Session.modify(
                session.id,
                metadata=metadata
            )

            return Response({
                'session_id': session.id,
                'payment_id': payment.id,
                'checkout_url': session.url
            })
        except ValueError:
            return Response({'error': 'invalid amount'}, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=False, methods=['get'])
    def booking_payments(self, request):
        """Get payments for a booking"""
        booking_id = request.query_params.get('booking_id')

        if booking_id:
            payments = Payment.objects.filter(booking_id=booking_id)
            serializer = self.get_serializer(payments, many=True)
            return Response(serializer.data)
        return Response({'error': 'booking_id is required'}, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """
        Met à jour manuellement le statut d'un paiement.

        Cette action est utile lorsque les webhooks Stripe ne fonctionnent pas correctement
        ou pour des tests.

        Parameters:
            request (Request): La requête contenant le nouveau statut
                - status (str): Le nouveau statut du paiement (COMPLETED, FAILED, etc.)

        Returns:
            Response: Le paiement mis à jour
        """
        payment = self.get_object()
        new_status = request.data.get('status')

        if not new_status:
            return Response({'error': 'Le statut est requis'}, status=status.HTTP_400_BAD_REQUEST)

        # Vérifier que le statut est valide
        valid_statuses = dict(Payment.PAYMENT_STATUS).keys()
        if new_status not in valid_statuses:
            return Response({
                'error': f'Statut invalide. Les statuts valides sont: {", ".join(valid_statuses)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Mettre à jour le statut
        old_status = payment.status
        payment.status = new_status
        payment.save()

        # Si c'est un paiement pour une réservation et que le statut est COMPLETED,
        # mettre à jour le statut de la réservation
        if payment.booking and new_status == 'COMPLETED' and payment.booking.status == 'PENDING':
            payment.booking.status = 'ACCEPTED'
            payment.booking.save()

        # Envoyer des notifications en fonction du nouveau statut
        try:
            if new_status == 'COMPLETED' and old_status != 'COMPLETED':
                from notifications.services import create_payment_success_notification
                create_payment_success_notification(payment)

            elif new_status == 'FAILED' and old_status != 'FAILED':
                from notifications.services import create_payment_failed_notification
                create_payment_failed_notification(payment)

            elif new_status == 'REFUNDED' and old_status != 'REFUNDED':
                from notifications.services import create_refund_completed_notification
                create_refund_completed_notification(payment)

            elif new_status == 'PARTIALLY_REFUNDED' and old_status != 'PARTIALLY_REFUNDED':
                from notifications.services import create_refund_completed_notification
                create_refund_completed_notification(payment)
        except Exception as e:
            # Loguer l'erreur mais ne pas échouer la requête
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors de l'envoi de la notification pour le paiement {payment.id}: {str(e)}")

        serializer = self.get_serializer(payment)
        return Response(serializer.data)

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=False, methods=['post'])
    @method_decorator(csrf_exempt)
    def webhook(self, request):
        """Gère les événements webhook de Stripe"""
        import logging
        logger = logging.getLogger(__name__)

        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

        if not sig_header:
            logger.error("Pas d'en-tête de signature Stripe")
            return Response({'error': 'Pas d\'en-tête de signature Stripe'}, status=status.HTTP_400_BAD_REQUEST)

        event = handle_webhook_event(payload, sig_header)

        if 'error' in event:
            logger.error(f"Erreur de webhook: {event['error']}")
            return Response({'error': event['error']}, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"Événement webhook reçu: {event.type}")

        # Gérer l'événement
        try:
            if event.type == 'payment_intent.succeeded':
                self._handle_payment_intent_succeeded(event.data.object)

            elif event.type == 'payment_intent.payment_failed':
                self._handle_payment_intent_failed(event.data.object)

            elif event.type == 'checkout.session.completed':
                self._handle_checkout_session_completed(event.data.object)

            elif event.type == 'checkout.session.expired':
                self._handle_checkout_session_expired(event.data.object)

            elif event.type == 'charge.refunded':
                self._handle_charge_refunded(event.data.object)

            elif event.type == 'charge.dispute.created':
                self._handle_dispute_created(event.data.object)

            elif event.type == 'account.updated':
                self._handle_account_updated(event.data.object)

            elif event.type == 'transfer.created':
                self._handle_transfer_created(event.data.object)

            elif event.type == 'transfer.failed':
                self._handle_transfer_failed(event.data.object)

            # Vous pouvez ajouter d'autres types d'événements selon vos besoins

        except Exception as e:
            logger.error(f"Erreur lors du traitement de l'événement {event.type}: {str(e)}")
            # Nous retournons quand même un 200 pour que Stripe ne réessaie pas

        # Retourner une réponse pour accuser réception de l'événement
        return HttpResponse(status=200)

    def _handle_payment_intent_succeeded(self, payment_intent):
        """Gère l'événement payment_intent.succeeded"""
        import logging
        logger = logging.getLogger(__name__)

        # Récupérer le paiement à partir des métadonnées
        payment_id = payment_intent.metadata.get('payment_id')

        if not payment_id:
            logger.warning(f"Aucun payment_id trouvé dans les métadonnées du payment_intent {payment_intent.id}")
            return

        try:
            payment = Payment.objects.get(id=payment_id)

            # Mettre à jour le statut du paiement
            payment.status = 'COMPLETED'

            # Stocker l'URL du reçu si disponible
            if hasattr(payment_intent, 'charges') and payment_intent.charges.data:
                charge = payment_intent.charges.data[0]
                if hasattr(charge, 'receipt_url'):
                    payment.receipt_url = charge.receipt_url

            # Stocker le type de méthode de paiement
            if hasattr(payment_intent, 'payment_method_types') and payment_intent.payment_method_types:
                payment.stripe_payment_method_type = payment_intent.payment_method_types[0]

            payment.save()

            logger.info(f"Paiement {payment_id} marqué comme COMPLETED")

            # Envoyer une notification de paiement réussi
            try:
                from notifications.services import create_payment_success_notification
                create_payment_success_notification(payment)
                logger.info(f"Notification de paiement réussi envoyée pour le paiement {payment_id}")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la notification de paiement réussi: {str(e)}")

            # Si c'est un paiement pour une réservation, mettre à jour le statut de la réservation
            if payment.booking and payment.booking.status == 'PENDING':
                payment.booking.status = 'ACCEPTED'
                payment.booking.save()
                logger.info(f"Réservation {payment.booking.id} mise à jour vers ACCEPTED")

            # Si c'est un paiement à un capitaine, créer un transfert
            if payment.booking and payment.booking.boat and payment.booking.boat.captain:
                self._create_captain_transfer(payment, payment_intent)

        except Payment.DoesNotExist:
            logger.error(f"Paiement {payment_id} non trouvé")

    def _create_captain_transfer(self, payment, payment_intent):
        """Crée un transfert vers le compte du capitaine"""
        import logging
        logger = logging.getLogger(__name__)

        captain = payment.booking.boat.captain

        if not captain.stripe_connect_id:
            logger.warning(f"Le capitaine {captain.user.id} n'a pas d'ID Stripe Connect")
            return

        # Calculer la part du capitaine (par exemple, 80% du paiement)
        captain_amount = int(payment_intent.amount * 0.8)

        # Créer un transfert vers le capitaine
        from .stripe_utils import create_transfer
        transfer = create_transfer(
            amount=captain_amount,
            destination=captain.stripe_connect_id,
            source_transaction=payment_intent.id,
            description=f"Paiement pour la réservation #{payment.booking.id}",
            metadata={
                'payment_id': str(payment.id),
                'booking_id': str(payment.booking.id),
                'captain_id': str(captain.user.id)
            }
        )

        if 'error' not in transfer:
            # Mettre à jour les gains du capitaine
            captain.earnings += captain_amount / 100  # Convertir les centimes en euros
            captain.save()
            logger.info(f"Transfert de {captain_amount/100}€ créé pour le capitaine {captain.user.id}")
        else:
            logger.error(f"Erreur lors du transfert au capitaine {captain.user.id}: {transfer['error']}")

    def _handle_payment_intent_failed(self, payment_intent):
        """Gère l'événement payment_intent.payment_failed"""
        import logging
        logger = logging.getLogger(__name__)

        payment_id = payment_intent.metadata.get('payment_id')

        if not payment_id:
            logger.warning(f"Aucun payment_id trouvé dans les métadonnées du payment_intent {payment_intent.id}")
            return

        try:
            payment = Payment.objects.get(id=payment_id)
            payment.status = 'FAILED'

            # Stocker les détails de l'échec
            error_message = None
            if hasattr(payment_intent, 'last_payment_error') and payment_intent.last_payment_error:
                error_message = payment_intent.last_payment_error.message
                payment.metadata = {
                    **payment.metadata,
                    'failure_message': error_message,
                    'failure_code': payment_intent.last_payment_error.code
                }

            payment.save()
            logger.info(f"Paiement {payment_id} marqué comme FAILED")

            # Envoyer une notification de paiement échoué
            try:
                from notifications.services import create_payment_failed_notification
                create_payment_failed_notification(payment, error_message)
                logger.info(f"Notification de paiement échoué envoyée pour le paiement {payment_id}")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la notification de paiement échoué: {str(e)}")

        except Payment.DoesNotExist:
            logger.error(f"Paiement {payment_id} non trouvé")

    def _handle_checkout_session_completed(self, session):
        """Gère l'événement checkout.session.completed"""
        import logging
        logger = logging.getLogger(__name__)

        # Vérifier si un paiement existe déjà avec cet ID de session
        payment_id = session.metadata.get('payment_id')
        if payment_id:
            try:
                payment = Payment.objects.get(id=payment_id)
                payment.status = 'COMPLETED'
                payment.stripe_payment_id = session.payment_intent

                # Mettre à jour les informations du client
                if hasattr(session, 'customer'):
                    payment.stripe_customer_id = session.customer

                payment.save()
                logger.info(f"Paiement existant {payment_id} mis à jour")

                # Mettre à jour la réservation si nécessaire
                if payment.booking and payment.booking.status == 'PENDING':
                    payment.booking.status = 'ACCEPTED'
                    payment.booking.save()
                    logger.info(f"Réservation {payment.booking.id} mise à jour vers ACCEPTED")

                return
            except Payment.DoesNotExist:
                pass

        # Récupérer le type de paiement et l'ID de réservation à partir des métadonnées
        payment_type = session.metadata.get('payment_type')
        booking_id = session.metadata.get('booking_id')
        wallet_id = session.metadata.get('wallet_id')
        transaction_type = session.metadata.get('transaction_type')

        # Créer un enregistrement de paiement
        payment_data = {
            'amount': session.amount_total / 100,  # Convertir les centimes en euros
            'type': payment_type or 'TRIP',
            'status': 'COMPLETED',
            'stripe_payment_id': session.payment_intent,
            'metadata': {
                'checkout_session_id': session.id,
                **session.metadata
            }
        }

        if hasattr(session, 'customer'):
            payment_data['stripe_customer_id'] = session.customer

        if booking_id:
            try:
                booking = Trip.objects.get(id=booking_id)
                payment_data['booking'] = booking

                # Mettre à jour le statut de la réservation
                if booking.status == 'PENDING':
                    booking.status = 'ACCEPTED'
                    booking.save()
                    logger.info(f"Réservation {booking_id} mise à jour vers ACCEPTED")
            except Trip.DoesNotExist:
                logger.error(f"Réservation {booking_id} non trouvée")

        # Créer le paiement
        payment = Payment.objects.create(**payment_data)
        logger.info(f"Nouveau paiement {payment.id} créé pour la session {session.id}")

        # Si c'est un achat de crédits, mettre à jour le portefeuille
        if wallet_id and transaction_type == 'PURCHASE':
            try:
                # Adapter l'importation de CreditWallet à notre architecture
                # Nous utilisons notre classe d'adaptation définie en haut du fichier
                wallet = CreditWallet.objects.get(id=wallet_id)

                # Créer une transaction
                from .models import Transaction
                transaction = Transaction.objects.create(
                    wallet=wallet,
                    amount=payment_data['amount'],
                    type='PURCHASE',
                    status='COMPLETED',
                    metadata={
                        'checkout_session_id': session.id,
                        'payment_id': str(payment.id)
                    }
                )

                # Mettre à jour le solde du portefeuille
                wallet.purchase_credits(payment_data['amount'])
                logger.info(f"Crédits achetés pour le portefeuille {wallet_id}: {payment_data['amount']}")
            except Exception as e:
                logger.error(f"Erreur lors de l'achat de crédits: {str(e)}")

    def _handle_checkout_session_expired(self, session):
        """Gère l'événement checkout.session.expired"""
        import logging
        logger = logging.getLogger(__name__)

        payment_id = session.metadata.get('payment_id')
        if payment_id:
            try:
                payment = Payment.objects.get(id=payment_id)
                payment.status = 'FAILED'
                payment.metadata = {
                    **payment.metadata,
                    'failure_reason': 'Session expirée'
                }
                payment.save()
                logger.info(f"Paiement {payment_id} marqué comme FAILED (session expirée)")
            except Payment.DoesNotExist:
                logger.error(f"Paiement {payment_id} non trouvé")

    def _handle_charge_refunded(self, charge):
        """Gère l'événement charge.refunded"""
        import logging
        logger = logging.getLogger(__name__)

        # Trouver le paiement associé à cette charge
        payment_intent_id = charge.payment_intent
        if not payment_intent_id:
            logger.warning(f"Aucun payment_intent trouvé pour la charge {charge.id}")
            return

        try:
            payment = Payment.objects.filter(stripe_payment_id=payment_intent_id).first()
            if not payment:
                logger.error(f"Aucun paiement trouvé pour le payment_intent {payment_intent_id}")
                return

            # Vérifier si c'est un remboursement partiel ou complet
            is_full_refund = charge.amount_refunded == charge.amount

            if is_full_refund:
                payment.status = 'REFUNDED'
                payment.refund_amount = payment.amount
                logger.info(f"Paiement {payment.id} remboursé intégralement")
            else:
                payment.status = 'PARTIALLY_REFUNDED'
                payment.refund_amount = charge.amount_refunded / 100  # Convertir les centimes en euros
                logger.info(f"Paiement {payment.id} remboursé partiellement: {payment.refund_amount}€")

            # Stocker l'ID du remboursement
            if hasattr(charge, 'refunds') and charge.refunds.data:
                payment.refund_id = charge.refunds.data[0].id

            payment.save()

            # Envoyer une notification de remboursement
            try:
                from notifications.services import create_refund_completed_notification
                create_refund_completed_notification(payment)
                logger.info(f"Notification de remboursement envoyée pour le paiement {payment.id}")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la notification de remboursement: {str(e)}")

            # Si c'est un paiement pour une réservation, mettre à jour le statut de la réservation
            if payment.booking and payment.booking.status == 'ACCEPTED':
                payment.booking.status = 'CANCELED'
                payment.booking.save()
                logger.info(f"Réservation {payment.booking.id} annulée suite au remboursement")

        except Exception as e:
            logger.error(f"Erreur lors du traitement du remboursement: {str(e)}")

    def _handle_dispute_created(self, dispute):
        """Gère l'événement charge.dispute.created"""
        import logging
        logger = logging.getLogger(__name__)

        # Trouver le paiement associé à cette contestation
        payment_intent_id = dispute.payment_intent
        if not payment_intent_id:
            logger.warning(f"Aucun payment_intent trouvé pour la contestation {dispute.id}")
            return

        try:
            payment = Payment.objects.filter(stripe_payment_id=payment_intent_id).first()
            if not payment:
                logger.error(f"Aucun paiement trouvé pour le payment_intent {payment_intent_id}")
                return

            # Mettre à jour le paiement avec les informations de la contestation
            payment.metadata = {
                **payment.metadata,
                'dispute': {
                    'id': dispute.id,
                    'reason': dispute.reason,
                    'status': dispute.status,
                    'amount': dispute.amount / 100,  # Convertir les centimes en euros
                    'created': dispute.created
                }
            }
            payment.save()
            logger.info(f"Contestation {dispute.id} enregistrée pour le paiement {payment.id}")

            # Notifier l'administrateur (vous pourriez implémenter cela plus tard)

        except Exception as e:
            logger.error(f"Erreur lors du traitement de la contestation: {str(e)}")

    def _handle_account_updated(self, account):
        """Gère l'événement account.updated"""
        import logging
        logger = logging.getLogger(__name__)

        # Trouver le capitaine associé à ce compte
        try:
            # Captain est déjà importé en haut du fichier
            captain = Captain.objects.filter(stripe_connect_id=account.id).first()
            if not captain:
                logger.warning(f"Aucun capitaine trouvé pour le compte Stripe {account.id}")
                return

            # Mettre à jour le statut du compte
            metadata = {}
            if hasattr(captain, 'metadata') and captain.metadata:
                metadata = captain.metadata

            metadata['stripe_account_status'] = {
                'charges_enabled': account.charges_enabled,
                'payouts_enabled': account.payouts_enabled,
                'requirements': account.requirements,
                'updated': account.created
            }
            captain.metadata = metadata
            captain.save()
            logger.info(f"Statut du compte Stripe mis à jour pour le capitaine {captain.user.id}")

        except Exception as e:
            logger.error(f"Erreur lors du traitement de la mise à jour du compte: {str(e)}")

    def _handle_transfer_created(self, transfer):
        """Gère l'événement transfer.created"""
        import logging
        logger = logging.getLogger(__name__)

        # Vérifier si c'est un transfert vers un capitaine
        captain_id = transfer.metadata.get('captain_id')
        if not captain_id:
            return

        try:
            # Captain est déjà importé en haut du fichier
            captain = Captain.objects.get(id=captain_id)

            # Enregistrer le transfert
            from .models import Transaction
            Transaction.objects.create(
                amount=transfer.amount / 100,  # Convertir les centimes en euros
                type='TRANSFER',
                status='COMPLETED',
                metadata={
                    'transfer_id': transfer.id,
                    'captain_id': captain_id,
                    'payment_id': transfer.metadata.get('payment_id'),
                    'booking_id': transfer.metadata.get('booking_id')
                }
            )
            logger.info(f"Transfert {transfer.id} enregistré pour le capitaine {captain_id}")

        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement du transfert: {str(e)}")

    def _handle_transfer_failed(self, transfer):
        """Gère l'événement transfer.failed"""
        import logging
        logger = logging.getLogger(__name__)

        # Vérifier si c'est un transfert vers un capitaine
        captain_id = transfer.metadata.get('captain_id')
        if not captain_id:
            return

        try:
            # Captain est déjà importé en haut du fichier
            captain = Captain.objects.get(id=captain_id)

            # Enregistrer l'échec du transfert
            from .models import Transaction
            Transaction.objects.create(
                amount=transfer.amount / 100,  # Convertir les centimes en euros
                type='TRANSFER',
                status='FAILED',
                metadata={
                    'transfer_id': transfer.id,
                    'captain_id': captain_id,
                    'payment_id': transfer.metadata.get('payment_id'),
                    'booking_id': transfer.metadata.get('booking_id'),
                    'failure_code': transfer.failure_code,
                    'failure_message': transfer.failure_message
                }
            )
            logger.info(f"Échec du transfert {transfer.id} enregistré pour le capitaine {captain_id}")

            # Notifier l'administrateur (vous pourriez implémenter cela plus tard)

        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement de l'échec du transfert: {str(e)}")

@extend_schema_view(
    list=extend_schema(tags=["Payments"], responses=TransactionSerializer(many=True)),
    retrieve=extend_schema(tags=["Payments"], responses=TransactionSerializer),
    create=extend_schema(tags=["Payments"], request=TransactionSerializer, responses=TransactionSerializer),
    update=extend_schema(tags=["Payments"], request=TransactionSerializer, responses=TransactionSerializer),
    partial_update=extend_schema(tags=["Payments"], request=TransactionSerializer, responses=TransactionSerializer),
    destroy=extend_schema(tags=["Payments"], responses=None),
)
class TransactionViewSet(viewsets.ModelViewSet):
    """ViewSet for the Transaction model"""
    queryset = Transaction.objects.all()
    serializer_class = TransactionSerializer
    permission_classes = [permissions.AllowAny]  # Permettre l'accès à tous pour les tests

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=False, methods=['post'])
    def record_transaction(self, request):
        """Record a transaction"""
        wallet_id = request.data.get('wallet_id')
        amount = request.data.get('amount')
        transaction_type = request.data.get('type')

        if wallet_id and amount and transaction_type:
            try:
                wallet = CreditWallet.objects.get(id=wallet_id)
                # Convertir le montant en Decimal
                from decimal import Decimal
                amount_decimal = Decimal(str(amount))
                
                # Créer la transaction
                transaction = Transaction.objects.create(
                    wallet=wallet,
                    amount=amount_decimal,
                    type=transaction_type,
                    description=f'Transaction de {amount}€',
                    balance_after=wallet.balance + amount_decimal,
                    metadata={
                        'transaction_type': transaction_type
                    }
                )

                # Update wallet balance
                if transaction_type == 'PURCHASE':
                    wallet.purchase_credits(float(amount))
                elif transaction_type == 'DEDUCTION':
                    wallet.deduct_credits(float(amount))

                serializer = self.get_serializer(transaction)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except CreditWallet.DoesNotExist:
                return Response({'error': 'wallet not found'}, status=status.HTTP_404_NOT_FOUND)
            except ValueError:
                return Response({'error': 'invalid amount'}, status=status.HTTP_400_BAD_REQUEST)
        return Response({'error': 'wallet_id, amount, and type are required'}, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=False, methods=['get'])
    def wallet_transactions(self, request):
        """Get transactions for a wallet"""
        wallet_id = request.query_params.get('wallet_id')

        if wallet_id:
            transactions = Transaction.objects.filter(wallet_id=wallet_id).order_by('-timestamp')
            serializer = self.get_serializer(transactions, many=True)
            return Response(serializer.data)
        return Response({'error': 'wallet_id is required'}, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=False, methods=['post'])
    def purchase_credits(self, request):
        """Acheter des crédits pour un portefeuille en utilisant Stripe"""
        wallet_id = request.data.get('wallet_id')
        amount = request.data.get('amount')
        success_url = request.data.get('success_url')
        cancel_url = request.data.get('cancel_url')
        customer_id = request.data.get('customer_id')
        payment_method_types = request.data.get('payment_method_types')
        locale = request.data.get('locale', 'fr')

        if not wallet_id or not amount:
            return Response({'error': 'wallet_id et amount sont requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            wallet = CreditWallet.objects.get(id=wallet_id)
            user = wallet.user if hasattr(wallet, 'user') else None

            # Convertir le montant en centimes pour Stripe
            amount_cents = int(float(amount) * 100)

            # Créer les métadonnées
            metadata = {
                'wallet_id': str(wallet.id),
                'transaction_type': 'PURCHASE'
            }

            if user:
                metadata['user_id'] = str(user.id)

            # Créer une session de paiement
            from .stripe_utils import create_checkout_session
            session = create_checkout_session(
                amount=amount_cents,
                currency='eur',
                product_name='Crédits Commodore',
                product_description=f'Achat de {amount} crédits',
                customer=customer_id,
                payment_method_types=payment_method_types,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata=metadata,
                locale=locale
            )

            if 'error' in session:
                return Response({'error': session['error']}, status=status.HTTP_400_BAD_REQUEST)

            # Créer une transaction en attente
            transaction = Transaction.objects.create(
                wallet=wallet,
                amount=float(amount),
                type='PURCHASE',
                status='PENDING',
                metadata={
                    'checkout_session_id': session.id,
                    **metadata
                }
            )

            # Mettre à jour les métadonnées avec l'ID de la transaction
            metadata['transaction_id'] = str(transaction.id)
            stripe.checkout.Session.modify(
                session.id,
                metadata=metadata
            )

            return Response({
                'session_id': session.id,
                'transaction_id': transaction.id,
                'checkout_url': session.url
            })
        except CreditWallet.DoesNotExist:
            return Response({'error': 'wallet not found'}, status=status.HTTP_404_NOT_FOUND)
        except ValueError:
            return Response({'error': 'invalid amount'}, status=status.HTTP_400_BAD_REQUEST)

@extend_schema_view(
    list=extend_schema(tags=["Payments"]),
)
class CaptainPaymentViewSet(viewsets.ViewSet):
    """ViewSet for captain payments"""
    permission_classes = [permissions.AllowAny]  # Permettre l'accès à tous pour les tests

    @extend_schema(tags=["Payments"], request=None, responses=None)
    @action(detail=False, methods=['post'])
    def create_connect_account(self, request):
        """Crée un compte Stripe Connect pour un capitaine"""
        captain_id = request.data.get('captain_id')
        email = request.data.get('email')
        country = request.data.get('country', 'FR')
        business_type = request.data.get('business_type', 'individual')
        business_profile = request.data.get('business_profile')

        if not captain_id or not email:
            return Response({'error': 'captain_id et email sont requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            captain = Captain.objects.get(id=captain_id)
            user = captain.user if hasattr(captain, 'user') else None

            # Créer un profil d'entreprise si non fourni
            if not business_profile and user:
                business_profile = {
                    'name': f"{user.first_name} {user.last_name}",
                    'url': request.build_absolute_uri('/'),
                    'product_description': 'Services de transport maritime',
                    'mcc': '4789'  # Code MCC pour les services de transport
                }

            # Créer un compte Connect
            from .stripe_utils import create_connect_account
            account = create_connect_account(
                email=email,
                country=country,
                business_type=business_type,
                business_profile=business_profile
            )

            if 'error' in account:
                return Response({'error': account['error']}, status=status.HTTP_400_BAD_REQUEST)

            # Mettre à jour le capitaine avec l'ID Stripe Connect
            captain.stripe_connect_id = account.id
            captain.save()

            return Response({
                'account_id': account.id,
                'captain_id': captain.id,
                'business_type': business_type,
                'country': country
            })
        except Captain.DoesNotExist:
            return Response({'error': 'captain not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def create_account_link(self, request):
        """Crée un lien de compte pour l'onboarding"""
        captain_id = request.data.get('captain_id')
        refresh_url = request.data.get('refresh_url')
        return_url = request.data.get('return_url')
        link_type = request.data.get('type', 'account_onboarding')  # 'account_onboarding' ou 'account_update'

        if not captain_id or not refresh_url or not return_url:
            return Response({'error': 'captain_id, refresh_url, et return_url sont requis'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            captain = Captain.objects.get(id=captain_id)

            if not captain.stripe_connect_id:
                return Response({'error': 'Le capitaine n\'a pas de compte Stripe Connect'}, status=status.HTTP_400_BAD_REQUEST)

            # Créer un lien de compte
            from .stripe_utils import create_account_link
            account_link = create_account_link(
                account_id=captain.stripe_connect_id,
                refresh_url=refresh_url,
                return_url=return_url,
                type=link_type
            )

            if 'error' in account_link:
                return Response({'error': account_link['error']}, status=status.HTTP_400_BAD_REQUEST)

            # Stocker les informations du lien
            metadata = {}
            if hasattr(captain, 'metadata') and captain.metadata:
                metadata = captain.metadata

            metadata['last_account_link'] = {
                'created': account_link.created,
                'expires_at': account_link.expires_at,
                'type': link_type
            }
            captain.metadata = metadata
            captain.save()

            return Response({
                'url': account_link.url,
                'expires_at': account_link.expires_at
            })
        except Captain.DoesNotExist:
            return Response({'error': 'captain not found'}, status=status.HTTP_404_NOT_FOUND)