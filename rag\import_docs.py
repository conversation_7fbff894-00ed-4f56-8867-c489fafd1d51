"""
Script pour importer tous les documents de documentation en une seule fois.
À exécuter avec : python manage.py shell < rag/import_docs.py
"""

import os
import glob
from django.utils import timezone
from rag.models import Document
from .services import _rag_service as rag_service
import logging

# Configurer le logger
logger = logging.getLogger(__name__)

# Chemin vers le dossier de documentation
docs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'documentation')

# Trouver tous les fichiers markdown
md_files = glob.glob(os.path.join(docs_dir, '*.md'))

print(f"📚 Trouvé {len(md_files)} documents à importer...")

# Importer chaque fichier
for md_file in md_files:
    filename = os.path.basename(md_file)
    title = os.path.splitext(filename)[0].replace('_', ' ').title()
    
    # Lire le contenu du fichier
    with open(md_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier si le document existe déjà
    existing_doc = Document.objects.filter(title=title).first()
    
    if existing_doc:
        print(f"📝 Mise à jour du document existant: {title}")
        if existing_doc.content != content:
            existing_doc.content = content
            existing_doc.embedding_generated = False
            existing_doc.updated_at = timezone.now()
            existing_doc.save()
            # Traiter le document mis à jour
            rag_service.process_document(existing_doc)
        else:
            print(f"✓ Le contenu n'a pas changé, aucune mise à jour nécessaire")
    else:
        # Créer un nouveau document
        print(f"➕ Création d'un nouveau document: {title}")
        doc = Document.objects.create(
            title=title,
            content=content,
            source=f"Documentation Interne - {filename}",
            category="Documentation",
            tags=["documentation", "commodore"]
        )
        
        # Traiter le document (générer les chunks et les embeddings)
        rag_service.process_document(doc)
    
print("\n✅ Importation terminée!")
print(f"📊 Statistiques:")
print(f"  - Documents totaux: {Document.objects.count()}")
print(f"  - Documents avec embeddings: {Document.objects.filter(embedding_generated=True).count()}")
print(f"  - Modèle LLM utilisé: {LLM_MODEL}")
print("\nLe système RAG est prêt à être utilisé!")
