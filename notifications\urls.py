"""
Module de configuration des URLs pour l'application notifications.

Ce module définit les routes URL pour l'API REST de l'application notifications,
permettant d'accéder aux différentes vues.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import NotificationViewSet, DeviceViewSet

router = DefaultRouter()
router.register('notifications', NotificationViewSet, basename='notification')
router.register('devices', DeviceViewSet, basename='device')

app_name = 'notifications'

urlpatterns = [
    path('', include(router.urls)),
]
