from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from django.utils import timezone
import uuid

from .models import ChatbotSession, ChatbotMessage
from .serializers import ChatbotMessageSerializer
from rag.rag_service import rag_service
from accounts.models import User  # UserProfile n'existe pas dans le modèle

@api_view(['POST'])
@authentication_classes([TokenAuthentication, SessionAuthentication])
@permission_classes([IsAuthenticated])
def chatbot_message(request):
    """
    Endpoint pour interagir avec le chatbot alimenté par Meta-Llama-3.
    Reçoit un message de l'utilisateur et retourne une réponse générée.
    """
    # Récupérer les données de la requête
    message = request.data.get('message', '').strip()
    session_id = request.data.get('session_id')
    
    # Vérifier les données
    if not message:
        return Response({
            'error': 'Un message est requis'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Récupérer le profil utilisateur pour personnaliser les réponses
    try:
        user_profile = UserProfile.objects.get(user=request.user)
        profile_type = user_profile.get_user_type_display()
    except UserProfile.DoesNotExist:
        profile_type = 'Client'  # Valeur par défaut
    
    # Créer ou récupérer une session
    if session_id:
        try:
            session = ChatbotSession.objects.get(
                session_id=session_id, 
                user=request.user
            )
        except ChatbotSession.DoesNotExist:
            # Créer une nouvelle session si l'ID fourni n'existe pas
            session = ChatbotSession.objects.create(
                user=request.user,
                session_id=uuid.uuid4()
            )
    else:
        # Créer une nouvelle session
        session = ChatbotSession.objects.create(
            user=request.user,
            session_id=uuid.uuid4()
        )
    
    # Mettre à jour le timestamp de dernière interaction
    session.last_interaction = timezone.now()
    session.save()
    
    # Enregistrer le message de l'utilisateur
    user_message = ChatbotMessage.objects.create(
        session=session,
        role=ChatbotMessage.Role.USER,
        content=message
    )
    
    # Générer une réponse avec le service RAG
    response_content = rag_service.generate_response(
        session=session,
        user_message=message,
        user_profile=profile_type
    )
    
    # Enregistrer la réponse
    assistant_message = ChatbotMessage.objects.create(
        session=session,
        role=ChatbotMessage.Role.ASSISTANT,
        content=response_content
    )
    
    # Préparer la réponse de l'API
    return Response({
        'session_id': str(session.session_id),
        'message': ChatbotMessageSerializer(assistant_message).data
    })

@api_view(['GET'])
@authentication_classes([TokenAuthentication, SessionAuthentication])
@permission_classes([IsAuthenticated])
def chatbot_history(request, session_id=None):
    """
    Récupère l'historique des messages pour une session de chatbot.
    Si aucun session_id n'est fourni, retourne toutes les sessions de l'utilisateur.
    """
    if session_id:
        try:
            session = ChatbotSession.objects.get(
                session_id=session_id, 
                user=request.user
            )
            messages = ChatbotMessage.objects.filter(session=session).order_by('created_at')
            return Response({
                'session_id': str(session.session_id),
                'messages': ChatbotMessageSerializer(messages, many=True).data
            })
        except ChatbotSession.DoesNotExist:
            return Response({
                'error': 'Session introuvable'
            }, status=status.HTTP_404_NOT_FOUND)
    else:
        # Retourner toutes les sessions de l'utilisateur
        sessions = ChatbotSession.objects.filter(user=request.user).order_by('-last_interaction')
        sessions_data = []
        
        for session in sessions:
            last_message = ChatbotMessage.objects.filter(
                session=session
            ).order_by('-created_at').first()
            
            sessions_data.append({
                'session_id': str(session.session_id),
                'last_interaction': session.last_interaction,
                'last_message': last_message.content if last_message else "",
                'created_at': session.created_at
            })
        
        return Response(sessions_data)

@api_view(['DELETE'])
@authentication_classes([TokenAuthentication, SessionAuthentication])
@permission_classes([IsAuthenticated])
def chatbot_clear_session(request, session_id):
    """
    Supprime une session de chatbot et tous ses messages.
    """
    try:
        session = ChatbotSession.objects.get(
            session_id=session_id, 
            user=request.user
        )
        session.delete()  # Supprime également tous les messages associés grâce à CASCADE
        return Response({
            'status': 'success',
            'message': 'Session supprimée avec succès'
        })
    except ChatbotSession.DoesNotExist:
        return Response({
            'error': 'Session introuvable'
        }, status=status.HTTP_404_NOT_FOUND)
