from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from .models import Trip, Shuttle
import uuid

class SharedPaymentInvitation(models.Model):
    """Modèle pour les invitations de paiement partagé."""
    
    STATUS_CHOICES = (
        ('PENDING', _('En attente')),
        ('ACCEPTED', _('Acceptée')),
        ('DECLINED', _('Refusée')),
        ('EXPIRED', _('Expirée')),
        ('PAID', _('Payée')),
    )
    
    trip = models.ForeignKey(Trip, on_delete=models.CASCADE, related_name='shared_payment_invitations')
    inviter = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='sent_payment_invitations')
    invitee_email = models.EmailField(_('email de l\'invité'))
    invitee = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='received_payment_invitations'
    )
    amount = models.DecimalField(_('montant'), max_digits=10, decimal_places=2)
    token = models.CharField(_('token d\'invitation'), max_length=100, unique=True, default=uuid.uuid4)
    status = models.CharField(_('statut'), max_length=20, choices=STATUS_CHOICES, default='PENDING')
    message = models.TextField(_('message'), blank=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    expires_at = models.DateTimeField(_('expire le'))
    
    class Meta:
        verbose_name = _('invitation de paiement partagé')
        verbose_name_plural = _('invitations de paiement partagé')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Invitation de {self.inviter.email} à {self.invitee_email} - {self.get_status_display()}"

class ShuttleBooking(models.Model):
    """Modèle pour les réservations de places sur une navette."""
    
    STATUS_CHOICES = (
        ('RESERVED', _('Réservée')),
        ('PAID', _('Payée')),
        ('CHECKED_IN', _('Embarquée')),
        ('COMPLETED', _('Terminée')),
        ('CANCELLED', _('Annulée')),
        ('REFUNDED', _('Remboursée')),
    )
    
    shuttle = models.ForeignKey(Shuttle, on_delete=models.CASCADE, related_name='bookings')
    client = models.ForeignKey('accounts.Client', on_delete=models.CASCADE, related_name='shuttle_bookings')
    def generate_booking_ref():
        """Génère une référence de réservation compatible avec max_length=20"""
        import random
        import string
        return 'BK' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=14))
    
    booking_reference = models.CharField(_('référence de réservation'), max_length=20, unique=True, default=generate_booking_ref)
    number_of_seats = models.PositiveIntegerField(_('nombre de places'), default=1)
    seat_numbers = models.JSONField(_('numéros de siège'), default=list, blank=True, null=True)
    passenger_names = models.JSONField(_('noms des passagers'), default=list)
    special_requests = models.TextField(_('demandes spéciales'), blank=True)
    status = models.CharField(_('statut'), max_length=20, choices=STATUS_CHOICES, default='RESERVED')
    amount_paid = models.DecimalField(_('montant payé'), max_digits=10, decimal_places=2, default=0)
    transaction_id = models.CharField(_('ID de transaction'), max_length=100, blank=True, null=True)
    payment_method = models.CharField(_('méthode de paiement'), max_length=50, blank=True)
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    
    class Meta:
        verbose_name = _('réservation de navette')
        verbose_name_plural = _('réservations de navette')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Réservation {self.booking_reference} - {self.client.user.email} - {self.shuttle.route_name}"

    def calculate_total_price(self):
        return self.shuttle.price_per_person * self.number_of_seats
