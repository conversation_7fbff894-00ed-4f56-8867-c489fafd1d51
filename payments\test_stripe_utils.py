import pytest
from unittest.mock import patch, MagicMock
from .stripe_utils import (
    create_payment_intent, create_checkout_session, create_connect_account,
    create_account_link, create_transfer, handle_webhook_event,
    create_customer, create_payment_method, attach_payment_method,
    detach_payment_method, list_payment_methods, create_refund
)

@pytest.mark.django_db
class TestStripeUtils:
    """Tests pour les fonctions utilitaires Stripe"""

    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent(self, mock_create):
        """Test de la création d'un payment intent"""
        # Simuler la réponse de Stripe
        mock_create.return_value = MagicMock(
            id='pi_test123',
            client_secret='pi_test123_secret',
            amount=1000,
            currency='eur',
            status='requires_payment_method',
            metadata={}
        )
        
        # Test avec les paramètres de base
        result = create_payment_intent(amount=1000)
        
        mock_create.assert_called_once()
        assert result.id == 'pi_test123'
        assert result.client_secret == 'pi_test123_secret'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_payment_intent(
            amount=1000,
            currency='eur',
            payment_method_types=['card'],
            customer='cus_test123',
            setup_future_usage='off_session',
            metadata={'test': 'value'},
            description='Test payment'
        )
        
        mock_create.assert_called_once()
        assert result.id == 'pi_test123'
    
    @patch('stripe.checkout.Session.create')
    def test_create_checkout_session(self, mock_create):
        """Test de la création d'une session checkout"""
        # Simuler la réponse de Stripe
        mock_create.return_value = MagicMock(
            id='cs_test123',
            url='https://checkout.stripe.com/test',
            payment_intent='pi_test123',
            amount_total=1000,
            currency='eur',
            status='open',
            metadata={}
        )
        
        # Test avec les paramètres de base
        result = create_checkout_session(amount=1000)
        
        mock_create.assert_called_once()
        assert result.id == 'cs_test123'
        assert result.url == 'https://checkout.stripe.com/test'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_checkout_session(
            amount=1000,
            currency='eur',
            product_name='Test Product',
            product_description='Test Description',
            customer='cus_test123',
            payment_method_types=['card'],
            success_url='https://example.com/success',
            cancel_url='https://example.com/cancel',
            metadata={'test': 'value'},
            locale='fr'
        )
        
        mock_create.assert_called_once()
        assert result.id == 'cs_test123'
    
    @patch('stripe.Account.create')
    def test_create_connect_account(self, mock_create):
        """Test de la création d'un compte Connect"""
        # Simuler la réponse de Stripe
        mock_create.return_value = MagicMock(
            id='acct_test123',
            email='<EMAIL>',
            country='FR',
            type='express',
            capabilities={
                'card_payments': {'requested': True},
                'transfers': {'requested': True}
            }
        )
        
        # Test avec les paramètres de base
        result = create_connect_account(email='<EMAIL>')
        
        mock_create.assert_called_once()
        assert result.id == 'acct_test123'
        assert result.email == '<EMAIL>'
        
        # Réinitialiser le mock
        mock_create.reset_mock()
        
        # Test avec tous les paramètres
        result = create_connect_account(
            email='<EMAIL>',
            country='FR',
            business_type='individual',
            business_profile={
                'name': 'Test Business',
                'url': 'https://example.com'
            }
        )
        
        mock_create.assert_called_once()
        assert result.id == 'acct_test123'
