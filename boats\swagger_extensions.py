from drf_spectacular.utils import extend_schema, OpenApiParameter
from .serializers import BoatSerializer, BoatListSerializer, MaintenanceRecordSerializer

# Décorateurs pour chaque vue à utiliser dans views.py

boat_list_schema = extend_schema(
    tags=["Boats"],
    responses=BoatListSerializer(many=True),
    parameters=[
        OpenApiParameter("boat_type", str, OpenApiParameter.QUERY, required=False),
        OpenApiParameter("capacity_min", int, OpenApiParameter.QUERY, required=False),
        OpenApiParameter("location", str, OpenApiParameter.QUERY, required=False),
    ],
)

boat_create_schema = extend_schema(
    tags=["Boats"],
    request=BoatSerializer,
    responses=BoatSerializer,
)

boat_detail_schema = extend_schema(
    tags=["Boats"],
    responses=BoatSerializer,
)

boat_patch_schema = extend_schema(
    tags=["Boats"],
    request=BoatSerializer,
    responses=BoatSerializer,
)

boat_toggle_schema = extend_schema(
    tags=["Boats"],
    responses=None,
)

maintenance_list_schema = extend_schema(
    tags=["Boats"],
    responses=MaintenanceRecordSerializer(many=True),
)

maintenance_create_schema = extend_schema(
    tags=["Boats"],
    request=MaintenanceRecordSerializer,
    responses=MaintenanceRecordSerializer,
)

# Ajoute d'autres décorateurs si besoin pour chaque vue spécifique
