# API Endpoints - Reviews

## 1. Gestion des avis

### 1.1. Liste des avis
- **Endpoint**: GET /api/reviews/
- **Description**: Récupérer la liste des avis selon différents filtres
- **Auth Required**: <PERSON><PERSON> (JWT <PERSON>)
- **Query Parameters**:
  - content_type_id: ID du type de contenu évalué (obligatoire)
  - object_id: ID de l'objet évalué (obligatoire)
  - rating: Filtrer par note spécifique (1-5)
  - verified_only: Avis vérifi<PERSON> uniquement (true/false)
  - has_response: Avis avec réponses uniquement (true/false)
  - date_from: Date de début (format YYYY-MM-DD)
  - date_to: Date de fin (format YYYY-MM-DD)
  - sort_by: Tri (rating, -rating, created_at, -created_at)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "author": {
      "id": 8,
      "first_name": "<PERSON>",
      "last_name": "<PERSON>",
      "profile_picture": "https://example.com/profiles/marie.jpg"
    },
    "type": "CAPTAIN",
    "rating": 5,
    "title": "Excellent capitaine, très professionnel",
    "comment": "Jean a été extrêmement professionnel et courtois. Navigation parfaite et très bonne communication.",
    "pros": "Ponctualité, connaissance de la région, communication",
    "cons": "",
    "cleanliness_rating": 5,
    "communication_rating": 5,
    "punctuality_rating": 5,
    "value_rating": 4,
    "is_verified": true,
    "created_at": "2025-05-20T15:30:45Z",
    "trip": {
      "id": 123,
      "start_location": "Port de Saint-Tropez",
      "end_location": "Plage de Pampelonne",
      "scheduled_start_time": "2025-05-20T14:00:00Z"
    },
    "reviewed_object": {
      "id": 5,
      "type": "captain",
      "name": "Jean Dupont"
    }
  }
]
```

### 1.2. Détails d'un avis
- **Endpoint**: GET /api/reviews/{id}/
- **Description**: Récupérer les détails d'un avis spécifique avec ses réponses
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "author": {
    "id": 8,
    "first_name": "Marie",
    "last_name": "Martin",
    "profile_picture": "https://example.com/profiles/marie.jpg"
  },
  "type": "CAPTAIN",
  "rating": 5,
  "title": "Excellent capitaine, très professionnel",
  "comment": "Jean a été extrêmement professionnel et courtois. Navigation parfaite et très bonne communication.",
  "pros": "Ponctualité, connaissance de la région, communication",
  "cons": "",
  "cleanliness_rating": 5,
  "communication_rating": 5,
  "punctuality_rating": 5,
  "value_rating": 4,
  "is_verified": true,
  "created_at": "2025-05-20T15:30:45Z",
  "updated_at": "2025-05-20T15:30:45Z",
  "trip": {
    "id": 123,
    "start_location": "Port de Saint-Tropez",
    "end_location": "Plage de Pampelonne",
    "scheduled_start_time": "2025-05-20T14:00:00Z"
  },
  "reviewed_object": {
    "id": 5,
    "type": "captain",
    "name": "Jean Dupont"
  },
  "responses": [
    {
      "id": 1,
      "author": {
        "id": 5,
        "first_name": "Jean",
        "last_name": "Dupont",
        "profile_picture": "https://example.com/profiles/jean.jpg"
      },
      "content": "Merci beaucoup pour votre avis positif ! Ce fut un plaisir de vous accompagner.",
      "created_at": "2025-05-21T09:15:30Z"
    }
  ]
}
```

### 1.3. Créer un avis
- **Endpoint**: POST /api/reviews/
- **Description**: Créer un nouvel avis
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "content_type": 7,
  "object_id": 5,
  "rating": 5,
  "title": "Excellent capitaine, très professionnel",
  "comment": "Jean a été extrêmement professionnel et courtois. Navigation parfaite et très bonne communication.",
  "pros": "Ponctualité, connaissance de la région, communication",
  "cons": "",
  "cleanliness_rating": 5,
  "communication_rating": 5,
  "punctuality_rating": 5,
  "value_rating": 4,
  "trip": 123
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "author": {
    "id": 8,
    "first_name": "Marie",
    "last_name": "Martin",
    "profile_picture": "https://example.com/profiles/marie.jpg"
  },
  "type": "CAPTAIN",
  "rating": 5,
  "title": "Excellent capitaine, très professionnel",
  "comment": "Jean a été extrêmement professionnel et courtois. Navigation parfaite et très bonne communication.",
  "pros": "Ponctualité, connaissance de la région, communication",
  "cons": "",
  "cleanliness_rating": 5,
  "communication_rating": 5,
  "punctuality_rating": 5,
  "value_rating": 4,
  "is_verified": true,
  "created_at": "2025-05-25T13:45:22Z",
  "updated_at": "2025-05-25T13:45:22Z",
  "trip": {
    "id": 123,
    "start_location": "Port de Saint-Tropez",
    "end_location": "Plage de Pampelonne",
    "scheduled_start_time": "2025-05-20T14:00:00Z"
  },
  "reviewed_object": {
    "id": 5,
    "type": "captain",
    "name": "Jean Dupont"
  },
  "response": null
}
```

### 1.4. Mettre à jour un avis
- **Endpoint**: PATCH /api/reviews/{id}/
- **Description**: Modifier un avis existant (uniquement par l'auteur)
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "rating": 4,
  "comment": "Jean a été très professionnel et courtois. Navigation parfaite et très bonne communication.",
  "value_rating": 3
}
```
- **Response (200 OK)**:
```json
{
  "id": 1,
  "author": {
    "id": 8,
    "first_name": "Marie",
    "last_name": "Martin",
    "profile_picture": "https://example.com/profiles/marie.jpg"
  },
  "type": "CAPTAIN",
  "rating": 4,
  "title": "Excellent capitaine, très professionnel",
  "comment": "Jean a été très professionnel et courtois. Navigation parfaite et très bonne communication.",
  "pros": "Ponctualité, connaissance de la région, communication",
  "cons": "",
  "cleanliness_rating": 5,
  "communication_rating": 5,
  "punctuality_rating": 5,
  "value_rating": 3,
  "is_verified": true,
  "created_at": "2025-05-25T13:45:22Z",
  "updated_at": "2025-05-25T13:50:12Z",
  "trip": {
    "id": 123,
    "start_location": "Port de Saint-Tropez",
    "end_location": "Plage de Pampelonne",
    "scheduled_start_time": "2025-05-20T14:00:00Z"
  },
  "reviewed_object": {
    "id": 5,
    "type": "captain",
    "name": "Jean Dupont"
  },
  "response": null
}
```

### 1.5. Supprimer un avis
- **Endpoint**: DELETE /api/reviews/{id}/
- **Description**: Supprimer un avis (uniquement par l'auteur)
- **Auth Required**: Oui (JWT Token)
- **Response (204 No Content)**

## 2. Réponses aux avis

### 2.1. Créer une réponse à un avis
- **Endpoint**: POST /api/reviews/{id}/responses/
- **Description**: Créer une réponse à un avis (uniquement par la personne évaluée ou administrateur)
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "content": "Merci beaucoup pour votre avis positif ! Ce fut un plaisir de vous accompagner."
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "author": {
    "id": 5,
    "first_name": "Jean",
    "last_name": "Dupont",
    "profile_picture": "https://example.com/profiles/jean.jpg"
  },
  "content": "Merci beaucoup pour votre avis positif ! Ce fut un plaisir de vous accompagner.",
  "created_at": "2025-05-21T09:15:30Z"
}
```

### 2.2. Mettre à jour une réponse
- **Endpoint**: PATCH /api/reviews/responses/{id}/
- **Description**: Modifier une réponse existante (uniquement par l'auteur)
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "content": "Merci beaucoup pour votre avis positif ! Ce fut un réel plaisir de vous accompagner. Au plaisir de vous revoir."
}
```
- **Response (200 OK)**:
```json
{
  "id": 1,
  "author": {
    "id": 5,
    "first_name": "Jean",
    "last_name": "Dupont",
    "profile_picture": "https://example.com/profiles/jean.jpg"
  },
  "content": "Merci beaucoup pour votre avis positif ! Ce fut un réel plaisir de vous accompagner. Au plaisir de vous revoir.",
  "created_at": "2025-05-21T09:15:30Z",
  "updated_at": "2025-05-25T13:55:15Z",
  "review_id": 1,
  "review_title": "Excellent capitaine, très professionnel"
}
```

### 2.3. Supprimer une réponse
- **Endpoint**: DELETE /api/reviews/responses/{id}/
- **Description**: Supprimer une réponse (uniquement par l'auteur)
- **Auth Required**: Oui (JWT Token)
- **Response (204 No Content)**

## 3. Signalement d'avis

### 3.1. Signaler un avis
- **Endpoint**: POST /api/reviews/{id}/report/
- **Description**: Signaler un avis inapproprié
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "reason": "OFFENSIVE",
  "description": "Cet avis contient des propos insultants"
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "review_id": 45,
  "reporter": {
    "id": 8,
    "first_name": "Marie",
    "last_name": "Martin"
  },
  "reason": "OFFENSIVE",
  "description": "Cet avis contient des propos insultants",
  "status": "PENDING",
  "created_at": "2025-05-25T13:57:30Z",
  "review_details": {
    "title": "Service décevant",
    "rating": 1,
    "author": {
      "id": 10,
      "name": "Pierre Dubois"
    }
  }
}
```

### 3.2. Statistiques des avis
- **Endpoint**: GET /api/reviews/statistics/
- **Description**: Récupérer des statistiques détaillées sur les avis d'un objet spécifique
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - content_type_id: ID du type de contenu évalué (obligatoire)
  - object_id: ID de l'objet évalué (obligatoire)
- **Response (200 OK)**:
```json
{
  "total_reviews": 45,
  "average_rating": 4.7,
  "rating_distribution": {
    "1": 0,
    "2": 1,
    "3": 3,
    "4": 10,
    "5": 31
  },
  "recent_reviews": [
    {
      "id": 45,
      "author_name": "Sophie Martin",
      "rating": 5,
      "title": "Excellente expérience",
      "created_at": "2025-05-20T14:30:00Z",
      "is_verified": true,
      "response_count": 1
    }
  ],
  "total_verified_reviews": 40,
  "response_rate": 85.0
}
```

### 3.3. Avis de l'utilisateur connecté
- **Endpoint**: GET /api/reviews/user/
- **Description**: Récupérer tous les avis écrits par l'utilisateur connecté
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
[
  {
    "id": 45,
    "author_name": "Jean Dupont",
    "rating": 5,
    "title": "Excellente expérience",
    "created_at": "2025-05-20T14:30:00Z",
    "is_verified": true,
    "response_count": 1
  },
  {
    "id": 32,
    "author_name": "Jean Dupont",
    "rating": 4,
    "title": "Très bon service",
    "created_at": "2025-05-10T09:15:00Z",
    "is_verified": true,
    "response_count": 0
  }
]
```

## Fonctionnalités implémentées

1. ✅ **Implémentation des vues** : Toutes les vues API ont été implémentées selon les endpoints documentés.

2. ✅ **Autorisations** : Les permissions sont configurées pour s'assurer que seuls les utilisateurs autorisés peuvent effectuer certaines actions.

3. ✅ **Validation** : Vérification qu'un utilisateur ne peut laisser qu'un seul avis par objet évalué.

4. ✅ **Modération** : Système de signalement et de modération automatisée après un certain nombre de signalements.

## À implémenter à l'avenir

1. **Notifications** : Envoyer des notifications lorsqu'un utilisateur reçoit un avis ou une réponse à son avis.

2. **Intégration avec le système de réputation** : Les notes doivent affecter la réputation des capitaines et établissements.
