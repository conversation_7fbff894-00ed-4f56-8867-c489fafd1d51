{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Commodore Assistant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-top: 50px;
            margin-bottom: 50px;
        }
        .chat-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            max-width: 80%;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: auto;
        }
        .assistant-message {
            background-color: #e9ecef;
            color: #212529;
            margin-right: auto;
        }
        .message-content {
            word-wrap: break-word;
        }
        .chat-input {
            display: flex;
        }
        .chat-input input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px 0 0 5px;
        }
        .chat-input button {
            border-radius: 0 5px 5px 0;
        }
        .sources-container {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .sources-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .source-item {
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
        }
        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
        }
        .suggestions {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .suggestions p {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 0.9em;
        }
        .suggestion-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .feedback-buttons {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 5px;
            padding: 5px;
            border-top: 1px solid #e9ecef;
        }
        .feedback-label {
            font-size: 0.8em;
            color: #6c757d;
            margin-right: 5px;
        }
        .feedback-thank-you {
            font-size: 0.8em;
            color: #28a745;
            font-style: italic;
        }
        .feedback-error {
            font-size: 0.8em;
            color: #dc3545;
            font-style: italic;
        }
        .feedback-form {
            width: 100%;
        }
        .feedback-details {
            font-size: 0.9em;
            min-height: 60px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="chat-container">
            <div class="chat-header">
                <h1>Commodore Assistant</h1>
                <p class="text-muted">Posez vos questions sur Commodore Taxi Boat</p>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message assistant-message">
                    <div class="message-content">
                        Bonjour ! Je suis l'assistant Commodore. Comment puis-je vous aider aujourd'hui ?
                    </div>
                </div>
            </div>

            <div class="loading d-none" id="loadingIndicator">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p>L'assistant réfléchit...</p>
            </div>

            <div class="sources-container d-none" id="sourcesContainer">
                <div class="sources-title">Sources utilisées :</div>
                <div id="sourcesList"></div>
            </div>

            <form id="chatForm" class="chat-input">
                <input type="text" id="userInput" class="form-control" placeholder="Tapez votre question ici..." required>
                <button type="submit" class="btn btn-primary">Envoyer</button>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatForm = document.getElementById('chatForm');
            const userInput = document.getElementById('userInput');
            const chatMessages = document.getElementById('chatMessages');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const sourcesContainer = document.getElementById('sourcesContainer');
            const sourcesList = document.getElementById('sourcesList');

            // Fonction pour ajouter un message au chat
            function addMessage(content, isUser, messageId = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';

                // Stocker l'ID du message pour le feedback
                if (messageId) {
                    contentDiv.dataset.messageId = messageId;
                }

                // Si c'est un message de l'assistant, ajouter des suggestions
                if (!isUser) {
                    // Créer le contenu du message
                    const textDiv = document.createElement('div');
                    textDiv.className = 'message-text';
                    textDiv.textContent = content;
                    contentDiv.appendChild(textDiv);

                    // Ajouter des suggestions de questions basées sur le contenu
                    let suggestions = [];

                    // Déterminer les suggestions en fonction du contenu de la réponse
                    if (content.toLowerCase().includes('paiement') || content.toLowerCase().includes('payer')) {
                        suggestions.push('Comment recharger mes crédits ?');
                        suggestions.push('Quelles sont les options de paiement disponibles ?');
                        suggestions.push('Comment fonctionne le rechargement automatique ?');
                    } else if (content.toLowerCase().includes('réservation') || content.toLowerCase().includes('réserver')) {
                        suggestions.push('Comment modifier ma réservation ?');
                        suggestions.push('Quelle est la politique d\'annulation ?');
                        suggestions.push('Puis-je réserver pour quelqu\'un d\'autre ?');
                    } else if (content.toLowerCase().includes('qr code')) {
                        suggestions.push('Que faire si mon QR code ne fonctionne pas ?');
                        suggestions.push('Où trouver mon QR code ?');
                        suggestions.push('Le capitaine peut-il m\'embarquer sans QR code ?');
                    } else if (content.toLowerCase().includes('annulation') || content.toLowerCase().includes('annuler')) {
                        suggestions.push('Comment être remboursé ?');
                        suggestions.push('Puis-je reporter ma course au lieu de l\'annuler ?');
                        suggestions.push('Quels sont les délais d\'annulation ?');
                    } else {
                        suggestions.push('Comment puis-je payer ma course ?');
                        suggestions.push('Comment réserver un bateau ?');
                        suggestions.push('Que faire en cas de problème avec mon QR code ?');
                    }

                    // Ajouter les suggestions si disponibles
                    if (suggestions.length > 0) {
                        const suggestionsDiv = document.createElement('div');
                        suggestionsDiv.className = 'suggestions';
                        suggestionsDiv.innerHTML = '<p>Questions suggérées :</p>';

                        const buttonsDiv = document.createElement('div');
                        buttonsDiv.className = 'suggestion-buttons';

                        suggestions.forEach(suggestion => {
                            const button = document.createElement('button');
                            button.className = 'btn btn-sm btn-outline-primary m-1';
                            button.textContent = suggestion;
                            button.addEventListener('click', function() {
                                userInput.value = suggestion;
                                chatForm.dispatchEvent(new Event('submit'));
                            });
                            buttonsDiv.appendChild(button);
                        });

                        suggestionsDiv.appendChild(buttonsDiv);
                        contentDiv.appendChild(suggestionsDiv);
                    }

                    // Ajouter des boutons de feedback pour les messages de l'assistant
                    if (!isUser) {
                        const feedbackDiv = document.createElement('div');
                        feedbackDiv.className = 'feedback-buttons mt-2';
                        feedbackDiv.innerHTML = `
                            <span class="feedback-label">Cette réponse vous a-t-elle été utile ?</span>
                            <button class="btn btn-sm btn-outline-success feedback-btn" data-value="helpful">👍 Oui</button>
                            <button class="btn btn-sm btn-outline-danger feedback-btn" data-value="not-helpful">👎 Non</button>
                        `;
                        contentDiv.appendChild(feedbackDiv);

                        // Ajouter les écouteurs d'événements pour les boutons de feedback
                        const feedbackButtons = feedbackDiv.querySelectorAll('.feedback-btn');
                        feedbackButtons.forEach(btn => {
                            btn.addEventListener('click', function() {
                                const value = this.getAttribute('data-value');
                                // Enregistrer le feedback via l'API
                                const messageId = contentDiv.dataset.messageId || 'unknown';
                                fetch('/api/rag/feedback/', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRFToken': getCookie('csrftoken')
                                    },
                                    body: JSON.stringify({
                                        message_id: messageId,
                                        feedback_type: value === 'helpful' ? 'positive' : 'negative',
                                        comments: ''  // Sera rempli plus tard si négatif
                                    })
                                })
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error('Erreur lors de l\'envoi du feedback');
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    console.log('Feedback enregistré:', data);
                                })
                                .catch(error => {
                                    console.error('Erreur:', error);
                                });

                                // Désactiver les boutons après le vote
                                feedbackButtons.forEach(b => b.disabled = true);

                                // Afficher un message de remerciement
                                const thankYouMsg = document.createElement('div');
                                thankYouMsg.className = 'feedback-thank-you mt-1';
                                thankYouMsg.textContent = 'Merci pour votre feedback !';
                                feedbackDiv.appendChild(thankYouMsg);

                                // Si le feedback est négatif, afficher un formulaire pour plus de détails
                                if (value === 'not-helpful') {
                                    const feedbackForm = document.createElement('div');
                                    feedbackForm.className = 'feedback-form mt-2';
                                    feedbackForm.innerHTML = `
                                        <textarea class="form-control feedback-details" placeholder="Pouvez-vous nous dire pourquoi cette réponse n'était pas utile ?"></textarea>
                                        <button class="btn btn-sm btn-primary mt-1 send-feedback">Envoyer</button>
                                    `;
                                    feedbackDiv.appendChild(feedbackForm);

                                    // Ajouter l'écouteur d'événement pour le bouton d'envoi
                                    feedbackForm.querySelector('.send-feedback').addEventListener('click', function() {
                                        const details = feedbackForm.querySelector('.feedback-details').value;

                                        // Envoyer les commentaires détaillés
                                        const messageId = contentDiv.dataset.messageId || 'unknown';
                                        fetch('/api/rag/feedback/', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json',
                                                'X-CSRFToken': getCookie('csrftoken')
                                            },
                                            body: JSON.stringify({
                                                message_id: messageId,
                                                feedback_type: 'negative',
                                                comments: details
                                            })
                                        })
                                        .then(response => {
                                            if (!response.ok) {
                                                throw new Error('Erreur lors de l\'envoi des commentaires');
                                            }
                                            return response.json();
                                        })
                                        .then(data => {
                                            console.log('Commentaires enregistrés:', data);
                                            feedbackForm.innerHTML = '<div class="feedback-thank-you">Merci pour vos commentaires détaillés !</div>';
                                        })
                                        .catch(error => {
                                            console.error('Erreur:', error);
                                            feedbackForm.innerHTML = '<div class="feedback-error">Une erreur est survenue. Veuillez réessayer.</div>';
                                        });
                                    });
                                }
                            });
                        });
                    }
                } else {
                    // Message utilisateur simple
                    contentDiv.textContent = content;
                }

                messageDiv.appendChild(contentDiv);
                chatMessages.appendChild(messageDiv);

                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Fonction pour afficher les sources
            function displaySources(sources) {
                sourcesList.innerHTML = '';

                if (sources && sources.length > 0) {
                    sources.forEach(source => {
                        const sourceItem = document.createElement('div');
                        sourceItem.className = 'source-item';
                        sourceItem.textContent = `${source.document_title} (${source.relevance})`;
                        sourcesList.appendChild(sourceItem);
                    });
                    sourcesContainer.classList.remove('d-none');
                } else {
                    sourcesContainer.classList.add('d-none');
                }
            }

            // Gestion de la soumission du formulaire
            chatForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const userMessage = userInput.value.trim();
                if (!userMessage) return;

                // Afficher le message de l'utilisateur
                addMessage(userMessage, true);

                // Vider le champ de saisie
                userInput.value = '';

                // Afficher l'indicateur de chargement
                loadingIndicator.classList.remove('d-none');

                // Envoyer la requête au serveur
                fetch('/api/rag/chat/api/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        message: userMessage
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Masquer l'indicateur de chargement
                    loadingIndicator.classList.add('d-none');

                    // Afficher la réponse de l'assistant
                    addMessage(data.response, false, data.message_id);

                    // Afficher les sources si disponibles
                    displaySources(data.sources);
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    loadingIndicator.classList.add('d-none');
                    addMessage("Désolé, une erreur s'est produite. Veuillez réessayer.", false);
                });
            });

            // Fonction pour récupérer le cookie CSRF
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
        });
    </script>
</body>
</html>
