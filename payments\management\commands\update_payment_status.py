from django.core.management.base import BaseCommand, CommandError
from payments.models import Payment
from notifications.services import (
    create_payment_success_notification,
    create_payment_failed_notification,
    create_refund_completed_notification
)
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Met à jour le statut d\'un paiement et envoie les notifications appropriées'

    def add_arguments(self, parser):
        parser.add_argument('payment_id', type=str, help='ID du paiement à mettre à jour')
        parser.add_argument('status', type=str, help='Nouveau statut du paiement (COMPLETED, FAILED, REFUNDED, PARTIALLY_REFUNDED)')
        parser.add_argument('--skip-notifications', action='store_true', help='Ne pas envoyer de notifications')

    def handle(self, *args, **options):
        payment_id = options['payment_id']
        new_status = options['status']
        skip_notifications = options.get('skip_notifications', False)

        try:
            payment = Payment.objects.get(id=payment_id)
        except Payment.DoesNotExist:
            raise CommandError(f'Paiement avec ID "{payment_id}" non trouvé')

        # Vérifier que le statut est valide
        valid_statuses = dict(Payment.PAYMENT_STATUS).keys()
        if new_status not in valid_statuses:
            raise CommandError(f'Statut invalide. Les statuts valides sont: {", ".join(valid_statuses)}')

        # Sauvegarder l'ancien statut pour les notifications
        old_status = payment.status

        # Mettre à jour le statut
        payment.status = new_status
        payment.save()

        self.stdout.write(self.style.SUCCESS(f'Statut du paiement {payment_id} mis à jour de "{old_status}" à "{new_status}"'))

        # Si c'est un paiement pour une réservation et que le statut est COMPLETED,
        # mettre à jour le statut de la réservation
        if payment.booking and new_status == 'COMPLETED' and payment.booking.status == 'PENDING':
            payment.booking.status = 'ACCEPTED'
            payment.booking.save()
            self.stdout.write(self.style.SUCCESS(f'Statut de la réservation {payment.booking.id} mis à jour vers "ACCEPTED"'))

        # Envoyer des notifications si nécessaire
        if not skip_notifications:
            try:
                if new_status == 'COMPLETED' and old_status != 'COMPLETED':
                    create_payment_success_notification(payment)
                    self.stdout.write(self.style.SUCCESS(f'Notification de paiement réussi envoyée'))

                elif new_status == 'FAILED' and old_status != 'FAILED':
                    create_payment_failed_notification(payment)
                    self.stdout.write(self.style.SUCCESS(f'Notification de paiement échoué envoyée'))

                elif new_status == 'REFUNDED' and old_status != 'REFUNDED':
                    create_refund_completed_notification(payment)
                    self.stdout.write(self.style.SUCCESS(f'Notification de remboursement complet envoyée'))

                elif new_status == 'PARTIALLY_REFUNDED' and old_status != 'PARTIALLY_REFUNDED':
                    create_refund_completed_notification(payment)
                    self.stdout.write(self.style.SUCCESS(f'Notification de remboursement partiel envoyée'))
            except Exception as e:
                self.stdout.write(self.style.WARNING(f'Erreur lors de l\'envoi des notifications: {str(e)}'))
