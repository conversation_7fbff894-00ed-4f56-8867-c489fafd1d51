import os
import json
import stripe
import requests
from datetime import datetime
from django.conf import settings

# Configuration de Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY

def create_webhook_signature(payload, timestamp=None):
    """Crée une signature webhook valide"""
    if timestamp is None:
        timestamp = int(datetime.now().timestamp())
    
    payload_str = json.dumps(payload)
    signed_payload = f"{timestamp}.{payload_str}"
    signature = stripe.WebhookSignature._compute_signature(
        signed_payload.encode('utf-8'),
        settings.STRIPE_WEBHOOK_SECRET
    )
    return f"t={timestamp},v1={signature}"

def send_webhook_event(event_type, data):
    """Envoie un événement webhook simulé au serveur local"""
    webhook_url = "http://localhost:8000/api/webhooks/stripe/"
    
    # Création du payload
    payload = {
        "id": f"evt_test_{datetime.now().timestamp()}",
        "object": "event",
        "type": event_type,
        "data": {
            "object": data
        }
    }

    # Création de la signature
    timestamp = int(datetime.now().timestamp())
    signature = create_webhook_signature(payload, timestamp)

    # Envoi de la requête
    try:
        response = requests.post(
            webhook_url,
            json=payload,
            headers={
                "Content-Type": "application/json",
                "Stripe-Signature": signature
            }
        )
        print(f"Événement {event_type} envoyé. Status: {response.status_code}")
        print(f"Réponse: {response.text}")
        return response
    except Exception as e:
        print(f"Erreur lors de l'envoi: {str(e)}")
        return None

def simulate_payment_success():
    """Simule un paiement réussi"""
    data = {
        "id": f"pi_test_{datetime.now().timestamp()}",
        "object": "payment_intent",
        "amount": 1000,  # 10€
        "currency": "eur",
        "status": "succeeded",
        "charges": {
            "data": [{
                "id": f"ch_test_{datetime.now().timestamp()}",
                "amount": 1000,
                "status": "succeeded"
            }]
        }
    }
    return send_webhook_event("payment_intent.succeeded", data)

def simulate_payment_failed():
    """Simule un paiement échoué"""
    data = {
        "id": f"pi_test_{datetime.now().timestamp()}",
        "object": "payment_intent",
        "amount": 1000,
        "currency": "eur",
        "status": "failed",
        "last_payment_error": {
            "message": "Votre carte a été refusée.",
            "code": "card_declined"
        }
    }
    return send_webhook_event("payment_intent.payment_failed", data)

def simulate_refund():
    """Simule un remboursement"""
    data = {
        "id": f"ch_test_{datetime.now().timestamp()}",
        "object": "charge",
        "amount": 1000,
        "amount_refunded": 1000,
        "refunded": True,
        "refunds": {
            "data": [{
                "id": f"re_test_{datetime.now().timestamp()}",
                "amount": 1000,
                "status": "succeeded"
            }]
        }
    }
    return send_webhook_event("charge.refunded", data)

def simulate_customer_created():
    """Simule la création d'un client"""
    data = {
        "id": f"cus_test_{datetime.now().timestamp()}",
        "object": "customer",
        "email": "<EMAIL>",
        "name": "Test Customer"
    }
    return send_webhook_event("customer.created", data)

def simulate_payment_method_attached():
    """Simule l'ajout d'une méthode de paiement"""
    data = {
        "id": f"pm_test_{datetime.now().timestamp()}",
        "object": "payment_method",
        "type": "card",
        "customer": f"cus_test_{datetime.now().timestamp()}",
        "card": {
            "brand": "visa",
            "last4": "4242",
            "exp_month": 12,
            "exp_year": 2025
        }
    }
    return send_webhook_event("payment_method.attached", data)

if __name__ == "__main__":
    print("Simulateur de webhooks Stripe")
    print("1. Simuler un paiement réussi")
    print("2. Simuler un paiement échoué")
    print("3. Simuler un remboursement")
    print("4. Simuler la création d'un client")
    print("5. Simuler l'ajout d'une méthode de paiement")
    print("0. Quitter")

    while True:
        choice = input("\nChoisissez une option (0-5): ")
        
        if choice == "0":
            break
        elif choice == "1":
            simulate_payment_success()
        elif choice == "2":
            simulate_payment_failed()
        elif choice == "3":
            simulate_refund()
        elif choice == "4":
            simulate_customer_created()
        elif choice == "5":
            simulate_payment_method_attached()
        else:
            print("Option invalide")
