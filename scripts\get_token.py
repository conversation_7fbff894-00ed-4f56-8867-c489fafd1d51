"""
Script pour générer un token JWT pour les tests.
À exécuter depuis le shell Django.
"""

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

# Vous pouvez changer cet ID si nécessaire
user = User.objects.get(id=1)  # Supposé être l'admin

# Générer un token qui ne va pas expirer trop rapidement
refresh = RefreshToken.for_user(user)
# Prolonger la durée de validité à 30 jours
refresh.access_token.set_exp(lifetime=60*60*24*30)  # 30 jours

print("\n=== TOKEN JWT POUR LES TESTS ===")
print(f"Access token: {str(refresh.access_token)}")
print("Copiez ce token dans votre script test_payments.ps1")
print("===============================\n")
