# 🚤 DOCUMENTATION COMPLÈTE - ESPACE BATELIER

## 📋 **INFORMATIONS GÉNÉRALES**

### **Base URL**
```
https://api.commodore.com/api/boatman/
```

### **Authentification**
Tous les endpoints (sauf login et forgot-password) nécessitent un token d'authentification :
```
Authorization: Token <user_token>
Content-Type: application/json
```

### **Format des réponses**
Toutes les réponses suivent le format standardisé :
```json
{
    "status": "success|error",
    "data": {...},
    "message": "Message descriptif",
    "timestamp": "2024-06-15T10:30:00Z"
}
```

---

## 🔐 **1. AUTHENTIFICATION**

### **POST /api/boatman/login/**
Connexion du batelier avec email et mot de passe.

**Headers requis :**
```
Content-Type: application/json
```

**Donn<PERSON> à envoyer :**
```json
{
    "email": "<EMAIL>",
    "password": "monMotDePasse123"
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "captain_id": "42",
        "token": "abc123def456ghi789jkl012mno345pqr678stu901vwx234yz",
        "name": "Captain Jack",
        "email": "<EMAIL>",
        "first_login": false,
        "requires_password_change": false
    },
    "message": "Connexion réussie",
    "timestamp": "2024-06-15T10:30:00Z"
}
```

**Réponse erreur - Identifiants invalides (401) :**
```json
{
    "status": "error",
    "error": "Identifiants invalides",
    "error_code": 401,
    "timestamp": "2024-06-15T10:30:00Z"
}
```

**Réponse erreur - Compte non batelier (403) :**
```json
{
    "status": "error",
    "error": "Accès refusé - Compte batelier requis",
    "error_code": 403,
    "timestamp": "2024-06-15T10:30:00Z"
}
```

### **POST /api/boatman/forgot-password/**
Initier la réinitialisation du mot de passe par email ou SMS.

**Headers requis :**
```
Content-Type: application/json
```

**Données à envoyer :**
```json
{
    "contact": "<EMAIL>"
}
```

**Ou par téléphone :**
```json
{
    "contact": "+33123456789"
}
```

**Réponse succès - Email (200) :**
```json
{
    "status": "success",
    "data": {
        "captain_id": "42",
        "contact_method": "email",
        "masked_contact": "ca****<EMAIL>"
    },
    "message": "Code de vérification envoyé",
    "timestamp": "2024-06-15T10:35:00Z"
}
```

**Réponse succès - SMS (200) :**
```json
{
    "status": "success",
    "data": {
        "captain_id": "42",
        "contact_method": "sms",
        "masked_contact": "+33***456789"
    },
    "message": "Code de vérification envoyé",
    "timestamp": "2024-06-15T10:35:00Z"
}
```

### **POST /api/boatman/verify-code/**
Vérifier le code de vérification reçu par email ou SMS.

**Données à envoyer :**
```json
{
    "captain_id": "42",
    "code": "1234"
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "captain_id": "42",
        "temp_token": "temp_abc123def456ghi789",
        "expires_in": 1800
    },
    "message": "Code vérifié avec succès",
    "timestamp": "2024-06-15T10:40:00Z"
}
```

### **POST /api/boatman/change-password/**
Changer le mot de passe (avec ancien mot de passe ou token temporaire).

**Headers requis :**
```
Authorization: Token <user_token>
Content-Type: application/json
```

**Changement normal :**
```json
{
    "captain_id": "42",
    "old_password": "ancienMotDePasse",
    "new_password": "nouveauMotDePasse123",
    "confirm_password": "nouveauMotDePasse123"
}
```

**Réinitialisation avec token temporaire :**
```json
{
    "captain_id": "42",
    "temp_token": "temp_abc123def456ghi789",
    "new_password": "nouveauMotDePasse123",
    "confirm_password": "nouveauMotDePasse123"
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "message": "Mot de passe mis à jour avec succès",
    "timestamp": "2024-06-15T10:45:00Z"
}
```

---

## 📊 **2. TABLEAU DE BORD**

### **GET /api/boatman/dashboard/**
Récupérer les données complètes du tableau de bord du batelier.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "captain": {
            "id": 42,
            "name": "Captain Jack",
            "email": "<EMAIL>",
            "phone": "+33123456789",
            "availability_status": "AVAILABLE",
            "average_rating": 4.8
        },
        "financial_summary": {
            "available_balance": 347.50,
            "total_tips": 125.00,
            "earnings_this_week": 280.00,
            "currency": "EUR"
        },
        "trip_statistics": {
            "total_shuttles": 156,
            "completed_shuttles": 148,
            "shuttles_this_week": 12,
            "shuttles_today": 3,
            "completion_rate": 94.87,
            "average_rating": 4.8
        },
        "upcoming_shuttles": [
            {
                "shuttle_id": "trip_789123",
                "date": "2024-06-15T16:30:00Z",
                "destination": "Aéroport Nice",
                "passengers": 2,
                "client": "Marie Dubois",
                "status": "ACCEPTED",
                "departure": "Hotel Paradise",
                "estimated_duration": 25
            },
            {
                "shuttle_id": "trip_789124",
                "date": "2024-06-15T18:00:00Z",
                "destination": "Port de Cannes",
                "passengers": 4,
                "client": "John Smith",
                "status": "PENDING",
                "departure": "Gare SNCF",
                "estimated_duration": 15
            }
        ],
        "next_shuttle": {
            "shuttle_id": "trip_789123",
            "date": "2024-06-15T16:30:00Z",
            "destination": "Aéroport Nice",
            "departure": "Hotel Paradise",
            "passengers": 2,
            "client": "Marie Dubois",
            "time_until": "Dans 2h15"
        },
        "quick_actions": [
            {
                "action": "view_shuttles",
                "label": "Voir mes courses",
                "url": "/api/boatman/shuttles/"
            },
            {
                "action": "view_wallet",
                "label": "Consulter portefeuille",
                "url": "/api/boatman/wallet/"
            },
            {
                "action": "update_availability",
                "label": "Changer disponibilité",
                "url": "/api/boatman/availability/"
            }
        ]
    },
    "message": "Données du tableau de bord récupérées avec succès",
    "timestamp": "2024-06-15T14:15:00Z"
}
```

### **GET /api/boatman/availability/**
Récupérer le statut de disponibilité actuel.

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "availability_status": "AVAILABLE",
        "is_available": true,
        "last_updated": "2024-06-15T14:00:00Z",
        "available_options": [
            {"value": "AVAILABLE", "label": "Disponible"},
            {"value": "BUSY", "label": "Occupé"},
            {"value": "OFFLINE", "label": "Hors ligne"},
            {"value": "MAINTENANCE", "label": "Maintenance"}
        ]
    },
    "message": "Statut de disponibilité récupéré",
    "timestamp": "2024-06-15T14:15:00Z"
}
```

### **POST /api/boatman/availability/**
Mettre à jour le statut de disponibilité.

**Données à envoyer :**
```json
{
    "availability_status": "BUSY"
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "availability_status": "BUSY",
        "is_available": false,
        "updated_at": "2024-06-15T14:20:00Z"
    },
    "message": "Disponibilité mise à jour: BUSY",
    "timestamp": "2024-06-15T14:20:00Z"
}
```

---

## 🚐 **3. GESTION DES COURSES**

### **GET /api/boatman/shuttles/**
Récupérer la liste de toutes les courses assignées au batelier avec filtres et pagination.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Paramètres URL optionnels :**
- `filter` : "all", "À venir", "En cours", "Terminées", "Annulées"
- `page` : Numéro de page (défaut: 1)
- `limit` : Nombre d'éléments par page (défaut: 20, max: 100)
- `search` : Recherche par nom client ou destination

**Exemples d'URLs :**
```
GET /api/boatman/shuttles/
GET /api/boatman/shuttles/?filter=À venir&page=1&limit=10
GET /api/boatman/shuttles/?search=aéroport&filter=Terminées
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "shuttles": [
            {
                "shuttle_id": "trip_789123",
                "client": "Marie Dubois",
                "client_phone": "+33234567890",
                "date": "2024-06-15T16:30:00Z",
                "departure": "Hotel Paradise Beach",
                "destination": "Aéroport Nice Côte d'Azur",
                "passengers": 2,
                "status": "ACCEPTED",
                "status_display": "À venir",
                "estimated_duration": 25,
                "distance_km": 12.5,
                "boat": {
                    "name": "Sea Explorer",
                    "capacity": 8
                },
                "payment": {
                    "amount": 0.00,
                    "status": "PAID",
                    "method": "FREE_SHUTTLE"
                },
                "special_requests": "Vol Air France AF1234, arrivée 16h15",
                "created_at": "2024-06-15T14:20:00Z",
                "can_start": true,
                "can_complete": false
            },
            {
                "shuttle_id": "trip_789124",
                "client": "John Smith",
                "client_phone": "+33345678901",
                "date": "2024-06-15T18:00:00Z",
                "departure": "Gare SNCF Cannes",
                "destination": "Hotel Paradise Beach",
                "passengers": 1,
                "status": "IN_PROGRESS",
                "status_display": "En cours",
                "estimated_duration": 15,
                "distance_km": 3.2,
                "boat": {
                    "name": "Sea Explorer",
                    "capacity": 8
                },
                "payment": {
                    "amount": 0.00,
                    "status": "PAID",
                    "method": "FREE_SHUTTLE"
                },
                "special_requests": "Train TGV 18h45 pour Paris",
                "created_at": "2024-06-15T17:30:00Z",
                "can_start": false,
                "can_complete": true
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 47,
            "total_pages": 3,
            "has_next": true,
            "has_previous": false
        },
        "filters": {
            "current_filter": "all",
            "search_term": "",
            "available_filters": [
                {"value": "all", "label": "Toutes", "count": 47},
                {"value": "À venir", "label": "À venir", "count": 8},
                {"value": "En cours", "label": "En cours", "count": 2},
                {"value": "Terminées", "label": "Terminées", "count": 35},
                {"value": "Annulées", "label": "Annulées", "count": 2}
            ]
        }
    },
    "message": "Courses récupérées avec succès",
    "timestamp": "2024-06-15T14:25:00Z"
}
```

### **GET /api/boatman/shuttle/{shuttle_id}/**
Récupérer les détails complets d'une course spécifique.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Paramètres URL :**
- `shuttle_id` : ID de la course (requis)

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "shuttle_id": "trip_789123",
        "status": "ACCEPTED",
        "status_display": "À venir",
        "date": "2024-06-15T16:30:00Z",
        "departure": "Hotel Paradise Beach",
        "destination": "Aéroport Nice Côte d'Azur",
        "passengers": 2,
        "estimated_duration": 25,
        "distance_km": 12.5,
        "client": {
            "name": "Marie Dubois",
            "phone": "+33234567890",
            "email": "<EMAIL>",
            "profile_picture": "https://aws.s3.com/profiles/marie_dubois.jpg"
        },
        "boat": {
            "name": "Sea Explorer",
            "capacity": 8,
            "type": "classic"
        },
        "payment": {
            "amount": 0.00,
            "status": "PAID",
            "method": "FREE_SHUTTLE",
            "tip": 0.00
        },
        "location": {
            "current": "Hotel Paradise Beach",
            "distance_remaining": null
        },
        "special_requests": "Vol Air France AF1234, arrivée 16h15. Famille avec enfant en bas âge.",
        "timeline": [
            {
                "event": "Course créée",
                "timestamp": "2024-06-15T14:20:00Z",
                "status": "completed"
            },
            {
                "event": "Course acceptée par l'établissement",
                "timestamp": "2024-06-15T14:25:00Z",
                "status": "completed"
            },
            {
                "event": "Batelier assigné",
                "timestamp": "2024-06-15T14:25:00Z",
                "status": "completed"
            }
        ],
        "actions": [
            {
                "action": "start",
                "label": "Démarrer la course",
                "method": "POST",
                "url": "/api/boatman/shuttle/trip_789123/start/",
                "requires_qr": true
            }
        ]
    },
    "message": "Détails de la course récupérés",
    "timestamp": "2024-06-15T14:30:00Z"
}
```

**Réponse erreur - Course non trouvée (404) :**
```json
{
    "status": "error",
    "error": "Course non trouvée",
    "error_code": 404,
    "message": "Cette course n'existe pas ou ne vous est pas assignée",
    "timestamp": "2024-06-15T14:30:00Z"
}
```

### **POST /api/boatman/shuttle/{shuttle_id}/validate-qr/**
Valider le QR code du client avant de démarrer la course.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Données à envoyer :**
```json
{
    "qr_code": "COMMODORE_TRIP_789123_456"
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "shuttle_id": "trip_789123",
        "qr_validated": true,
        "client": "Marie Dubois",
        "passengers": 2,
        "destination": "Aéroport Nice Côte d'Azur",
        "can_start": true
    },
    "message": "QR code validé, embarquement confirmé",
    "timestamp": "2024-06-15T16:25:00Z"
}
```

**Réponse erreur - QR code invalide (400) :**
```json
{
    "status": "error",
    "error": "QR code invalide",
    "error_code": 400,
    "message": "Le QR code fourni ne correspond pas à cette course",
    "timestamp": "2024-06-15T16:25:00Z"
}
```

### **POST /api/boatman/shuttle/{shuttle_id}/start/**
Démarrer une course après validation du QR code.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "shuttle_id": "trip_789123",
        "status": "IN_PROGRESS",
        "started_at": "2024-06-15T16:30:00Z",
        "estimated_arrival": "2024-06-15T16:55:00Z"
    },
    "message": "Course démarrée avec succès",
    "timestamp": "2024-06-15T16:30:00Z"
}
```

**Réponse erreur - Course non démarrable (400) :**
```json
{
    "status": "error",
    "error": "Impossible de démarrer une course avec le statut: COMPLETED",
    "error_code": 400,
    "message": "Cette course ne peut pas être démarrée dans son état actuel",
    "timestamp": "2024-06-15T16:30:00Z"
}
```

### **POST /api/boatman/shuttle/{shuttle_id}/end/**
Terminer une course en cours.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "shuttle_id": "trip_789123",
        "status": "COMPLETED",
        "completed_at": "2024-06-15T16:52:00Z",
        "actual_duration": 22,
        "estimated_duration": 25
    },
    "message": "Course terminée avec succès",
    "timestamp": "2024-06-15T16:52:00Z"
}
```

### **GET /api/boatman/shuttle/{shuttle_id}/track/**
Suivre une course en cours avec informations de localisation.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "shuttle_id": "trip_789123",
        "status": "IN_PROGRESS",
        "current_location": "En route vers Aéroport Nice Côte d'Azur",
        "distance_remaining": 3.8,
        "destination": "Aéroport Nice Côte d'Azur",
        "passengers": 2,
        "client": "Marie Dubois",
        "tracking_info": {
            "elapsed_time_minutes": 15,
            "estimated_arrival": "2024-06-15T16:55:00Z",
            "progress_percentage": 70
        },
        "coordinates": {
            "latitude": 43.5528,
            "longitude": 7.0174
        }
    },
    "message": "Informations de suivi récupérées",
    "timestamp": "2024-06-15T16:45:00Z"
}
```

---

## 👤 **4. GESTION DU PROFIL**

### **GET /api/boatman/profile/**
Récupérer les informations complètes du profil du batelier.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "captain_id": "42",
        "personal_info": {
            "name": "Captain Jack",
            "first_name": "Captain",
            "last_name": "Jack",
            "email": "<EMAIL>",
            "phone": "+33123456789",
            "profile_picture": "https://aws.s3.com/profiles/captain_jack.jpg",
            "date_joined": "2024-01-15T10:00:00Z"
        },
        "professional_info": {
            "experience": "8 ans d'expérience en navigation côtière et plaisance",
            "license_number": "LIC123456",
            "availability_status": "AVAILABLE",
            "is_available": true,
            "average_rating": 4.8,
            "total_trips": 156,
            "rate_per_km": 25.00,
            "rate_per_hour": 50.00
        },
        "boat": {
            "id": 178,
            "name": "Sea Explorer",
            "registration": "FR123456",
            "capacity": 8,
            "fuel_type": "essence",
            "fuel_consumption": 12.0,
            "boat_type": "classic",
            "photos": [
                "https://aws.s3.com/boats/sea_explorer_1.jpg",
                "https://aws.s3.com/boats/sea_explorer_2.jpg"
            ],
            "features": [
                "Climatisation",
                "Système audio",
                "Gilets de sauvetage",
                "Trousse de secours"
            ],
            "is_available": true
        },
        "contact_preferences": {
            "email_notifications": true,
            "sms_notifications": true,
            "push_notifications": true
        },
        "account_status": {
            "is_verified": true,
            "documents_uploaded": true,
            "background_check": "approved"
        }
    },
    "message": "Profil récupéré avec succès",
    "timestamp": "2024-06-15T17:00:00Z"
}
```

### **PATCH /api/boatman/profile/**
Mettre à jour les informations du profil.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Données à envoyer :**
```json
{
    "first_name": "Jean",
    "last_name": "Dupont",
    "phone_number": "+***********",
    "experience": "10 ans d'expérience en navigation côtière",
    "contact_preferences": {
        "email_notifications": false,
        "sms_notifications": true,
        "push_notifications": true
    }
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "updated_fields": [
            "first_name",
            "last_name",
            "phone_number",
            "experience",
            "contact_preferences.email_notifications"
        ],
        "profile_updated_at": "2024-06-15T17:05:00Z"
    },
    "message": "Profil mis à jour (5 champs modifiés)",
    "timestamp": "2024-06-15T17:05:00Z"
}
```

### **GET /api/boatman/boat/**
Récupérer les informations des bateaux du batelier.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "boats": [
            {
                "id": 178,
                "name": "Sea Explorer",
                "registration": "FR123456",
                "capacity": 8,
                "boat_type": "classic",
                "fuel_type": "essence",
                "fuel_consumption": 12.0,
                "year": 2020,
                "manufacturer": "Beneteau",
                "model": "Flyer 8",
                "length": 8.5,
                "photos": [
                    "https://aws.s3.com/boats/sea_explorer_1.jpg",
                    "https://aws.s3.com/boats/sea_explorer_2.jpg"
                ],
                "features": [
                    "Climatisation",
                    "Système audio",
                    "Gilets de sauvetage",
                    "Trousse de secours",
                    "GPS"
                ],
                "is_available": true,
                "zone_served": "Côte d'Azur",
                "radius": 25,
                "created_at": "2024-01-15T10:30:00Z"
            }
        ],
        "total_boats": 1,
        "main_boat": {
            "id": 178,
            "name": "Sea Explorer",
            "capacity": 8,
            "boat_type": "classic"
        }
    },
    "message": "Informations des bateaux récupérées",
    "timestamp": "2024-06-15T17:10:00Z"
}
```

### **PATCH /api/boatman/boat/**
Mettre à jour les informations du bateau.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Données à envoyer :**
```json
{
    "boat_id": 178,
    "name": "Ocean Explorer",
    "capacity": 10,
    "fuel_consumption": 10.5,
    "features": [
        "Climatisation",
        "Système audio premium",
        "Gilets de sauvetage",
        "Trousse de secours",
        "GPS",
        "Radar"
    ]
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "boat_id": 178,
        "updated_fields": [
            "name",
            "capacity",
            "fuel_consumption",
            "features"
        ],
        "boat_updated_at": "2024-06-15T17:15:00Z"
    },
    "message": "Bateau mis à jour (4 champs modifiés)",
    "timestamp": "2024-06-15T17:15:00Z"
}
```

---

## 💰 **5. PORTEFEUILLE ET PAIEMENTS**

### **GET /api/boatman/wallet/**
Consulter le portefeuille avec historique des transactions.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Paramètres URL optionnels :**
- `page` : Numéro de page pour les transactions (défaut: 1)
- `limit` : Nombre de transactions par page (défaut: 20)

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "wallet": {
            "available_balance": 347.50,
            "currency": "EUR",
            "total_earned": 2840.00,
            "total_withdrawn": 2492.50,
            "pending_amount": 125.00
        },
        "transactions": [
            {
                "transaction_id": "pay_789123",
                "type": "Paiement course",
                "amount": 25.00,
                "date": "2024-06-15T16:52:00Z",
                "status": "COMPLETED",
                "description": "Paiement course #trip_789123",
                "details": {
                    "trip_id": "trip_789123",
                    "destination": "Aéroport Nice",
                    "passengers": 2,
                    "client": "Marie Dubois"
                },
                "reference": "TXN_789123"
            },
            {
                "transaction_id": "tip_789124",
                "type": "Pourboire",
                "amount": 15.00,
                "date": "2024-06-15T14:30:00Z",
                "status": "COMPLETED",
                "description": "Pourboire course #trip_789120",
                "details": {
                    "trip_id": "trip_789120",
                    "destination": "Port de Cannes",
                    "passengers": 4,
                    "client": "John Smith"
                },
                "reference": "TIP_789124"
            },
            {
                "transaction_id": "wd_789125",
                "type": "Retrait",
                "amount": -200.00,
                "date": "2024-06-14T10:00:00Z",
                "status": "COMPLETED",
                "description": "Retrait vers compte bancaire",
                "details": {
                    "withdrawal_method": "BANK_TRANSFER",
                    "processing_time": "1-3 jours ouvrés"
                },
                "reference": "WD_20240614100000"
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 156,
            "total_pages": 8,
            "has_next": true,
            "has_previous": false
        },
        "statistics": {
            "earnings_this_month": 680.00,
            "earnings_this_week": 180.00,
            "total_tips": 340.00,
            "average_trip_earning": 18.20,
            "total_trips_paid": 156
        },
        "quick_actions": [
            {
                "action": "withdraw",
                "label": "Retirer des fonds",
                "url": "/api/boatman/wallet/withdraw/",
                "enabled": true
            },
            {
                "action": "payment_methods",
                "label": "Gérer les moyens de paiement",
                "url": "/api/boatman/payment-methods/",
                "enabled": true
            }
        ]
    },
    "message": "Portefeuille récupéré avec succès",
    "timestamp": "2024-06-15T17:20:00Z"
}
```

### **POST /api/boatman/wallet/withdraw/**
Effectuer un retrait de fonds vers le compte bancaire.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Données à envoyer :**
```json
{
    "amount": 200.00
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "withdrawal_id": "wd_789126",
        "amount": 200.00,
        "new_balance": 147.50,
        "processing_time": "1-3 jours ouvrés",
        "reference": "WD_20240615172500",
        "payment_method": {
            "type": "Compte bancaire",
            "last_four": "1234"
        }
    },
    "message": "Retrait effectué, fonds en cours de transfert",
    "timestamp": "2024-06-15T17:25:00Z"
}
```

**Réponse erreur - Solde insuffisant (400) :**
```json
{
    "status": "error",
    "error": "Solde insuffisant",
    "error_code": 400,
    "data": {
        "available_balance": 147.50,
        "requested_amount": 500.00
    },
    "message": "Le montant demandé dépasse votre solde disponible",
    "timestamp": "2024-06-15T17:25:00Z"
}
```

**Réponse erreur - Aucune méthode de paiement (400) :**
```json
{
    "status": "error",
    "error": "Aucune méthode de paiement configurée",
    "error_code": 400,
    "message": "Veuillez ajouter une méthode de paiement avant de pouvoir effectuer un retrait",
    "timestamp": "2024-06-15T17:25:00Z"
}
```

### **GET /api/boatman/payment-methods/**
Récupérer les méthodes de paiement configurées.

**Headers requis :**
```
Authorization: Token abc123def456
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "payment_methods": [
            {
                "id": 42,
                "type": "CARD",
                "cardholder": "Captain Jack",
                "card_last_four": "1234",
                "expiry_month": "12",
                "expiry_year": "2026",
                "is_active": true,
                "is_default": true,
                "created_at": "2024-01-15T10:00:00Z"
            }
        ],
        "total_methods": 1,
        "has_default": true
    },
    "message": "Méthodes de paiement récupérées",
    "timestamp": "2024-06-15T17:30:00Z"
}
```

### **POST /api/boatman/payment-methods/**
Ajouter ou mettre à jour une méthode de paiement.

**Headers requis :**
```
Authorization: Token abc123def456
Content-Type: application/json
```

**Données à envoyer :**
```json
{
    "cardholder": "Captain Jack",
    "card_number": "4111 1111 1111 1234",
    "expiry_date": "12/26",
    "cvv": "123"
}
```

**Réponse succès (200) :**
```json
{
    "status": "success",
    "data": {
        "payment_method_id": 42,
        "cardholder": "Captain Jack",
        "card_last_four": "1234",
        "expiry_date": "12/26",
        "is_default": true
    },
    "message": "Méthode de paiement mise à jour avec succès",
    "timestamp": "2024-06-15T17:35:00Z"
}
```

---

## ⚠️ **6. CODES D'ERREUR ET GESTION D'ERREURS**

### **Codes d'erreur HTTP standardisés**

| Code | Signification | Description |
|------|---------------|-------------|
| **200** | Succès | Requête traitée avec succès |
| **400** | Requête invalide | Données manquantes ou invalides |
| **401** | Non authentifié | Token manquant ou invalide |
| **403** | Accès refusé | Utilisateur non autorisé pour cette action |
| **404** | Non trouvé | Ressource inexistante |
| **409** | Conflit | Conflit avec l'état actuel (ex: course déjà démarrée) |
| **422** | Entité non traitable | Données valides mais logiquement incorrectes |
| **500** | Erreur serveur | Erreur interne du serveur |

### **Codes d'erreur métier spécifiques**

| Code | Type | Description |
|------|------|-------------|
| **4001** | AUTH_ERROR | Compte batelier requis |
| **4002** | TRIP_ERROR | Course non assignée au batelier |
| **4003** | TRIP_STATE_ERROR | Action impossible dans l'état actuel |
| **4004** | WALLET_ERROR | Opération portefeuille impossible |
| **4005** | PAYMENT_ERROR | Problème méthode de paiement |
| **5001** | EMAIL_ERROR | Erreur envoi email |
| **5002** | EXTERNAL_API_ERROR | Erreur API externe |

### **Format d'erreur standardisé**
```json
{
    "status": "error",
    "error": "Message d'erreur principal",
    "error_code": 4001,
    "message": "Message détaillé pour l'utilisateur",
    "details": {
        "field": "valeur",
        "additional_info": "..."
    },
    "timestamp": "2024-06-15T17:40:00Z"
}
```

---

## 🔗 **7. INTÉGRATION AVEC LES AUTRES SYSTÈMES**

### **Flux de données - Navettes gratuites**

1. **Client** → Demande de navette gratuite via l'app
2. **Système Trips** → Crée une `ShuttleTripRequest` pour l'établissement
3. **Établissement** → Reçoit la demande via `/api/establishments/shuttle-requests/`
4. **Établissement** → Accepte et assigne un batelier via `/api/establishments/shuttle-requests/{id}/accept/`
5. **Système** → Crée un `Trip` avec `trip_type='NAVETTES_GRATUITES'` et `amount=0.00`
6. **Batelier** → Voit la course assignée via `/api/boatman/shuttles/`
7. **Batelier** → Gère la course (start/end) via l'espace batelier

### **Relations de données critiques**

```
Client → ShuttleTripRequest → Establishment → Captain → Trip
```

- **ShuttleTripRequest.establishment** : Établissement destinataire
- **Trip.captain** : Batelier assigné par l'établissement
- **Trip.trip_type** : Toujours `'NAVETTES_GRATUITES'` pour les navettes
- **Trip.total_price** : Toujours `0.00` pour les navettes gratuites
- **Trip.payment_status** : Toujours `'PAID'` (gratuit)

### **Notifications automatiques**

Le système envoie automatiquement des notifications :

- **Au batelier** quand une course lui est assignée
- **Au client** quand la course démarre/se termine
- **À l'établissement** pour les mises à jour importantes

### **Sécurité et permissions**

- **Bateliers** ne voient que leurs courses assignées
- **Établissements** ne voient que leurs propres navettes
- **Validation QR** obligatoire pour démarrer une course
- **Tokens d'authentification** avec expiration

---

## 📱 **8. EXEMPLES D'UTILISATION COMPLÈTE**

### **Scénario : Course de navette complète**

**1. Batelier consulte ses courses :**
```bash
GET /api/boatman/shuttles/?filter=À venir
Authorization: Token abc123def456
```

**2. Batelier voit les détails d'une course :**
```bash
GET /api/boatman/shuttle/trip_789123/
Authorization: Token abc123def456
```

**3. Client arrive, batelier valide le QR :**
```bash
POST /api/boatman/shuttle/trip_789123/validate-qr/
Authorization: Token abc123def456
Content-Type: application/json

{
    "qr_code": "COMMODORE_TRIP_789123_456"
}
```

**4. Batelier démarre la course :**
```bash
POST /api/boatman/shuttle/trip_789123/start/
Authorization: Token abc123def456
```

**5. Suivi en cours de route :**
```bash
GET /api/boatman/shuttle/trip_789123/track/
Authorization: Token abc123def456
```

**6. Arrivée, batelier termine la course :**
```bash
POST /api/boatman/shuttle/trip_789123/end/
Authorization: Token abc123def456
```

### **Scénario : Gestion du profil**

**1. Consultation du profil :**
```bash
GET /api/boatman/profile/
Authorization: Token abc123def456
```

**2. Mise à jour des informations :**
```bash
PATCH /api/boatman/profile/
Authorization: Token abc123def456
Content-Type: application/json

{
    "phone_number": "+***********",
    "experience": "10 ans d'expérience"
}
```

### **Scénario : Gestion financière**

**1. Consultation du portefeuille :**
```bash
GET /api/boatman/wallet/
Authorization: Token abc123def456
```

**2. Retrait de fonds :**
```bash
POST /api/boatman/wallet/withdraw/
Authorization: Token abc123def456
Content-Type: application/json

{
    "amount": 200.00
}
```

---

## ✅ **9. STATUT DE L'IMPLÉMENTATION**

### **Fonctionnalités complètes :**
- ✅ **Authentification complète** (login, forgot password, change password)
- ✅ **Tableau de bord** avec statistiques et courses à venir
- ✅ **Gestion des courses** (liste, détails, start, end, track, QR validation)
- ✅ **Gestion du profil** (personnel et bateau)
- ✅ **Portefeuille** (consultation, retrait, méthodes de paiement)
- ✅ **Intégration établissements** (courses assignées par les établissements)
- ✅ **Notifications automatiques** (via signaux Django)
- ✅ **Permissions et sécurité** (accès restreint aux ressources propres)

### **Intégration système :**
- ✅ **Trips Application** : Courses assignées visibles dans l'espace batelier
- ✅ **Establishments Application** : Assignation de bateliers fonctionnelle
- ✅ **Payments Application** : Portefeuille et transactions intégrés
- ✅ **Notifications Application** : Notifications automatiques actives

### **Tests et qualité :**
- ✅ **Tests unitaires** complets pour toutes les fonctionnalités
- ✅ **Validation des données** avec messages d'erreur clairs
- ✅ **Gestion d'erreurs** standardisée
- ✅ **Documentation API** complète avec exemples JSON

**L'espace batelier est entièrement fonctionnel et prêt pour la production !** 🎉
```
