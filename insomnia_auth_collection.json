{"_type": "export", "__export_format": 4, "__export_date": "2025-05-28T17:11:44.000Z", "__export_source": "cascade-ai", "resources": [{"_id": "wrk_auth_commodore", "parentId": null, "modified": *************, "created": *************, "name": "Commodore Auth API", "description": "Tests automatisés A-Z <PERSON> (register, verify, login, patch, reset)", "_type": "workspace"}, {"_id": "env_auth_commodore", "parentId": "wrk_auth_commodore", "modified": *************, "created": *************, "name": "Base Environment", "data": {"base_url": "http://127.0.0.1:8000/api", "client_email": "<EMAIL>", "client_password": "x8DB1HuquYAx", "captain_email": "<EMAIL>", "captain_password": "2TxovKReQ8SB", "establishment_email": "<EMAIL>", "establishment_password": "i2PCpELJ5AXf"}, "_type": "environment"}, {"_id": "fld_auth_flow", "parentId": "wrk_auth_commodore", "modified": *************, "created": *************, "name": "Flow Authentification", "_type": "request_group"}, {"_id": "req_register_client", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "1. Register CLIENT", "method": "POST", "url": "{{ base_url }}/register/", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"Client Test\",\n  \"email\": \"{{ client_email }}\",\n  \"password\": \"{{ client_password }}\",\n  \"user_type\": \"CLIENT\"\n}"}, "_type": "request"}, {"_id": "req_register_captain", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "1. Register CAPTAIN", "method": "POST", "url": "{{ base_url }}/register/", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"Captain Test\",\n  \"email\": \"{{ captain_email }}\",\n  \"password\": \"{{ captain_password }}\",\n  \"user_type\": \"CAPTAIN\"\n}"}, "_type": "request"}, {"_id": "req_register_establishment", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "1. Register ESTABLISHMENT", "method": "POST", "url": "{{ base_url }}/register/", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"Etab Test\",\n  \"email\": \"{{ establishment_email }}\",\n  \"password\": \"{{ establishment_password }}\",\n  \"user_type\": \"ESTABLISHMENT\"\n}"}, "_type": "request"}, {"_id": "req_verify_email", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "2. <PERSON><PERSON><PERSON> (à compléter)", "method": "POST", "url": "{{ base_url }}/verify-email/", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"<A REMPLACER>\",\n  \"code\": \"<A REMPLACER>\"\n}"}, "_type": "request"}, {"_id": "req_login_client", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "3. <PERSON><PERSON>", "method": "POST", "url": "{{ base_url }}/login/", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"{{ client_email }}\",\n  \"password\": \"{{ client_password }}\"\n}"}, "_type": "request"}, {"_id": "req_login_captain", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "3. Login CAPTAIN", "method": "POST", "url": "{{ base_url }}/login/", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"{{ captain_email }}\",\n  \"password\": \"{{ captain_password }}\"\n}"}, "_type": "request"}, {"_id": "req_login_establishment", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "3. <PERSON><PERSON> ESTABLISHMENT", "method": "POST", "url": "{{ base_url }}/login/", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"{{ establishment_email }}\",\n  \"password\": \"{{ establishment_password }}\"\n}"}, "_type": "request"}, {"_id": "req_patch_profile", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "4. PATCH Profile (à adapter selon le token)", "method": "PATCH", "url": "{{ base_url }}/accounts/profile/", "headers": [{"name": "Authorization", "value": "Bearer <A REMPLACER PAR LE TOKEN OBTENU EN LOGIN>"}], "body": {"mimeType": "application/json", "text": "{\n  \"client_profile\": {\n    \"date_of_birth\": \"1992-04-15\"\n  }\n}"}, "_type": "request"}, {"_id": "req_password_reset_request", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "5. Password Reset Request", "method": "POST", "url": "{{ base_url }}/password/reset/", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"<A REMPLACER>\"\n}"}, "_type": "request"}, {"_id": "req_password_reset_verify", "parentId": "fld_auth_flow", "modified": *************, "created": *************, "name": "6. Password Reset Verify (à compléter)", "method": "POST", "url": "{{ base_url }}/password/reset/verify/", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"<A REMPLACER>\",\n  \"code\": \"<A REMPLACER>\",\n  \"new_password\": \"<A REMPLACER>\"\n}"}, "_type": "request"}]}