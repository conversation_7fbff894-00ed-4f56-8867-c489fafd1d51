"""
Test complet du système de paiements et de mise à jour des statuts.
Ce script teste tous les workflows de paiement et vérifie que les statuts se mettent à jour correctement.
"""

import os
import sys
import django
import time
from decimal import Decimal
from datetime import datetime, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from trips.models import Trip, Shuttle, TripRequest
from payments.models import Payment, Transaction, Wallet
from payments.wallet_security_service import WalletSecurityService
from accounts.models import Client, Captain, Establishment
from boats.models import Boat

User = get_user_model()


class PaymentSystemTester:
    """Testeur complet du système de paiements"""

    def __init__(self):
        self.test_results = []
        self.errors = []
        self.test_users = {}
        self.test_trips = {}
        self.test_payments = {}

    def setup_test_environment(self):
        """Créer l'environnement de test complet"""
        print("🔧 CONFIGURATION DE L'ENVIRONNEMENT DE TEST")
        print("-" * 50)

        # 1. Créer un client de test
        try:
            client_user = User.objects.get(email='<EMAIL>')
            client_user.delete()
        except User.DoesNotExist:
            pass

        client_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestClient123!',
            first_name='Client',
            last_name='PaymentTest',
            type='CLIENT'
        )

        client = Client.objects.create(user=client_user)
        self.test_users['client'] = client_user
        self.test_users['client_profile'] = client

        # 2. Créer un capitaine de test
        try:
            captain_user = User.objects.get(email='<EMAIL>')
            captain_user.delete()
        except User.DoesNotExist:
            pass

        captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestCaptain123!',
            first_name='Captain',
            last_name='PaymentTest',
            type='CAPTAIN'
        )

        captain = Captain.objects.create(user=captain_user)
        self.test_users['captain'] = captain_user
        self.test_users['captain_profile'] = captain

        # 3. Créer un établissement de test
        try:
            establishment_user = User.objects.get(email='<EMAIL>')
            establishment_user.delete()
        except User.DoesNotExist:
            pass

        establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='TestEstablishment123!',
            first_name='Establishment',
            last_name='PaymentTest',
            type='ESTABLISHMENT'
        )

        establishment = Establishment.objects.create(
            user=establishment_user,
            name="Test Payment Establishment",
            type="RESTAURANT",
            address="123 Test Street"
        )
        self.test_users['establishment'] = establishment_user
        self.test_users['establishment_profile'] = establishment

        # 4. Créer un bateau de test (supprimer d'abord s'il existe)
        Boat.objects.filter(captain=captain).delete()

        boat = Boat.objects.create(
            name="Test Payment Boat",
            registration_number="TEST_PAY_001",
            boat_type="CLASSIC",
            capacity=8,
            captain=captain,
            fuel_type="GASOLINE",
            fuel_consumption=Decimal('10.5'),
            zone_served="Test Zone",
            radius=50
        )
        self.test_users['boat'] = boat

        # 5. Ajouter des fonds aux portefeuilles
        client_wallet = Wallet.objects.get(user=client_user)
        captain_wallet = Wallet.objects.get(user=captain_user)

        # Créditer le portefeuille client
        WalletSecurityService.credit_wallet_secure(
            wallet_id=client_wallet.id,
            amount=Decimal('500.00'),
            description="Fonds de test pour paiements",
            reference="TEST_PAYMENT_SETUP",
            user=client_user
        )

        print(f"✅ Client créé: {client_user.email}")
        print(f"✅ Capitaine créé: {captain_user.email}")
        print(f"✅ Établissement créé: {establishment_user.email}")
        print(f"✅ Bateau créé: {boat.name}")
        print(f"✅ Portefeuille client: {client_wallet.balance}€")
        print(f"✅ Portefeuille capitaine: {captain_wallet.balance}€")

    def test_trip_payment_workflow(self):
        """Test complet du workflow de paiement de course"""
        print("\n🚗 TEST: Workflow de paiement de course")
        print("-" * 50)

        client = self.test_users['client_profile']
        captain = self.test_users['captain_profile']
        boat = self.test_users['boat']

        # 1. Créer une course directement
        departure_time = timezone.now() + timedelta(hours=2)

        trip = Trip.objects.create(
            client=client,
            captain=captain,
            boat=boat,
            start_location='Port de Test',
            end_location='Destination Test',
            scheduled_start_time=departure_time,
            scheduled_end_time=departure_time + timedelta(hours=1),
            passenger_count=4,
            base_price=Decimal('25.50'),
            total_price=Decimal('25.50'),
            trip_type='COURSE_SIMPLE',
            status=Trip.Status.ACCEPTED,
            payment_status='PENDING'
        )

        self.test_trips['main_trip'] = trip
        print(f"✅ Course créée: {trip.id} - Statut: {trip.status} - Paiement: {trip.payment_status}")

        # 4. Effectuer le paiement
        try:
            from payments.services import PaymentService

            payment_result = PaymentService.process_payment(
                user=self.test_users['client'],
                amount=trip.total_price,
                payment_method_id="wallet",
                description=f"Paiement course #{trip.id}",
                metadata={
                    'trip_id': trip.id,
                    'payment_type': 'TRIP'
                },
                payment_type='TRIP'
            )

            # Vérifier que le paiement a réussi
            if payment_result.get('status') == 'succeeded':
                print(f"✅ Paiement réussi: {payment_result.get('id')}")

                # Mettre à jour le statut de la course
                trip.payment_status = 'PAID'
                trip.save()

                # Vérifier les statuts
                trip.refresh_from_db()
                client_wallet = Wallet.objects.get(user=self.test_users['client'])

                print(f"✅ Statut course après paiement: {trip.status}")
                print(f"✅ Statut paiement course: {trip.payment_status}")
                print(f"✅ Nouveau solde client: {client_wallet.balance}€")

                # Vérifier qu'une transaction a été créée
                transaction = Transaction.objects.filter(
                    wallet=client_wallet,
                    type=Transaction.TransactionType.DEBIT
                ).order_by('-created_at').first()

                if transaction and transaction.amount == trip.total_price:
                    print(f"✅ Transaction créée: {transaction.id} - {transaction.amount}€")
                    self.test_results.append("Trip payment workflow: PASSED")
                else:
                    print(f"❌ Transaction manquante ou incorrecte")
                    self.test_results.append("Trip payment workflow: FAILED")
                    self.errors.append("Transaction de paiement manquante")
            else:
                print(f"❌ Paiement échoué: {payment_result}")
                self.test_results.append("Trip payment workflow: FAILED")
                self.errors.append("Paiement de course échoué")

        except Exception as e:
            print(f"❌ Erreur lors du paiement: {str(e)}")
            self.test_results.append("Trip payment workflow: FAILED")
            self.errors.append(f"Erreur paiement course: {str(e)}")

    def test_trip_status_transitions(self):
        """Test des transitions de statuts de course"""
        print("\n🔄 TEST: Transitions de statuts de course")
        print("-" * 50)

        trip = self.test_trips.get('main_trip')
        if not trip:
            print("❌ Aucune course de test disponible")
            self.test_results.append("Trip status transitions: FAILED")
            self.errors.append("Aucune course de test")
            return

        # 1. Démarrer la course
        initial_status = trip.status
        print(f"Statut initial: {initial_status}")

        if trip.start_trip(user=self.test_users['captain']):
            trip.refresh_from_db()
            print(f"✅ Course démarrée: {trip.status}")

            if trip.status == Trip.Status.IN_PROGRESS:
                self.test_results.append("Trip start transition: PASSED")
            else:
                self.test_results.append("Trip start transition: FAILED")
                self.errors.append(f"Statut incorrect après démarrage: {trip.status}")
        else:
            print("❌ Impossible de démarrer la course")
            self.test_results.append("Trip start transition: FAILED")
            self.errors.append("Impossible de démarrer la course")

        # 2. Terminer la course
        time.sleep(1)  # Attendre un peu

        if trip.complete_trip(user=self.test_users['captain']):
            trip.refresh_from_db()
            print(f"✅ Course terminée: {trip.status}")

            if trip.status == Trip.Status.COMPLETED:
                self.test_results.append("Trip completion transition: PASSED")

                # Vérifier que le capitaine a été crédité
                captain_wallet = Wallet.objects.get(user=self.test_users['captain'])
                captain_transactions = Transaction.objects.filter(
                    wallet=captain_wallet,
                    type=Transaction.TransactionType.CREDIT
                ).order_by('-created_at')

                if captain_transactions.exists():
                    print(f"✅ Capitaine crédité: {captain_transactions.first().amount}€")
                    self.test_results.append("Captain credit on completion: PASSED")
                else:
                    print("⚠️  Capitaine non crédité automatiquement")
                    self.test_results.append("Captain credit on completion: FAILED")
                    self.errors.append("Capitaine non crédité après complétion")
            else:
                self.test_results.append("Trip completion transition: FAILED")
                self.errors.append(f"Statut incorrect après complétion: {trip.status}")
        else:
            print("❌ Impossible de terminer la course")
            self.test_results.append("Trip completion transition: FAILED")
            self.errors.append("Impossible de terminer la course")

    def test_tip_payment_workflow(self):
        """Test du workflow de paiement de pourboire"""
        print("\n💰 TEST: Workflow de paiement de pourboire")
        print("-" * 50)

        trip = self.test_trips.get('main_trip')
        if not trip or trip.status != Trip.Status.COMPLETED:
            print("❌ Aucune course terminée disponible pour le pourboire")
            self.test_results.append("Tip payment workflow: FAILED")
            self.errors.append("Aucune course terminée pour pourboire")
            return

        try:
            # Paiement de pourboire
            tip_amount = Decimal('5.00')
            client_wallet = Wallet.objects.get(user=self.test_users['client'])
            captain_wallet = Wallet.objects.get(user=self.test_users['captain'])

            client_balance_before = client_wallet.balance
            captain_balance_before = captain_wallet.balance

            # Débiter le client
            debit_result = WalletSecurityService.debit_wallet_secure(
                wallet_id=client_wallet.id,
                amount=tip_amount,
                description=f"Pourboire pour course #{trip.id}",
                reference=f"TIP_TRIP_{trip.id}",
                user=self.test_users['client']
            )

            # Créditer le capitaine
            credit_result = WalletSecurityService.credit_wallet_secure(
                wallet_id=captain_wallet.id,
                amount=tip_amount,
                description=f"Pourboire reçu pour course #{trip.id}",
                reference=f"TIP_RECEIVED_{trip.id}",
                user=self.test_users['captain']
            )

            # Créer le paiement de pourboire
            tip_payment = Payment.objects.create(
                user=self.test_users['client'],
                trip=trip,
                amount=tip_amount,
                type=Payment.PaymentType.TIP,
                payment_method=Payment.PaymentMethod.WALLET,
                status=Payment.Status.COMPLETED,
                description=f"Pourboire pour course #{trip.id}"
            )

            # Mettre à jour la course
            trip.tip = tip_amount
            trip.save()

            # Vérifier les soldes
            client_wallet.refresh_from_db()
            captain_wallet.refresh_from_db()

            print(f"✅ Pourboire payé: {tip_amount}€")
            print(f"✅ Client: {client_balance_before}€ → {client_wallet.balance}€")
            print(f"✅ Capitaine: {captain_balance_before}€ → {captain_wallet.balance}€")
            print(f"✅ Paiement créé: {tip_payment.id}")

            if (client_wallet.balance == client_balance_before - tip_amount and
                captain_wallet.balance == captain_balance_before + tip_amount):
                self.test_results.append("Tip payment workflow: PASSED")
            else:
                self.test_results.append("Tip payment workflow: FAILED")
                self.errors.append("Soldes incorrects après pourboire")

        except Exception as e:
            print(f"❌ Erreur lors du paiement de pourboire: {str(e)}")
            self.test_results.append("Tip payment workflow: FAILED")
            self.errors.append(f"Erreur pourboire: {str(e)}")

    def test_carbon_offset_workflow(self):
        """Test du workflow de compensation carbone"""
        print("\n🌱 TEST: Workflow de compensation carbone")
        print("-" * 50)

        trip = self.test_trips.get('main_trip')
        if not trip or trip.status != Trip.Status.COMPLETED:
            print("❌ Aucune course terminée disponible pour compensation carbone")
            self.test_results.append("Carbon offset workflow: FAILED")
            self.errors.append("Aucune course terminée pour compensation")
            return

        try:
            # Calculer l'empreinte carbone (exemple)
            boat = trip.boat
            if boat and boat.fuel_consumption:
                # Estimation: 30 minutes de trajet
                duration_hours = Decimal('0.5')
                fuel_consumed = boat.fuel_consumption * duration_hours

                # Facteur d'émission (kg CO2/L)
                emission_factor = Decimal('2.32') if boat.fuel_type == 'GASOLINE' else Decimal('2.68')
                carbon_footprint = fuel_consumed * emission_factor

                # Coût de compensation (80€/tonne)
                compensation_rate = Decimal('80.00')  # €/tonne
                carbon_cost = (carbon_footprint / 1000) * compensation_rate  # Convertir kg en tonnes
                carbon_cost = carbon_cost.quantize(Decimal('0.01'))

                print(f"✅ Carburant consommé: {fuel_consumed}L")
                print(f"✅ Empreinte carbone: {carbon_footprint}kg CO2")
                print(f"✅ Coût compensation: {carbon_cost}€")

                if carbon_cost > 0:
                    client_wallet = Wallet.objects.get(user=self.test_users['client'])
                    balance_before = client_wallet.balance

                    # Paiement de compensation carbone
                    debit_result = WalletSecurityService.debit_wallet_secure(
                        wallet_id=client_wallet.id,
                        amount=carbon_cost,
                        description=f"Compensation carbone pour course #{trip.id}",
                        reference=f"CARBON_TRIP_{trip.id}",
                        user=self.test_users['client']
                    )

                    # Créer le paiement de compensation
                    carbon_payment = Payment.objects.create(
                        user=self.test_users['client'],
                        trip=trip,
                        amount=carbon_cost,
                        type=Payment.PaymentType.CARBON_OFFSET,
                        payment_method=Payment.PaymentMethod.WALLET,
                        status=Payment.Status.COMPLETED,
                        description=f"Compensation carbone pour course #{trip.id}",
                        metadata={
                            'carbon_footprint_kg': str(carbon_footprint),
                            'fuel_consumed_l': str(fuel_consumed),
                            'emission_factor': str(emission_factor)
                        }
                    )

                    client_wallet.refresh_from_db()

                    print(f"✅ Compensation payée: {carbon_cost}€")
                    print(f"✅ Solde client: {balance_before}€ → {client_wallet.balance}€")
                    print(f"✅ Paiement créé: {carbon_payment.id}")

                    if client_wallet.balance == balance_before - carbon_cost:
                        self.test_results.append("Carbon offset workflow: PASSED")
                    else:
                        self.test_results.append("Carbon offset workflow: FAILED")
                        self.errors.append("Solde incorrect après compensation carbone")
                else:
                    print("⚠️  Coût de compensation trop faible")
                    self.test_results.append("Carbon offset workflow: PASSED")
            else:
                print("⚠️  Données de carburant manquantes")
                self.test_results.append("Carbon offset workflow: PASSED")

        except Exception as e:
            print(f"❌ Erreur lors de la compensation carbone: {str(e)}")
            self.test_results.append("Carbon offset workflow: FAILED")
            self.errors.append(f"Erreur compensation carbone: {str(e)}")

    def test_refund_workflow(self):
        """Test du workflow de remboursement"""
        print("\n💰 TEST: Workflow de remboursement")
        print("-" * 50)

        client = self.test_users['client_profile']
        captain = self.test_users['captain_profile']
        boat = self.test_users['boat']

        # 1. Créer une nouvelle course pour le test de remboursement
        departure_time = timezone.now() + timedelta(hours=3)

        refund_trip = Trip.objects.create(
            client=client,
            captain=captain,
            boat=boat,
            start_location='Port Refund Test',
            end_location='Destination Refund Test',
            scheduled_start_time=departure_time,
            scheduled_end_time=departure_time + timedelta(hours=1),
            passenger_count=2,
            base_price=Decimal('15.00'),
            total_price=Decimal('15.00'),
            trip_type='COURSE_SIMPLE',
            status=Trip.Status.ACCEPTED,
            payment_status='PENDING'
        )

        # 2. Payer la course
        try:
            from payments.services import PaymentService

            payment_result = PaymentService.process_payment(
                user=self.test_users['client'],
                amount=refund_trip.total_price,
                payment_method_id="wallet",
                description=f"Paiement course remboursement #{refund_trip.id}",
                metadata={
                    'trip_id': refund_trip.id,
                    'payment_type': 'TRIP'
                },
                payment_type='TRIP'
            )

            if payment_result.get('status') == 'succeeded':
                refund_trip.payment_status = 'PAID'
                refund_trip.save()
                print(f"✅ Course payée pour test remboursement: {refund_trip.id}")

                # 3. Annuler la course
                if refund_trip.cancel_trip(user=self.test_users['client'], reason="Test de remboursement"):
                    refund_trip.refresh_from_db()
                    print(f"✅ Course annulée: {refund_trip.status}")

                    # 4. Effectuer le remboursement
                    client_wallet = Wallet.objects.get(user=self.test_users['client'])
                    balance_before_refund = client_wallet.balance

                    try:
                        # Utiliser le service sécurisé pour créditer le remboursement
                        refund_result = WalletSecurityService.credit_wallet_secure(
                            wallet_id=client_wallet.id,
                            amount=refund_trip.total_price,
                            description=f"Remboursement course #{refund_trip.id}",
                            reference=f"REFUND_TRIP_{refund_trip.id}",
                            user=self.test_users['client']
                        )

                        # Mettre à jour le statut de paiement
                        refund_trip.payment_status = 'REFUNDED'
                        refund_trip.save()

                        client_wallet.refresh_from_db()
                        balance_after_refund = client_wallet.balance

                        print(f"✅ Remboursement effectué: {refund_trip.total_price}€")
                        print(f"✅ Solde avant: {balance_before_refund}€, après: {balance_after_refund}€")
                        print(f"✅ Statut paiement: {refund_trip.payment_status}")

                        if balance_after_refund == balance_before_refund + refund_trip.total_price:
                            self.test_results.append("Refund workflow: PASSED")
                        else:
                            self.test_results.append("Refund workflow: FAILED")
                            self.errors.append("Solde incorrect après remboursement")

                    except Exception as e:
                        print(f"❌ Erreur lors du remboursement: {str(e)}")
                        self.test_results.append("Refund workflow: FAILED")
                        self.errors.append(f"Erreur remboursement: {str(e)}")
                else:
                    print("❌ Impossible d'annuler la course")
                    self.test_results.append("Refund workflow: FAILED")
                    self.errors.append("Impossible d'annuler la course")
            else:
                print("❌ Paiement initial échoué pour test remboursement")
                self.test_results.append("Refund workflow: FAILED")
                self.errors.append("Paiement initial échoué")

        except Exception as e:
            print(f"❌ Erreur lors du test de remboursement: {str(e)}")
            self.test_results.append("Refund workflow: FAILED")
            self.errors.append(f"Erreur test remboursement: {str(e)}")

    def run_all_tests(self):
        """Exécuter tous les tests du système de paiement"""
        print("🚀 DÉMARRAGE DES TESTS COMPLETS DU SYSTÈME DE PAIEMENT")
        print("=" * 70)

        start_time = time.time()

        # Configuration
        self.setup_test_environment()

        # Tests principaux
        self.test_trip_payment_workflow()
        self.test_trip_status_transitions()
        self.test_tip_payment_workflow()
        self.test_carbon_offset_workflow()
        self.test_refund_workflow()

        end_time = time.time()

        # Résumé
        print("\n" + "=" * 70)
        print("📊 RÉSUMÉ DES TESTS DU SYSTÈME DE PAIEMENT")
        print("=" * 70)

        passed_tests = [t for t in self.test_results if "PASSED" in t]
        failed_tests = [t for t in self.test_results if "FAILED" in t]

        print(f"✅ Tests réussis: {len(passed_tests)}")
        print(f"❌ Tests échoués: {len(failed_tests)}")
        print(f"⏱️  Temps d'exécution: {end_time - start_time:.2f} secondes")

        if failed_tests:
            print("\n❌ TESTS ÉCHOUÉS:")
            for test in failed_tests:
                print(f"  - {test}")

        if self.errors:
            print("\n🚨 ERREURS DÉTECTÉES:")
            for error in self.errors:
                print(f"  - {error}")

        if not failed_tests and not self.errors:
            print("\n🎉 TOUS LES TESTS DU SYSTÈME DE PAIEMENT ONT RÉUSSI!")
            print("✅ Le système de paiement et de statuts fonctionne correctement")
        else:
            print("\n⚠️  ATTENTION: Des problèmes ont été détectés")
            print("❌ Vérifier les erreurs avant déploiement")

        return len(failed_tests) == 0 and len(self.errors) == 0


if __name__ == "__main__":
    tester = PaymentSystemTester()
    success = tester.run_all_tests()

    # Code de sortie pour les scripts automatisés
    sys.exit(0 if success else 1)
