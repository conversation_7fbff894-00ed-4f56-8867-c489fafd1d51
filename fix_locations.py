import os
import django
import json

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "commodore.settings")
django.setup()

from django.db import connection

def is_valid_json(data):
    """Check if the provided data is a valid JSON string."""
    if not isinstance(data, str) or not data.strip():
        # If it's not a string (e.g., already a dict/list) or is empty, we don't need to convert it.
        return True
    try:
        json.loads(data)
        return True
    except json.JSONDecodeError:
        return False

def fix_table_locations(table_name):
    """Updates location fields in a given table to be valid JSON strings."""
    print(f"-- Processing table: {table_name} --")
    updated_count = 0
    with connection.cursor() as cursor:
        try:
            cursor.execute(f"SELECT id, start_location, end_location FROM {table_name}")
            rows = cursor.fetchall()
        except Exception as e:
            print(f"Could not read from table {table_name}. It might not exist or have these columns. Error: {e}")
            return

        for row_id, start_loc, end_loc in rows:
            new_start_loc, new_end_loc = start_loc, end_loc
            needs_update = False

            if not is_valid_json(start_loc):
                new_start_loc = json.dumps({'name': start_loc}, ensure_ascii=False)
                needs_update = True
            
            if not is_valid_json(end_loc):
                new_end_loc = json.dumps({'name': end_loc}, ensure_ascii=False)
                needs_update = True

            if needs_update:
                cursor.execute(
                    f"UPDATE {table_name} SET start_location = %s, end_location = %s WHERE id = %s",
                    [new_start_loc, new_end_loc, row_id]
                )
                updated_count += 1
    
    print(f"Updated {updated_count} rows in {table_name}.")

if __name__ == "__main__":
    print("Starting database location data cleanup...")
    fix_table_locations("trips_trip")
    fix_table_locations("trips_shuttle")
    print("\nData cleanup finished.")
    print("You can now try running the migration again:")
    print("python manage.py migrate trips")
