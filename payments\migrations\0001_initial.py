# Generated by Django 4.2.8 on 2025-05-30 23:54

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("trips", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Payment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="Montant",
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        default="EUR", max_length=3, verbose_name="Devise"
                    ),
                ),
                (
                    "type",
                    models.Char<PERSON><PERSON>(
                        choices=[
                            ("TRIP", "Course"),
                            ("SHUTTLE", "Navette"),
                            ("TIP", "Pourboire"),
                            ("CARBON_OFFSET", "Compensation carbone"),
                            ("WALLET_RECHARGE", "Recharge portefeuille"),
                            ("SUBSCRIPTION", "Abonnement"),
                            ("ESTABLISHMENT", "Établissement"),
                            ("MAINTENANCE", "Maintenance"),
                            ("PROMOTION", "Promotion"),
                            ("SHARED_TRIP", "Course partagée"),
                        ],
                        max_length=20,
                        verbose_name="Type",
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("CARD", "Carte bancaire"),
                            ("WALLET", "Portefeuille"),
                            ("APPLE_PAY", "Apple Pay"),
                            ("GOOGLE_PAY", "Google Pay"),
                            ("SEPA", "Prélèvement SEPA"),
                            ("TRANSFER", "Virement bancaire"),
                        ],
                        max_length=20,
                        verbose_name="Méthode de paiement",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "En attente"),
                            ("PROCESSING", "En cours"),
                            ("COMPLETED", "Complété"),
                            ("FAILED", "Échoué"),
                            ("REFUNDED", "Remboursé"),
                            ("PARTIALLY_REFUNDED", "Partiellement remboursé"),
                            ("CANCELED", "Annulé"),
                        ],
                        default="PENDING",
                        max_length=20,
                        verbose_name="Statut",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "stripe_payment_id",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="ID Stripe"
                    ),
                ),
                (
                    "stripe_payment_intent_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="ID intention de paiement",
                    ),
                ),
                (
                    "stripe_customer_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="ID client Stripe",
                    ),
                ),
                (
                    "stripe_refund_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="ID remboursement Stripe",
                    ),
                ),
                (
                    "receipt_url",
                    models.URLField(
                        blank=True,
                        max_length=500,
                        null=True,
                        verbose_name="URL du reçu",
                    ),
                ),
                (
                    "refund_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="Montant remboursé",
                    ),
                ),
                (
                    "refund_reason",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Raison du remboursement",
                    ),
                ),
                (
                    "refunded_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Date de remboursement"
                    ),
                ),
                (
                    "loyalty_points_earned",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Points de fidélité gagnés"
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Métadonnées"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Créé le"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Mis à jour le"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Complété le"
                    ),
                ),
                (
                    "shuttle",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payments",
                        to="trips.shuttle",
                        verbose_name="Navette",
                    ),
                ),
                (
                    "trip",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payments",
                        to="trips.trip",
                        verbose_name="Course",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payments",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Utilisateur",
                    ),
                ),
            ],
            options={
                "verbose_name": "Paiement",
                "verbose_name_plural": "Paiements",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Wallet",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("CLIENT", "Client"),
                            ("ESTABLISHMENT", "Établissement"),
                            ("CAPTAIN", "Capitaine"),
                        ],
                        max_length=20,
                        verbose_name="Type",
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        default="EUR", max_length=3, verbose_name="Devise"
                    ),
                ),
                (
                    "balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="Solde",
                    ),
                ),
                (
                    "loyalty_points",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Points de fidélité"
                    ),
                ),
                (
                    "total_earned",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        verbose_name="Total gagné",
                    ),
                ),
                (
                    "total_spent",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        verbose_name="Total dépensé",
                    ),
                ),
                (
                    "stripe_customer_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="ID client Stripe",
                    ),
                ),
                (
                    "default_payment_method",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Méthode de paiement par défaut",
                    ),
                ),
                (
                    "daily_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="Limite quotidienne",
                    ),
                ),
                (
                    "transaction_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="Limite par transaction",
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Métadonnées"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Créé le"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Mis à jour le"),
                ),
                (
                    "last_transaction_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Dernière transaction le"
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="wallet",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Utilisateur",
                    ),
                ),
            ],
            options={
                "verbose_name": "Portefeuille",
                "verbose_name_plural": "Portefeuilles",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("CREDIT", "Crédit"),
                            ("DEBIT", "Débit"),
                            ("REFUND", "Remboursement"),
                            ("TRANSFER", "Transfert"),
                            ("ADJUSTMENT", "Ajustement"),
                        ],
                        max_length=20,
                        verbose_name="Type",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Montant"
                    ),
                ),
                (
                    "balance_after",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Solde après"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Métadonnées"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Créé le"
                    ),
                ),
                (
                    "payment",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="transactions",
                        to="payments.payment",
                        verbose_name="Paiement",
                    ),
                ),
                (
                    "related_transaction",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="linked_transactions",
                        to="payments.transaction",
                        verbose_name="Transaction liée",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="payments.wallet",
                        verbose_name="Portefeuille",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transaction",
                "verbose_name_plural": "Transactions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddField(
            model_name="payment",
            name="wallet",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="payments",
                to="payments.wallet",
                verbose_name="Portefeuille",
            ),
        ),
    ]
