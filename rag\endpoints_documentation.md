# Documentation des Endpoints de l'API RAG Commodore

## Introduction

Cette documentation détaille tous les endpoints disponibles dans l'API RAG (Retrieval Augmented Generation) de Commodore. Elle est destinée aux développeurs qui souhaitent intégrer le chatbot intelligent dans leurs applications.

Le système RAG a été récemment optimisé pour offrir :
- Des temps de réponse rapides (3-4 secondes)
- Des réponses de haute qualité, structurées et personnalisées
- Un support hors ligne pour les applications mobiles
- Une robustesse face aux erreurs et aux problèmes de connexion

## Base URL

Tous les endpoints sont accessibles à partir de la base URL suivante :

```
https://api.commodore.com/api/rag/
```

Pour les environnements de développement, utilisez :

```
http://localhost:8000/api/rag/
```

## Authentification

L'API utilise l'authentification par token JWT. Pour obtenir un token, utilisez l'endpoint d'authentification standard de Commodore :

```
POST /api/auth/token/
```

**Requête :**
```json
{
  "email": "<EMAIL>",
  "password": "motdepasse"
}
```

**Réponse :**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

Incluez le token d'accès dans l'en-tête Authorization de vos requêtes :
```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Endpoints

### 1. Sessions de chat

#### 1.1 Créer une session

Crée une nouvelle session de chat pour l'utilisateur.

**Endpoint :** `http://127.0.0.1:8000/api/rag/sessions/`

**Requête :**
```json
{
  "title": "Assistance réservation"
}
```

**Réponse :**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "title": "Assistance réservation",
  "created_at": "2025-05-14T12:34:56.789Z",
  "updated_at": "2025-05-14T12:34:56.789Z"
}
```

#### 1.2 Lister les sessions

Récupère la liste des sessions de chat de l'utilisateur.

**Endpoint :** `GET /sessions/`

**Réponse :**
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "title": "Assistance réservation",
    "created_at": "2025-05-14T12:34:56.789Z",
    "updated_at": "2025-05-14T12:34:56.789Z"
  },
  {
    "id": "661f9511-f30c-52e5-b827-557766551111",
    "title": "Support technique",
    "created_at": "2025-05-13T10:20:30.456Z",
    "updated_at": "2025-05-13T10:25:45.678Z"
  }
]
```

#### 1.3 Récupérer une session

Récupère les détails d'une session de chat spécifique.

**Endpoint :** `GET /sessions/{session_id}/`

**Réponse :**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "title": "Assistance réservation",
  "created_at": "2025-05-14T12:34:56.789Z",
  "updated_at": "2025-05-14T12:34:56.789Z"
}
```

#### 1.4 Supprimer une session

Supprime une session de chat.

**Endpoint :** `DELETE /sessions/{session_id}/`

**Réponse :** `204 No Content`

### 2. Messages de chat

#### 2.1 Lister les messages d'une session

Récupère tous les messages d'une session de chat.

**Endpoint :** `GET /sessions/{session_id}/messages/`

**Réponse :**
```json
[
  {
    "id": "7f9c24e8-85bb-4fcb-a4c2-6f7a5e1234ab",
    "role": "user",
    "content": "Comment réserver un bateau-taxi ?",
    "created_at": "2025-05-14T12:35:10.123Z"
  },
  {
    "id": "9a8b7c6d-5e4f-3a2b-1c0d-9e8f7a6b5c4d",
    "role": "assistant",
    "content": "Pour réserver un bateau-taxi sur Commodore, suivez ces étapes :\n\n1. Ouvrez l'application Commodore\n2. Sélectionnez votre point de départ et d'arrivée sur la carte\n3. Choisissez l'heure de départ souhaitée\n4. Indiquez le nombre de passagers\n5. Vérifiez le prix estimé\n6. Confirmez votre réservation\n\nVous recevrez une confirmation par email avec un QR code à présenter au capitaine.",
    "created_at": "2025-05-14T12:35:11.456Z"
  }
]
```

#### 2.2 Envoyer un message

Envoie un message au chatbot et reçoit une réponse.

**Endpoint :** `POST /chat/api/`

**Requête :**
```json
{
  "message": "Comment réserver un bateau-taxi ?",
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "profile": "Client"
}
```

**Réponse :**
```json
{
  "response": "Pour réserver un bateau-taxi avec Commodore Taxi Boat, suivez ces étapes :\n\n1. **Téléchargez l'application** Commodore Taxi Boat.\n2. **Créez votre compte** utilisateur.\n3. **Indiquez votre lieu de prise en charge et votre destination** dans l'application.\n\nCommodore Taxi Boat est une plateforme numérique qui vous permet de réserver facilement un taxi boat privé, piloté par un capitaine certifié, pour vos déplacements maritimes. Tous les trajets sont 100 % privatisés : aucun partage avec d'autres passagers.\n\nSi vous rencontrez des difficultés lors de la réservation, n'hésitez pas à contacter notre support client à <EMAIL> pour une assistance personnalisée.\n\nConformément au RGPD, vos données personnelles sont traitées de manière sécurisée et ne sont conservées que pour la durée nécessaire au service.",
  "sources": [
    {
      "title": "Guide de réservation",
      "section": "Réservation de bateau-taxi"
    },
    {
      "title": "FAQ Commodore",
      "section": "Questions fréquentes"
    }
  ],
  "response_time": 3.42,
  "profile": "Client"
}
```

### 3. Documents

#### 3.1 Lister les documents

Récupère la liste des documents disponibles dans la base de connaissances.

**Endpoint :** `GET /documents/`

**Réponse :**
```json
[
  {
    "id": "1a2b3c4d-5e6f-7g8h-9i0j-1k2l3m4n5o6p",
    "title": "Guide utilisateur Commodore",
    "category": "Documentation",
    "created_at": "2025-05-01T10:00:00.000Z",
    "updated_at": "2025-05-10T15:30:00.000Z",
    "embedding_generated": true
  }
]
```

#### 3.2 Ajouter un document

Ajoute un nouveau document à la base de connaissances.

**Endpoint :** `POST /documents/`

**Requête :**
```json
{
  "title": "FAQ Commodore",
  "content": "# FAQ Commodore\n\n## Comment réserver un bateau-taxi ?\nPour réserver un bateau-taxi...\n\n## Comment payer ma course ?\nVous pouvez payer votre course...",
  "category": "FAQ"
}
```

**Réponse :**
```json
{
  "id": "9i8h7g6f-5e4d-3c2b-1a0z-9y8x7w6v5u4t",
  "title": "FAQ Commodore",
  "category": "FAQ",
  "created_at": "2025-05-14T14:00:00.000Z",
  "updated_at": "2025-05-14T14:00:00.000Z",
  "embedding_generated": false
}
```

#### 3.3 Récupérer un document

Récupère les détails d'un document spécifique.

**Endpoint :** `GET /documents/{document_id}/`

**Réponse :**
```json
{
  "id": "9i8h7g6f-5e4d-3c2b-1a0z-9y8x7w6v5u4t",
  "title": "FAQ Commodore",
  "content": "# FAQ Commodore\n\n## Comment réserver un bateau-taxi ?\nPour réserver un bateau-taxi...\n\n## Comment payer ma course ?\nVous pouvez payer votre course...",
  "category": "FAQ",
  "created_at": "2025-05-14T14:00:00.000Z",
  "updated_at": "2025-05-14T14:00:00.000Z",
  "embedding_generated": false
}
```

#### 3.4 Mettre à jour un document

Met à jour un document existant dans la base de connaissances.

**Endpoint :** `PUT /documents/{document_id}/`

**Requête :**
```json
{
  "title": "FAQ Commodore - Mise à jour",
  "content": "# FAQ Commodore\n\n## Comment réserver un bateau-taxi ?\nPour réserver un bateau-taxi...\n\n## Comment payer ma course ?\nVous pouvez payer votre course...\n\n## Comment annuler ma réservation ?\nPour annuler une réservation...",
  "category": "FAQ"
}
```

**Réponse :**
```json
{
  "id": "9i8h7g6f-5e4d-3c2b-1a0z-9y8x7w6v5u4t",
  "title": "FAQ Commodore - Mise à jour",
  "category": "FAQ",
  "created_at": "2025-05-14T14:00:00.000Z",
  "updated_at": "2025-05-14T15:30:00.000Z",
  "embedding_generated": false
}
```

#### 3.5 Supprimer un document

Supprime un document de la base de connaissances.

**Endpoint :** `DELETE /documents/{document_id}/`

**Réponse :** `204 No Content`

### 4. Support hors ligne

#### 4.1 Récupérer les données hors ligne

Récupère un package de données pour le support hors ligne, incluant les FAQ et les documents importants.

**Endpoint :** `GET /offline/data/?profile=Client`

**Réponse :**
```json
{
  "faqs": {
    "client": [
      {
        "question": "Comment réserver un bateau-taxi ?",
        "response": "Pour réserver un bateau-taxi sur Commodore, ouvrez l'application, sélectionnez votre point de départ et d'arrivée, choisissez l'heure de départ, indiquez le nombre de passagers, vérifiez le prix et confirmez votre réservation. Vous recevrez une confirmation par email avec un QR code à présenter au capitaine.",
        "intent": "specific",
        "entities": ["réservation", "bateau", "taxi"],
        "profile": "Client"
      }
    ],
    "captain": [],
    "establishment": [],
    "general": []
  },
  "documents": [
    {
      "title": "Guide utilisateur Commodore",
      "content": "Extrait du guide utilisateur...",
      "category": "Documentation",
      "updated_at": "2025-05-10T15:30:00.000Z"
    }
  ],
  "version": "1.0",
  "generated_at": "2025-05-14T16:00:00.000Z",
  "expires_at": "2025-05-21T16:00:00.000Z"
}
```

#### 4.2 Récupérer les FAQ hors ligne

Récupère uniquement les FAQ pour le support hors ligne.

**Endpoint :** `GET /offline/faqs/?profile=Client&limit=5`

**Réponse :**
```json
{
  "faqs": [
    {
      "question": "Comment réserver un bateau-taxi ?",
      "response": "Pour réserver un bateau-taxi sur Commodore, ouvrez l'application, sélectionnez votre point de départ et d'arrivée, choisissez l'heure de départ, indiquez le nombre de passagers, vérifiez le prix et confirmez votre réservation. Vous recevrez une confirmation par email avec un QR code à présenter au capitaine.",
      "intent": "specific",
      "entities": ["réservation", "bateau", "taxi"],
      "profile": "Client"
    },
    {
      "question": "Comment payer ma course ?",
      "response": "Vous pouvez payer votre course sur Commodore par carte bancaire (Visa, Mastercard, Apple Pay) lors de la réservation ou utiliser des crédits prépayés. Pour recharger vos crédits, allez dans votre profil > \"Mon solde\".",
      "intent": "specific",
      "entities": ["paiement", "course", "crédit"],
      "profile": "Client"
    }
  ]
}
```

### 5. Feedback

#### 5.1 Soumettre un feedback

Soumet un feedback sur une réponse du chatbot.

**Endpoint :** `POST /feedback/`

**Requête :**
```json
{
  "message_id": "9a8b7c6d-5e4f-3a2b-1c0d-9e8f7a6b5c4d",
  "feedback_type": "positive",
  "comments": "Réponse très claire et utile !"
}
```

**Réponse :**
```json
{
  "id": "1q2w3e4r-5t6y-7u8i-9o0p-1a2s3d4f5g6h",
  "message_id": "9a8b7c6d-5e4f-3a2b-1c0d-9e8f7a6b5c4d",
  "feedback_type": "positive",
  "comments": "Réponse très claire et utile !",
  "created_at": "2025-05-14T17:00:00.000Z"
}
```

## Bonnes pratiques d'utilisation

### Gestion des sessions

1. **Créez une session par conversation** : Chaque conversation avec le chatbot doit avoir sa propre session pour maintenir le contexte.
2. **Réutilisez les sessions existantes** : Pour continuer une conversation, utilisez l'ID de session existant plutôt que d'en créer une nouvelle.
3. **Limitez la taille des sessions** : Si une session contient trop de messages, envisagez d'en créer une nouvelle pour éviter des temps de réponse trop longs.

### Optimisation des performances

1. **Utilisez le cache Redis** : Le système utilise Redis pour mettre en cache les réponses fréquentes, réduisant les temps de réponse de 18-19 secondes à 3-4 secondes. Assurez-vous que Redis est correctement configuré et démarré.
2. **Téléchargez les données hors ligne** : Pour les applications mobiles, téléchargez régulièrement les données hors ligne pour permettre un fonctionnement sans connexion, en utilisant l'endpoint `/offline/data/`.
3. **Limitez la fréquence des requêtes** : Évitez d'envoyer plus de 10 requêtes par minute pour ne pas surcharger le serveur.
4. **Utilisez des sessions optimisées** : Limitez le nombre de messages par session à 20 pour maintenir des performances optimales. Créez une nouvelle session si nécessaire.

### Amélioration de la qualité des réponses

1. **Spécifiez le profil utilisateur** : Incluez toujours le paramètre `profile` pour obtenir des réponses adaptées au type d'utilisateur (Client, Capitaine, Établissement). Les réponses seront automatiquement personnalisées avec des conseils spécifiques au profil.
2. **Posez des questions précises** : Plus la question est précise, plus la réponse sera pertinente. Le système détecte automatiquement l'intention et les entités dans la question pour optimiser la recherche.
3. **Soumettez des feedbacks** : Utilisez l'endpoint de feedback pour signaler les bonnes et mauvaises réponses, ce qui permet d'améliorer le système.
4. **Utilisez le contexte de conversation** : Le système prend en compte les messages précédents dans la session pour générer des réponses plus cohérentes.

### Gestion des erreurs

1. **Implémentez des mécanismes de retry** : En cas d'erreur temporaire (code 5xx), réessayez la requête après un court délai. Le système RAG lui-même implémente des mécanismes de retry pour les appels à Gemini.
2. **Vérifiez les codes de statut** : Traitez différemment les erreurs 4xx (erreur client) et 5xx (erreur serveur). Les erreurs 5xx peuvent être temporaires et justifier un retry.
3. **Fallback hors ligne** : Si le serveur est inaccessible, utilisez les données hors ligne pour fournir une réponse de base. Le système RAG implémente également des mécanismes de fallback internes en cas d'erreur.
4. **Gestion des timeouts** : Configurez un timeout approprié pour les requêtes (10-15 secondes) pour éviter de bloquer l'interface utilisateur.

### Sécurité


1. **Protégez les tokens d'authentification** : Ne stockez jamais les tokens JWT dans le localStorage ou des cookies non sécurisés. Utilisez des mécanismes de stockage sécurisés comme EncryptedSharedPreferences sur Android ou Keychain sur iOS.
2. **Validez les entrées utilisateur** : Filtrez les entrées utilisateur pour éviter les injections ou les attaques XSS. Le système RAG effectue également une validation côté serveur.
3. **Respectez le RGPD** : N'envoyez pas de données personnelles sensibles dans les questions au chatbot. Le système ajoute automatiquement des mentions RGPD lorsque des données personnelles sont mentionnées.
4. **Utilisez HTTPS** : Toutes les communications avec l'API doivent être effectuées via HTTPS pour garantir la confidentialité des données.
5. **Implémentez la rotation des tokens** : Utilisez l'endpoint de rafraîchissement des tokens pour maintenir la sécurité de l'authentification.
