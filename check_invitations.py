"""
Script pour vérifier l'état des invitations de paiement partagé.
"""
import os
import django

# Configurer l'environnement Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from trips.shared_payments import SharedPaymentInvitation

# Afficher toutes les invitations
print("=== INVITATIONS DE PAIEMENT PARTAGÉ ===")
for inv in SharedPaymentInvitation.objects.all():
    print(f"ID: {inv.id}")
    print(f"Token: {inv.token}")
    print(f"Trip: {inv.trip_id}")
    print(f"Montant: {inv.amount}")
    print(f"Email invité: {inv.invitee_email}")
    print(f"Statut: {inv.status}")
    print("-" * 50)

# Créer une nouvelle invitation
print("\n=== CRÉATION D'UNE NOUVELLE INVITATION ===")
from django.contrib.auth import get_user_model
from trips.models import Trip
from django.utils import timezone
import datetime

User = get_user_model()
try:
    # Récupérer le trip et l'utilisateur
    trip = Trip.objects.get(id=1)
    inviter = User.objects.get(id=2)  # Client: <EMAIL>
    
    # Créer une date d'expiration (7 jours dans le futur)
    expires_at = timezone.now() + datetime.timedelta(days=7)
    
    # Créer l'invitation
    invitation = SharedPaymentInvitation.objects.create(
        trip=trip,
        inviter=inviter,
        invitee_email="<EMAIL>",
        amount=40.00,
        expires_at=expires_at,
        status='PENDING',
        message="Invitation finale pour le test de paiement partagé"
    )
    
    print(f"✓ Nouvelle invitation créée avec succès!")
    print(f"  ID: {invitation.id}")
    print(f"  Token: {invitation.token}")
    print(f"  Trip: {trip.id}")
    print(f"  Montant: {invitation.amount}")
    print(f"  Email invité: {invitation.invitee_email}")
    print(f"  Expire le: {invitation.expires_at}")
    print(f"\nUtilisez ce token dans le script de test:")
    print(f'$SHARED_PAYMENT_TOKEN = "{invitation.token}"')
    
except Exception as e:
    print(f"❌ Erreur lors de la création de l'invitation: {str(e)}")
