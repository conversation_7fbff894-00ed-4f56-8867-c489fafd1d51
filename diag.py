import os
import django

# Initialisation du contexte Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from boats.models import Boat
from accounts.models import Captain

USER_ID = 209  # À adapter si besoin

print("--- DIAGNOSTIC CAPITAINE & BATEAUX ---")

try:
    capitaine = Captain.objects.get(user__id=USER_ID)
    print(f"Capitaine {USER_ID} trouvé :")
    print(f"  - Disponible : {capitaine.is_available}")
    print(f"  - Tarif horaire : {capitaine.rate_per_hour}")
    print(f"  - Nom : {capitaine.user.first_name} {capitaine.user.last_name}")
    print(f"  - Email : {capitaine.user.email}")
    print(f"  - Expérience : {capitaine.experience}")
    print(f"  - Note moyenne : {capitaine.average_rating}")
    print(f"  - Nombre total de courses : {capitaine.total_trips}")

    bateaux = Boat.objects.filter(captain__user__id=USER_ID)
    if not bateaux:
        print("Aucun bateau associé à ce capitaine.")
    else:
        print(f"Bateaux du capitaine ({len(bateaux)}) :")
        for boat in bateaux:
            print(f"- {boat.name} | Disponible : {boat.is_available} | Type : {boat.boat_type} | Capacité : {boat.capacity}")
            # Critères pour être éligible à un devis
            if boat.is_available:
                print("    -> Ce bateau est DISPONIBLE pour les devis.")
            else:
                print("    -> Ce bateau n'est PAS disponible pour les devis.")
except Captain.DoesNotExist:
    print(f"Aucun capitaine trouvé avec user_id={USER_ID}")