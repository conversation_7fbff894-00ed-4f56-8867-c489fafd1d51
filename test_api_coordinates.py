#!/usr/bin/env python
"""
Test de l'API pour les coordonnées d'établissement
"""

import os
import sys
import django
import json
import requests
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, User
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def get_jwt_token_for_establishment():
    """Obtenir un token JWT pour un utilisateur établissement"""
    
    # Trouver un utilisateur établissement existant
    establishment_user = User.objects.filter(establishment__isnull=False).first()
    
    if not establishment_user:
        print("❌ Aucun utilisateur établissement trouvé")
        return None, None
    
    # Générer un token JWT
    refresh = RefreshToken.for_user(establishment_user)
    access_token = str(refresh.access_token)
    
    return access_token, establishment_user

def test_api_coordinates():
    """Tester l'API de mise à jour des coordonnées"""
    print("🧪 Test de l'API des coordonnées")
    print("=" * 50)
    
    # 1. Obtenir un token
    print("1. Obtention du token JWT...")
    token, user = get_jwt_token_for_establishment()
    
    if not token:
        return False
    
    print(f"   ✅ Token obtenu pour: {user.email}")
    print(f"   Établissement: {user.establishment.name}")
    
    # 2. Tester l'endpoint PATCH
    print("2. Test de l'endpoint PATCH...")
    
    url = "http://localhost:8000/api/accounts/establishment/profile/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Données de test avec coordonnées de Cannes
    test_data = {
        "establishment_profile": {
            "longitude": "7.0167",
            "latitude": "43.5528"
        }
    }
    
    try:
        response = requests.patch(url, headers=headers, json=test_data, timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Requête réussie")
            response_data = response.json()
            print(f"   Réponse: {json.dumps(response_data, indent=2)}")
            
            # 3. Vérifier en base de données
            print("3. Vérification en base de données...")
            user.establishment.refresh_from_db()
            
            longitude = user.establishment.longitude
            latitude = user.establishment.latitude
            
            print(f"   Longitude en DB: {longitude}")
            print(f"   Latitude en DB: {latitude}")
            
            if longitude and latitude:
                print("   ✅ Coordonnées sauvegardées en base")
                return True
            else:
                print("   ❌ Coordonnées non sauvegardées")
                return False
        else:
            print(f"   ❌ Erreur HTTP: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Erreur de requête: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_direct_model_update():
    """Test direct sur le modèle"""
    print("\n🔧 Test direct du modèle")
    print("=" * 30)
    
    try:
        est = Establishment.objects.first()
        if not est:
            print("❌ Aucun établissement trouvé")
            return False
        
        print(f"Établissement: {est.name}")
        
        # Test avec Decimal
        est.longitude = Decimal('7.0167')
        est.latitude = Decimal('43.5528')
        est.save()
        
        print(f"Après save - Longitude: {est.longitude}, Latitude: {est.latitude}")
        
        # Refresh depuis la DB
        est.refresh_from_db()
        print(f"Après refresh - Longitude: {est.longitude}, Latitude: {est.latitude}")
        
        if est.longitude and est.latitude:
            print("✅ Modèle fonctionne correctement")
            return True
        else:
            print("❌ Problème avec le modèle")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Démarrage des tests...")
    
    # Test du modèle d'abord
    model_ok = test_direct_model_update()
    
    if model_ok:
        # Test de l'API
        api_ok = test_api_coordinates()
        success = api_ok
    else:
        success = False
    
    print(f"\n{'🎉 Tous les tests réussis!' if success else '❌ Certains tests ont échoué'}")
    sys.exit(0 if success else 1)
