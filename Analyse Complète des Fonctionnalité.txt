Analyse Complète des Fonctionnalités
Fonctionnalités de Base (Basées sur les Maquettes)
Types d'utilisateurs : Client, Capitaine, Établissement.
Champs principaux (extraits des maquettes) :
Client : Nom d'utilisateur, email, téléphone, mot de passe, photo de profil, détails de paiement (carte/portefeuille), détails de course (date, heure, lieux, passagers, commentaires, coût, statut, pourboire).
Capitaine : Nom, email, expérience, note moyenne, bateau (nom, matricule, couleur, capacité, carburant, consommation), détails de course, revenus.
Établissement : Type (restaurant, hôtel, plage privée), nom, adresse, description, photos, email, batelier interne (nom, email, bateau), détails de navette, portefeuille.
Fonctionnalités principales :
Inscription/connexion, gestion de profil, réservation/gestion de courses/navettes, paiement, suivi en temps réel, historique, évaluation.
Fonctionnalités Spécifiques Ajoutées
Authentification :
Méthodes :
Classique : Email + mot de passe.
Sociale : Facebook, Google, Apple.
Processus d'inscription :
L'utilisateur remplit les informations de base (email, mot de passe, téléphone, etc.).
Une étape explicite demande de choisir le type d'utilisateur : Client, Capitaine, ou Établissement.
Après soumission, un email de vérification avec un code ou un lien est envoyé pour valider l'inscription.
Vérification par email :
Envoi d'un code à 4 chiffres ou d'un lien de vérification.
L'utilisateur doit confirmer avant d'activer le compte.
Connexion :
Par email/mot de passe ou via un jeton OAuth2 pour les connexions sociales.
Réinitialisation du mot de passe via email ou SMS.
Remboursements Automatiques :
Déclenchés en cas d'annulation (par client, capitaine, établissement) ou de problème (ex. : indisponibilité du bateau).
Scénarios :
Annulation avant confirmation : remboursement intégral.
Annulation par capitaine/établissement : remboursement intégral + possible compensation.
Problème pendant la course : remboursement partiel/intégral.
Champs : ID de transaction, montant, raison, statut, date.
Notifications :
Canaux : Push (application mobile), email, SMS.
Événements : Nouvelle course, acceptation/rejet, annulation, paiement, remboursement, message de chat.
Champs : Type (push, email, SMS), destinataire, contenu, statut, date.
Chat de Discussion :
Messagerie en temps réel entre client/capitaine et établissement/capitaine.
Fonctionnalités : Messages texte, pièces jointes, historique, statut (envoyé, lu, non lu).
Champs : Expéditeur, destinataire, contenu, pièce jointe, statut, date.
Chatbot :
Réponses automatiques aux FAQ, assistance à la navigation, support multilingue.
Intégration avec notifications (ex. : alerte via chatbot pour annulation).
Champs : Session ID, utilisateur, message utilisateur, réponse bot, date.
User Stories Complètes
Voici la liste exhaustive des user stories pour chaque type d'utilisateur, intégrant l'authentification mise à jour et toutes les fonctionnalités.

Client
En tant que client, je veux m'inscrire en fournissant mon email, mot de passe, et téléphone, puis choisir "Client" comme type d'utilisateur.
En tant que client, je veux m'inscrire via Facebook, Google, ou Apple, puis choisir "Client" comme type d'utilisateur.
En tant que client, je veux recevoir un email de vérification avec un code ou un lien pour valider mon inscription.
En tant que client, je veux me connecter avec mon email/mot de passe ou via Facebook/Google/Apple.
En tant que client, je veux réinitialiser mon mot de passe en recevant un code par email ou SMS.
En tant que client, je veux gérer mon profil (nom d'utilisateur, email, téléphone, photo de profil).
En tant que client, je veux ajouter des fonds à mon portefeuille pour payer mes courses.
En tant que client, je veux réserver une course en spécifiant la date, l'heure, le lieu de départ, le lieu d'arrivée, le nombre de passagers, et des commentaires.
En tant que client, je veux consulter la liste des capitaines disponibles avec leurs bateaux, notes, et expériences.
En tant que client, je veux annuler une réservation avant confirmation et recevoir un remboursement automatique.
En tant que client, je veux payer une course via mon portefeuille ou carte bancaire.
En tant que client, je veux suivre une course en temps réel sur une carte.
En tant que client, je veux consulter l'historique de mes courses (date, lieux, coût, statut, capitaine).
En tant que client, je veux évaluer un capitaine après une course et laisser un commentaire.
En tant que client, je veux recevoir des notifications push pour les mises à jour de ma course (acceptation, annulation, démarrage).
En tant que client, je veux recevoir des emails ou SMS pour les confirmations importantes (inscription, paiement, remboursement).
En tant que client, je veux discuter avec le capitaine via un chat intégré pour clarifier les détails de la course.
En tant que client, je veux interagir avec un chatbot pour des réponses rapides (ex. : statut de course, remboursements).
En tant que client, je veux recevoir un remboursement automatique si ma course est annulée par le capitaine ou l'établissement.
En tant que client, je veux consulter l'historique de mes remboursements (montant, raison, date).
Capitaine
En tant que capitaine, je veux m'inscrire en fournissant mon nom, email, expérience, détails du bateau, et choisir "Capitaine" comme type d'utilisateur.
En tant que capitaine, je veux m'inscrire via Facebook, Google, ou Apple, puis choisir "Capitaine" comme type d'utilisateur.
En tant que capitaine, je veux recevoir un email de vérification avec un code ou un lien pour valider mon inscription.
En tant que capitaine, je veux me connecter avec mon email/mot de passe ou via Facebook/Google/Apple.
En tant que capitaine, je veux réinitialiser mon mot de passe via email ou SMS.
En tant que capitaine, je veux gérer mon profil (nom, expérience, photo, détails du bateau).
En tant que capitaine, je veux ajouter ou modifier les informations de mes bateaux.
En tant que capitaine, je veux consulter les demandes de courses en attente avec leurs détails (client, lieux, passagers, commentaires).
En tant que capitaine, je veux accepter ou rejeter une demande de course.
En tant que capitaine, je veux démarrer une course acceptée.
En tant que capitaine, je veux annuler une course avant qu'elle ne commence, déclenchant un remboursement automatique.
En tant que capitaine, je veux suivre une course en cours sur une carte.
En tant que capitaine, je veux consulter l'historique de mes courses (date, lieux, client, coût, pourboire).
En tant que capitaine, je veux consulter mon solde disponible et mes revenus totaux.
En tant que capitaine, je veux recevoir des notifications push pour les nouvelles demandes de courses ou changements de statut.
En tant que capitaine, je veux discuter avec le client ou l'établissement via un chat intégré.
En tant que capitaine, je veux interagir avec un chatbot pour des informations sur les règles ou le statut des courses.
En tant que capitaine, je veux être informé si un remboursement est effectué en raison d'une annulation de ma part.
Établissement
En tant qu'établissement, je veux m'inscrire en fournissant les détails de mon établissement (type, nom, adresse, description, photos, email) et choisir "Établissement" comme type d'utilisateur.
En tant qu'établissement, je veux m'inscrire via Facebook, Google, ou Apple, puis choisir "Établissement" comme type d'utilisateur.
En tant qu'établissement, je veux recevoir un email de vérification avec un code ou un lien pour valider mon inscription.
En tant qu'établissement, je veux me connecter avec mon email/mot de passe ou via Facebook/Google/Apple.
En tant qu'établissement, je veux réinitialiser mon mot de passe via email ou SMS.
En tant qu'établissement, je veux gérer mon profil (nom, adresse, description, photos).
En tant qu'établissement, je veux ajouter un batelier interne (nom, email, bateau, matricule, capacité, carburant).
En tant qu'établissement, je veux demander une navette pour mes clients (date, heure, départ, arrivée, passagers).
En tant qu'établissement, je veux assigner un capitaine interne ou indépendant à une navette.
En tant qu'établissement, je veux consulter les demandes de navettes (en attente, à venir, en cours, terminées, annulées).
En tant qu'établissement, je veux annuler une navette, déclenchant un remboursement automatique si nécessaire.
En tant qu'établissement, je veux recharger mon portefeuille pour payer les navettes.
En tant qu'établissement, je veux consulter le solde disponible et le nombre de navettes réalisées.
En tant qu'établissement, je veux recevoir des notifications push pour les mises à jour des navettes.
En tant qu'établissement, je veux recevoir des emails pour les confirmations importantes (inscription, paiement, remboursement).
En tant qu'établissement, je veux discuter avec le capitaine via un chat intégré.
En tant qu'établissement, je veux interagir avec un chatbot pour des informations sur la gestion des navettes.
En tant qu'établissement, je veux consulter l'historique de mes remboursements (montant, raison, date).
Modèles Django
Les modèles sont mis à jour pour inclure l'authentification sociale (Facebook, Google, Apple), la vérification par email, et toutes les fonctionnalités précédentes (remboursements, notifications, chat, chatbot).

models.py
python
Afficher en ligne
Endpoints de l'API
Les endpoints sont mis à jour pour inclure l'authentification via Facebook, Google, Apple, la vérification par email, et toutes les fonctionnalités demandées. Chaque endpoint est sécurisé avec JWT et des permissions spécifiques.

Authentification
POST /api/auth/register/
Description : Inscription classique avec choix du type d'utilisateur et vérification par email.
Champs :
email (string)
password (string, min 8 caractères)
phone_number (string, optionnel)
user_type (string: client, captain, establishment)
username (string, pour Client)
experience (string, pour Capitaine)
boat (object, pour Capitaine : { "name": string, "registration_number": string, "color": string, "capacity": int, "fuel_type": string, "fuel_consumption": float })
establishment_type (string, pour Établissement)
name (string, pour Établissement)
address (string, pour Établissement)
description (string, pour Établissement)
main_photo (file, pour Établissement)
secondary_photos (list of files, pour Établissement)
Sécurité : Aucun token requis, validation des champs.
Réponse : { "user_id": int, "email": string, "message": "Vérifiez votre email pour activer le compte" }
POST /api/auth/verify-email/
Description : Vérifier l'email avec le code reçu.
Champs :
email (string)
code (string, 4 chiffres)
Sécurité : Aucun token requis.
Réponse : { "message": "Email vérifié, compte activé" }
POST /api/auth/social-login/
Description : Connexion ou inscription via Facebook, Google, ou Apple.
Champs :
provider (string: facebook, google, apple)
access_token (string, jeton OAuth2 du fournisseur)
user_type (string: client, captain, establishment, requis si nouvel utilisateur)
username (string, pour Client, si nouvel utilisateur)
experience (string, pour Capitaine, si nouvel utilisateur)
boat (object, pour Capitaine, si nouvel utilisateur)
establishment_type, name, address, description, main_photo, secondary_photos (pour Établissement, si nouvel utilisateur)
Sécurité : Aucun token requis, validation du jeton OAuth2.
Réponse : { "access_token": string, "refresh_token": string, "user_id": int, "email": string }
POST /api/auth/login/
Description : Connexion classique.
Champs :
email (string)
password (string)
Sécurité : Aucun token requis.
Réponse : { "access_token": string, "refresh_token": string }
POST /api/auth/password/reset/
Description : Demande de réinitialisation de mot de passe.
Champs :
email (string) ou phone_number (string)
Sécurité : Aucun token requis.
Réponse : { "message": "Code de vérification envoyé par email/SMS" }
POST /api/auth/password/reset/verify/
Description : Vérification du code et mise à jour du mot de passe.
Champs :
email (string) ou phone_number (string)
code (string, 4 chiffres)
new_password (string)
Sécurité : Aucun token requis.
Réponse : { "message": "Mot de passe réinitialisé" }
Client
GET /api/client/profile/
Description : Récupérer le profil du client.
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : { "username": string, "email": string, "phone_number": string, "profile_picture": string }
PUT /api/client/profile/
Description : Mettre à jour le profil.
Champs :
username (string, optionnel)
phone_number (string, optionnel)
profile_picture (file, optionnel)
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : { "username": string, "email": string, "phone_number": string, "profile_picture": string }
POST /api/client/wallet/fund/
Description : Ajouter des fonds au portefeuille.
Champs :
amount (decimal)
card_holder (string)
card_number (string)
expiry_date (string, MM/YY)
cvv (string)
Sécurité : JWT, permission IsClient, email vérifié, chiffrement des données.
Réponse : { "balance": decimal, "message": "Portefeuille rechargé" }
POST /api/client/trips/
Description : Créer une demande de course.
Champs :
start_location (string)
end_location (string)
date_time (datetime)
passengers (integer)
comments (string, optionnel)
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : { "trip_id": int, "status": string }
GET /api/client/trips/
Description : Liste des courses.
Query Params : status (string: pending, upcoming, in_progress, completed, canceled)
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : [{ "trip_id": int, "start_location": string, "end_location": string, "date_time": datetime, "passengers": int, "cost": decimal, "status": string, "captain": { "name": string, "boat": string }, ... }]
PATCH /api/client/trips/{id}/cancel/
Description : Annuler une course, déclenche un remboursement.
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : { "message": "Course annulée, remboursement initié" }
POST /api/client/trips/{id}/pay/
Description : Payer une course.
Champs :
use_wallet (boolean)
card_holder (string, si use_wallet=False)
card_number (string, si use_wallet=False)
expiry_date (string, si use_wallet=False)
cvv (string, si use_wallet=False)
Sécurité : JWT, permission IsClient, email vérifié, chiffrement.
Réponse : { "message": "Paiement effectué" }
POST /api/client/trips/{id}/review/
Description : Évaluer une course.
Champs :
rating (float, 0-5)
comment (string, optionnel)
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : { "message": "Évaluation enregistrée" }
GET /api/client/trips/{id}/track/
Description : Suivre une course en temps réel.
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : { "trip_id": int, "status": string, "current_location": { "lat": float, "lon": float } }
GET /api/client/refunds/
Description : Consulter les remboursements.
Query Params : status (string: pending, completed, failed)
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : [{ "refund_id": int, "trip_id": int, "amount": decimal, "reason": string, "status": string, "created_at": datetime }]
GET /api/client/notifications/
Description : Consulter les notifications.
Query Params : type (string: push, email, sms), status (string: sent, read, failed)
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : [{ "notification_id": int, "type": string, "content": string, "status": string, "created_at": datetime }]
PATCH /api/client/notifications/{id}/read/
Description : Marquer une notification comme lue.
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : { "message": "Notification marquée comme lue" }
POST /api/client/chat/
Description : Envoyer un message dans le chat.
Champs :
receiver_id (int)
trip_id (int, optionnel)
content (string)
attachment (file, optionnel)
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : { "message_id": int, "content": string, "created_at": datetime }
GET /api/client/chat/{trip_id}/
Description : Consulter l'historique du chat.
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : [{ "message_id": int, "sender_id": int, "content": string, "attachment": string, "status": string, "created_at": datetime }]
POST /api/client/chatbot/
Description : Interagir avec le chatbot.
Champs :
session_id (uuid, optionnel)
message (string)
Sécurité : JWT, permission IsClient, email vérifié.
Réponse : { "session_id": uuid, "bot_response": string, "created_at": datetime }
Capitaine
GET /api/captain/profile/
Description : Récupérer le profil du capitaine.
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "name": string, "email": string, "experience": string, "average_rating": float, "boats": [{ "name": string, ... }] }
PUT /api/captain/profile/
Description : Mettre à jour le profil.
Champs :
experience (string, optionnel)
profile_picture (file, optionnel)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "name": string, "email": string, "experience": string, ... }
POST /api/captain/boats/
Description : Ajouter un bateau.
Champs :
name (string)
registration_number (string)
color (string, optionnel)
capacity (integer)
fuel_type (string)
fuel_consumption (float, optionnel)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "boat_id": int, "name": string, ... }
GET /api/captain/trips/
Description : Liste des courses.
Query Params : status (string: pending, upcoming, in_progress, completed, canceled)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : [{ "trip_id": int, "client": string, "start_location": string, ... }]
PATCH /api/captain/trips/{id}/accept/
Description : Accepter une course.
Champs :
boat_id (int)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "message": "Course acceptée" }
PATCH /api/captain/trips/{id}/reject/
Description : Rejeter une course, déclenche un remboursement si nécessaire.
Champs :
reason (string)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "message": "Course rejetée, remboursement initié si applicable" }
PATCH /api/captain/trips/{id}/start/
Description : Démarrer une course.
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "message": "Course démarrée" }
PATCH /api/captain/trips/{id}/cancel/
Description : Annuler une course, déclenche un remboursement.
Champs :
reason (string)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "message": "Course annulée, remboursement initié" }
PATCH /api/captain/trips/{id}/update-location/
Description : Mettre à jour la position.
Champs :
lat (float)
lon (float)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "message": "Position mise à jour" }
GET /api/captain/wallet/
Description : Consulter le solde/revenus.
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "balance": decimal, "total_earnings": decimal, "trips_completed": int }
GET /api/captain/notifications/
Description : Consulter les notifications.
Query Params : type (string: push, email, sms), status (string: sent, read, failed)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : [{ "notification_id": int, "type": string, "content": string, "status": string, "created_at": datetime }]
PATCH /api/captain/notifications/{id}/read/
Description : Marquer une notification comme lue.
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "message": "Notification marquée comme lue" }
POST /api/captain/chat/
Description : Envoyer un message dans le chat.
Champs :
receiver_id (int)
trip_id (int, optionnel)
content (string)
attachment (file, optionnel)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "message_id": int, "content": string, "created_at": datetime }
GET /api/captain/chat/{trip_id}/
Description : Consulter l'historique du chat.
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : [{ "message_id": int, "sender_id": int, "content": string, "attachment": string, "status": string, "created_at": datetime }]
POST /api/captain/chatbot/
Description : Interagir avec le chatbot.
Champs :
session_id (uuid, optionnel)
message (string)
Sécurité : JWT, permission IsCaptain, email vérifié.
Réponse : { "session_id": uuid, "bot_response": string, "created_at": datetime }
Établissement
GET /api/establishment/profile/
Description : Récupérer le profil de l'établissement.
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : { "name": string, "type": string, "address": string, "description": string, "main_photo": string, "secondary_photos": [string], ... }
PUT /api/establishment/profile/
Description : Mettre à jour le profil.
Champs :
name (string, optionnel)
type (string, optionnel)
address (string, optionnel)
description (string, optionnel)
main_photo (file, optionnel)
secondary_photos (list of files, optionnel)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : { "name": string, "type": string, ... }
POST /api/establishment/captains/
Description : Ajouter un batelier interne.
Champs :
name (string)
email (string)
boat_name (string)
registration_number (string)
color (string, optionnel)
capacity (integer)
fuel_type (string)
fuel_consumption (float, optionnel)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : { "captain_id": int, "name": string, "boat": { "name": string, ... } }
POST /api/establishment/trips/
Description : Demander une navette.
Champs :
start_location (string)
end_location (string)
date_time (datetime)
passengers (integer)
client_id (int, optionnel)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : { "trip_id": int, "status": string }
POST /api/establishment/trips/{id}/assign/
Description : Assigner un capitaine.
Champs :
captain_id (int)
boat_id (int)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : { "message": "Capitaine assigné" }
GET /api/establishment/trips/
Description : Liste des navettes.
Query Params : status (string: pending, upcoming, in_progress, completed, canceled)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : [{ "trip_id": int, "client": string, "start_location": string, ... }]
PATCH /api/establishment/trips/{id}/cancel/
Description : Annuler une navette, déclenche un remboursement.
Champs :
reason (string)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : { "message": "Navette annulée, remboursement initié" }
POST /api/establishment/wallet/fund/
Description : Ajouter des fonds au portefeuille.
Champs :
amount (decimal)
card_holder (string)
card_number (string)
expiry_date (string)
cvv (string)
Sécurité : JWT, permission IsEstablishment, email vérifié, chiffrement.
Réponse : { "balance": decimal, "message": "Portefeuille rechargé" }
GET /api/establishment/refunds/
Description : Consulter les remboursements.
Query Params : status (string: pending, completed, failed)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : [{ "refund_id": int, "trip_id": int, "amount": decimal, "reason": string, "status": string, "created_at": datetime }]
GET /api/establishment/notifications/
Description : Consulter les notifications.
Query Params : type (string: push, email, sms), status (string: sent, read, failed)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : [{ "notification_id": int, "type": string, "content": string, "status": string, "created_at": datetime }]
PATCH /api/establishment/notifications/{id}/read/
Description : Marquer une notification comme lue.
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : { "message": "Notification marquée comme lue" }
POST /api/establishment/chat/
Description : Envoyer un message dans le chat.
Champs :
receiver_id (int)
trip_id (int, optionnel)
content (string)
attachment (file, optionnel)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : { "message_id": int, "content": string, "created_at": datetime }
GET /api/establishment/chat/{trip_id}/
Description : Consulter l'historique du chat.
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : [{ "message_id": int, "sender_id": int, "content": string, "attachment": string, "status": string, "created_at": datetime }]
POST /api/establishment/chatbot/
Description : Interagir avec le chatbot.
Champs :
session_id (uuid, optionnel)
message (string)
Sécurité : JWT, permission IsEstablishment, email vérifié.
Réponse : { "session_id": uuid, "bot_response": string, "created_at": datetime }









STRUCTURE COMPLÈTE DE L’APPLICATION "Navette Maritime"
============================================================

Ce document regroupe toutes les fonctionnalités, user stories, endpoints, permissions JWT,
et considérations techniques nécessaires à la construction complète de l'application.

TYPES D'UTILISATEURS
- Client
- Capitaine
- Établissement (restaurant, hôtel, plage privée)

CHAMPS PRINCIPAUX PAR UTILISATEUR
- Client : nom d'utilisateur, email, téléphone, mot de passe, photo, moyens de paiement, historique de course.
- Capitaine : nom, email, expérience, bateaux, revenu, évaluation.
- Établissement : nom, type, adresse, description, photos, navettes, bateliers internes.

AUTHENTIFICATION ET SÉCURITÉ
- Inscription classique ou via réseaux sociaux (Facebook, Google, Apple).
- Vérification d’email obligatoire avec code ou lien.
- Réinitialisation de mot de passe via email ou SMS.
- JWT avec refresh token et permissions spécifiques par rôle.

USER STORIES
- Pour chaque rôle (Client, Capitaine, Établissement), voir le détail complet dans les sections correspondantes.
- Inclut gestion de profil, paiements, messagerie, chatbot, historique, remboursements, etc.

ENDPOINTS DISPONIBLES
Tous les endpoints sont documentés et sécurisés.
- /api/auth/register/, /api/auth/login/, /api/auth/verify-email/, /api/auth/social-login/…
- /api/client/profile/, /api/client/trips/, /api/client/wallet/fund/, /api/client/chat/…
- /api/captain/profile/, /api/captain/boats/, /api/captain/trips/, /api/captain/wallet/…
- /api/establishment/profile/, /api/establishment/trips/, /api/establishment/captains/…

DOCUMENTATION SWAGGER
- Intégrée via DRF-YASG ou DRF-SPECTACULAR.
- Accessible via /swagger/ ou /api/docs/
- Toutes les routes et schémas de données sont décrits automatiquement.

FONCTIONNALITÉS SPÉCIALES
- Messagerie temps réel : WebSocket ou Django Channels.
- Chatbot intégré avec NLP multilingue (spaCy, transformers).
- Notifications via Push, Email, SMS (Firebase, Twilio...)
- Pagination intégrée dans les endpoints de liste (DRF pagination).
- Remboursements automatiques selon scénarios définis.




FIN DU DOCUMENT
============================================================
Ce fichier peut être utilisé comme cahier des charges technique intégral pour le développement complet de l’application.












