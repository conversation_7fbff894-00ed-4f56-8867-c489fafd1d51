"""
Module de tests pour les vues API de l'application payments.

Ce module contient les tests unitaires pour les vues API de l'application payments.
"""

import pytest
import json
from decimal import Decimal
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from payments.models import Wallet, Transaction
# Utiliser notre adaptateur pour les modèles Ride et Location
from payments.models_rides_adapter import Ride
from trips.models import Location
from boats.models import Boat
from unittest.mock import patch, MagicMock

User = get_user_model()

@pytest.fixture
def api_client():
    """Fixture pour créer un client API."""
    return APIClient()

@pytest.fixture
def user():
    """Fixture pour créer un utilisateur."""
    return User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='testpassword'
    )

@pytest.fixture
def authenticated_client(api_client, user):
    """Fixture pour créer un client API authentifié."""
    api_client.force_authenticate(user=user)
    return api_client

@pytest.fixture
def wallet(user):
    """Fixture pour créer un portefeuille."""
    return Wallet.objects.create(
        user=user,
        balance=Decimal('100.00')
    )

@pytest.fixture
def client_user():
    """Fixture pour créer un utilisateur client."""
    return User.objects.create_user(
        username='client',
        email='<EMAIL>',
        password='clientpassword'
    )

@pytest.fixture
def captain_user():
    """Fixture pour créer un utilisateur capitaine."""
    return User.objects.create_user(
        username='captain',
        email='<EMAIL>',
        password='captainpassword'
    )

@pytest.fixture
def boat(captain_user):
    """Fixture pour créer un bateau."""
    return Boat.objects.create(
        name='Test Boat',
        captain=captain_user,
        capacity=10,
        model='Test Model',
        license_plate='ABC123'
    )

@pytest.fixture
def start_location():
    """Fixture pour créer un emplacement de départ."""
    return Location.objects.create(
        latitude=43.296482,
        longitude=5.369780,
        address='123 Rue de Départ, 13000 Marseille',
        type='port'
    )

@pytest.fixture
def end_location():
    """Fixture pour créer un emplacement d'arrivée."""
    return Location.objects.create(
        latitude=43.295482,
        longitude=5.368780,
        address='456 Rue d\'Arrivée, 13000 Marseille',
        type='beach'
    )

@pytest.fixture
def ride(client_user, captain_user, boat, start_location, end_location):
    """Fixture pour créer un trajet."""
    return Ride.objects.create(
        client=client_user,
        captain=captain_user,
        boat=boat,
        start_location=start_location,
        end_location=end_location,
        distance=5.0,
        price=Decimal('50.00'),
        status='pending',
        carbon_compensation=Decimal('2.50')
    )

@pytest.fixture
def client_wallet(client_user):
    """Fixture pour créer un portefeuille pour le client."""
    return Wallet.objects.create(
        user=client_user,
        balance=Decimal('100.00')
    )

@pytest.mark.django_db
class TestWalletDetailView:
    """Tests pour la vue WalletDetailView."""

    def test_get_wallet(self, authenticated_client, wallet):
        """Teste la récupération des détails du portefeuille."""
        url = reverse('wallet-detail')
        response = authenticated_client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == wallet.id
        assert Decimal(response.data['balance']) == wallet.balance

    def test_get_wallet_unauthenticated(self, api_client):
        """Teste la récupération des détails du portefeuille sans authentification."""
        url = reverse('wallet-detail')
        response = api_client.get(url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_get_wallet_not_found(self, authenticated_client):
        """Teste la récupération des détails du portefeuille inexistant."""
        url = reverse('wallet-detail')
        response = authenticated_client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND

@pytest.mark.django_db
class TestWalletRechargeView:
    """Tests pour la vue WalletRechargeView."""

    @patch('payments.views_api.create_checkout_session')
    def test_recharge_wallet(self, mock_create_checkout_session, authenticated_client, wallet):
        """Teste la recharge du portefeuille."""
        # Configurer le mock
        mock_session = MagicMock()
        mock_session.id = 'cs_test_123'
        mock_session.url = 'https://checkout.stripe.com/pay/cs_test_123'
        mock_session.payment_intent = 'pi_test_123'
        mock_create_checkout_session.return_value = mock_session

        url = reverse('wallet-recharge')
        data = {
            'amount': '50.00',
            'success_url': 'https://example.com/success',
            'cancel_url': 'https://example.com/cancel'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert 'session_id' in response.data
        assert 'transaction_id' in response.data
        assert 'checkout_url' in response.data
        assert response.data['session_id'] == 'cs_test_123'
        assert response.data['checkout_url'] == 'https://checkout.stripe.com/pay/cs_test_123'

        # Vérifier que la transaction a été créée
        transaction = Transaction.objects.get(id=response.data['transaction_id'])
        assert transaction.wallet == wallet
        assert transaction.amount == Decimal('50.00')
        assert transaction.type == 'recharge'
        assert transaction.status == 'pending'
        assert transaction.stripe_payment_intent_id == 'pi_test_123'

    def test_recharge_wallet_no_amount(self, authenticated_client, wallet):
        """Teste la recharge du portefeuille sans montant."""
        url = reverse('wallet-recharge')
        data = {
            'success_url': 'https://example.com/success',
            'cancel_url': 'https://example.com/cancel'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

    def test_recharge_wallet_invalid_amount(self, authenticated_client, wallet):
        """Teste la recharge du portefeuille avec un montant invalide."""
        url = reverse('wallet-recharge')
        data = {
            'amount': 'invalid',
            'success_url': 'https://example.com/success',
            'cancel_url': 'https://example.com/cancel'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

    def test_recharge_wallet_unauthenticated(self, api_client):
        """Teste la recharge du portefeuille sans authentification."""
        url = reverse('wallet-recharge')
        data = {
            'amount': '50.00',
            'success_url': 'https://example.com/success',
            'cancel_url': 'https://example.com/cancel'
        }
        response = api_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

@pytest.mark.django_db
class TestRidePaymentView:
    """Tests pour la vue RidePaymentView."""

    def test_pay_ride(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le paiement d'un trajet."""
        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('ride-payment')
        data = {
            'ride_id': ride.id
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        assert 'id' in response.data
        assert response.data['type'] == 'payment'
        assert response.data['status'] == 'completed'
        assert Decimal(response.data['amount']) == ride.price

        # Vérifier que le portefeuille a été débité
        client_wallet.refresh_from_db()
        assert client_wallet.balance == Decimal('50.00')  # 100 - 50

        # Vérifier que le trajet a été mis à jour
        ride.refresh_from_db()
        assert ride.status == 'accepted'

    def test_pay_ride_insufficient_balance(self, authenticated_client, client_user, ride):
        """Teste le paiement d'un trajet avec un solde insuffisant."""
        # Créer un portefeuille avec un solde insuffisant
        wallet = Wallet.objects.create(
            user=client_user,
            balance=Decimal('10.00')
        )

        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('ride-payment')
        data = {
            'ride_id': ride.id
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data
        assert 'insuffisant' in response.data['error']

    def test_pay_ride_not_client(self, authenticated_client, captain_user, ride, client_wallet):
        """Teste le paiement d'un trajet par un utilisateur qui n'est pas le client."""
        # Authentifier le capitaine
        authenticated_client.force_authenticate(user=captain_user)

        url = reverse('ride-payment')
        data = {
            'ride_id': ride.id
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'error' in response.data

    def test_pay_ride_already_paid(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le paiement d'un trajet déjà payé."""
        # Créer une transaction de paiement
        Transaction.objects.create(
            wallet=client_wallet,
            ride=ride,
            amount=ride.price,
            type='payment',
            status='completed'
        )

        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('ride-payment')
        data = {
            'ride_id': ride.id
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data
        assert 'déjà été payé' in response.data['error']

@pytest.mark.django_db
class TestTipPaymentView:
    """Tests pour la vue TipPaymentView."""

    def test_pay_tip(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le paiement d'un pourboire."""
        # Mettre le trajet en statut completed
        ride.status = 'completed'
        ride.save()

        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('tip-payment')
        data = {
            'ride_id': ride.id,
            'amount': '10.00'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        assert 'id' in response.data
        assert response.data['type'] == 'tip'
        assert response.data['status'] == 'completed'
        assert Decimal(response.data['amount']) == Decimal('10.00')

        # Vérifier que le portefeuille a été débité
        client_wallet.refresh_from_db()
        assert client_wallet.balance == Decimal('90.00')  # 100 - 10

    def test_pay_tip_ride_not_completed(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le paiement d'un pourboire pour un trajet non terminé."""
        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('tip-payment')
        data = {
            'ride_id': ride.id,
            'amount': '10.00'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data
        assert 'terminé' in response.data['error']

    def test_pay_tip_negative_amount(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le paiement d'un pourboire avec un montant négatif."""
        # Mettre le trajet en statut completed
        ride.status = 'completed'
        ride.save()

        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('tip-payment')
        data = {
            'ride_id': ride.id,
            'amount': '-10.00'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data
        assert 'positif' in response.data['error']

@pytest.mark.django_db
class TestCarbonOffsetView:
    """Tests pour la vue CarbonOffsetView."""

    def test_pay_carbon_offset(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le paiement de la compensation carbone."""
        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('carbon-offset')
        data = {
            'ride_id': ride.id,
            'amount': '5.00'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        assert 'id' in response.data
        assert response.data['type'] == 'carbon_offset'
        assert response.data['status'] == 'completed'
        assert Decimal(response.data['amount']) == Decimal('5.00')

        # Vérifier que le portefeuille a été débité
        client_wallet.refresh_from_db()
        assert client_wallet.balance == Decimal('95.00')  # 100 - 5

        # Vérifier que la compensation carbone du trajet a été mise à jour
        ride.refresh_from_db()
        assert ride.carbon_compensation == Decimal('5.00')

    def test_pay_carbon_offset_negative_amount(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le paiement de la compensation carbone avec un montant négatif."""
        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('carbon-offset')
        data = {
            'ride_id': ride.id,
            'amount': '-5.00'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data
        assert 'positif' in response.data['error']

@pytest.mark.django_db
class TestRefundPaymentView:
    """Tests pour la vue RefundPaymentView."""

    def test_refund_payment(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le remboursement d'un paiement."""
        # Créer une transaction de paiement
        payment_transaction = Transaction.objects.create(
            wallet=client_wallet,
            ride=ride,
            amount=ride.price,
            type='payment',
            status='completed',
            stripe_payment_intent_id='pi_test_123'
        )

        # Débiter le portefeuille
        client_wallet.balance -= ride.price
        client_wallet.save()

        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('refund-payment')
        data = {
            'ride_id': ride.id,
            'reason': 'Annulation par le client'
        }

        # Mocker la fonction create_refund
        with patch('payments.views_api.create_refund') as mock_create_refund:
            mock_create_refund.return_value = {'id': 're_test_123'}

            response = authenticated_client.post(url, data, format='json')
            assert response.status_code == status.HTTP_201_CREATED
            assert 'id' in response.data
            assert response.data['type'] == 'refund'
            assert response.data['status'] == 'completed'
            assert Decimal(response.data['amount']) == ride.price

            # Vérifier que le portefeuille a été recrédité
            client_wallet.refresh_from_db()
            assert client_wallet.balance == Decimal('100.00')  # 100 - 50 + 50

            # Vérifier que le trajet a été annulé
            ride.refresh_from_db()
            assert ride.status == 'canceled'

    def test_refund_payment_no_payment(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le remboursement d'un paiement inexistant."""
        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('refund-payment')
        data = {
            'ride_id': ride.id,
            'reason': 'Annulation par le client'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data
        assert 'Aucun paiement' in response.data['error']

    def test_refund_payment_already_refunded(self, authenticated_client, client_user, ride, client_wallet):
        """Teste le remboursement d'un paiement déjà remboursé."""
        # Créer une transaction de paiement
        payment_transaction = Transaction.objects.create(
            wallet=client_wallet,
            ride=ride,
            amount=ride.price,
            type='payment',
            status='completed'
        )

        # Créer une transaction de remboursement
        refund_transaction = Transaction.objects.create(
            wallet=client_wallet,
            ride=ride,
            amount=ride.price,
            type='refund',
            status='completed'
        )

        # Authentifier le client
        authenticated_client.force_authenticate(user=client_user)

        url = reverse('refund-payment')
        data = {
            'ride_id': ride.id,
            'reason': 'Annulation par le client'
        }
        response = authenticated_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data
        assert 'déjà été remboursé' in response.data['error']
