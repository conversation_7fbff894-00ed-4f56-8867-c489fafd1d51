# Generated by Django 4.2.8 on 2025-05-30 23:54

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import trips.shared_payments
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("boats", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Shuttle",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "route_name",
                    models.CharField(max_length=100, verbose_name="nom de la route"),
                ),
                (
                    "start_location",
                    models.CharField(max_length=255, verbose_name="lieu de départ"),
                ),
                (
                    "end_location",
                    models.CharField(max_length=255, verbose_name="lieu d'arrivée"),
                ),
                ("stops", models.J<PERSON><PERSON><PERSON>(default=list, verbose_name="arrêts")),
                (
                    "departure_time",
                    models.DateTimeField(verbose_name="heure de départ"),
                ),
                ("arrival_time", models.DateTimeField(verbose_name="heure d'arrivée")),
                (
                    "frequency",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="fréquence"
                    ),
                ),
                (
                    "days_of_week",
                    models.JSONField(default=list, verbose_name="jours de la semaine"),
                ),
                ("max_capacity", models.IntegerField(verbose_name="capacité maximale")),
                (
                    "current_bookings",
                    models.IntegerField(
                        default=0, verbose_name="réservations actuelles"
                    ),
                ),
                (
                    "price_per_person",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        verbose_name="prix par personne",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("SCHEDULED", "Programmée"),
                            ("IN_PROGRESS", "En cours"),
                            ("COMPLETED", "Terminée"),
                            ("CANCELLED", "Annulée"),
                        ],
                        default="SCHEDULED",
                        max_length=20,
                        verbose_name="statut",
                    ),
                ),
                (
                    "is_recurring",
                    models.BooleanField(default=False, verbose_name="récurrent"),
                ),
                (
                    "cancellation_policy",
                    models.TextField(blank=True, verbose_name="politique d'annulation"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                (
                    "boat",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shuttles",
                        to="boats.boat",
                    ),
                ),
                (
                    "captain",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shuttles",
                        to="accounts.captain",
                    ),
                ),
                (
                    "establishment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shuttles",
                        to="accounts.establishment",
                    ),
                ),
            ],
            options={
                "verbose_name": "navette",
                "verbose_name_plural": "navettes",
                "ordering": ["departure_time"],
            },
        ),
        migrations.CreateModel(
            name="Trip",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "start_location",
                    models.CharField(max_length=255, verbose_name="lieu de départ"),
                ),
                (
                    "end_location",
                    models.CharField(max_length=255, verbose_name="lieu d'arrivée"),
                ),
                (
                    "scheduled_start_time",
                    models.DateTimeField(verbose_name="heure de départ prévue"),
                ),
                (
                    "scheduled_end_time",
                    models.DateTimeField(verbose_name="heure d'arrivée prévue"),
                ),
                (
                    "actual_start_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="heure de départ réelle"
                    ),
                ),
                (
                    "actual_end_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="heure d'arrivée réelle"
                    ),
                ),
                (
                    "passenger_count",
                    models.IntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="nombre de passagers",
                    ),
                ),
                (
                    "passenger_names",
                    models.JSONField(default=list, verbose_name="noms des passagers"),
                ),
                (
                    "special_requests",
                    models.TextField(blank=True, verbose_name="demandes spéciales"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "En attente"),
                            ("ACCEPTED", "Acceptée"),
                            ("REJECTED", "Refusée"),
                            ("IN_PROGRESS", "En cours"),
                            ("COMPLETED", "Terminée"),
                            ("CANCELLED", "Annulée"),
                        ],
                        default="PENDING",
                        max_length=20,
                        verbose_name="statut",
                    ),
                ),
                (
                    "current_location",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="position actuelle"
                    ),
                ),
                (
                    "tracking_data",
                    models.JSONField(default=list, verbose_name="données de suivi"),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="prix de base"
                    ),
                ),
                (
                    "additional_charges",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="frais supplémentaires",
                    ),
                ),
                (
                    "tip",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="pourboire",
                    ),
                ),
                (
                    "total_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="prix total"
                    ),
                ),
                (
                    "payment_status",
                    models.CharField(
                        default="PENDING",
                        max_length=20,
                        verbose_name="statut du paiement",
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="méthode de paiement"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                (
                    "cancellation_reason",
                    models.TextField(blank=True, verbose_name="raison d'annulation"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="notes")),
                (
                    "boat",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trips",
                        to="boats.boat",
                    ),
                ),
                (
                    "captain",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trips",
                        to="accounts.captain",
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trips",
                        to="accounts.client",
                    ),
                ),
                (
                    "establishment",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trips",
                        to="accounts.establishment",
                    ),
                ),
            ],
            options={
                "verbose_name": "course",
                "verbose_name_plural": "courses",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ShuttleBooking",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "booking_reference",
                    models.CharField(
                        default=trips.shared_payments.ShuttleBooking.generate_booking_ref,
                        max_length=20,
                        unique=True,
                        verbose_name="référence de réservation",
                    ),
                ),
                (
                    "number_of_seats",
                    models.PositiveIntegerField(
                        default=1, verbose_name="nombre de places"
                    ),
                ),
                (
                    "seat_numbers",
                    models.JSONField(
                        blank=True,
                        default=list,
                        null=True,
                        verbose_name="numéros de siège",
                    ),
                ),
                (
                    "passenger_names",
                    models.JSONField(default=list, verbose_name="noms des passagers"),
                ),
                (
                    "special_requests",
                    models.TextField(blank=True, verbose_name="demandes spéciales"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("RESERVED", "Réservée"),
                            ("PAID", "Payée"),
                            ("CHECKED_IN", "Embarquée"),
                            ("COMPLETED", "Terminée"),
                            ("CANCELLED", "Annulée"),
                            ("REFUNDED", "Remboursée"),
                        ],
                        default="RESERVED",
                        max_length=20,
                        verbose_name="statut",
                    ),
                ),
                (
                    "amount_paid",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="montant payé",
                    ),
                ),
                (
                    "transaction_id",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="ID de transaction",
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="méthode de paiement"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shuttle_bookings",
                        to="accounts.client",
                    ),
                ),
                (
                    "shuttle",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookings",
                        to="trips.shuttle",
                    ),
                ),
            ],
            options={
                "verbose_name": "réservation de navette",
                "verbose_name_plural": "réservations de navette",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="SharedPaymentInvitation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "invitee_email",
                    models.EmailField(max_length=254, verbose_name="email de l'invité"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="montant"
                    ),
                ),
                (
                    "token",
                    models.CharField(
                        default=uuid.uuid4,
                        max_length=100,
                        unique=True,
                        verbose_name="token d'invitation",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "En attente"),
                            ("ACCEPTED", "Acceptée"),
                            ("DECLINED", "Refusée"),
                            ("EXPIRED", "Expirée"),
                            ("PAID", "Payée"),
                        ],
                        default="PENDING",
                        max_length=20,
                        verbose_name="statut",
                    ),
                ),
                ("message", models.TextField(blank=True, verbose_name="message")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="expire le")),
                (
                    "invitee",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="received_payment_invitations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "inviter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_payment_invitations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "trip",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shared_payment_invitations",
                        to="trips.trip",
                    ),
                ),
            ],
            options={
                "verbose_name": "invitation de paiement partagé",
                "verbose_name_plural": "invitations de paiement partagé",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Location",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        decimal_places=6, max_digits=9, verbose_name="latitude"
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        decimal_places=6, max_digits=9, verbose_name="longitude"
                    ),
                ),
                (
                    "accuracy",
                    models.FloatField(blank=True, null=True, verbose_name="précision"),
                ),
                (
                    "speed",
                    models.FloatField(blank=True, null=True, verbose_name="vitesse"),
                ),
                (
                    "heading",
                    models.FloatField(blank=True, null=True, verbose_name="cap"),
                ),
                (
                    "altitude",
                    models.FloatField(blank=True, null=True, verbose_name="altitude"),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="horodatage"),
                ),
                (
                    "trip",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="locations",
                        to="trips.trip",
                    ),
                ),
            ],
            options={
                "verbose_name": "position",
                "verbose_name_plural": "positions",
                "ordering": ["-timestamp"],
            },
        ),
    ]
