





# 🚐 Workflow Complet : Navette Gratuite avec Capitaines Indépendants

## Résumé du Processus

Ce document décrit le processus complet et automatisé de gestion des navettes gratuites avec capitaines indépendants dans Commodore, tel qu’implémenté dans le backend.



### Payload complet pour créer une demande de navette gratuite

```json
{
  "departure_location": {
    "city_name": "Port de Cannes",
    "coordinates": {
      "latitude": 43.5528,
      "longitude": 7.0174
    },
    "timestamp": "2025-06-11T06:26:25+02:00"
  },
  "arrival_location": {
    "city_name": "Restaurant La Plage",
    "coordinates": {
      "latitude": 43.53,
      "longitude": 7.04
    },
    "timestamp": "2025-06-11T06:26:25+02:00"
  },
  "passenger_count": 2,
  "establishment": 5,
  "departure_date": "2025-06-13",
  "departure_time": "19:30:00",
  "message": "Nous aimerions réserver une table pour 20h"
}
```

### Workflow complet détaillé

#### 1. Création de la demande

- **Acteur** : Client
- **Endpoint** : `POST /api/trips/requests/shuttle/`
- **Payload** : Voir ci-dessus
- **Réponse** : Confirmation que la demande a été créée et sera traitée par l'établissement
  Réponse :

```json
{
  "trip_request": {
    "id": 48,
    "departure_location": {
      "city_name": "Calavi",
      "coordinates": {
        "latitude": 43.5528,
        "longitude": 7.0174
      },
      "timestamp": "2025-06-20T06:26:25+02:00"
    },
    "arrival_location": {
      "city_name": "Djeffa",
      "coordinates": {
        "latitude": 20.53,
        "longitude": 7.04
      },
      "timestamp": "2025-06-11T06:26:25+02:00"
    },
    "client": {
      "user": {
        "id": 161,
        "email": "<EMAIL>",
        "first_name": "Marie",
        "last_name": "Dubois",
        "phone_number": "+33123456789",
        "type": "CLIENT",
        "profile_picture": "https://cdn.commodore.com/profiles/marie.jpg",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": "1990-05-14",
      "nationality": "FR",
      "preferred_language": "fr",
      "emergency_contact_name": "Jean Dubois Armel",
      "emergency_contact_phone": "+33698765432"
    },
    "distance_km": "2560.02",
    "establishment": 211,
    "establishment_details": {
      "user": {
        "id": 211,
        "email": "<EMAIL>",
        "first_name": "",
        "last_name": "",
        "phone_number": "+33612345678",
        "type": "ESTABLISHMENT",
        "profile_picture": "",
        "is_active": true
      },
      "name": "Nobu",
      "type": "RESTAURANT",
      "address": "Route de l'Escalet, 83350 Ramatuelle",
      "description": "Niché au cœur de la baie de Ramatuelle, le Blue Wave Beach Club vous accueille dans un cadre idyllique mêlant élégance et détente.",
      "main_photo": "/media/https%3A/cdn.commodore.com/establishments/nobu_main.jpg",
      "secondary_photos": [
        "https://cdn.commodore.com/establishments/nobu_1.jpg",
        "https://cdn.commodore.com/establishments/nobu_2.jpg",
        "https://cdn.commodore.com/establishments/nobu_3.jpg"
      ],
      "wallet_balance": "0.00",
      "business_name": "",
      "business_type": "",
      "registration_number": "",
      "tax_id": "",
      "opening_hours": {},
      "services_offered": [],
      "average_rating": "0.00",
      "location_coordinates": "",
      "website": "",
      "social_media": {}
    },
    "trip_type": "SHUTTLE",
    "status": "PENDING",
    "passenger_count": 2,
    "created_at": "2025-06-19T17:13:40.366426+02:00",
    "updated_at": "2025-06-19T17:13:40.377123+02:00",
    "expires_at": "2025-06-19T17:23:40.366426+02:00",
    "departure_date": "2025-06-19",
    "departure_time": "19:30:00",
    "message": "Nous aimerions réserver une table pour 20h"
  },
  "message": "Demande de navette créée. L'établissement sera notifié.",
  "distance_to_establishment": "2560.02 km"
}
```

#### 2. Notification de l'établissement

- **Acteur** : Système
- **Endpoint interne** : Utilise le service de notification
- **Contenu** : L'établissement reçoit une notification avec les détails de la demande de navette

#### 3. Traitement par l'établissement

- **Acteur** : Établissement
- **Endpoint** : `GET /api/establishments/shuttle-requests/`
- **Réponse** : Liste des demandes de navettes en attente

```json
{
	"status": "success",
	"data": {
		"items": [
			{
				"id": "76",
				"type": "SHUTTLE",
				"date": "2025-07-22",
				"time": "19:30",
				"status": "En attente",
				"departure": "GOTAME BAD ",
				"destination": "Restaurant La Plage de  kétou",
				"client_name": "Marie Dubois",
				"client_phone": "+33123456789",
				"passengers": 4,
				"amount": 0.0,
				"message": "Nous aimerions réserver une table pour 20h",
				"created_at": "2025-06-25T10:30:15.813561+00:00"
			},
			{
				"id": "75",
				"type": "SHUTTLE",
				"date": "2025-07-22",
				"time": "19:30",
				"status": "En attente",
				"departure": "GOTAME BAD ",
				"destination": "Restaurant La Plage de  kétou",
				"client_name": "Marie Dubois",
				"client_phone": "+33123456789",
				"passengers": 4,
				"amount": 0.0,
				"message": "Nous aimerions réserver une table pour 20h",
				"created_at": "2025-06-25T08:24:26.388680+00:00"
			},
			{
				"id": "74",
				"type": "SHUTTLE",
				"date": "2025-07-22",
				"time": "19:30",
				"status": "En attente",
				"departure": "GOTAME BAD ",
				"destination": "Restaurant La Plage de  kétou",
				"client_name": "Marie Dubois",
				"client_phone": "+33123456789",
				"passengers": 4,
				"amount": 0.0,
				"message": "Nous aimerions réserver une table pour 20h",
				"created_at": "2025-06-25T08:20:16.417265+00:00"
			}
		],
		"pagination": {
			"page": 1,
			"limit": 20,
			"total": 3
		}
	}
}
```

-**Endpoint d'assignation** : `GET /api/boats/` -**Response** :retournera le bateau du batelier que tu as créé.

```json
[
	{
		"id": 86,
		"name": "Beneteau Flyer Armel 7.7 SUNdeck Charlie",
		"registration_number": "MA-3455-CMC",
		"boat_type": "CLASSIC",
		"capacity": 10,
		"status": "AVAILABLE",
		"captain_name": "Armel Smith",
		"location": "Zone par défaut"
	}
]
```

- **Endpoint d'assignation** : `POST /api/establishments/shuttle-requests/{request_id}/assign/`
- **Payload** :
  ```json
  {
    "boatman_id": 12,
    "boat_id": 5,
    "message": "Merci de prendre en charge cette navette"
  }
  ```
- **Réponse** : Confirmation que la navette a été assignée au batelier
{
	"status": "success",
	"message": "Demande acceptée et course créée",
	"trip_id": "33"
}




### Option B : Rechercher un capitaine indépendant

#### a. Génération automatique des devis
- **Endpoint** : `GET /api/establishments/quotes/{quote_id}/choose/`
- **Action** : Génération dynamique d’un devis pour chaque capitaine indépendant disponible.
- **Réponse** :
  ```json
  {
    "status": "success",
    "data": {
      "quotes": [
        {
          "captain": {
            "id": 42,
            "name": "Jules Lefevre",
            "average_rating": 4.5
          },
          "boat": null, // ou détails si déjà affecté
          "base_price": 38.00,
          "details": "Capitaine Jules Lefevre, 12 ans d'expérience",
          "estimated_time": "00:30:00"
        }
      ]
    }
  }





Sélection d’un devis
Endpoint : POST /api/establishments/quotes/{quote_id}/choose/
Réponse :
json
{
  "success": true,
  "message": "Demande envoyée au capitaine",
  "trip": { ... },
  "next_step": "Attendez la réponse du capitaine (délai: 10 minutes)"
}
c. Acceptation de la course par le capitaine
Endpoint : POST /api/trips/{trip_id}/accept/
Réponse :
json
{
  "message": "Course acceptée",
  "trip_id": 456
}
d. Paiement de la course
Endpoint : POST /api/trips/{trip_id}/payment/
Payload :
json
{
  "payment_method": "card",
  "amount": 38.00
}
Réponse :
json
{
  "message": "Paiement effectué"
}
e. Annulation de la course (ajouté)
Endpoint : POST /api/trips/{trip_id}/cancel/
Payload :
json
{
  "reason": "Le client a annulé la course"
}
Réponse :
json
{
  "message": "Course annulée",
  "trip_id": 456
}
f. Notifications automatiques
À chaque étape, le système envoie une notification à tous les acteurs concernés.
g. Cas d’indisponibilité
Si aucun capitaine n’est disponible, la réponse de /quotes/ sera :
json
{
  "status": "success",
  "data": {
    "quotes": []
  },
  "message": "Aucun capitaine indépendant disponible pour cette demande."
