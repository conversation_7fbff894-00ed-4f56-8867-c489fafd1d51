"""
Module de gestion des paiements pour les capitaines.

Ce module contient les vues pour la gestion des méthodes de paiement,
codes de vérification et processus de paiement pour les capitaines.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.core.cache import cache
from django.core.mail import send_mail
from django.conf import settings
import random
import string
import stripe
from decimal import Decimal


class PaymentMethodView(APIView):
    """
    Endpoint pour ajouter/mettre à jour la méthode de paiement.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        POST /api/payments/payment-method/
        
        Ajoute ou met à jour la méthode de paiement du capitaine.
        """
        
        try:
            if not hasattr(request.user, 'captain'):
                return Response({
                    'status': 'error',
                    'message': 'Seuls les capitaines peuvent gérer leurs méthodes de paiement'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Récupérer les données de la carte
            cardholder = request.data.get('cardholder')
            card_number = request.data.get('card_number')
            expiry_date = request.data.get('expiry_date')
            cvv = request.data.get('cvv')
            
            # Validations basiques
            if not all([cardholder, card_number, expiry_date, cvv]):
                return Response({
                    'status': 'error',
                    'message': 'Tous les champs de la carte sont requis'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validation format carte (simple)
            if len(card_number.replace(' ', '')) < 13:
                return Response({
                    'status': 'error',
                    'message': 'Numéro de carte invalide'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validation CVV
            if len(cvv) < 3 or len(cvv) > 4:
                return Response({
                    'status': 'error',
                    'message': 'CVV invalide'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validation date d'expiration (MM/YY)
            try:
                month, year = expiry_date.split('/')
                if int(month) < 1 or int(month) > 12:
                    raise ValueError
            except (ValueError, IndexError):
                return Response({
                    'status': 'error',
                    'message': 'Date d\'expiration invalide (format: MM/YY)'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Simuler l'enregistrement de la méthode de paiement
            # En production, utiliser Stripe pour créer un PaymentMethod
            captain = request.user.captain
            
            # Stocker les informations (en production, stocker seulement l'ID Stripe)
            payment_method_data = {
                'cardholder': cardholder,
                'last_four': card_number[-4:],
                'expiry_date': expiry_date,
                'brand': 'visa'  # Déterminer automatiquement en production
            }
            
            # Sauvegarder dans les métadonnées du capitaine
            if not hasattr(captain, 'payment_methods'):
                captain.payment_methods = []
            
            # Mettre à jour ou ajouter
            captain.payment_methods = [payment_method_data]  # Simplification: une seule carte
            captain.save()
            
            return Response({
                'status': 'success',
                'message': 'Méthode de paiement ajoutée/mise à jour avec succès'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Erreur lors de l\'ajout de la méthode de paiement: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProcessPaymentView(APIView):
    """
    Endpoint pour traiter un paiement.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        POST /api/payments/process-payment/
        
        Traite un paiement pour le capitaine.
        """
        
        try:
            if not hasattr(request.user, 'captain'):
                return Response({
                    'status': 'error',
                    'message': 'Seuls les capitaines peuvent effectuer des paiements'
                }, status=status.HTTP_403_FORBIDDEN)
            
            amount = request.data.get('amount')
            payment_method_id = request.data.get('payment_method_id')
            
            if not amount or not payment_method_id:
                return Response({
                    'status': 'error',
                    'message': 'Montant et méthode de paiement requis'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                amount = Decimal(str(amount))
                if amount <= 0:
                    raise ValueError
            except (ValueError, TypeError):
                return Response({
                    'status': 'error',
                    'message': 'Montant invalide'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Simuler le traitement du paiement
            # En production, utiliser Stripe PaymentIntent
            transaction_id = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
            
            # Créer une transaction dans le système
            from .models import Transaction, Wallet
            from decimal import Decimal
            
            # Récupérer ou créer le portefeuille de l'utilisateur
            wallet, _ = Wallet.objects.get_or_create(user=request.user)
            
            # Convertir le montant en Decimal
            amount_decimal = Decimal(str(amount))
            
            # Créer la transaction
            transaction = Transaction.objects.create(
                wallet=wallet,
                amount=amount_decimal,
                type='DEBIT',
                description=f'Paiement de {amount}€',
                balance_after=wallet.balance - amount_decimal,
                metadata={
                    'payment_method_id': payment_method_id,
                    'reference': transaction_id,
                    'transaction_type': 'PAYMENT'
                }
            )
            
            return Response({
                'status': 'success',
                'message': 'Paiement effectué avec succès',
                'transaction_id': transaction_id
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Erreur lors du traitement du paiement: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SendVerificationCodeView(APIView):
    """
    Endpoint pour envoyer un code de vérification.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        POST /api/payments/send-verification-code/
        
        Envoie un code de vérification par email ou SMS.
        """
        
        try:
            contact_method = request.data.get('contact_method')  # 'email' ou 'phone'
            contact_value = request.data.get('contact_value')
            
            if not contact_method or not contact_value:
                return Response({
                    'status': 'error',
                    'message': 'Méthode de contact et valeur requises'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Générer un code de vérification
            verification_code = ''.join(random.choices(string.digits, k=4))
            
            # Stocker le code en cache (expire en 10 minutes)
            cache_key = f'verification_code_{request.user.id}_{contact_method}'
            cache.set(cache_key, verification_code, 600)  # 10 minutes
            
            if contact_method == 'email':
                # Envoyer par email
                try:
                    send_mail(
                        'Code de vérification Commodore',
                        f'Votre code de vérification est: {verification_code}',
                        settings.DEFAULT_FROM_EMAIL,
                        [contact_value],
                        fail_silently=False,
                    )
                except Exception as e:
                    return Response({
                        'status': 'error',
                        'message': 'Erreur lors de l\'envoi de l\'email'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            elif contact_method == 'phone':
                # Simuler l'envoi SMS (en production, utiliser un service SMS)
                print(f"SMS envoyé à {contact_value}: Code {verification_code}")
            
            else:
                return Response({
                    'status': 'error',
                    'message': 'Méthode de contact invalide (email ou phone)'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            return Response({
                'status': 'success',
                'message': 'Code de vérification envoyé'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Erreur lors de l\'envoi du code: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VerifyCodeView(APIView):
    """
    Endpoint pour vérifier un code.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        POST /api/payments/verify-code/
        
        Vérifie le code de vérification saisi.
        """
        
        try:
            code = request.data.get('code')
            contact_method = request.data.get('contact_method', 'email')  # Par défaut email
            
            if not code:
                return Response({
                    'status': 'error',
                    'message': 'Code de vérification requis'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Récupérer le code stocké
            cache_key = f'verification_code_{request.user.id}_{contact_method}'
            stored_code = cache.get(cache_key)
            
            if not stored_code:
                return Response({
                    'status': 'error',
                    'message': 'Code expiré ou invalide'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if code != stored_code:
                return Response({
                    'status': 'error',
                    'message': 'Code incorrect'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Code valide, le supprimer du cache
            cache.delete(cache_key)
            
            # Marquer comme vérifié (optionnel)
            if hasattr(request.user, 'captain'):
                captain = request.user.captain
                if contact_method == 'email':
                    captain.email_verified = True
                elif contact_method == 'phone':
                    captain.phone_verified = True
                captain.save()
            
            return Response({
                'status': 'success',
                'message': 'Code vérifié avec succès'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Erreur lors de la vérification: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
