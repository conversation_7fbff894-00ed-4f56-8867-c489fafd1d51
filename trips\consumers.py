import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.utils import timezone
from .models import Trip, Location

class LocationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.trip_id = self.scope['url_route']['kwargs']['trip_id']
        self.trip_group_name = f'trip_{self.trip_id}'
        self.user = self.scope['user']

        # Vérifier si l'utilisateur a accès à ce trajet
        if await self.can_access_trip():
            await self.channel_layer.group_add(
                self.trip_group_name,
                self.channel_name
            )
            await self.accept()
        else:
            await self.close()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.trip_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        data = json.loads(text_data)
        
        # Si c'est le capitaine qui envoie sa position
        if await self.is_captain() and 'location' in data:
            location = data['location']
            
            # Sauvegarder la position
            await self.save_location(location)
            
            # Envoyer la mise à jour à tous les membres du groupe
            await self.channel_layer.group_send(
                self.trip_group_name,
                {
                    'type': 'location_update',
                    'location': location
                }
            )

    async def location_update(self, event):
        location = event['location']
        
        # Envoyer la mise à jour au WebSocket
        await self.send(text_data=json.dumps({
            'type': 'location_update',
            'location': location,
            'timestamp': timezone.now().isoformat()
        }))

    @database_sync_to_async
    def can_access_trip(self):
        try:
            trip = Trip.objects.get(id=self.trip_id)
            return (trip.client == self.user or 
                   trip.captain == self.user or 
                   trip.establishment and trip.establishment.owner == self.user)
        except Trip.DoesNotExist:
            return False

    @database_sync_to_async
    def is_captain(self):
        try:
            trip = Trip.objects.get(id=self.trip_id)
            return trip.captain == self.user
        except Trip.DoesNotExist:
            return False

    @database_sync_to_async
    def save_location(self, location_data):
        trip = Trip.objects.get(id=self.trip_id)
        Location.objects.create(
            trip=trip,
            latitude=location_data['latitude'],
            longitude=location_data['longitude'],
            accuracy=location_data.get('accuracy'),
            speed=location_data.get('speed'),
            heading=location_data.get('heading'),
            altitude=location_data.get('altitude'),
            timestamp=timezone.now()
        )
