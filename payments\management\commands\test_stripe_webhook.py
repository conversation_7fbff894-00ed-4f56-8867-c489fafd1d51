from django.core.management.base import BaseCommand, CommandError
import stripe
from django.conf import settings
import json
import requests
import hmac
import hashlib
import time
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Simule un événement webhook Stripe pour tester la mise à jour des paiements'

    def add_arguments(self, parser):
        parser.add_argument('payment_id', type=str, help='ID du paiement à mettre à jour')
        parser.add_argument('event_type', type=str, choices=['payment_intent.succeeded', 'payment_intent.payment_failed', 'checkout.session.completed'], help='Type d\'événement à simuler')
        parser.add_argument('--stripe_id', type=str, help='ID Stripe du paiement (si différent de l\'ID du paiement)')

    def handle(self, *args, **options):
        from payments.models import Payment

        payment_id = options['payment_id']
        event_type = options['event_type']
        stripe_id = options.get('stripe_id')

        try:
            payment = Payment.objects.get(id=payment_id)
        except Payment.DoesNotExist:
            raise CommandError(f'Paiement avec ID "{payment_id}" non trouvé')

        # Utiliser l'ID Stripe du paiement s'il est fourni, sinon utiliser celui du paiement
        if not stripe_id:
            if payment.stripe_payment_id:
                stripe_id = payment.stripe_payment_id
            else:
                raise CommandError(f'Le paiement {payment_id} n\'a pas d\'ID Stripe. Veuillez fournir un ID Stripe avec --stripe_id')

        # Créer un événement simulé
        if event_type == 'payment_intent.succeeded':
            event_data = self._create_payment_intent_succeeded_event(payment_id, stripe_id)
        elif event_type == 'payment_intent.payment_failed':
            event_data = self._create_payment_intent_failed_event(payment_id, stripe_id)
        elif event_type == 'checkout.session.completed':
            event_data = self._create_checkout_session_completed_event(payment_id, stripe_id)

        # Envoyer l'événement au webhook
        webhook_url = 'http://localhost:8000/api/payments/webhook/'
        
        # Créer une signature valide
        timestamp = int(time.time())
        payload = f"{timestamp}.{json.dumps(event_data)}"
        signature = hmac.new(
            settings.STRIPE_WEBHOOK_SECRET.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        headers = {
            'Content-Type': 'application/json',
            'Stripe-Signature': f"t={timestamp},v1={signature}"
        }
        
        response = requests.post(webhook_url, json=event_data, headers=headers)
        
        if response.status_code == 200:
            self.stdout.write(self.style.SUCCESS(f'Événement {event_type} envoyé avec succès au webhook'))
        else:
            self.stdout.write(self.style.ERROR(f'Erreur lors de l\'envoi de l\'événement au webhook: {response.status_code} {response.text}'))

    def _create_payment_intent_succeeded_event(self, payment_id, stripe_id):
        """Crée un événement payment_intent.succeeded simulé"""
        return {
            "id": f"evt_{self._generate_random_id()}",
            "object": "event",
            "api_version": "2025-04-30.basil",
            "created": int(time.time()),
            "data": {
                "object": {
                    "id": stripe_id,
                    "object": "payment_intent",
                    "amount": 1000,
                    "currency": "eur",
                    "status": "succeeded",
                    "metadata": {
                        "payment_id": payment_id
                    },
                    "charges": {
                        "data": [
                            {
                                "id": f"ch_{self._generate_random_id()}",
                                "object": "charge",
                                "amount": 1000,
                                "currency": "eur",
                                "receipt_url": f"https://pay.stripe.com/receipts/{self._generate_random_id()}"
                            }
                        ]
                    },
                    "payment_method_types": ["card"]
                }
            },
            "type": "payment_intent.succeeded"
        }

    def _create_payment_intent_failed_event(self, payment_id, stripe_id):
        """Crée un événement payment_intent.payment_failed simulé"""
        return {
            "id": f"evt_{self._generate_random_id()}",
            "object": "event",
            "api_version": "2025-04-30.basil",
            "created": int(time.time()),
            "data": {
                "object": {
                    "id": stripe_id,
                    "object": "payment_intent",
                    "amount": 1000,
                    "currency": "eur",
                    "status": "requires_payment_method",
                    "metadata": {
                        "payment_id": payment_id
                    },
                    "last_payment_error": {
                        "code": "card_declined",
                        "message": "Votre carte a été refusée."
                    }
                }
            },
            "type": "payment_intent.payment_failed"
        }

    def _create_checkout_session_completed_event(self, payment_id, stripe_id):
        """Crée un événement checkout.session.completed simulé"""
        return {
            "id": f"evt_{self._generate_random_id()}",
            "object": "event",
            "api_version": "2025-04-30.basil",
            "created": int(time.time()),
            "data": {
                "object": {
                    "id": f"cs_{self._generate_random_id()}",
                    "object": "checkout.session",
                    "amount_total": 1000,
                    "currency": "eur",
                    "payment_intent": stripe_id,
                    "metadata": {
                        "payment_id": payment_id
                    },
                    "customer": f"cus_{self._generate_random_id()}"
                }
            },
            "type": "checkout.session.completed"
        }

    def _generate_random_id(self, length=24):
        """Génère un ID aléatoire pour simuler les IDs Stripe"""
        import random
        import string
        return ''.join(random.choice(string.ascii_lowercase + string.digits) for _ in range(length))
