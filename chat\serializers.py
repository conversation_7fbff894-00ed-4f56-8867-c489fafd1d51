from rest_framework import serializers
from .models import Chat<PERSON><PERSON>, Message, ChatbotSession, ChatbotMessage
from accounts.serializers import UserSerializer
from trips.serializers import TripSerializer

class MessageSerializer(serializers.ModelSerializer):
    sender = UserSerializer(read_only=True)
    read_by = UserSerializer(many=True, read_only=True)
    delivered_to = UserSerializer(many=True, read_only=True)

    class Meta:
        model = Message
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'deleted_at')

    def validate_content(self, value):
        if not value.strip() and not self.initial_data.get('attachment'):
            raise serializers.ValidationError(
                "Le message ne peut pas être vide sans pièce jointe"
            )
        return value

class ChatRoomSerializer(serializers.ModelSerializer):
    participants = UserSerializer(many=True, read_only=True)
    trip = TripSerializer(read_only=True)
    last_message = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()

    class Meta:
        model = ChatRoom
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def get_last_message(self, obj):
        last_message = obj.messages.filter(deleted_at__isnull=True).last()
        if last_message:
            return MessageSerializer(last_message).data
        return None

    def get_unread_count(self, obj):
        user = self.context.get('request').user
        return obj.messages.filter(
            deleted_at__isnull=True,
            is_read=False
        ).exclude(sender=user).count()

class ChatRoomListSerializer(serializers.ModelSerializer):
    """Sérialiseur simplifié pour la liste des salons de discussion"""
    other_participant = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()

    class Meta:
        model = ChatRoom
        fields = ('id', 'name', 'type', 'other_participant', 'last_message',
                 'unread_count', 'updated_at')

    def get_other_participant(self, obj):
        user = self.context.get('request').user
        other = obj.participants.exclude(id=user.id).first()
        if other:
            return {
                'id': other.id,
                'name': other.get_full_name(),
                'profile_picture': other.profile_picture.url if other.profile_picture else None
            }
        return None

    def get_last_message(self, obj):
        last_message = obj.messages.filter(deleted_at__isnull=True).last()
        if last_message:
            return {
                'content': last_message.content[:50],
                'created_at': last_message.created_at,
                'is_read': last_message.is_read
            }
        return None

    def get_unread_count(self, obj):
        user = self.context.get('request').user
        return obj.messages.filter(
            deleted_at__isnull=True,
            is_read=False
        ).exclude(sender=user).count()

class ChatbotMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatbotMessage
        fields = '__all__'
        read_only_fields = ('created_at',)

class ChatbotSessionSerializer(serializers.ModelSerializer):
    messages = ChatbotMessageSerializer(many=True, read_only=True)
    user = UserSerializer(read_only=True)

    class Meta:
        model = ChatbotSession
        fields = '__all__'
        read_only_fields = ('created_at', 'last_interaction', 'session_id')
