import os
import django
import sys
from decimal import Decimal

# Configurer l'environnement Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from payments.models import Wallet, Transaction
from django.utils import timezone

def add_funds_to_wallet(user_id, amount):
    """
    Ajoute des fonds au portefeuille d'un utilisateur.
    
    Args:
        user_id (int): ID de l'utilisateur
        amount (float): Montant à ajouter
    
    Returns:
        bool: True si l'opération a réussi, False sinon
    """
    try:
        wallet = Wallet.objects.get(user_id=user_id)
        
        # Créer une transaction
        old_balance = wallet.balance
        decimal_amount = Decimal(str(amount))
        
        transaction = Transaction.objects.create(
            wallet=wallet,
            amount=decimal_amount,
            type='CREDIT',
            description=f'Ajout manuel de fonds: {amount}',
            balance_after=old_balance + decimal_amount,
        )
        
        # Mettre à jour le solde du portefeuille
        wallet.balance += decimal_amount
        wallet.last_transaction_at = timezone.now()
        wallet.save()
        
        print(f"Ajout de {amount} au portefeuille de l'utilisateur {user_id} réussi!")
        print(f"Nouveau solde: {wallet.balance}")
        return True
    
    except Wallet.DoesNotExist:
        print(f"Portefeuille introuvable pour l'utilisateur {user_id}")
        return False
    except Exception as e:
        print(f"Erreur lors de l'ajout de fonds: {str(e)}")
        return False

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print("Usage: python add_wallet_funds.py <user_id> <amount>")
        sys.exit(1)
    
    try:
        user_id = int(sys.argv[1])
        amount = float(sys.argv[2])
        add_funds_to_wallet(user_id, amount)
    except ValueError:
        print("L'ID utilisateur doit être un entier et le montant doit être un nombre.")
        sys.exit(1)
