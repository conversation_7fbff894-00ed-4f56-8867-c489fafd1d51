#!/usr/bin/env python
"""
Test de l'API complète pour les navettes
"""

import os
import django
import json
import requests

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, Client, User
from django.contrib.auth import authenticate
from rest_framework.authtoken.models import Token
from datetime import date, timedelta

def test_shuttle_api():
    print("🚀 Test de l'API navette complète")
    print("=" * 50)
    
    # 1. Trouver un client pour l'authentification
    client = Client.objects.first()
    if not client:
        print("❌ Aucun client trouvé")
        return False
    
    # 2. Obtenir ou créer un token
    token, created = Token.objects.get_or_create(user=client.user)
    print(f"✅ Token obtenu pour: {client.user.email}")
    
    # 3. Trouver un établissement avec coordonnées
    establishment = Establishment.objects.filter(
        longitude__isnull=False, 
        latitude__isnull=False
    ).first()
    
    if not establishment:
        print("❌ Aucun établissement avec coordonnées trouvé")
        return False
    
    print(f"✅ Établissement: {establishment.name}")
    print(f"   Coordonnées: {establishment.latitude}, {establishment.longitude}")
    
    # 4. Payload utilisateur (SANS arrival_location)
    payload = {
        "departure_location": {
            "city_name": "Port de Cannes",
            "coordinates": {
                "latitude": 43.5528,
                "longitude": 7.0174
            },
            "timestamp": "2025-06-26T11:30:00+00:00"
        },
        "passenger_count": 2,
        "establishment": establishment.user.id,
        "departure_date": (date.today() + timedelta(days=1)).strftime('%Y-%m-%d'),
        "departure_time": "19:30:00",
        "message": "Test API navette sans arrival_location"
    }
    
    print("\n📝 Payload envoyé (sans arrival_location):")
    print(json.dumps(payload, indent=2))
    
    # 5. Appel API
    headers = {
        'Authorization': f'Token {token.key}',
        'Content-Type': 'application/json'
    }
    
    try:
        print("\n🌐 Appel API POST /api/trips/requests/shuttle/")
        response = requests.post(
            'http://localhost:8000/api/trips/requests/shuttle/',
            json=payload,
            headers=headers,
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print("✅ Navette créée avec succès!")
            
            trip_request = data.get('trip_request', {})
            distance = data.get('distance_to_establishment')
            
            print(f"   Distance calculée: {distance}")
            print(f"   ID navette: {trip_request.get('id')}")
            
            # Vérifier les nouvelles coordonnées dans la réponse
            coordinates = data.get('coordinates', {})
            route_info = data.get('route_info', {})

            if coordinates:
                print("   ✅ Coordonnées incluses dans la réponse:")
                departure = coordinates.get('departure', {})
                destination = coordinates.get('destination', {})

                print(f"      Départ: Lat {departure.get('latitude')}, Lon {departure.get('longitude')}")
                print(f"      Destination: Lat {destination.get('latitude')}, Lon {destination.get('longitude')}")

            if route_info:
                print("   ✅ Informations de route:")
                print(f"      De: {route_info.get('departure_location')}")
                print(f"      Vers: {route_info.get('destination_location')}")
                print(f"      Distance: {route_info.get('distance_km')} km")

            return True
        else:
            print(f"❌ Erreur API: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Détails: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   Réponse: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur Django")
        print("   Assurez-vous que le serveur Django est démarré (python manage.py runserver)")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = test_shuttle_api()
    print(f"\n{'🎉 Test API réussi!' if success else '❌ Test API échoué'}")
