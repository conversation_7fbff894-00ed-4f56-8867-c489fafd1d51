# Guide d'intégration des API Commodore pour l'application mobile

Ce guide détail<PERSON> comment intégrer les API Commodore dans l'application mobile, avec des exemples pratiques et des recommandations.

## Table des matières

1. [Configuration initiale](#configuration-initiale)
2. [Authentification](#authentification)
3. [Gestion des profils et favoris](#gestion-des-profils-et-favoris)
4. [Réservation de courses](#réservation-de-courses)
5. [Gestion des paiements](#gestion-des-paiements)
6. [Utilisation du chatbot et RAG](#utilisation-du-chatbot-et-rag)
7. [Notifications push](#notifications-push)
8. [Bonnes pratiques et optimisation](#bonnes-pratiques-et-optimisation)

## Configuration initiale

### URL de base

```
# Environnement de production
BASE_URL = "https://api.commodore.com/api/"

# Environnement de développement
BASE_URL = "http://localhost:8000/api/"
```

### Headers communs

```
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept-Language: fr
```

### Gestion des erreurs

Toutes les API suivent un format d'erreur standardisé :

```json
{
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Email ou mot de passe incorrect",
    "details": {}
  }
}
```

## Authentification

### Processus d'inscription et connexion

1. **Inscription** : `POST /auth/register/`
2. **Vérification email** : `POST /auth/verify-email/`
3. **Connexion** : `POST /auth/login/`
4. **Rafraîchissement du token** : `POST /auth/token/refresh/`

### Exemple de workflow d'authentification

```swift
// Swift (iOS)
func register(email: String, password: String, userType: String) {
    let params = [
        "email": email,
        "password": password,
        "user_type": userType
    ]
    
    AF.request("\(BASE_URL)auth/register/", 
               method: .post, 
               parameters: params, 
               encoding: JSONEncoding.default)
        .responseJSON { response in
            // Traiter la réponse
        }
}
```

```kotlin
// Kotlin (Android)
fun register(email: String, password: String, userType: String) {
    val params = JSONObject().apply {
        put("email", email)
        put("password", password)
        put("user_type", userType)
    }
    
    val request = JsonObjectRequest(
        Request.Method.POST, 
        "${BASE_URL}auth/register/", 
        params,
        { response -> 
            // Traiter la réponse
        },
        { error -> 
            // Gérer l'erreur
        }
    )
    
    requestQueue.add(request)
}
```

### Stockage sécurisé des tokens

Utilisez le KeyStore (Android) ou le Keychain (iOS) pour stocker les tokens JWT de façon sécurisée.

## Gestion des profils et favoris

### Récupérer et modifier le profil

```swift
// Swift (iOS)
func getUserProfile() {
    AF.request("\(BASE_URL)accounts/profile/", 
               method: .get, 
               headers: authHeaders)
        .responseDecodable(of: UserProfile.self) { response in
            if let profile = response.value {
                // Utiliser le profil
            }
        }
}
```

### Gérer les favoris

```kotlin
// Kotlin (Android) - Ajouter un capitaine en favori
fun addFavoriteCaptain(captainId: Int, notes: String) {
    val params = JSONObject().apply {
        put("captain", captainId)
        put("notes", notes)
    }
    
    val request = JsonObjectRequest(
        Request.Method.POST, 
        "${BASE_URL}accounts/favorites/captains/", 
        params,
        { response -> 
            // Traiter la réponse
        },
        { error -> 
            // Gérer l'erreur
        }
    )
    
    requestQueue.add(request)
}
```

## Réservation de courses

### Flux de réservation

1. **Rechercher des bateaux disponibles** : `GET /boats/available/`
2. **Créer une course** : `POST /trips/`
3. **Suivre le statut** : `GET /trips/{id}/`
4. **Payer la course** : `POST /payments/trips/{trip_id}/`
5. **Évaluer la course** : `POST /reviews/`

### Exemple de réservation de course

```swift
// Swift (iOS)
func createTrip(startLocation: String, endLocation: String, scheduledTime: Date, passengers: Int) {
    let formatter = ISO8601DateFormatter()
    let params: [String: Any] = [
        "start_location": startLocation,
        "end_location": endLocation,
        "scheduled_start_time": formatter.string(from: scheduledTime),
        "passenger_count": passengers
    ]
    
    AF.request("\(BASE_URL)trips/", 
               method: .post, 
               parameters: params, 
               encoding: JSONEncoding.default,
               headers: authHeaders)
        .responseJSON { response in
            // Traiter la réponse
        }
}
```

## Gestion des paiements

### Ajouter une méthode de paiement

```kotlin
// Kotlin (Android)
fun addPaymentMethod(token: String) {
    val params = JSONObject().apply {
        put("token", token)
    }
    
    val request = JsonObjectRequest(
        Request.Method.POST, 
        "${BASE_URL}payments/methods/", 
        params,
        { response -> 
            // Traiter la réponse
        },
        { error -> 
            // Gérer l'erreur
        }
    )
    
    requestQueue.add(request)
}
```

### Gérer le portefeuille

```swift
// Swift (iOS)
func addCreditsToWallet(amount: Double, paymentMethodId: String) {
    let params: [String: Any] = [
        "amount": amount,
        "payment_method_id": paymentMethodId
    ]
    
    AF.request("\(BASE_URL)payments/wallet/add_credits/", 
               method: .post, 
               parameters: params, 
               encoding: JSONEncoding.default,
               headers: authHeaders)
        .responseJSON { response in
            // Traiter la réponse
        }
}
```

## Utilisation du chatbot et RAG

### Intégration du chatbot Meta-Llama-3

```swift
// Swift (iOS)
func sendChatbotMessage(message: String, sessionId: String) {
    let params: [String: Any] = [
        "message": message,
        "session_id": sessionId
    ]
    
    AF.request("\(BASE_URL)chat/chatbot/message/", 
               method: .post, 
               parameters: params, 
               encoding: JSONEncoding.default,
               headers: authHeaders)
        .responseJSON { response in
            // Traiter la réponse
        }
}
```

### Utilisation du système RAG

```kotlin
// Kotlin (Android)
fun askRagSystem(query: String, sessionId: String, userProfile: String) {
    val params = JSONObject().apply {
        put("query", query)
        put("session_id", sessionId)
        put("user_profile", userProfile)
        put("max_sources", 3)
        put("locale", "fr")
    }
    
    val request = JsonObjectRequest(
        Request.Method.POST, 
        "${BASE_URL}rag/chat/", 
        params,
        { response -> 
            // Traiter la réponse avec la réponse générée et les sources
        },
        { error -> 
            // Gérer l'erreur
        }
    )
    
    requestQueue.add(request)
}
```

### Support hors ligne

```swift
// Swift (iOS)
func downloadOfflineData(userProfile: String) {
    AF.request("\(BASE_URL)rag/offline/bundle/?profile=\(userProfile)", 
               method: .get, 
               headers: authHeaders)
        .responseData { response in
            if let data = response.value {
                // Stocker les données pour une utilisation hors ligne
            }
        }
}
```

## Notifications push

### Enregistrement de l'appareil

```kotlin
// Kotlin (Android)
fun registerDevice(deviceToken: String, deviceType: String) {
    val params = JSONObject().apply {
        put("registration_id", deviceToken)
        put("type", deviceType)
        put("device_id", getUniqueDeviceId())
    }
    
    val request = JsonObjectRequest(
        Request.Method.POST, 
        "${BASE_URL}notifications/devices/", 
        params,
        { response -> 
            // Traiter la réponse
        },
        { error -> 
            // Gérer l'erreur
        }
    )
    
    requestQueue.add(request)
}
```

### Traitement des notifications

Sur iOS, configurez le projet pour recevoir les notifications APNS.
Sur Android, utilisez Firebase Cloud Messaging (FCM).

```swift
// Swift (iOS)
func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
    if let notificationType = userInfo["type"] as? String {
        switch notificationType {
        case "NEW_MESSAGE":
            // Traiter la notification de nouveau message
            break
        case "TRIP_STATUS_CHANGED":
            // Traiter la notification de changement de statut
            break
        default:
            break
        }
    }
    
    completionHandler(.newData)
}
```

## Bonnes pratiques et optimisation

### Mise en cache

Utilisez une stratégie de mise en cache pour les données qui changent peu fréquemment :

```swift
// Swift (iOS) avec Alamofire
let cachePolicy = URLRequest.CachePolicy.returnCacheDataElseLoad
let configuration = URLSessionConfiguration.default
configuration.requestCachePolicy = cachePolicy
let session = Session(configuration: configuration)

session.request("\(BASE_URL)boats/")
    .responseDecodable(of: [Boat].self) { response in
        // Traiter la réponse
    }
```

### Gestion de la connexion internet

Détectez les changements de connectivité et adaptez le comportement de l'application :

```kotlin
// Kotlin (Android)
class NetworkChangeReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        if (isNetworkAvailable(context)) {
            // Connecté à Internet, synchroniser les données
            syncOfflineChanges()
        } else {
            // Pas de connexion, passer en mode hors ligne
            switchToOfflineMode()
        }
    }
    
    private fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetworkInfo = connectivityManager.activeNetworkInfo
        return activeNetworkInfo != null && activeNetworkInfo.isConnected
    }
}
```

### Sécurité

- Vérifiez l'intégrité du certificat SSL/TLS pour éviter les attaques man-in-the-middle
- Ne stockez jamais les mots de passe en clair
- Renouvelez régulièrement les tokens JWT
- Nettoyez les données sensibles lors de la déconnexion

```swift
// Swift (iOS)
func logout() {
    // Supprimer le token
    KeychainWrapper.standard.removeObject(forKey: "jwt_token")
    
    // Effacer les données sensibles
    UserDefaults.standard.removeObject(forKey: "user_profile")
    UserDefaults.standard.synchronize()
    
    // Rediriger vers l'écran de connexion
    navigateToLoginScreen()
}
```

## Conclusion

Ce guide couvre les principaux points d'intégration des API Commodore dans votre application mobile. Pour toute question ou assistance, contactez l'équipe backend à <EMAIL>.

N'oubliez pas de consulter les fichiers `endpoints.txt` dans chaque répertoire d'application pour des détails exhaustifs sur les API disponibles.
