"""
Commande de gestion Django pour corriger les problèmes de sécurité des portefeuilles.
Cette commande vérifie et corrige les incohérences dans les soldes des portefeuilles.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from decimal import Decimal
from payments.models import Wallet, Transaction, Payment
from payments.wallet_security_service import WalletSecurityService
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Corrige les problèmes de sécurité des portefeuilles et vérifie la cohérence des données'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Affiche les corrections sans les appliquer',
        )
        parser.add_argument(
            '--fix-balances',
            action='store_true',
            help='Corrige les soldes incohérents',
        )
        parser.add_argument(
            '--audit-transactions',
            action='store_true',
            help='Audit complet des transactions',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔍 AUDIT DE SÉCURITÉ DES PORTEFEUILLES')
        )
        self.stdout.write('=' * 60)

        dry_run = options['dry_run']
        fix_balances = options['fix_balances']
        audit_transactions = options['audit_transactions']

        if dry_run:
            self.stdout.write(
                self.style.WARNING('MODE DRY-RUN: Aucune modification ne sera appliquée')
            )

        # 1. Vérifier les soldes négatifs
        self.check_negative_balances(dry_run, fix_balances)

        # 2. Vérifier la cohérence des transactions
        self.check_transaction_consistency(dry_run, fix_balances)

        # 3. Audit complet si demandé
        if audit_transactions:
            self.audit_all_transactions(dry_run)

        # 4. Vérifier les montants en float
        self.check_float_amounts(dry_run, fix_balances)

        self.stdout.write(
            self.style.SUCCESS('\n✅ AUDIT TERMINÉ')
        )

    def check_negative_balances(self, dry_run, fix_balances):
        """Vérifie et corrige les soldes négatifs"""
        self.stdout.write('\n🚨 VÉRIFICATION DES SOLDES NÉGATIFS')
        self.stdout.write('-' * 40)

        negative_wallets = Wallet.objects.filter(balance__lt=0)
        
        if not negative_wallets.exists():
            self.stdout.write(
                self.style.SUCCESS('✅ Aucun solde négatif trouvé')
            )
            return

        self.stdout.write(
            self.style.ERROR(f'❌ {negative_wallets.count()} portefeuilles avec solde négatif trouvés')
        )

        for wallet in negative_wallets:
            self.stdout.write(
                f'  - Wallet {wallet.id} (User: {wallet.user.email}): {wallet.balance}€'
            )

            if fix_balances and not dry_run:
                # Corriger en remettant le solde à 0 et créer une transaction d'ajustement
                with transaction.atomic():
                    adjustment_amount = abs(wallet.balance)
                    
                    # Utiliser le service sécurisé pour créditer
                    try:
                        WalletSecurityService.credit_wallet_secure(
                            wallet_id=wallet.id,
                            amount=adjustment_amount,
                            description="Correction automatique - solde négatif",
                            reference=f"FIX_NEGATIVE_{wallet.id}",
                            user=wallet.user
                        )
                        
                        self.stdout.write(
                            self.style.SUCCESS(f'    ✅ Corrigé: +{adjustment_amount}€')
                        )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'    ❌ Erreur lors de la correction: {str(e)}')
                        )

    def check_transaction_consistency(self, dry_run, fix_balances):
        """Vérifie la cohérence entre les transactions et les soldes"""
        self.stdout.write('\n🔍 VÉRIFICATION DE LA COHÉRENCE DES TRANSACTIONS')
        self.stdout.write('-' * 50)

        inconsistent_wallets = []

        for wallet in Wallet.objects.all():
            # Calculer le solde basé sur les transactions
            credits = Transaction.objects.filter(
                wallet=wallet,
                type=Transaction.TransactionType.CREDIT
            ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')

            debits = Transaction.objects.filter(
                wallet=wallet,
                type=Transaction.TransactionType.DEBIT
            ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')

            calculated_balance = credits - debits
            actual_balance = wallet.balance

            if abs(calculated_balance - actual_balance) > Decimal('0.01'):
                inconsistent_wallets.append({
                    'wallet': wallet,
                    'calculated': calculated_balance,
                    'actual': actual_balance,
                    'difference': actual_balance - calculated_balance
                })

        if not inconsistent_wallets:
            self.stdout.write(
                self.style.SUCCESS('✅ Tous les soldes sont cohérents')
            )
            return

        self.stdout.write(
            self.style.ERROR(f'❌ {len(inconsistent_wallets)} incohérences trouvées')
        )

        for item in inconsistent_wallets:
            wallet = item['wallet']
            self.stdout.write(
                f'  - Wallet {wallet.id} (User: {wallet.user.email}):'
            )
            self.stdout.write(
                f'    Calculé: {item["calculated"]}€, Actuel: {item["actual"]}€, '
                f'Différence: {item["difference"]}€'
            )

            if fix_balances and not dry_run:
                # Corriger le solde
                with transaction.atomic():
                    try:
                        if item["difference"] > 0:
                            # Débiter l'excédent
                            WalletSecurityService.debit_wallet_secure(
                                wallet_id=wallet.id,
                                amount=item["difference"],
                                description="Correction automatique - excédent",
                                reference=f"FIX_EXCESS_{wallet.id}",
                                user=wallet.user
                            )
                        else:
                            # Créditer le manque
                            WalletSecurityService.credit_wallet_secure(
                                wallet_id=wallet.id,
                                amount=abs(item["difference"]),
                                description="Correction automatique - manque",
                                reference=f"FIX_SHORTAGE_{wallet.id}",
                                user=wallet.user
                            )
                        
                        self.stdout.write(
                            self.style.SUCCESS(f'    ✅ Corrigé')
                        )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'    ❌ Erreur lors de la correction: {str(e)}')
                        )

    def audit_all_transactions(self, dry_run):
        """Audit complet de toutes les transactions"""
        self.stdout.write('\n📊 AUDIT COMPLET DES TRANSACTIONS')
        self.stdout.write('-' * 40)

        total_transactions = Transaction.objects.count()
        total_payments = Payment.objects.count()
        total_wallets = Wallet.objects.count()

        self.stdout.write(f'📈 Statistiques générales:')
        self.stdout.write(f'  - Transactions: {total_transactions}')
        self.stdout.write(f'  - Paiements: {total_payments}')
        self.stdout.write(f'  - Portefeuilles: {total_wallets}')

        # Vérifier les transactions orphelines
        orphan_transactions = Transaction.objects.filter(payment__isnull=True)
        if orphan_transactions.exists():
            self.stdout.write(
                self.style.WARNING(f'⚠️  {orphan_transactions.count()} transactions sans paiement associé')
            )

        # Vérifier les paiements sans transaction
        orphan_payments = Payment.objects.filter(transactions__isnull=True)
        if orphan_payments.exists():
            self.stdout.write(
                self.style.WARNING(f'⚠️  {orphan_payments.count()} paiements sans transaction associée')
            )

    def check_float_amounts(self, dry_run, fix_balances):
        """Vérifie les montants qui pourraient avoir des erreurs de float"""
        self.stdout.write('\n🔢 VÉRIFICATION DES ERREURS DE FLOAT')
        self.stdout.write('-' * 40)

        # Vérifier les montants avec plus de 2 décimales
        problematic_transactions = Transaction.objects.extra(
            where=["amount::text ~ '\\.\\d{3,}'"]
        )

        if not problematic_transactions.exists():
            self.stdout.write(
                self.style.SUCCESS('✅ Aucune erreur de précision trouvée')
            )
            return

        self.stdout.write(
            self.style.WARNING(f'⚠️  {problematic_transactions.count()} transactions avec erreurs de précision')
        )

        for trans in problematic_transactions[:10]:  # Limiter l'affichage
            self.stdout.write(
                f'  - Transaction {trans.id}: {trans.amount}€'
            )

        if fix_balances and not dry_run:
            self.stdout.write(
                self.style.WARNING('⚠️  Correction des erreurs de float non implémentée automatiquement')
            )
            self.stdout.write('    Veuillez vérifier manuellement ces transactions')
