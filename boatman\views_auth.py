"""
Vues d'authentification pour l'espace batelier.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.authtoken.models import Token
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate, get_user_model
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
import random
import string
import logging
from accounts.serializers import UserProfileSerializer

from accounts.models import Captain
from notifications.services import create_notification

User = get_user_model()
logger = logging.getLogger(__name__)


class BoatmanLoginView(APIView):
    """
    Connexion du batelier.
    
    POST /api/boatman/login/
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """Authentifier un batelier"""
        
        email = request.data.get('email')
        password = request.data.get('password')
        
        if not email or not password:
            return Response({
                'status': 'error',
                'error': 'Email et mot de passe requis',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Authentifier l'utilisateur
        user = authenticate(email=email, password=password)
        
        if not user:
            return Response({
                'status': 'error',
                'error': 'Identifiants invalides',
                'error_code': 401
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # 1. Vérifier que l'utilisateur est un capitaine
        if not hasattr(user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)
            
        # 2. Vérifier que l'utilisateur est bien de type CAPTAIN
        if user.type != User.Types.CAPTAIN:
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Type de compte incorrect',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)
            
        # 3. Vérifier que le capitaine est bien un batelier (enregistré par un établissement)
        captain = user.captain
        if not captain.registered_by_establishment:
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Interface réservée aux BATELIERS enregistrés par un établissement',
                'details': 'Les capitaines non enregistrés par un établissement doivent utiliser l\'interface capitaine',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)
        
        captain = user.captain
        
        # Créer ou récupérer le token (clé API héritée)
        token, created = Token.objects.get_or_create(user=user)

        # Générer les JWT access / refresh tokens pour l'authentification mobile moderne
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        # Sérialiser les données utilisateur complète (identique à /api/login)
        user_data = UserProfileSerializer(user).data
        
        # Mettre à jour la dernière connexion
        user.last_login = timezone.now()
        user.save()
        
        # Vérifier si c'est la première connexion ou si un changement de mot de passe est requis
        if user.first_login or user.requires_password_change:
            # Générer un code à 6 chiffres
            verification_code = ''.join(random.choices(string.digits, k=6))
            
            # Stocker le code dans la session ou une table temporaire
            # Pour simplifier, nous utilisons un token spécial avec le code
            user.verification_code = verification_code
            user.verification_code_created = timezone.now()
            user.save()
            
            # Envoyer le code par email
            try:
                send_mail(
                    subject="Votre code de vérification Commodore",
                    message=f"Votre code de vérification est: {verification_code}\n\nCe code est valable pendant 10 minutes.",
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=False
                )
                code_sent = True
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi du code de vérification: {str(e)}")
                code_sent = False
            
            return Response({
                'access_token': str(access_token),
                'refresh_token': str(refresh),
                'user': user_data,
                'first_login': user.first_login,
                'verification_required': True,
                'code_sent': code_sent,
                'contact_masked': user.email[:3] + '***' + user.email.split('@')[0][-2:] + '@' + user.email.split('@')[1]
            })
        
        # Connexion normale (pas de première connexion ni de changement de mot de passe requis)
        return Response({
            'access_token': str(access_token),
            'refresh_token': str(refresh),
            'user': user_data,
            'first_login': user.first_login
        })


class BoatmanForgotPasswordView(APIView):
    """
    Mot de passe oublié pour le batelier.
    
    POST /api/boatman/forgot-password/
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """Initier la réinitialisation du mot de passe"""
        
        contact = request.data.get('contact')
        
        if not contact:
            return Response({
                'status': 'error',
                'error': 'Contact (email ou téléphone) requis',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Déterminer si c'est un email ou un téléphone
        if '@' in contact:
            # C'est un email
            try:
                user = User.objects.get(email=contact)
            except User.DoesNotExist:
                return Response({
                    'status': 'error',
                    'error': 'Aucun compte trouvé avec cet email',
                    'error_code': 404
                }, status=status.HTTP_404_NOT_FOUND)
        else:
            # C'est un téléphone
            try:
                user = User.objects.get(phone_number=contact)
            except User.DoesNotExist:
                return Response({
                    'status': 'error',
                    'error': 'Aucun compte trouvé avec ce numéro',
                    'error_code': 404
                }, status=status.HTTP_404_NOT_FOUND)
        
        # Vérifier que c'est un capitaine
        if not hasattr(user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Compte batelier non trouvé',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Générer un code de vérification
        verification_code = ''.join(random.choices(string.digits, k=4))
        
        # Récupérer le capitaine pour l'utiliser plus tard
        captain = user.captain
        
        # Stocker le code directement dans les champs de l'utilisateur pour la cohérence
        user.verification_code = verification_code
        user.verification_code_created = timezone.now()
        user.save()
        
        # Envoyer le code
        if '@' in contact:
            # Envoyer par email
            try:
                send_mail(
                    subject='Code de vérification Commodore',
                    message=f'Votre code de vérification est : {verification_code}\n\nCe code expire dans 10 minutes.',
                    from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                    recipient_list=[contact],
                    fail_silently=False,
                )
                method = 'email'
            except Exception as e:
                logger.error(f"Erreur envoi email: {str(e)}")
                return Response({
                    'status': 'error',
                    'error': 'Erreur lors de l\'envoi de l\'email',
                    'error_code': 500
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            # Envoyer par SMS (simulation)
            method = 'sms'
            logger.info(f"SMS envoyé à {contact}: {verification_code}")
        
        return Response({
            'status': 'success',
            'data': {
                'captain_id': str(captain.user.id),  # Utiliser user.id comme clé primaire
                'contact_method': method,
                'masked_contact': self._mask_contact(contact)
            },
            'message': 'Code de vérification envoyé',
            'timestamp': timezone.now().isoformat()
        })
    
    def _mask_contact(self, contact):
        """Masquer le contact pour la sécurité"""
        if '@' in contact:
            # Email
            parts = contact.split('@')
            username = parts[0]
            domain = parts[1]
            masked_username = username[:2] + '*' * (len(username) - 4) + username[-2:] if len(username) > 4 else username[:1] + '*' * (len(username) - 1)
            return f"{masked_username}@{domain}"
        else:
            # Téléphone
            return contact[:3] + '*' * (len(contact) - 6) + contact[-3:]


class BoatmanVerifyCodeView(APIView):
    """
    Vérifier le code de vérification.
    
    POST /api/boatman/verify-code/
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """Vérifier le code de vérification"""
        
        captain_id = request.data.get('captain_id')
        code = request.data.get('code')
        
        if not captain_id or not code:
            return Response({
                'status': 'error',
                'error': 'captain_id et code requis',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Rechercher l'utilisateur par son ID
            user = User.objects.get(id=captain_id)
            # Vérifier que c'est un capitaine
            if not hasattr(user, 'captain'):
                return Response({
                    'status': 'error',
                    'error': 'Utilisateur non associé à un profil capitaine',
                    'error_code': 404
                }, status=status.HTTP_404_NOT_FOUND)
            captain = user.captain
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'error': 'Utilisateur non trouvé',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Vérifier le code
        stored_code = user.verification_code
        code_created = user.verification_code_created
        
        if not stored_code or not code_created:
            return Response({
                'status': 'error',
                'error': 'Aucun code de vérification en cours',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Vérifier l'expiration (10 minutes)
        if timezone.now() > code_created + timezone.timedelta(minutes=10):
            return Response({
                'status': 'error',
                'error': 'Code de vérification expiré',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Vérifier le code
        if stored_code != code:
            return Response({
                'status': 'error',
                'error': 'Code de vérification invalide',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Code valide, nettoyer les champs de vérification
        user.verification_code = None
        user.verification_code_created = None
        
        # Générer un token temporaire pour le changement de mot de passe
        # Limité à 6 caractères pour respecter la contrainte du modèle
        temp_token = ''.join(random.choices(string.ascii_letters + string.digits, k=6))
        
        # Stocker le token dans un champ temporaire de l'utilisateur
        # Pour simplifier, nous utilisons le champ verification_code pour stocker le token
        # et verification_code_created pour stocker la date d'expiration
        user.verification_code = temp_token
        user.verification_code_created = timezone.now()
        user.save()
        
        return Response({
            'status': 'success',
            'data': {
                'captain_id': str(user.id),  # Utiliser l'ID utilisateur
                'temp_token': temp_token,
                'expires_in': 1800  # 30 minutes en secondes
            },
            'message': 'Code vérifié avec succès',
            'timestamp': timezone.now().isoformat()
        })


class BoatmanChangePasswordView(APIView):
    """
    Changer le mot de passe du batelier.
    
    POST /api/boatman/change-password/
    """
    permission_classes = [AllowAny]  # Permettre l'accès sans authentification pour le reset de mot de passe

    def post(self, request):
        """Changer le mot de passe"""
        
        captain_id = request.data.get('captain_id')
        old_password = request.data.get('old_password')
        new_password = request.data.get('new_password')
        confirm_password = request.data.get('confirm_password')
        temp_token = request.data.get('temp_token')  # Pour reset sans ancien mot de passe
        
        if not captain_id:
            return Response({
                'status': 'error',
                'error': 'captain_id requis',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not new_password or not confirm_password:
            return Response({
                'status': 'error',
                'error': 'Nouveau mot de passe et confirmation requis',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if new_password != confirm_password:
            return Response({
                'status': 'error',
                'error': 'Les mots de passe ne correspondent pas',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validation du mot de passe
        if len(new_password) < 8:
            return Response({
                'status': 'error',
                'error': 'Le mot de passe doit contenir au moins 8 caractères',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Rechercher l'utilisateur par son ID
            user = User.objects.get(id=captain_id)
            # Vérifier que c'est un capitaine
            if not hasattr(user, 'captain'):
                return Response({
                    'status': 'error',
                    'error': 'Utilisateur non associé à un profil capitaine',
                    'error_code': 404
                }, status=status.HTTP_404_NOT_FOUND)
            captain = user.captain
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'error': 'Utilisateur non trouvé',
                'error_code': 404
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Vérifier l'autorisation
        if temp_token:
            # Reset avec token temporaire
            stored_token = user.verification_code
            code_created = user.verification_code_created
            
            if not stored_token or stored_token != temp_token:
                return Response({
                    'status': 'error',
                    'error': 'Token de réinitialisation invalide',
                    'error_code': 400
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if code_created:
                # Vérifier l'expiration (30 minutes)
                if timezone.now() > code_created + timezone.timedelta(minutes=30):
                    return Response({
                        'status': 'error',
                        'error': 'Token de réinitialisation expiré',
                        'error_code': 400
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Nettoyer le token temporaire
            user.verification_code = None
            user.verification_code_created = None
            user.save()
            
        else:
            # Changement normal avec ancien mot de passe
            if not old_password:
                return Response({
                    'status': 'error',
                    'error': 'Ancien mot de passe requis',
                    'error_code': 400
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if not user.check_password(old_password):
                return Response({
                    'status': 'error',
                    'error': 'Ancien mot de passe incorrect',
                    'error_code': 400
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Changer le mot de passe
        user.set_password(new_password)
        
        # Réinitialiser les indicateurs de première connexion et de changement de mot de passe requis
        user.first_login = False
        user.requires_password_change = False
        user.save()
        
        return Response({
            'status': 'success',
            'message': 'Mot de passe mis à jour avec succès',
            'timestamp': timezone.now().isoformat()
        })
