"""
Tests complets pour l'application trips.

Ce module contient tous les tests unitaires et d'intégration
pour les fonctionnalités de gestion des courses.
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from decimal import Decimal
from unittest.mock import patch, MagicMock
import json

from accounts.models import Client, Captain, Establishment
from boats.models import Boat
from .models import Trip, TripRequest, TripQuote, SimpleTrip, HourlyTrip, ShuttleTrip
from payments.models import Transaction

User = get_user_model()


class TripModelTestCase(TestCase):
    """Tests pour les modèles Trip et classes associées"""

    def setUp(self):
        """Configuration initiale pour les tests"""
        # Créer des utilisateurs
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='<PERSON>',
            last_name='Doe',
            phone_number='+***********'
        )

        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Captain',
            last_name='Jack',
            phone_number='+***********'
        )

        # Créer les profils
        self.client = Client.objects.create(user=self.client_user)
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans d\'expérience',
            license_number='LIC001'
        )

        # Créer un bateau
        self.boat = Boat.objects.create(
            captain=self.captain,
            name='Test Boat',
            boat_type='classic',
            capacity=8
        )

    def test_trip_creation(self):
        """Test de création d'une course simple"""
        trip = Trip.objects.create(
            client=self.client,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Port de Cannes',
            end_location='Plage des sables',
            passenger_count=4,
            base_price=Decimal('35.00'),
            total_price=Decimal('40.00')
        )

        self.assertEqual(trip.status, Trip.Status.PENDING)
        self.assertEqual(trip.client, self.client)
        self.assertEqual(trip.captain, self.captain)
        self.assertEqual(trip.boat, self.boat)
        self.assertEqual(trip.total_price, Decimal('40.00'))

    def test_trip_status_transitions(self):
        """Test des transitions de statut d'une course"""
        trip = Trip.objects.create(
            client=self.client,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Port A',
            end_location='Port B',
            passenger_count=2,
            base_price=Decimal('25.00'),
            total_price=Decimal('25.00')
        )

        # Test transition PENDING -> ACCEPTED
        trip.status = Trip.Status.ACCEPTED
        trip.save()
        self.assertEqual(trip.status, Trip.Status.ACCEPTED)

        # Test transition ACCEPTED -> IN_PROGRESS
        trip.status = Trip.Status.IN_PROGRESS
        trip.save()
        self.assertEqual(trip.status, Trip.Status.IN_PROGRESS)

        # Test transition IN_PROGRESS -> COMPLETED
        trip.status = Trip.Status.COMPLETED
        trip.save()
        self.assertEqual(trip.status, Trip.Status.COMPLETED)

    def test_trip_price_calculation(self):
        """Test du calcul automatique des prix"""
        # Test avec distance simulée
        with patch('trips.models.calculate_distance_and_price') as mock_calc:
            mock_calc.return_value = (10.5, Decimal('42.00'))  # 10.5 km, 42€

            trip = Trip.objects.create(
                client=self.client,
                captain=self.captain,
                boat=self.boat,
                trip_type='COURSE_SIMPLE',
                start_location='Port de Cannes',
                end_location='Îles de Lérins',
                passenger_count=6
            )

            # Vérifier que le calcul a été appelé
            mock_calc.assert_called_once()
            self.assertEqual(trip.distance_km, 10.5)

    def test_qr_code_generation(self):
        """Test de génération automatique du QR code"""
        trip = Trip.objects.create(
            client=self.client,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Port A',
            end_location='Port B',
            passenger_count=2,
            base_price=Decimal('25.00'),
            total_price=Decimal('25.00')
        )

        # Le QR code devrait être généré automatiquement
        self.assertIsNotNone(trip.qr_code)
        self.assertTrue(trip.qr_code.startswith('data:image/png;base64,'))


class TripAPITestCase(APITestCase):
    """Tests pour les endpoints API de l'application trips"""

    def setUp(self):
        """Configuration initiale pour les tests API"""
        # Créer des utilisateurs
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='John',
            last_name='Doe'
        )

        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Captain',
            last_name='Jack'
        )

        # Créer les profils
        self.client = Client.objects.create(user=self.client_user)
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans',
            license_number='LIC001'
        )

        # Créer un bateau
        self.boat = Boat.objects.create(
            captain=self.captain,
            name='Test Boat',
            boat_type='classic',
            capacity=8
        )

        # Créer les tokens d'authentification
        self.client_token = Token.objects.create(user=self.client_user)
        self.captain_token = Token.objects.create(user=self.captain_user)

        # Configurer le client API
        self.api_client = APIClient()

    def test_create_trip_request(self):
        """Test de création d'une demande de course"""
        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.client_token.key)

        data = {
            'trip_type': 'COURSE_SIMPLE',
            'start_location': 'Port de Cannes',
            'end_location': 'Plage des sables',
            'passenger_count': 4,
            'scheduled_start_time': '2024-06-15T16:30:00Z',
            'special_requests': 'Anniversaire'
        }

        response = self.api_client.post('/api/trips/request/', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('request_id', response.data)
        self.assertIn('quotes', response.data)

    def test_choose_quote(self):
        """Test de choix d'un devis par le client"""
        # Créer une demande et un devis
        trip_request = TripRequest.objects.create(
            client=self.client,
            trip_type='COURSE_SIMPLE',
            start_location='Port A',
            end_location='Port B',
            passenger_count=2
        )

        quote = TripQuote.objects.create(
            trip_request=trip_request,
            captain=self.captain,
            boat=self.boat,
            price=Decimal('30.00'),
            estimated_duration=30
        )

        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.client_token.key)

        response = self.api_client.post(f'/api/trips/quotes/{quote.id}/choose/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('trip_id', response.data)

    def test_captain_accept_trip(self):
        """Test d'acceptation d'une course par le capitaine"""
        # Créer une course en attente
        trip = Trip.objects.create(
            client=self.client,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Port A',
            end_location='Port B',
            passenger_count=2,
            base_price=Decimal('25.00'),
            total_price=Decimal('25.00'),
            status=Trip.Status.PENDING
        )

        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

        response = self.api_client.post(f'/api/trips/{trip.id}/accept/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Vérifier que le statut a changé
        trip.refresh_from_db()
        self.assertEqual(trip.status, Trip.Status.ACCEPTED)

    def test_captain_reject_trip(self):
        """Test de refus d'une course par le capitaine"""
        # Créer une course en attente
        trip = Trip.objects.create(
            client=self.client,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Port A',
            end_location='Port B',
            passenger_count=2,
            base_price=Decimal('25.00'),
            total_price=Decimal('25.00'),
            status=Trip.Status.PENDING
        )

        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

        data = {'reason': 'Indisponible'}
        response = self.api_client.post(f'/api/trips/{trip.id}/reject/', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Vérifier que le statut a changé
        trip.refresh_from_db()
        self.assertEqual(trip.status, Trip.Status.REJECTED)
        self.assertEqual(trip.cancellation_reason, 'Indisponible')

    def test_trip_start(self):
        """Test de démarrage d'une course"""
        # Créer une course acceptée
        trip = Trip.objects.create(
            client=self.client,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Port A',
            end_location='Port B',
            passenger_count=2,
            base_price=Decimal('25.00'),
            total_price=Decimal('25.00'),
            status=Trip.Status.ACCEPTED
        )

        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

        response = self.api_client.post(f'/api/trips/{trip.id}/start/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Vérifier que le statut a changé
        trip.refresh_from_db()
        self.assertEqual(trip.status, Trip.Status.IN_PROGRESS)
        self.assertIsNotNone(trip.actual_start_time)

    def test_trip_complete(self):
        """Test de complétion d'une course"""
        # Créer une course en cours
        trip = Trip.objects.create(
            client=self.client,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Port A',
            end_location='Port B',
            passenger_count=2,
            base_price=Decimal('25.00'),
            total_price=Decimal('25.00'),
            status=Trip.Status.IN_PROGRESS
        )

        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

        response = self.api_client.post(f'/api/trips/{trip.id}/complete/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Vérifier que le statut a changé
        trip.refresh_from_db()
        self.assertEqual(trip.status, Trip.Status.COMPLETED)
        self.assertIsNotNone(trip.actual_end_time)

    def test_qr_code_verification(self):
        """Test de vérification de QR code"""
        # Créer une course avec QR code
        trip = Trip.objects.create(
            client=self.client,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Port A',
            end_location='Port B',
            passenger_count=2,
            base_price=Decimal('25.00'),
            total_price=Decimal('25.00')
        )

        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.client_token.key)

        data = {'trip_id': trip.id}
        response = self.api_client.post('/api/trips/verify-qr/', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['valid'])
        self.assertIn('ticket_info', response.data)
        self.assertEqual(response.data['ticket_info']['trip_id'], trip.id)

    def test_captain_dashboard(self):
        """Test du tableau de bord capitaine"""
        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

        response = self.api_client.get('/api/trips/captain/dashboard/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('dashboard', response.data)
        self.assertIn('captain', response.data['dashboard'])
        self.assertIn('boat', response.data['dashboard'])

    def test_captain_history(self):
        """Test de l'historique des courses du capitaine"""
        # Créer quelques courses
        for i in range(3):
            Trip.objects.create(
                client=self.client,
                captain=self.captain,
                boat=self.boat,
                trip_type='COURSE_SIMPLE',
                start_location=f'Port {i}',
                end_location=f'Destination {i}',
                passenger_count=2,
                base_price=Decimal('25.00'),
                total_price=Decimal('25.00'),
                status=Trip.Status.COMPLETED
            )

        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

        response = self.api_client.get('/api/trips/captain/history/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('trips', response.data)
        self.assertEqual(response.data['trips']['count'], 3)
        self.assertIn('statistics', response.data)


class WalletIntegrationTestCase(APITestCase):
    """Tests d'intégration pour le système de wallet et paiements"""

    def setUp(self):
        """Configuration initiale pour les tests wallet"""
        # Créer des utilisateurs
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='John',
            last_name='Doe'
        )

        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Captain',
            last_name='Jack'
        )

        # Créer les profils
        self.client = Client.objects.create(user=self.client_user)
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='5 ans',
            license_number='LIC001'
        )

        # Créer un bateau
        self.boat = Boat.objects.create(
            captain=self.captain,
            name='Test Boat',
            boat_type='classic',
            capacity=8
        )

        # Créer les tokens
        self.client_token = Token.objects.create(user=self.client_user)
        self.captain_token = Token.objects.create(user=self.captain_user)

        self.api_client = APIClient()

    @patch('payments.views_wallet.auto_credit_captain_on_trip_completion')
    def test_automatic_captain_credit_on_completion(self, mock_credit):
        """Test du crédit automatique du capitaine après complétion"""
        mock_credit.return_value = True

        # Créer une course
        trip = Trip.objects.create(
            client=self.client,
            captain=self.captain,
            boat=self.boat,
            trip_type='COURSE_SIMPLE',
            start_location='Port A',
            end_location='Port B',
            passenger_count=2,
            base_price=Decimal('25.00'),
            total_price=Decimal('25.00'),
            status=Trip.Status.IN_PROGRESS,
            payment_status='PAID'
        )

        # Compléter la course
        trip.status = Trip.Status.COMPLETED
        trip.save()

        # Vérifier que la fonction de crédit a été appelée
        mock_credit.assert_called_once_with(trip.id)

    def test_captain_wallet_view(self):
        """Test de consultation du wallet capitaine"""
        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

        response = self.api_client.get('/api/payments/wallet/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('balance', response.data)
        self.assertIn('transactions', response.data)


class ShuttleTestCase(APITestCase):
    """Tests pour les navettes gratuites"""

    def setUp(self):
        """Configuration pour les tests navettes"""
        # Créer un établissement
        self.establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Hotel',
            last_name='Paradise'
        )

        self.establishment = Establishment.objects.create(
            user=self.establishment_user,
            name='Hotel Paradise',
            establishment_type='HOTEL'
        )

        # Créer un client
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        self.client = Client.objects.create(user=self.client_user)

        # Tokens
        self.establishment_token = Token.objects.create(user=self.establishment_user)
        self.client_token = Token.objects.create(user=self.client_user)

        self.api_client = APIClient()

    def test_shuttle_request_creation(self):
        """Test de création d'une demande de navette"""
        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.client_token.key)

        data = {
            'trip_type': 'NAVETTES_GRATUITES',
            'start_location': 'Aéroport',
            'end_location': 'Hotel Paradise',
            'passenger_count': 2,
            'scheduled_start_time': '2024-06-15T16:30:00Z'
        }

        response = self.api_client.post('/api/trips/request/', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('request_id', response.data)

    def test_establishment_shuttle_management(self):
        """Test de gestion des navettes par l'établissement"""
        self.api_client.credentials(HTTP_AUTHORIZATION='Token ' + self.establishment_token.key)

        response = self.api_client.get('/api/trips/shuttle/list/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('shuttle_requests', response.data)
