"""
Vues du tableau de bord pour l'espace batelier.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Sum, Count, Q
from decimal import Decimal
from datetime import datetime, timedelta

from accounts.models import Captain
from trips.models import Trip
from payments.models import Payment, Wallet
from accounts.permissions import IsBoatman


class BoatmanDashboardView(APIView):
    """
    Tableau de bord du batelier.
    
    GET /api/boatman/dashboard/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def get(self, request):
        """Récupérer les données du tableau de bord"""
        
        # La permission IsBoatman s'en charge déjà
        captain = request.user.captain
        
        # Récupérer le portefeuille
        wallet = getattr(request.user, 'wallet', None)
        available_balance = wallet.balance if wallet else Decimal('0.00')
        
        # Statistiques des courses
        total_shuttles = Trip.objects.filter(captain=captain).count()
        completed_shuttles = Trip.objects.filter(
            captain=captain, 
            status='COMPLETED'
        ).count()
        
        # Pourboires totaux
        total_tips = Payment.objects.filter(
            user=request.user,
            type=Payment.PaymentType.TIP,
            status=Payment.Status.COMPLETED
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Courses à venir (prochaines 7 jours)
        upcoming_date = timezone.now() + timedelta(days=7)
        upcoming_shuttles = Trip.objects.filter(
            captain=captain,
            status__in=['PENDING', 'ACCEPTED'],
            scheduled_start_time__lte=upcoming_date
        ).select_related('client__user').order_by('scheduled_start_time')[:5]
        
        upcoming_shuttles_data = []
        for trip in upcoming_shuttles:
            upcoming_shuttles_data.append({
                'shuttle_id': str(trip.id),
                'date': trip.scheduled_start_time.isoformat(),
                'destination': trip.end_location,
                'passengers': trip.passenger_count,
                'client': trip.client.user.get_full_name() if trip.client else 'Client inconnu',
                'status': trip.status,
                'departure': trip.start_location,
                'estimated_duration': trip.estimated_duration or 30
            })
        
        # Statistiques de la semaine
        week_start = timezone.now() - timedelta(days=7)
        week_stats = {
            'shuttles_this_week': Trip.objects.filter(
                captain=captain,
                created_at__gte=week_start
            ).count(),
            'earnings_this_week': Payment.objects.filter(
                user=request.user,
                type__in=[Payment.PaymentType.TRIP, Payment.PaymentType.TIP],
                status=Payment.Status.COMPLETED,
                created_at__gte=week_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00'),
            'average_rating': float(captain.average_rating) if captain.average_rating else 0.0
        }
        
        # Courses du jour
        today = timezone.now().date()
        today_shuttles = Trip.objects.filter(
            captain=captain,
            scheduled_start_time__date=today
        ).count()
        
        # Prochaine course
        next_shuttle = Trip.objects.filter(
            captain=captain,
            status__in=['PENDING', 'ACCEPTED'],
            scheduled_start_time__gt=timezone.now()
        ).order_by('scheduled_start_time').first()
        
        next_shuttle_data = None
        if next_shuttle:
            next_shuttle_data = {
                'shuttle_id': str(next_shuttle.id),
                'date': next_shuttle.scheduled_start_time.isoformat(),
                'destination': next_shuttle.end_location,
                'departure': next_shuttle.start_location,
                'passengers': next_shuttle.passenger_count,
                'client': next_shuttle.client.user.get_full_name() if next_shuttle.client else 'Client inconnu',
                'time_until': self._calculate_time_until(next_shuttle.scheduled_start_time)
            }
        
        # Construction de la réponse pour coller à la maquette Commodore mobile
        courses_a_venir = []
        for trip in upcoming_shuttles:
            client_user = trip.client.user if trip.client else None
            courses_a_venir.append({
                'client_nom': client_user.get_full_name() if client_user else 'Client inconnu',
                'client_photo': client_user.profile_picture if client_user and client_user.profile_picture else '',
                'date_heure': trip.scheduled_start_time.strftime('%a. %d %b - %Hh%M'),
                'lieu_depart': trip.start_location,
                'lieu_arrivee': trip.end_location,
                'passagers': trip.passenger_count,
                'trip_id': str(trip.id),
            })

        return Response({
            'solde_disponible': float(available_balance),
            'courses_realisees': completed_shuttles,
            'pourboire_total': float(total_tips),
            'courses_a_venir': courses_a_venir,
            'nom_batelier': request.user.get_full_name(),
            'photo_batelier': request.user.profile_picture if request.user.profile_picture else '',
            'devise': 'EUR',
        })
    
    def _calculate_time_until(self, target_time):
        """Calculer le temps restant jusqu'à une heure cible"""
        now = timezone.now()
        if target_time <= now:
            return "Maintenant"
        
        diff = target_time - now
        
        if diff.days > 0:
            return f"Dans {diff.days} jour{'s' if diff.days > 1 else ''}"
        
        hours = diff.seconds // 3600
        minutes = (diff.seconds % 3600) // 60
        
        if hours > 0:
            return f"Dans {hours}h{minutes:02d}"
        else:
            return f"Dans {minutes} min"


class BoatmanAvailabilityView(APIView):
    """
    Gestion de la disponibilité du batelier.
    
    GET/POST /api/boatman/availability/
    """
    permission_classes = [IsAuthenticated, IsBoatman]

    def get(self, request):
        """Récupérer le statut de disponibilité"""
        
        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)
        
        captain = request.user.captain
        
        return Response({
            'status': 'success',
            'data': {
                'availability_status': captain.availability_status,
                'is_available': captain.is_available,
                'last_updated': captain.updated_at.isoformat() if hasattr(captain, 'updated_at') else None,
                'available_options': [
                    {'value': 'AVAILABLE', 'label': 'Disponible'},
                    {'value': 'BUSY', 'label': 'Occupé'},
                    {'value': 'OFFLINE', 'label': 'Hors ligne'},
                    {'value': 'MAINTENANCE', 'label': 'Maintenance'}
                ]
            },
            'message': 'Statut de disponibilité récupéré',
            'timestamp': timezone.now().isoformat()
        })

    def post(self, request):
        """Mettre à jour la disponibilité"""
        
        if not hasattr(request.user, 'captain'):
            return Response({
                'status': 'error',
                'error': 'Accès refusé - Compte batelier requis',
                'error_code': 403
            }, status=status.HTTP_403_FORBIDDEN)
        
        captain = request.user.captain
        new_status = request.data.get('availability_status')
        
        if not new_status:
            return Response({
                'status': 'error',
                'error': 'availability_status requis',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        valid_statuses = ['AVAILABLE', 'BUSY', 'OFFLINE', 'MAINTENANCE']
        if new_status not in valid_statuses:
            return Response({
                'status': 'error',
                'error': f'Statut invalide. Options valides: {", ".join(valid_statuses)}',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Mettre à jour le statut
        captain.availability_status = new_status
        captain.is_available = new_status == 'AVAILABLE'
        captain.save()
        
        # Mettre à jour les bateaux associés
        captain.boats.update(is_available=captain.is_available)
        
        return Response({
            'status': 'success',
            'data': {
                'availability_status': captain.availability_status,
                'is_available': captain.is_available,
                'updated_at': timezone.now().isoformat()
            },
            'message': f'Disponibilité mise à jour: {new_status}',
            'timestamp': timezone.now().isoformat()
        })
