"""
Validateurs personnalisés pour l'application establishments.
"""

import re
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from .constants import VALIDATION_LIMITS, ERROR_MESSAGES

User = get_user_model()


class BoatmanDataValidator:
    """Validateur pour les données d'enregistrement des bateliers"""
    
    @staticmethod
    def validate_email(email):
        """Valider l'email du batelier"""
        if not email:
            raise ValidationError("L'email est requis")
        
        # Vérifier le format de l'email
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValidationError("Format d'email invalide")
        
        # Vérifier l'unicité
        if User.objects.filter(email=email).exists():
            raise ValidationError(ERROR_MESSAGES['EMAIL_ALREADY_EXISTS'])
        
        return email
    
    @staticmethod
    def validate_phone_number(phone_number):
        """Valider le numéro de téléphone"""
        if not phone_number:
            raise ValidationError("Le numéro de téléphone est requis")
        
        # Format international basique
        phone_pattern = r'^\+?[1-9]\d{1,14}$'
        if not re.match(phone_pattern, phone_number.replace(' ', '').replace('-', '')):
            raise ValidationError("Format de numéro de téléphone invalide")
        
        return phone_number
    
    @staticmethod
    def validate_name(name, field_name):
        """Valider les noms (prénom/nom)"""
        if not name:
            raise ValidationError(f"Le {field_name} est requis")
        
        if len(name) < 2:
            raise ValidationError(f"Le {field_name} doit contenir au moins 2 caractères")
        
        if len(name) > 30:
            raise ValidationError(f"Le {field_name} ne peut pas dépasser 30 caractères")
        
        # Vérifier que le nom ne contient que des lettres et espaces
        if not re.match(r'^[a-zA-ZÀ-ÿ\s\-\']+$', name):
            raise ValidationError(f"Le {field_name} ne peut contenir que des lettres, espaces, tirets et apostrophes")
        
        return name.strip()
    
    @staticmethod
    def validate_experience(experience):
        """Valider l'expérience"""
        if experience and len(experience) > VALIDATION_LIMITS['max_experience_length']:
            raise ValidationError(f"L'expérience ne peut pas dépasser {VALIDATION_LIMITS['max_experience_length']} caractères")
        
        return experience
    
    @staticmethod
    def validate_license_number(license_number):
        """Valider le numéro de licence"""
        if license_number and len(license_number) > VALIDATION_LIMITS['max_license_number_length']:
            raise ValidationError(f"Le numéro de licence ne peut pas dépasser {VALIDATION_LIMITS['max_license_number_length']} caractères")
        
        return license_number
    
    @classmethod
    def validate_boatman_data(cls, data):
        """Valider toutes les données d'un batelier"""
        validated_data = {}
        
        # Validation des champs requis
        required_fields = ['email', 'first_name', 'last_name']
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValidationError(f"Le champ {field} est requis")
        
        # Validation de chaque champ
        validated_data['email'] = cls.validate_email(data['email'])
        validated_data['first_name'] = cls.validate_name(data['first_name'], 'prénom')
        validated_data['last_name'] = cls.validate_name(data['last_name'], 'nom')
        phone_number = data.get('phone_number', '')
        if phone_number:
            validated_data['phone_number'] = cls.validate_phone_number(phone_number)
        else:
            validated_data['phone_number'] = ''
        validated_data['experience'] = cls.validate_experience(data.get('experience', ''))
        validated_data['license_number'] = cls.validate_license_number(data.get('license_number', ''))
        
        return validated_data


class WalletValidator:
    """Validateur pour les opérations de portefeuille"""
    
    @staticmethod
    def validate_amount(amount):
        """Valider un montant"""
        if amount is None:
            raise ValidationError("Le montant est requis")
        
        try:
            amount = Decimal(str(amount))
        except (ValueError, TypeError):
            raise ValidationError("Montant invalide")
        
        if amount <= 0:
            raise ValidationError("Le montant doit être positif")
        
        if amount > Decimal('10000.00'):
            raise ValidationError("Le montant ne peut pas dépasser 10 000€")
        
        return amount
    
    @staticmethod
    def validate_payment_method(payment_method):
        """Valider la méthode de paiement"""
        valid_methods = ['CARD', 'BANK_TRANSFER', 'PAYPAL']
        
        if payment_method not in valid_methods:
            raise ValidationError(f"Méthode de paiement invalide. Méthodes acceptées: {', '.join(valid_methods)}")
        
        return payment_method


class ShuttleValidator:
    """Validateur pour les opérations de navettes"""
    
    @staticmethod
    def validate_shuttle_assignment(captain_id, boat_id):
        """Valider l'assignation d'une navette"""
        if not captain_id:
            raise ValidationError("L'ID du capitaine est requis")
        
        if not boat_id:
            raise ValidationError("L'ID du bateau est requis")
        
        # Vérifier que les IDs sont des entiers
        try:
            captain_id = int(captain_id)
            boat_id = int(boat_id)
        except (ValueError, TypeError):
            raise ValidationError("IDs invalides")
        
        return captain_id, boat_id
    
    @staticmethod
    def validate_rejection_reason(reason):
        """Valider la raison de rejet d'une navette"""
        if not reason:
            return "Aucune raison fournie"
        
        if len(reason) > 500:
            raise ValidationError("La raison ne peut pas dépasser 500 caractères")
        
        return reason.strip()


class EstablishmentValidator:
    """Validateur pour les données d'établissement"""
    
    @staticmethod
    def validate_availability(availability):
        """Valider le statut de disponibilité"""
        if availability is None:
            raise ValidationError("Le statut de disponibilité est requis")
        
        if not isinstance(availability, bool):
            raise ValidationError("Le statut de disponibilité doit être un booléen")
        
        return availability
    
    @staticmethod
    def validate_pagination_params(page, limit):
        """Valider les paramètres de pagination"""
        try:
            page = int(page) if page else 1
            limit = int(limit) if limit else 20
        except (ValueError, TypeError):
            raise ValidationError("Paramètres de pagination invalides")
        
        if page < 1:
            page = 1
        
        if limit < 1:
            limit = 20
        elif limit > 100:
            limit = 100
        
        return page, limit
    
    @staticmethod
    def validate_date_range(date_from, date_to):
        """Valider une plage de dates"""
        from datetime import datetime
        
        if date_from:
            try:
                date_from = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
            except ValueError:
                raise ValidationError("Format de date de début invalide")
        
        if date_to:
            try:
                date_to = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
            except ValueError:
                raise ValidationError("Format de date de fin invalide")
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError("La date de début ne peut pas être postérieure à la date de fin")
        
        return date_from, date_to


def validate_establishment_access(user):
    """Valider que l'utilisateur est un établissement"""
    if not user or not user.is_authenticated:
        raise ValidationError("Authentification requise")
    
    if not hasattr(user, 'establishment'):
        raise ValidationError(ERROR_MESSAGES['ACCESS_DENIED'])
    
    return True


def validate_json_field(data, field_name, required_keys=None):
    """Valider un champ JSON"""
    if not isinstance(data, dict):
        raise ValidationError(f"Le champ {field_name} doit être un objet JSON valide")
    
    if required_keys:
        missing_keys = [key for key in required_keys if key not in data]
        if missing_keys:
            raise ValidationError(f"Clés manquantes dans {field_name}: {', '.join(missing_keys)}")
    
    return data
