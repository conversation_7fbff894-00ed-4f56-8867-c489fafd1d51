"""
Test complet de la logique métier selon les spécifications exactes.
Ce script teste les trois workflows de courses et vérifie que les relations entre entités sont correctes.
"""

import os
import sys
import django
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from decimal import Decimal
from django.utils import timezone
from datetime import timedelta, date, time

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Captain, Client, Establishment
from boats.models import Boat
from trips.models import Trip, TripRequest, SimpleTripRequest, HourlyTripRequest, ShuttleTripRequest, TripQuote
from payments.models import Wallet, Payment

User = get_user_model()


class BusinessLogicCompleteTest(APITestCase):
    """Test complet de la logique métier selon les spécifications"""
    
    def setUp(self):
        """Configuration initiale pour tous les tests"""
        
        # 1. CRÉER UN CLIENT
        self.client_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Marie',
            last_name='Dubois',
            phone_number='+***********'
        )
        self.client_profile = Client.objects.create(user=self.client_user)
        self.client_wallet = Wallet.objects.create(user=self.client_user, balance=Decimal('500.00'))
        
        # 2. CRÉER UN ÉTABLISSEMENT
        self.establishment_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Hotel',
            last_name='Paradise',
            phone_number='+3**********'
        )
        self.establishment = Establishment.objects.create(
            user=self.establishment_user,
            name='Hotel Paradise Beach',
            type='HOTEL',
            address='123 Promenade des Anglais, Cannes',
            phone_number='+3**********'
        )
        
        # 3. CRÉER UN BATELIER ENREGISTRÉ PAR L'ÉTABLISSEMENT
        self.boatman_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Pierre',
            last_name='Batelier',
            phone_number='+33111222333',
            is_captain=True
        )
        self.boatman = Captain.objects.create(
            user=self.boatman_user,
            experience='3 ans d\'expérience navettes',
            license_number='BOAT001',
            rate_per_km=Decimal('20.00'),
            rate_per_hour=Decimal('40.00'),
            is_available=True,
            availability_status='AVAILABLE',
            metadata={'registered_by_establishment_id': self.establishment.id}
        )
        
        # 4. CRÉER UN CAPITAINE EXTERNE (INDÉPENDANT)
        self.captain_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Captain',
            last_name='Jack',
            phone_number='+33444555666',
            is_captain=True
        )
        self.captain = Captain.objects.create(
            user=self.captain_user,
            experience='10 ans d\'expérience',
            license_number='CAP001',
            rate_per_km=Decimal('30.00'),
            rate_per_hour=Decimal('60.00'),
            is_available=True,
            availability_status='AVAILABLE'
        )
        
        # 5. CRÉER DES BATEAUX
        self.boatman_boat = Boat.objects.create(
            captain=self.boatman,
            name='Navette Paradise',
            boat_type='classic',
            capacity=6,
            fuel_type='gasoline',
            fuel_consumption=20.0,
            is_available=True
        )
        
        self.captain_boat = Boat.objects.create(
            captain=self.captain,
            name='Sea Explorer',
            boat_type='speedboat',
            capacity=8,
            fuel_type='gasoline',
            fuel_consumption=25.0,
            is_available=True
        )
        
        # 6. CRÉER LES CLIENTS API
        self.client_token = Token.objects.create(user=self.client_user)
        self.establishment_token = Token.objects.create(user=self.establishment_user)
        self.boatman_token = Token.objects.create(user=self.boatman_user)
        self.captain_token = Token.objects.create(user=self.captain_user)
        
        self.client_api = APIClient()
        self.client_api.credentials(HTTP_AUTHORIZATION='Token ' + self.client_token.key)
        
        self.establishment_api = APIClient()
        self.establishment_api.credentials(HTTP_AUTHORIZATION='Token ' + self.establishment_token.key)
        
        self.boatman_api = APIClient()
        self.boatman_api.credentials(HTTP_AUTHORIZATION='Token ' + self.boatman_token.key)
        
        self.captain_api = APIClient()
        self.captain_api.credentials(HTTP_AUTHORIZATION='Token ' + self.captain_token.key)

    def test_workflow_a_free_shuttle_with_boatman(self):
        """
        TEST WORKFLOW A: NAVETTE GRATUITE AVEC BATELIER DISPONIBLE
        
        Scénario: Client demande navette → Établissement accepte → Assigne son batelier
        """
        print("\n=== TEST WORKFLOW A: NAVETTE GRATUITE AVEC BATELIER ===")
        
        # 1. CLIENT DEMANDE UNE NAVETTE GRATUITE
        shuttle_data = {
            'establishment_id': self.establishment.id,
            'departure_location': {
                'city_name': 'Aéroport Nice',
                'coordinates': {'latitude': 43.6584, 'longitude': 7.2159}
            },
            'arrival_location': {
                'city_name': 'Hotel Paradise Beach',
                'coordinates': {'latitude': 43.5528, 'longitude': 7.0174}
            },
            'departure_date': (timezone.now() + timedelta(hours=2)).date().isoformat(),
            'departure_time': (timezone.now() + timedelta(hours=2)).time().isoformat(),
            'passenger_count': 2,
            'message': 'Arrivée vol AF1234'
        }
        
        response = self.client_api.post('/api/trips/requests/shuttle/', shuttle_data, format='json')
        print(f"1. Demande navette: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        request_id = response.data['data']['request_id']
        print(f"   Request ID: {request_id}")
        
        # 2. ÉTABLISSEMENT VOIT LA DEMANDE
        response = self.establishment_api.get('/api/establishments/shuttle-requests/')
        print(f"2. Établissement voit demandes: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['requests']), 1)
        
        # 3. ÉTABLISSEMENT ACCEPTE ET ASSIGNE SON BATELIER
        accept_data = {
            'captain_id': self.boatman.id,
            'boat_id': self.boatman_boat.id,
            'estimated_pickup_time': (timezone.now() + timedelta(hours=2)).isoformat()
        }
        
        response = self.establishment_api.post(
            f'/api/establishments/shuttle-requests/{request_id}/accept/',
            accept_data,
            format='json'
        )
        print(f"3. Établissement accepte: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        trip_id = response.data['data']['trip_id']
        print(f"   Trip ID créé: {trip_id}")
        
        # 4. VÉRIFIER QUE LA COURSE EST BIEN CRÉÉE
        trip = Trip.objects.get(id=trip_id)
        self.assertEqual(trip.trip_type, 'NAVETTES_GRATUITES')
        self.assertEqual(trip.total_price, Decimal('0.00'))
        self.assertEqual(trip.payment_status, 'PAID')
        self.assertEqual(trip.captain, self.boatman)
        print(f"   ✅ Course gratuite créée correctement")
        
        # 5. BATELIER VOIT LA COURSE DANS SON ESPACE
        response = self.boatman_api.get('/api/boatman/shuttles/')
        print(f"4. Batelier voit courses: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['shuttles']), 1)
        
        shuttle = response.data['data']['shuttles'][0]
        self.assertEqual(shuttle['payment']['amount'], 0.0)
        self.assertEqual(shuttle['payment']['method'], 'FREE_SHUTTLE')
        print(f"   ✅ Batelier voit la navette gratuite")

    def test_workflow_a_free_shuttle_fallback_external_captain(self):
        """
        TEST WORKFLOW A: NAVETTE GRATUITE - FALLBACK CAPITAINE EXTERNE
        
        Scénario: Client demande navette → Établissement n'a pas de batelier → 
                 Établissement agit comme client et réserve un capitaine externe
        """
        print("\n=== TEST WORKFLOW A: FALLBACK CAPITAINE EXTERNE ===")
        
        # 1. RENDRE LE BATELIER INDISPONIBLE
        self.boatman.is_available = False
        self.boatman.availability_status = 'BUSY'
        self.boatman.save()
        
        # 2. CLIENT DEMANDE UNE NAVETTE
        shuttle_data = {
            'establishment_id': self.establishment.id,
            'departure_location': {
                'city_name': 'Gare SNCF Cannes',
                'coordinates': {'latitude': 43.5528, 'longitude': 7.0174}
            },
            'arrival_location': {
                'city_name': 'Hotel Paradise Beach',
                'coordinates': {'latitude': 43.5528, 'longitude': 7.0174}
            },
            'departure_date': (timezone.now() + timedelta(hours=3)).date().isoformat(),
            'departure_time': (timezone.now() + timedelta(hours=3)).time().isoformat(),
            'passenger_count': 4
        }
        
        response = self.client_api.post('/api/trips/requests/shuttle/', shuttle_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        request_id = response.data['data']['request_id']
        
        # 3. ÉTABLISSEMENT VOIT QU'IL N'A PAS DE BATELIER DISPONIBLE
        response = self.establishment_api.get('/api/establishments/available-resources/')
        print(f"1. Ressources disponibles: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Vérifier qu'aucun batelier de l'établissement n'est disponible
        available_captains = response.data['data']['available_captains']
        establishment_boatmen = [c for c in available_captains if c['id'] == self.boatman.id]
        self.assertEqual(len(establishment_boatmen), 0)
        print(f"   ✅ Aucun batelier de l'établissement disponible")
        
        # 4. ÉTABLISSEMENT PEUT VOIR LES CAPITAINES EXTERNES DISPONIBLES
        external_captains = [c for c in available_captains if c['id'] == self.captain.user.id]  # Captain utilise user comme clé primaire
        self.assertEqual(len(external_captains), 1)
        print(f"   ✅ Capitaine externe disponible: {external_captains[0]['name']}")
        
        # 5. ÉTABLISSEMENT ACCEPTE AVEC CAPITAINE EXTERNE
        accept_data = {
            'captain_id': self.captain.user.id,  # Captain utilise user comme clé primaire
            'boat_id': self.captain_boat.id,
            'estimated_pickup_time': (timezone.now() + timedelta(hours=3)).isoformat(),
            'notes': 'Aucun batelier disponible, utilisation capitaine externe'
        }
        
        response = self.establishment_api.post(
            f'/api/establishments/shuttle-requests/{request_id}/accept/',
            accept_data,
            format='json'
        )
        print(f"2. Acceptation avec capitaine externe: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        trip_id = response.data['data']['trip_id']
        
        # 6. VÉRIFIER QUE LA COURSE EST CRÉÉE AVEC LE CAPITAINE EXTERNE
        trip = Trip.objects.get(id=trip_id)
        self.assertEqual(trip.captain, self.captain)  # Capitaine externe
        self.assertEqual(trip.trip_type, 'NAVETTES_GRATUITES')
        self.assertEqual(trip.total_price, Decimal('0.00'))
        print(f"   ✅ Course assignée au capitaine externe")

    def test_workflow_b_paid_trip_simple(self):
        """
        TEST WORKFLOW B: COURSE PAYANTE SIMPLE
        
        Scénario: Client remplit critères → Système propose capitaines → 
                 Client sélectionne → Paiement → QR code
        """
        print("\n=== TEST WORKFLOW B: COURSE PAYANTE SIMPLE ===")
        
        # 1. CLIENT FAIT UNE DEMANDE DE COURSE SIMPLE
        trip_data = {
            'departure_location': {
                'city_name': 'Port de Cannes',
                'coordinates': {'latitude': 43.5528, 'longitude': 7.0174}
            },
            'arrival_location': {
                'city_name': 'Îles de Lérins',
                'coordinates': {'latitude': 43.5184, 'longitude': 7.0457}
            },
            'departure_date': (timezone.now() + timedelta(hours=4)).date().isoformat(),
            'departure_time': (timezone.now() + timedelta(hours=4)).time().isoformat(),
            'passenger_count': 4,
            'boat_type': 'speedboat',
            'special_requests': 'Voyage romantique'
        }
        
        response = self.client_api.post('/api/trips/requests/simple/', trip_data, format='json')
        print(f"1. Demande course simple: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        request_id = response.data['data']['request_id']
        
        # 2. SYSTÈME GÉNÈRE DES DEVIS DE TOUS LES CAPITAINES DISPONIBLES
        response = self.client_api.get(f'/api/trips/requests/{request_id}/')
        print(f"2. Récupération devis: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        quotes = response.data['data']['quotes']
        self.assertGreater(len(quotes), 0)
        print(f"   ✅ {len(quotes)} devis générés")
        
        # Vérifier que chaque capitaine a son propre prix
        for quote in quotes:
            print(f"   - {quote['captain_name']}: {quote['total_price']}€")
        
        # 3. CLIENT SÉLECTIONNE UN CAPITAINE (le premier speedboat disponible)
        selected_quote = None
        for quote in quotes:
            if quote['boat_type'] == 'speedboat':
                selected_quote = quote
                break
        
        self.assertIsNotNone(selected_quote)
        print(f"   ✅ Client sélectionne: {selected_quote['captain_name']}")
        
        # 4. CLIENT ACCEPTE LE DEVIS
        response = self.client_api.post(f'/api/trips/quotes/{selected_quote["quote_id"]}/accept/')
        print(f"3. Acceptation devis: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        trip_id = response.data['data']['trip_id']
        
        # 5. CAPITAINE VALIDE LA DEMANDE
        response = self.captain_api.post(f'/api/trips/{trip_id}/accept/')
        print(f"4. Capitaine valide: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 6. CLIENT PAIE LA COURSE
        payment_data = {
            'payment_method': 'wallet'
        }
        
        response = self.client_api.post(f'/api/trips/{trip_id}/payment/', payment_data, format='json')
        print(f"5. Paiement course: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['booking_confirmed'])
        self.assertIn('qr_code', response.data)
        
        qr_code = response.data['qr_code']
        print(f"   ✅ QR code généré: {qr_code[:20]}...")
        
        # 7. VÉRIFIER QUE LA COURSE EST CORRECTEMENT CONFIGURÉE
        trip = Trip.objects.get(id=trip_id)
        self.assertEqual(trip.trip_type, 'COURSE_SIMPLE')
        self.assertEqual(trip.payment_status, 'PAID')
        self.assertGreater(trip.total_price, Decimal('0.00'))
        print(f"   ✅ Course payante créée: {trip.total_price}€")

    def test_workflow_b_paid_trip_hourly(self):
        """
        TEST WORKFLOW B: MISE À DISPOSITION (TARIFICATION HORAIRE)
        """
        print("\n=== TEST WORKFLOW B: MISE À DISPOSITION ===")
        
        # 1. CLIENT DEMANDE UNE MISE À DISPOSITION
        hourly_data = {
            'departure_location': {
                'city_name': 'Marina Cannes',
                'coordinates': {'latitude': 43.5528, 'longitude': 7.0174}
            },
            'departure_date': (timezone.now() + timedelta(hours=5)).date().isoformat(),
            'departure_time': (timezone.now() + timedelta(hours=5)).time().isoformat(),
            'duration_hours': 3,
            'passenger_count': 6,
            'boat_type': 'classic',
            'special_requests': 'Tour des îles avec arrêts baignade'
        }
        
        response = self.client_api.post('/api/trips/requests/hourly/', hourly_data, format='json')
        print(f"1. Demande mise à disposition: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        request_id = response.data['data']['request_id']
        
        # 2. VÉRIFIER QUE LES DEVIS UTILISENT LA TARIFICATION HORAIRE
        response = self.client_api.get(f'/api/trips/requests/{request_id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        quotes = response.data['data']['quotes']
        self.assertGreater(len(quotes), 0)
        
        # Vérifier qu'au moins un devis utilise la tarification horaire
        hourly_quote = None
        for quote in quotes:
            # Le prix devrait être basé sur rate_per_hour * duration_hours
            if quote['captain_name'] == self.captain.user.get_full_name():
                expected_price = self.captain.rate_per_hour * 3  # 3 heures
                self.assertEqual(Decimal(str(quote['total_price'])), expected_price)
                hourly_quote = quote
                break
        
        self.assertIsNotNone(hourly_quote)
        print(f"   ✅ Tarification horaire correcte: {hourly_quote['total_price']}€ pour 3h")

    def test_workflow_c_post_trip_payments(self):
        """
        TEST WORKFLOW C: PAIEMENTS POST-VOYAGE
        
        Scénario: Course terminée → Compensation carbone optionnelle → Pourboire optionnel
        """
        print("\n=== TEST WORKFLOW C: PAIEMENTS POST-VOYAGE ===")
        
        # 1. CRÉER UNE COURSE TERMINÉE
        trip = Trip.objects.create(
            client=self.client_profile,
            captain=self.captain,
            boat=self.captain_boat,
            trip_type='COURSE_SIMPLE',
            start_location='Cannes',
            end_location='Îles de Lérins',
            scheduled_start_time=timezone.now() - timedelta(hours=2),
            actual_start_time=timezone.now() - timedelta(hours=2),
            actual_end_time=timezone.now() - timedelta(minutes=75),
            estimated_duration=30,
            actual_duration=30,
            passenger_count=4,
            base_price=Decimal('80.00'),
            total_price=Decimal('80.00'),
            status='COMPLETED',
            payment_status='PAID'
        )
        
        print(f"1. Course terminée créée: ID {trip.id}")
        
        # 2. CLIENT CONSULTE L'EMPREINTE CARBONE
        response = self.client_api.get(f'/api/trips/{trip.id}/carbon-footprint/')
        print(f"2. Consultation empreinte carbone: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        carbon_data = response.data['carbon_footprint']
        self.assertIn('co2_kg', carbon_data)
        self.assertIn('compensation_cost_euros', carbon_data)
        self.assertGreater(carbon_data['co2_kg'], 0)
        
        print(f"   ✅ Empreinte: {carbon_data['co2_kg']} kg CO₂")
        print(f"   ✅ Coût compensation: {carbon_data['compensation_cost_euros']}€")
        
        # 3. CLIENT PAIE LA COMPENSATION CARBONE (OPTIONNELLE)
        carbon_payment_data = {
            'payment_method': 'wallet'
        }
        
        response = self.client_api.post(
            f'/api/trips/{trip.id}/carbon-compensation/',
            carbon_payment_data,
            format='json'
        )
        print(f"3. Paiement compensation carbone: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('co2_compensated_kg', response.data)
        
        # 4. CLIENT DONNE UN POURBOIRE (OPTIONNEL)
        tip_data = {
            'amount': '15.00',
            'payment_method': 'wallet'
        }
        
        response = self.client_api.post(f'/api/trips/{trip.id}/tip/', tip_data, format='json')
        print(f"4. Paiement pourboire: {response.status_code}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['amount'], 15.0)
        
        print(f"   ✅ Pourboire de 15€ donné au capitaine")
        
        # 5. VÉRIFIER QUE LES PAIEMENTS SONT SÉPARÉS
        payments = Payment.objects.filter(trip=trip)
        payment_types = [p.type for p in payments]
        
        self.assertIn('CARBON_OFFSET', payment_types)
        self.assertIn('TIP', payment_types)
        print(f"   ✅ Paiements séparés créés: {len(payments)} transactions")

    def test_entity_relationships_validation(self):
        """
        TEST DES RELATIONS ENTRE ENTITÉS
        
        Vérifier que toutes les relations sont correctement configurées
        """
        print("\n=== TEST DES RELATIONS ENTRE ENTITÉS ===")
        
        # 1. RELATION CLIENT → TRIPS
        client_trips = self.client_profile.trips.all()
        print(f"1. Client peut accéder à ses courses: {len(client_trips)} courses")
        
        # 2. RELATION ESTABLISHMENT → SHUTTLE_REQUESTS
        establishment_requests = self.establishment.shuttle_requests.all()
        print(f"2. Établissement peut voir ses demandes: {len(establishment_requests)} demandes")
        
        # 3. RELATION CAPTAIN → TRIPS
        captain_trips = self.captain.trips.all()
        boatman_trips = self.boatman.trips.all()
        print(f"3. Capitaine peut voir ses courses: {len(captain_trips)} courses")
        print(f"4. Batelier peut voir ses courses: {len(boatman_trips)} courses")
        
        # 4. RELATION BOAT → TRIPS
        boat_trips = self.captain_boat.trips.all()
        print(f"5. Bateau peut voir ses courses: {len(boat_trips)} courses")
        
        # 5. VÉRIFIER LES MÉTADONNÉES D'ENREGISTREMENT
        boatman_metadata = self.boatman.metadata or {}
        establishment_id = boatman_metadata.get('registered_by_establishment_id')
        self.assertEqual(establishment_id, self.establishment.id)
        print(f"6. ✅ Batelier correctement lié à l'établissement: {establishment_id}")
        
        print(f"\n✅ TOUTES LES RELATIONS SONT CORRECTES")


def run_tests():
    """Exécuter tous les tests de logique métier"""
    print("🚀 DÉMARRAGE DES TESTS DE LOGIQUE MÉTIER COMPLÈTE")
    print("=" * 60)
    
    # Créer une instance de test
    test_instance = BusinessLogicCompleteTest()
    test_instance.setUp()
    
    try:
        # Exécuter tous les tests
        test_instance.test_workflow_a_free_shuttle_with_boatman()
        test_instance.test_workflow_a_free_shuttle_fallback_external_captain()
        test_instance.test_workflow_b_paid_trip_simple()
        test_instance.test_workflow_b_paid_trip_hourly()
        test_instance.test_workflow_c_post_trip_payments()
        test_instance.test_entity_relationships_validation()
        
        print("\n" + "=" * 60)
        print("🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS !")
        print("✅ La logique métier est correctement implémentée")
        print("✅ Les relations entre entités sont respectées")
        print("✅ Les trois workflows fonctionnent parfaitement")
        
    except Exception as e:
        print(f"\n❌ ERREUR DANS LES TESTS: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    run_tests()
