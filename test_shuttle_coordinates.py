#!/usr/bin/env python
"""
Test des coordonnées pour les navettes
"""

import os
import django
import json
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, User, Client
from trips.models import ShuttleTripRequest
from trips.serializers import ShuttleTripRequestSerializer
from django.utils import timezone
from datetime import date, time, timedelta

def test_shuttle_distance_calculation():
    print("🧪 Test du calcul de distance pour les navettes")
    print("=" * 60)
    
    try:
        # 1. Trouver un établissement avec coordonnées
        establishment = Establishment.objects.filter(
            longitude__isnull=False, 
            latitude__isnull=False
        ).first()
        
        if not establishment:
            print("❌ Aucun établissement avec coordonnées trouvé")
            return False
        
        print(f"✅ Établissement trouvé: {establishment.name}")
        print(f"   Coordonnées: {establishment.latitude}, {establishment.longitude}")
        
        # 2. Trouver un client
        client = Client.objects.first()
        if not client:
            print("❌ Aucun client trouvé")
            return False
        
        print(f"✅ Client trouvé: {client.user.email}")
        
        # 3. Créer une demande de navette avec seulement les coordonnées de départ
        departure_location = {
            "city_name": "Port de Cannes",
            "coordinates": {
                "latitude": 43.5528,
                "longitude": 7.0174
            },
            "timestamp": timezone.now().isoformat()
        }
        
        # Données de test (sans arrival_location)
        shuttle_data = {
            'departure_location': departure_location,
            'passenger_count': 2,
            'establishment': establishment.user.id,
            'departure_date': (date.today() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'departure_time': '19:30:00',
            'message': 'Test de navette avec coordonnées automatiques'
        }
        
        print("4. Test du sérialiseur...")
        serializer = ShuttleTripRequestSerializer(data=shuttle_data)
        
        if serializer.is_valid():
            print("   ✅ Sérialiseur valide")
            print("   📝 Payload utilisateur (sans arrival_location):")
            print(f"   {json.dumps(shuttle_data, indent=2, default=str)}")
            print("   ✅ Le système utilisera automatiquement les coordonnées de l'établissement")
            
            # 5. Sauvegarder et tester le calcul de distance
            print("5. Création de la navette...")
            shuttle_request = serializer.save(client=client, establishment=establishment)
            
            print(f"   ✅ Navette créée avec ID: {shuttle_request.id}")
            
            # 6. Calculer la distance
            print("6. Calcul de la distance...")
            distance = shuttle_request.calculate_distance()
            
            if distance:
                print(f"   ✅ Distance calculée: {distance} km")
                
                # 7. Vérifier la sérialisation complète
                print("7. Test de sérialisation complète...")
                full_serializer = ShuttleTripRequestSerializer(shuttle_request)
                response_data = full_serializer.data
                
                print("   ✅ Réponse sérialisée:")
                print(f"   Distance: {response_data.get('distance_km')} km")
                print(f"   Établissement: {response_data.get('establishment_details', {}).get('name')}")
                
                establishment_coords = response_data.get('establishment_details', {}).get('coordinates')
                if establishment_coords:
                    print(f"   Coordonnées établissement: {establishment_coords}")
                
                return True
            else:
                print("   ❌ Échec du calcul de distance")
                return False
        else:
            print(f"   ❌ Erreurs de validation: {serializer.errors}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_payload():
    """Test avec le payload exact de l'utilisateur"""
    print("\n🔧 Test avec le payload utilisateur")
    print("=" * 40)
    
    # Payload de l'utilisateur (sans arrival_location)
    user_payload = {
        "departure_location": {
            "city_name": "Port de Cannes",
            "coordinates": {
                "latitude": 43.5528,
                "longitude": 7.0174
            },
            "timestamp": "2025-06-11T06:26:25+02:00"
        },
        "passenger_count": 2,
        "establishment": 5,  # Nous utiliserons le premier établissement disponible
        "departure_date": (date.today() + timedelta(days=1)).strftime('%Y-%m-%d'),
        "departure_time": "19:30:00",
        "message": "Nous aimerions réserver une table pour 20h"
    }
    
    # Adapter l'ID de l'établissement
    establishment = Establishment.objects.filter(
        longitude__isnull=False, 
        latitude__isnull=False
    ).first()
    
    if establishment:
        user_payload['establishment'] = establishment.user.id
        print(f"✅ Utilisation de l'établissement: {establishment.name}")
        
        serializer = ShuttleTripRequestSerializer(data=user_payload)
        if serializer.is_valid():
            print("✅ Payload utilisateur valide avec le nouveau système")
            return True
        else:
            print(f"❌ Erreurs: {serializer.errors}")
            return False
    else:
        print("❌ Aucun établissement avec coordonnées disponible")
        return False

if __name__ == "__main__":
    print("🚀 Tests des navettes avec coordonnées automatiques")
    print("=" * 70)
    
    success1 = test_shuttle_distance_calculation()
    success2 = test_api_payload()
    
    overall_success = success1 and success2
    print(f"\n{'🎉 Tous les tests réussis!' if overall_success else '❌ Certains tests ont échoué'}")
