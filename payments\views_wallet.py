"""
Module de gestion des wallets et répartition automatique des revenus.

Ce module contient les vues pour la gestion des portefeuilles des capitaines,
l'historique des transactions et les retraits de fonds.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from decimal import Decimal
from django.utils.translation import gettext_lazy as _
from .wallet_security_service import WalletSecurityService
from .models import Payment, Transaction
from accounts.models import Captain
from trips.models import Trip
from notifications.models import Notification
from accounts.permissions import IsCaptain


class CaptainWalletView(APIView):
    """
    Endpoint pour consulter le solde et l'historique du wallet capitaine.
    """
    permission_classes = [IsAuthenticated, IsCaptain]

    def get(self, request):
        """
        Retourne le solde et l'historique des transactions du capitaine.

        URL: GET /api/payments/wallet/
        Query params:
        - limit: nombre de transactions à retourner (défaut: 20)
        - offset: décalage pour pagination
        """

        try:
            captain = request.user.captain

            # Paramètres de pagination
            limit = int(request.GET.get('limit', 20))
            offset = int(request.GET.get('offset', 0))

            # Récupérer les transactions du capitaine
            transactions = Transaction.objects.filter(
                wallet__user=request.user
            ).order_by('-created_at')[offset:offset+limit]

            # Calculer le solde total et le montant en attente (transactions PENDING)
            from django.db.models import Sum
            total_balance = transactions.aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.00')

            pending_balance = Transaction.objects.filter(
                wallet__user=request.user,
                status='PENDING'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

            # Calculer les statistiques
            total_earned = Transaction.objects.filter(
                wallet__user=request.user,
                type='CREDIT',
                metadata__transaction_type='TRIP_EARNING'
            ).aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.00')

            total_withdrawn = Transaction.objects.filter(
                wallet__user=request.user,
                type='DEBIT',
                metadata__transaction_type='WITHDRAWAL'
            ).aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.00')

            # Sérialiser les transactions
            transactions_data = []
            for trans in transactions:
                transactions_data.append({
                    'id': trans.id,
                    'amount': float(trans.amount),
                    'date': trans.created_at,
                    'type': trans.type,  # CREDIT ou DEBIT
                    'description': trans.description,
                    'status': getattr(trans, 'status', 'COMPLETED')
                })

            return Response({
                'success': True,
                'balance': float(captain.wallet_balance),
                'transactions': transactions_data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'Erreur lors de la récupération du wallet: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WithdrawFundsView(APIView):
    """
    Endpoint pour retrait de fonds par le capitaine.
    """
    permission_classes = [IsAuthenticated, IsCaptain]

    def post(self, request):
        """
        Permet au capitaine de demander un retrait de fonds.

        URL: POST /api/payments/withdraw/

        Body: {
            "amount": 150.00,
            "bank_account": {
                "iban": "FR76...",
                "bic": "BNPAFRPP",
                "account_holder": "John Doe"
            },
            "reason": "Retrait mensuel"
        }
        """

        try:
            # Vérifier que l'utilisateur est un capitaine
            if not hasattr(request.user, 'captain'):
                return Response({
                    'error': 'Seuls les capitaines peuvent effectuer des retraits'
                }, status=status.HTTP_403_FORBIDDEN)

            captain = request.user.captain
            amount = Decimal(str(request.data.get('amount', 0)))

            # Validations
            if amount <= 0:
                return Response({
                    'error': 'Le montant doit être positif'
                }, status=status.HTTP_400_BAD_REQUEST)

            if amount > captain.wallet_balance:
                return Response({
                    'error': f'Solde insuffisant. Solde disponible: {captain.wallet_balance}€'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Montant minimum de retrait
            if amount < Decimal('10.00'):
                return Response({
                    'error': 'Le montant minimum de retrait est de 10€'
                }, status=status.HTTP_400_BAD_REQUEST)

            bank_account = request.data.get('bank_account', {})
            if not all([bank_account.get('iban'), bank_account.get('account_holder')]):
                return Response({
                    'error': 'Informations bancaires incomplètes'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Transaction atomique
            with transaction.atomic():
                # Obtenir le portefeuille sécurisé
                wallet = request.user.wallet

                # Utiliser le service sécurisé pour débiter le portefeuille
                debit_result = WalletSecurityService.debit_wallet_secure(
                    wallet_id=wallet.id,
                    amount=amount,
                    description=request.data.get('reason', _('Retrait du portefeuille')),
                    user=request.user
                )

                # Recharger le portefeuille pour avoir le solde à jour
                wallet.refresh_from_db()

                # Mettre à jour le champ duplicatif sur le modèle Captain
                captain.wallet_balance = wallet.balance
                captain.save(update_fields=['wallet_balance'])

                # Récupérer la transaction créée par le service sécurisé
                withdrawal_transaction = Transaction.objects.get(id=debit_result['transaction_id'])

                # --- Déclencher le transfert Stripe (platform -> compte Connect) ---
                stripe_transfer_id = None
                if getattr(captain, 'stripe_connect_id', None):
                    from payments.stripe_utils import create_transfer
                    transfer_amount_cents = int(amount * 100)
                    transfer = create_transfer(
                        amount=transfer_amount_cents,
                        destination=captain.stripe_connect_id,
                        description=f"Retrait portefeuille capitaine #{captain.user.id}",
                        metadata={'transaction_id': str(withdrawal_transaction.id), 'captain_id': str(captain.user.id)}
                    )
                    if 'error' in transfer:
                        # Échec du transfert : consigner l'erreur et marquer la transaction comme FAILED
                        withdrawal_transaction.metadata['transfer_error'] = transfer['error']
                    else:
                        stripe_transfer_id = transfer.id
                        withdrawal_transaction.metadata['stripe_transfer_id'] = transfer.id

                # Enrichir la métadonnée pour faciliter les filtres
                withdrawal_transaction.metadata.update({
                    'transaction_type': 'WITHDRAWAL',
                    'bank_account': bank_account,
                    'reason': request.data.get('reason', ''),
                    'withdrawal_method': 'BANK_TRANSFER'
                })
                withdrawal_transaction.description = f"Retrait de {amount}€ vers {bank_account.get('iban', '')[-4:]}"
                withdrawal_transaction.save()

                # Créer notification
                Notification.objects.create(
                    user=request.user,
                    type=Notification.Type.SYSTEM,
                    title='Demande de retrait enregistrée',
                    message=f'Votre demande de retrait de {amount}€ a été enregistrée et sera traitée sous 2-3 jours ouvrés',
                    content_object=withdrawal_transaction
                )

            return Response({
                'success': True,
                'message': 'Demande de retrait enregistrée',
                'withdrawal': {
                    'id': withdrawal_transaction.id,
                    'amount': float(amount),
                    'status': 'PENDING',
                    'estimated_processing_time': '2-3 jours ouvrés',
                    'new_balance': float(captain.wallet_balance)
                }
            }, status=status.HTTP_201_CREATED)

        except ValueError:
            return Response({
                'error': 'Montant invalide'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': f'Erreur lors du retrait: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def auto_credit_captain_on_trip_completion(trip_id):
    """
    Fonction utilitaire pour créditer automatiquement le capitaine
    après complétion d'une course.

    À appeler depuis le signal post_save du modèle Trip
    ou depuis la vue de complétion de course.
    """

    try:
        trip = Trip.objects.get(id=trip_id)

        # Vérifier que la course est bien terminée
        if trip.status != Trip.Status.COMPLETED:
            return False

        # Vérifier que le paiement a été effectué
        if trip.payment_status != 'PAID':
            return False

        # Vérifier qu'on n'a pas déjà crédité le capitaine
        existing_transaction = Transaction.objects.filter(
            wallet__user=trip.captain.user,
            type='CREDIT',
            metadata__trip_id=trip.id
        ).exists()

        if existing_transaction:
            return False  # Déjà crédité

        # Calculer la commission (80% pour le capitaine, 20% pour la plateforme)
        captain_share = trip.total_price * Decimal('0.80')
        platform_commission = trip.total_price * Decimal('0.20')

        # Transaction atomique
        with transaction.atomic():
            # Utiliser le service sécurisé pour créditer le capitaine
            from .wallet_security_service import WalletSecurityService

            captain_wallet = trip.captain.user.wallet

            # Créditer le wallet du capitaine
            credit_result = WalletSecurityService.credit_wallet_secure(
                wallet_id=captain_wallet.id,
                amount=captain_share,
                description=f"Revenus course #{trip.id} - {trip.start_location} → {trip.end_location}",
                reference=f"TRIP_EARNING_{trip.id}",
                user=trip.captain.user,
                metadata={
                    'trip_id': trip.id,
                    'total_trip_price': float(trip.total_price),
                    'captain_share_percentage': 80,
                    'platform_commission': float(platform_commission)
                }
            )
            
            # Mettre à jour le solde dans le modèle Captain également
            trip.captain.wallet_balance += captain_share
            trip.captain.save()

            # Créer notification pour le capitaine
            from notifications.services import create_notification

            create_notification(
                user=trip.captain.user,
                title='Revenus crédités !',
                message=f'Vous avez reçu {captain_share}€ pour la course terminée',
                notification_type='EARNINGS_CREDITED',
                related_object_id=trip.id
            )

        return True

    except Trip.DoesNotExist:
        return False
    except Exception as e:
        print(f"Erreur lors du crédit automatique: {str(e)}")
        return False


class TripEarningsView(APIView):
    """
    Endpoint pour voir les revenus par course pour un capitaine.
    """
    permission_classes = [IsAuthenticated, IsCaptain]

    def get(self, request):
        """
        Retourne l'historique des revenus par course.

        URL: GET /api/payments/earnings/
        """

        try:
            # Vérifier que l'utilisateur est un capitaine
            if not hasattr(request.user, 'captain'):
                return Response({
                    'error': 'Seuls les capitaines peuvent consulter leurs revenus'
                }, status=status.HTTP_403_FORBIDDEN)

            # Récupérer les transactions d'earnings
            earnings = Transaction.objects.filter(
                user=request.user,
                transaction_type='TRIP_EARNING'
            ).order_by('-created_at')

            earnings_data = []
            for earning in earnings:
                trip_id = earning.metadata.get('trip_id') if earning.metadata else None
                trip = None

                if trip_id:
                    try:
                        trip = Trip.objects.get(id=trip_id)
                    except Trip.DoesNotExist:
                        pass

                earnings_data.append({
                    'id': earning.id,
                    'amount': float(earning.amount),
                    'created_at': earning.created_at,
                    'description': earning.description,
                    'trip': {
                        'id': trip.id if trip else trip_id,
                        'start_location': trip.start_location if trip else 'N/A',
                        'end_location': trip.end_location if trip else 'N/A',
                        'client_name': trip.client.user.get_full_name() if trip else 'N/A',
                        'completed_at': trip.actual_end_time if trip else None
                    } if trip else None
                })

            return Response({
                'success': True,
                'earnings': earnings_data,
                'total_earnings': float(sum(e.amount for e in earnings))
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'Erreur lors de la récupération des revenus: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
