from django.shortcuts import render
from django.http import HttpResponse
from django.views.generic import TemplateView
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.conf import settings
import logging

from .services import APIDocumentationService
from .models import APIDocumentation

logger = logging.getLogger(__name__)


class AIDocumentationView(TemplateView):
    """
    Vue principale pour afficher la documentation générée par OpenAI
    """
    template_name = 'ai_docs/documentation.html'
    
    @method_decorator(cache_page(60 * 15))  # Cache pendant 15 minutes
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)
    
    def get(self, request, *args, **kwargs):
        """
        Affiche la documentation AI ou génère une nouvelle si nécessaire
        """
        service = APIDocumentationService()
        
        # Essayer de récupérer la documentation existante
        doc = service.get_latest_documentation()
        
        if not doc:
            # Pas de documentation existante, essayer de la générer
            logger.info("Aucune documentation trouvée, génération en cours...")
            
            # Scanner les endpoints
            endpoints = service.scan_api_endpoints()
            service.save_endpoints_to_db(endpoints)
            
            # Générer la documentation
            doc_html = service.generate_api_documentation()
            
            if doc_html:
                # Documentation générée avec succès
                return HttpResponse(doc_html, content_type='text/html')
            else:
                # Échec de la génération, afficher une page par défaut
                return self.render_fallback_documentation()
        else:
            # Documentation existante trouvée
            return HttpResponse(doc.content, content_type='text/html')
    
    def render_fallback_documentation(self):
        """
        Affiche une documentation de base si OpenAI n'est pas disponible
        """
        fallback_html = """
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Commodore Taxi Boat - Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        .header h1 {
            font-size: 3rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .card h2 {
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
        }
        .method {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.8rem;
            margin-right: 0.5rem;
        }
        .get { background: #28a745; color: white; }
        .post { background: #007bff; color: white; }
        .put { background: #ffc107; color: black; }
        .delete { background: #dc3545; color: white; }
        .patch { background: #6f42c1; color: white; }
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Monaco', 'Consolas', monospace;
            overflow-x: auto;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚤 API Commodore Taxi Boat</h1>
            <p>Documentation de l'API de transport maritime</p>
        </div>
        
        <div class="card">
            <div class="warning">
                <strong>⚠️ Documentation en mode de base</strong><br>
                La documentation complète générée par OpenAI n'est pas disponible. 
                Configurez votre clé API OpenAI dans les paramètres pour une documentation enrichie.
            </div>
        </div>
        
        <div class="card">
            <h2>🔐 Authentification</h2>
            <p>L'API utilise l'authentification par token. Incluez le header suivant dans vos requêtes :</p>
            <div class="code">Authorization: Token votre_token_ici</div>
        </div>
        
        <div class="card">
            <h2>📱 Modules principaux</h2>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/accounts/</strong> - Gestion des comptes utilisateurs
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/trips/</strong> - Réservation et gestion des courses
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/payments/</strong> - Paiements et portefeuilles
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/boats/</strong> - Gestion des bateaux
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/chat/</strong> - Messagerie instantanée
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/notifications/</strong> - Système de notifications
            </div>
        </div>
        
        <div class="card">
            <h2>🛠️ Documentation complète</h2>
            <p>Pour accéder à la documentation Swagger complète :</p>
            <a href="/api/docs/" class="btn">Voir la documentation Swagger</a>
        </div>
        
        <div class="card">
            <h2>📞 Support</h2>
            <p>Pour toute question ou assistance :</p>
            <ul>
                <li>Email : <EMAIL></li>
                <li>Documentation : <a href="/api/docs/">Documentation Swagger</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
        """
        return HttpResponse(fallback_html, content_type='text/html')


def refresh_documentation(request):
    """
    Endpoint pour rafraîchir la documentation
    """
    if not request.user.is_staff:
        return HttpResponse("Accès non autorisé", status=403)
    
    service = APIDocumentationService()
    success = service.refresh_documentation()
    
    if success:
        return HttpResponse("Documentation rafraîchie avec succès")
    else:
        return HttpResponse("Erreur lors du rafraîchissement", status=500)
