import pytest
from unittest.mock import patch, MagicMock
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from accounts.models import User, Client as Passenger
# Adaptation pour les modèles qui n'existent pas dans la structure actuelle
class ImpactStatistics:
    pass

pytestmark = pytest.mark.unit

@pytest.fixture
def api_client():
    """Fixture pour le client API"""
    return APIClient()

@pytest.fixture
def user():
    """Fixture pour un utilisateur"""
    return User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='SecurePassword123!'
    )

@pytest.fixture
def impact_statistics():
    """Fixture pour les statistiques d'impact"""
    return ImpactStatistics.objects.create()

@pytest.fixture
def passenger(user, impact_statistics):
    """Fixture pour un passager"""
    return Passenger.objects.create(
        user=user,
        impact_statistics=impact_statistics
    )

@pytest.mark.django_db
class TestPaymentMethodViewSet:
    """Tests pour PaymentMethodViewSet"""

    @patch('payments.stripe_utils.create_customer')
    def test_create_customer(self, mock_create_customer, api_client, user, passenger):
        """Test de la méthode create_customer"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_create_customer.return_value = MagicMock(
            id='cus_test123',
            email='<EMAIL>',
            name='Test User'
        )
        
        # Appeler la vue
        url = reverse('payment-methods-create-customer')
        data = {
            'passenger_id': str(passenger.id),
            'email': '<EMAIL>',
            'name': 'Test User',
            'phone': '+33612345678'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert response.data['customer_id'] == 'cus_test123'
        assert response.data['passenger_id'] == passenger.id
        
        # Vérifier que le passager a été mis à jour
        passenger.refresh_from_db()
        assert passenger.stripe_customer_id == 'cus_test123'
    
    @patch('payments.stripe_utils.attach_payment_method')
    def test_add_payment_method(self, mock_attach_payment_method, api_client, user, passenger):
        """Test de la méthode add_payment_method"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Mettre à jour le passager avec un ID client Stripe
        passenger.stripe_customer_id = 'cus_test123'
        passenger.save()
        
        # Simuler la réponse de Stripe
        mock_attach_payment_method.return_value = MagicMock(
            id='pm_test123',
            type='card',
            card=MagicMock(
                brand='visa',
                last4='4242',
                exp_month=12,
                exp_year=2025
            )
        )
        
        # Appeler la vue
        url = reverse('payment-methods-add-payment-method')
        data = {
            'passenger_id': str(passenger.id),
            'payment_method_id': 'pm_test123'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert response.data['payment_method_id'] == 'pm_test123'
        assert response.data['type'] == 'card'
        assert response.data['customer_id'] == 'cus_test123'
    
    @patch('payments.stripe_utils.detach_payment_method')
    def test_remove_payment_method(self, mock_detach_payment_method, api_client, user):
        """Test de la méthode remove_payment_method"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Simuler la réponse de Stripe
        mock_detach_payment_method.return_value = MagicMock(
            id='pm_test123',
            type='card'
        )
        
        # Appeler la vue
        url = reverse('payment-methods-remove-payment-method')
        data = {
            'payment_method_id': 'pm_test123'
        }
        response = api_client.post(url, data)
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert response.data['status'] == 'méthode de paiement supprimée'
        assert response.data['payment_method_id'] == 'pm_test123'
    
    @patch('payments.stripe_utils.list_payment_methods')
    def test_list_methods(self, mock_list_payment_methods, api_client, user, passenger):
        """Test de la méthode list_methods"""
        # Authentifier l'utilisateur
        api_client.force_authenticate(user=user)
        
        # Mettre à jour le passager avec un ID client Stripe
        passenger.stripe_customer_id = 'cus_test123'
        passenger.save()
        
        # Simuler la réponse de Stripe
        mock_payment_method = MagicMock(
            id='pm_test123',
            type='card',
            created=1625097600,
            card=MagicMock(
                brand='visa',
                last4='4242',
                exp_month=12,
                exp_year=2025
            )
        )
        mock_list_payment_methods.return_value = MagicMock(
            data=[mock_payment_method]
        )
        
        # Appeler la vue
        url = reverse('payment-methods-list-methods')
        response = api_client.get(url, {'passenger_id': str(passenger.id)})
        
        # Vérifier la réponse
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['id'] == 'pm_test123'
        assert response.data[0]['type'] == 'card'
        assert 'card' in response.data[0]
        assert response.data[0]['card']['brand'] == 'visa'
        assert response.data[0]['card']['last4'] == '4242'
