# Script pour tester les webhooks Stripe
# Pour exécuter ce script, utilisez la commande : .\test_webhooks.ps1

Write-Host "=== TEST DES WEBHOOKS STRIPE POUR COMMODORE TAXI BOAT ==="
Write-Host "Ce script utilise Stripe CLI pour simuler des webhooks Stripe"
Write-Host ""

# Variables de configuration
$WEBHOOK_URL = "http://localhost:8000/api/payments/webhooks/stripe/"
$STRIPE_KEY = "sk_test_51RQVnNDvUQ1WBV9hLrYCMsSWn4dUgWb3kbKWfNj70oO3spL17pkkPmwRZC5dzke0wM4jEtXlEr4fyeJV5iKiI20h00wq0jpWYi"

# Configuration de Stripe CLI avec la clé API
$env:STRIPE_API_KEY = $STRIPE_KEY

# Fonction pour tester un webhook spécifique
function Test-Webhook {
    param (
        [Parameter(Mandatory=$true)]
        [string]$EventType,
        
        [string]$Data = ""
    )
    
    Write-Host "Envoi d'un événement webhook de type: $EventType"
    
    # Utiliser Stripe CLI pour envoyer un webhook simulé
    if ($Data -eq "") {
        # Utiliser un événement généré automatiquement par Stripe CLI
        $result = stripe trigger $EventType --webhook-endpoint=$WEBHOOK_URL
    }
    else {
        # Utiliser les données fournies
        $result = stripe trigger $EventType --webhook-endpoint=$WEBHOOK_URL --data=$Data
    }
    
    Write-Host "Événement webhook envoyé: $EventType"
    Write-Host "Résultat: $result"
    Write-Host ""
    
    return $result
}

# Fonction pour écouter les webhooks en temps réel
function Start-WebhookListener {
    Write-Host "Démarrage de l'écoute des webhooks Stripe..."
    Write-Host "Les événements seront transmis à: $WEBHOOK_URL"
    Write-Host "Appuyez sur Ctrl+C pour arrêter l'écoute."
    Write-Host ""
    
    # Démarrer l'écoute des webhooks avec Stripe CLI
    stripe listen --forward-to $WEBHOOK_URL --api-key $STRIPE_KEY
}

# Menu interactif
function Show-Menu {
    Clear-Host
    Write-Host "=== MENU DE TEST DES WEBHOOKS STRIPE ==="
    Write-Host "1: Simuler un paiement réussi (payment_intent.succeeded)"
    Write-Host "2: Simuler un paiement échoué (payment_intent.payment_failed)"
    Write-Host "3: Simuler un remboursement (charge.refunded)"
    Write-Host "4: Simuler une session de paiement complétée (checkout.session.completed)"
    Write-Host "5: Écouter les webhooks en temps réel"
    Write-Host "0: Quitter"
    Write-Host ""
    
    $choice = Read-Host "Choisissez une option"
    
    switch ($choice) {
        "1" {
            Test-Webhook -EventType "payment_intent.succeeded"
            pause
        }
        "2" {
            Test-Webhook -EventType "payment_intent.payment_failed"
            pause
        }
        "3" {
            Test-Webhook -EventType "charge.refunded"
            pause
        }
        "4" {
            Test-Webhook -EventType "checkout.session.completed"
            pause
        }
        "5" {
            Start-WebhookListener
            # Cette fonction s'exécute jusqu'à ce que l'utilisateur appuie sur Ctrl+C
        }
        "0" {
            return
        }
        default {
            Write-Host "Option invalide!"
            pause
        }
    }
    
    Show-Menu
}

# Vérifier que Stripe CLI est installé
try {
    $stripeVersion = stripe --version
    Write-Host "Stripe CLI détecté: $stripeVersion"
    Write-Host ""
}
catch {
    Write-Host "Erreur: Stripe CLI n'est pas installé ou n'est pas dans le PATH."
    Write-Host "Installez Stripe CLI avec la commande: choco install stripe-cli -y"
    Write-Host ""
    exit
}

# Démarrer le menu interactif
Show-Menu
