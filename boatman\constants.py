"""
Constantes pour l'application boatman.
"""

# Statuts de disponibilité des capitaines
AVAILABILITY_STATUS_CHOICES = [
    ('AVAILABLE', 'Disponible'),
    ('BUSY', 'Occupé'),
    ('OFFLINE', 'Hors ligne'),
    ('MAINTENANCE', 'Maintenance'),
]

# Statuts des courses
TRIP_STATUS_CHOICES = [
    ('PENDING', 'En attente'),
    ('ACCEPTED', 'Acceptée'),
    ('IN_PROGRESS', 'En cours'),
    ('COMPLETED', 'Terminée'),
    ('CANCELLED', 'Annulée'),
]

# Types de notifications pour les bateliers
BOATMAN_NOTIFICATION_TYPES = [
    ('TRIP_ASSIGNED', 'Course assignée'),
    ('TRIP_STARTED', 'Course démarrée'),
    ('TRIP_COMPLETED', 'Course terminée'),
    ('TRIP_CANCELLED', 'Course annulée'),
    ('TRIP_REMINDER', 'Rappel de course'),
    ('PAYMENT_RECEIVED', 'Paiement reçu'),
    ('WITHDRAWAL_COMPLETED', 'Retrait traité'),
    ('WITHDRAWAL_FAILED', 'Retrait échoué'),
    ('DAILY_SUMMARY', 'Résumé quotidien'),
    ('PENDING_TRIPS_WARNING', 'Courses en attente'),
]

# Types de paiements pour les bateliers
PAYMENT_TYPES = [
    ('TRIP', 'Paiement course'),
    ('TIP', 'Pourboire'),
    ('WITHDRAWAL', 'Retrait'),
    ('REFUND', 'Remboursement'),
    ('BONUS', 'Bonus'),
]

# Statuts de paiement
PAYMENT_STATUS_CHOICES = [
    ('PENDING', 'En attente'),
    ('COMPLETED', 'Terminé'),
    ('FAILED', 'Échoué'),
    ('CANCELLED', 'Annulé'),
]

# Méthodes de paiement
PAYMENT_METHODS = [
    ('CARD', 'Carte bancaire'),
    ('BANK_TRANSFER', 'Virement bancaire'),
    ('PAYPAL', 'PayPal'),
    ('FREE_SHUTTLE', 'Navette gratuite'),
]

# Types de bateaux
BOAT_TYPES = [
    ('classic', 'Classique'),
    ('speedboat', 'Hors-bord'),
    ('yacht', 'Yacht'),
    ('catamaran', 'Catamaran'),
]

# Paramètres par défaut pour les nouveaux capitaines
DEFAULT_CAPTAIN_SETTINGS = {
    'rate_per_km': 25.00,
    'rate_per_hour': 50.00,
    'availability_status': 'AVAILABLE',
    'is_available': True,
    'metadata': {
        'email_notifications': True,
        'sms_notifications': True,
        'push_notifications': True,
        'auto_accept_shuttles': False,
        'preferred_working_hours': {
            'start': '08:00',
            'end': '20:00'
        },
        'preferred_zones': [],
        'languages': ['fr'],
        'emergency_contact': {}
    }
}

# Paramètres de validation
VALIDATION_RULES = {
    'password_min_length': 8,
    'verification_code_length': 4,
    'verification_code_expiry_minutes': 10,
    'temp_token_expiry_minutes': 30,
    'max_trip_start_early_minutes': 30,
    'min_withdrawal_amount': 10.00,
    'max_withdrawal_amount': 5000.00,
}

# Messages d'erreur standardisés
ERROR_MESSAGES = {
    'ACCESS_DENIED': 'Accès refusé - Compte batelier requis',
    'TRIP_NOT_FOUND': 'Course non trouvée ou non assignée',
    'TRIP_CANNOT_START': 'Impossible de démarrer cette course',
    'TRIP_CANNOT_COMPLETE': 'Impossible de terminer cette course',
    'QR_CODE_INVALID': 'QR code invalide',
    'INSUFFICIENT_FUNDS': 'Solde insuffisant',
    'NO_PAYMENT_METHOD': 'Aucune méthode de paiement configurée',
    'INVALID_CREDENTIALS': 'Identifiants invalides',
    'VERIFICATION_CODE_EXPIRED': 'Code de vérification expiré',
    'VERIFICATION_CODE_INVALID': 'Code de vérification invalide',
    'PASSWORD_TOO_SHORT': 'Le mot de passe doit contenir au moins 8 caractères',
    'PASSWORDS_DONT_MATCH': 'Les mots de passe ne correspondent pas',
}

# Messages de succès standardisés
SUCCESS_MESSAGES = {
    'LOGIN_SUCCESS': 'Connexion réussie',
    'PASSWORD_CHANGED': 'Mot de passe mis à jour avec succès',
    'VERIFICATION_CODE_SENT': 'Code de vérification envoyé',
    'CODE_VERIFIED': 'Code vérifié avec succès',
    'TRIP_STARTED': 'Course démarrée avec succès',
    'TRIP_COMPLETED': 'Course terminée avec succès',
    'QR_VALIDATED': 'QR code validé, embarquement confirmé',
    'PROFILE_UPDATED': 'Profil mis à jour',
    'AVAILABILITY_UPDATED': 'Disponibilité mise à jour',
    'WITHDRAWAL_PROCESSED': 'Retrait effectué, fonds en cours de transfert',
    'PAYMENT_METHOD_ADDED': 'Méthode de paiement mise à jour avec succès',
}

# Configuration des filtres
FILTER_OPTIONS = {
    'trip_status': [
        {'value': 'all', 'label': 'Toutes'},
        {'value': 'À venir', 'label': 'À venir'},
        {'value': 'En cours', 'label': 'En cours'},
        {'value': 'Terminées', 'label': 'Terminées'},
        {'value': 'Annulées', 'label': 'Annulées'},
    ],
    'availability_status': [
        {'value': 'AVAILABLE', 'label': 'Disponible'},
        {'value': 'BUSY', 'label': 'Occupé'},
        {'value': 'OFFLINE', 'label': 'Hors ligne'},
        {'value': 'MAINTENANCE', 'label': 'Maintenance'},
    ]
}

# Paramètres de pagination
PAGINATION_DEFAULTS = {
    'page_size': 20,
    'max_page_size': 100,
    'page_size_query_param': 'limit',
    'page_query_param': 'page',
}

# Configuration des notifications push
NOTIFICATION_SETTINGS = {
    'trip_reminder_minutes': 30,
    'daily_summary_hour': 20,  # 20h00
    'low_balance_threshold': 50.00,
    'auto_notification_types': [
        'TRIP_ASSIGNED',
        'TRIP_REMINDER',
        'PAYMENT_RECEIVED',
        'WITHDRAWAL_COMPLETED',
    ]
}

# Codes d'erreur HTTP personnalisés
HTTP_ERROR_CODES = {
    'CAPTAIN_ACCESS_REQUIRED': 4001,
    'TRIP_OWNERSHIP_ERROR': 4002,
    'TRIP_STATE_ERROR': 4003,
    'WALLET_OPERATION_ERROR': 4004,
    'QR_VALIDATION_ERROR': 4005,
    'AUTHENTICATION_ERROR': 4006,
    'VERIFICATION_ERROR': 4007,
}

# Configuration des emails
EMAIL_TEMPLATES = {
    'verification_code': {
        'subject': 'Code de vérification Commodore',
        'template': 'Votre code de vérification est : {code}\n\nCe code expire dans {expiry_minutes} minutes.'
    },
    'password_changed': {
        'subject': 'Mot de passe modifié - Commodore',
        'template': 'Votre mot de passe a été modifié avec succès le {date}.'
    }
}

# Configuration des SMS
SMS_TEMPLATES = {
    'verification_code': 'Commodore: Votre code de vérification est {code}. Expire dans {expiry_minutes} min.',
}

# Limites de sécurité
SECURITY_LIMITS = {
    'max_login_attempts': 5,
    'login_lockout_minutes': 15,
    'max_verification_attempts': 3,
    'verification_lockout_minutes': 30,
    'token_expiry_days': 30,
}

# Configuration des statistiques
STATS_PERIODS = {
    'daily': 1,
    'weekly': 7,
    'monthly': 30,
    'quarterly': 90,
    'yearly': 365,
}

# Types de métadonnées pour les capitaines
METADATA_TYPES = {
    'preferences': [
        'email_notifications',
        'sms_notifications', 
        'push_notifications',
        'auto_accept_shuttles'
    ],
    'working_hours': [
        'preferred_start_time',
        'preferred_end_time',
        'available_days'
    ],
    'professional': [
        'languages',
        'specialties',
        'certifications',
        'emergency_contact'
    ]
}

# Configuration de l'intégration
INTEGRATION_SETTINGS = {
    'establishment_notification_events': [
        'trip_started',
        'trip_completed',
        'trip_cancelled'
    ],
    'client_notification_events': [
        'trip_started',
        'trip_completed'
    ],
    'auto_create_wallet': True,
    'auto_create_boat': True,
    'default_boat_capacity': 6,
    'default_zone_radius': 20,
}
