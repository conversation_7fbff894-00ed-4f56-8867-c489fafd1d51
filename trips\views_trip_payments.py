"""
Vues pour les paiements des courses payantes (Course Simple & Mise à Disposition).
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from decimal import Decimal

from .models import Trip, TripQuote, TripRequest
from payments.models import Payment, Wallet
from payments.services import PaymentService
from .qr_service import generate_trip_qr_code, generate_trip_qr_image


class TripPaymentView(APIView):
    """Paiement d'une course acceptée"""
    permission_classes = [IsAuthenticated]

    def post(self, request, trip_id):
        """Effectuer le paiement d'une course"""
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur est autorisé à payer (client ou établissement propriétaire)
        allowed = (
            hasattr(request.user, 'client') and trip.client == getattr(request.user, 'client', None)
        ) or (
            hasattr(request.user, 'establishment') and trip.establishment == getattr(request.user, 'establishment', None)
        )
        if not allowed:
            return Response(
                {'error': 'Seul le client ou l’établissement concerné peut payer cette course'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la course peut être payée
        if not trip.can_be_paid():
            return Response(
                {'error': f'Impossible de payer cette course (statut: {trip.status}, paiement: {trip.payment_status})'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        payment_method = request.data.get('payment_method', 'card')
        
        try:
            # Créer le paiement principal
            if payment_method == 'wallet':
                # Paiement via portefeuille
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=trip.total_price,
                    payment_method_id="wallet",
                    description=f"Course #{trip.id} - {trip.start_location} vers {trip.end_location}",
                    metadata={
                        'trip_id': trip.id,
                        'captain_id': trip.captain.pk,
                        'boat_id': trip.boat.id,
                        'trip_payment': True
                    },
                    payment_type=Payment.PaymentType.TRIP
                )
            else:
                # Paiement par carte
                payment_method_id = request.data.get('payment_method_id')
                if not payment_method_id:
                    return Response(
                        {'error': 'payment_method_id requis pour le paiement par carte'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=trip.total_price,
                    payment_method_id=payment_method_id,
                    description=f"Course #{trip.id} - {trip.start_location} vers {trip.end_location}",
                    metadata={
                        'trip_id': trip.id,
                        'captain_id': trip.captain.pk,
                        'boat_id': trip.boat.id,
                        'trip_payment': True
                    },
                    payment_type=Payment.PaymentType.TRIP
                )
            
            # Marquer la course comme payée
            trip.mark_as_paid()
            
            # Générer le QR code pour la course
            qr_code = generate_trip_qr_code(trip)
            qr_image = generate_trip_qr_image(trip)
            
            return Response({
                'message': 'Paiement effectué avec succès',
                'payment_id': payment_result.get('id'),
                'trip_id': trip.id,
                'amount_paid': trip.total_price,
                'payment_method': payment_method,
                'trip_status': trip.status,
                'payment_status': trip.payment_status,
                'qr_code': qr_code,
                'qr_image': qr_image,
                'booking_confirmed': True
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Erreur lors du paiement: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TripPaymentStatusView(APIView):
    """Vérification du statut de paiement d'une course"""
    permission_classes = [IsAuthenticated]

    def get(self, request, trip_id):
        """Récupérer le statut de paiement d'une course"""
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur a accès à cette course
        if not (
            (hasattr(request.user, 'client') and trip.client == request.user.client) or
            (hasattr(request.user, 'captain') and trip.captain == request.user.captain)
        ):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Récupérer les paiements associés
        payments = Payment.objects.filter(
            trip=trip,
            type__in=[Payment.PaymentType.TRIP, Payment.PaymentType.TIP, Payment.PaymentType.CARBON_OFFSET]
        ).order_by('-created_at')
        
        payment_data = []
        for payment in payments:
            payment_data.append({
                'id': payment.id,
                'type': payment.type,
                'amount': payment.amount,
                'status': payment.status,
                'payment_method': payment.payment_method,
                'created_at': payment.created_at,
                'completed_at': payment.completed_at
            })
        
        return Response({
            'trip_id': trip.id,
            'trip_status': trip.status,
            'payment_status': trip.payment_status,
            'total_price': trip.total_price,
            'base_price': trip.base_price,
            'additional_charges': trip.additional_charges,
            'tip': trip.tip,
            'can_be_paid': trip.can_be_paid(),
            'qr_code': trip.qr_code,
            'payments': payment_data
        })


class WalletBalanceView(APIView):
    """Consultation du solde du portefeuille"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Récupérer le solde du portefeuille de l'utilisateur"""
        try:
            wallet = Wallet.objects.get(user=request.user)
            
            return Response({
                'balance': wallet.balance,
                'currency': wallet.currency,
                'loyalty_points': wallet.loyalty_points,
                'total_earned': wallet.total_earned,
                'total_spent': wallet.total_spent,
                'can_pay_amount': lambda amount: wallet.can_pay(Decimal(str(amount)))
            })
            
        except Wallet.DoesNotExist:
            return Response(
                {'error': 'Portefeuille non trouvé'},
                status=status.HTTP_404_NOT_FOUND
            )


class WalletRechargeView(APIView):
    """Recharge du portefeuille"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Recharger le portefeuille de l'utilisateur"""
        amount = request.data.get('amount')
        payment_method_id = request.data.get('payment_method_id')
        
        if not amount:
            return Response(
                {'error': 'Montant requis'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not payment_method_id:
            return Response(
                {'error': 'payment_method_id requis'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            amount = Decimal(str(amount))
            if amount <= 0:
                return Response(
                    {'error': 'Le montant doit être positif'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Minimum de recharge
            if amount < Decimal('10.00'):
                return Response(
                    {'error': 'Montant minimum de recharge: 10€'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Maximum de recharge
            if amount > Decimal('500.00'):
                return Response(
                    {'error': 'Montant maximum de recharge: 500€'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
        except (ValueError, TypeError):
            return Response(
                {'error': 'Montant invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Effectuer le paiement de recharge
            payment_result = PaymentService.process_payment(
                user=request.user,
                amount=amount,
                payment_method_id=payment_method_id,
                description=f"Recharge portefeuille - {amount}€",
                metadata={
                    'wallet_recharge': True
                },
                payment_type=Payment.PaymentType.WALLET_RECHARGE
            )
            
            # Ajouter les fonds au portefeuille
            wallet, created = Wallet.objects.get_or_create(user=request.user)
            wallet.add_funds(
                amount=amount,
                payment_method=Payment.PaymentMethod.CARD,
                description=f"Recharge via carte - {amount}€"
            )
            # Recharger l'objet pour récupérer le nouveau solde mis à jour
            wallet.refresh_from_db()
            
            return Response({
                'message': 'Portefeuille rechargé avec succès',
                'payment_id': payment_result.get('id'),
                'amount_added': amount,
                'new_balance': wallet.balance,
                'payment_method': 'card'
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Erreur lors de la recharge: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TripQRCodeView(APIView):
    """Génération et récupération du QR code d'une course"""
    permission_classes = [IsAuthenticated]

    def get(self, request, trip_id):
        """Récupérer le QR code d'une course"""
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur est le client de cette course
        if not (hasattr(request.user, 'client') and trip.client == request.user.client):
            return Response(
                {'error': 'Seul le client peut accéder au QR code'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la course est payée
        if trip.payment_status != 'PAID':
            return Response(
                {'error': 'La course doit être payée pour générer le QR code'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Générer le QR code s'il n'existe pas
        if not trip.qr_code:
            qr_code = generate_trip_qr_code(trip)
        else:
            qr_code = trip.qr_code
        
        # Générer l'image QR code
        qr_image = generate_trip_qr_image(trip)
        
        return Response({
            'trip_id': trip.id,
            'qr_code': qr_code,
            'qr_image': qr_image,
            'trip_details': {
                'departure': trip.start_location,
                'arrival': trip.end_location,
                'scheduled_time': trip.scheduled_start_time,
                'captain_name': trip.captain.user.get_full_name(),
                'boat_name': trip.boat.name,
                'passenger_count': trip.passenger_count
            }
        })
