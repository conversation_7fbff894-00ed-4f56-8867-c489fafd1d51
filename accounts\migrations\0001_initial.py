# Generated by Django 4.2.8 on 2025-05-30 23:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.<PERSON>r<PERSON><PERSON>(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=254, unique=True, verbose_name="adresse email"
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        blank=True, max_length=15, verbose_name="numéro de téléphone"
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("CLIENT", "Client"),
                            ("CAPTAIN", "Capitaine"),
                            ("ESTABLISHMENT", "Établissement"),
                        ],
                        max_length=20,
                        verbose_name="type d'utilisateur",
                    ),
                ),
                (
                    "profile_picture",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="profile_pictures/",
                        verbose_name="photo de profil",
                    ),
                ),
                (
                    "email_verified",
                    models.BooleanField(default=False, verbose_name="email vérifié"),
                ),
                (
                    "phone_verified",
                    models.BooleanField(
                        default=False, verbose_name="téléphone vérifié"
                    ),
                ),
                ("facebook_id", models.CharField(blank=True, max_length=50, null=True)),
                ("google_id", models.CharField(blank=True, max_length=50, null=True)),
                ("apple_id", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Captain",
            fields=[
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        serialize=False,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                ("experience", models.TextField(verbose_name="expérience")),
                (
                    "average_rating",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=3,
                        verbose_name="note moyenne",
                    ),
                ),
                (
                    "total_trips",
                    models.IntegerField(
                        default=0, verbose_name="nombre total de courses"
                    ),
                ),
                (
                    "wallet_balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="solde du portefeuille",
                    ),
                ),
                (
                    "is_available",
                    models.BooleanField(default=True, verbose_name="disponible"),
                ),
                (
                    "current_location",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="position actuelle"
                    ),
                ),
                (
                    "license_number",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="numéro de licence"
                    ),
                ),
                (
                    "license_expiry_date",
                    models.DateField(
                        blank=True,
                        null=True,
                        verbose_name="date d'expiration de la licence",
                    ),
                ),
                (
                    "years_of_experience",
                    models.IntegerField(default=0, verbose_name="années d'expérience"),
                ),
                (
                    "certifications",
                    models.JSONField(default=list, verbose_name="certifications"),
                ),
                (
                    "specializations",
                    models.JSONField(default=list, verbose_name="spécialisations"),
                ),
                (
                    "availability_status",
                    models.CharField(
                        default="AVAILABLE",
                        max_length=20,
                        verbose_name="statut de disponibilité",
                    ),
                ),
                (
                    "boat_photos",
                    models.JSONField(default=list, verbose_name="photos du bateau"),
                ),
                (
                    "rate_per_km",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=7,
                        null=True,
                        verbose_name="tarif par kilomètre (€)",
                    ),
                ),
                (
                    "rate_per_hour",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=7,
                        null=True,
                        verbose_name="tarif par heure (€)",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Client",
            fields=[
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        serialize=False,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "wallet_balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="solde du portefeuille",
                    ),
                ),
                (
                    "date_of_birth",
                    models.DateField(
                        blank=True, null=True, verbose_name="date de naissance"
                    ),
                ),
                (
                    "nationality",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="nationalité"
                    ),
                ),
                (
                    "preferred_language",
                    models.CharField(
                        default="fr", max_length=10, verbose_name="langue préférée"
                    ),
                ),
                (
                    "emergency_contact_name",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        verbose_name="nom du contact d'urgence",
                    ),
                ),
                (
                    "emergency_contact_phone",
                    models.CharField(
                        blank=True,
                        max_length=15,
                        verbose_name="téléphone du contact d'urgence",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Establishment",
            fields=[
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        serialize=False,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="nom")),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("RESTAURANT", "Restaurant"),
                            ("HOTEL", "Hôtel"),
                            ("PRIVATE_BEACH", "Plage privée"),
                        ],
                        max_length=20,
                        verbose_name="type d'établissement",
                    ),
                ),
                ("address", models.CharField(max_length=255, verbose_name="adresse")),
                ("description", models.TextField(verbose_name="description")),
                (
                    "main_photo",
                    models.ImageField(
                        upload_to="establishment_photos/",
                        verbose_name="photo principale",
                    ),
                ),
                (
                    "secondary_photos",
                    models.JSONField(default=list, verbose_name="photos secondaires"),
                ),
                (
                    "wallet_balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="solde du portefeuille",
                    ),
                ),
                (
                    "business_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="nom commercial"
                    ),
                ),
                (
                    "business_type",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="type d'activité"
                    ),
                ),
                (
                    "registration_number",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        verbose_name="numéro d'enregistrement",
                    ),
                ),
                (
                    "tax_id",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="numéro de TVA"
                    ),
                ),
                (
                    "opening_hours",
                    models.JSONField(default=dict, verbose_name="horaires d'ouverture"),
                ),
                (
                    "services_offered",
                    models.JSONField(default=list, verbose_name="services proposés"),
                ),
                (
                    "average_rating",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=3,
                        verbose_name="note moyenne",
                    ),
                ),
                (
                    "location_coordinates",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="coordonnées GPS"
                    ),
                ),
                ("website", models.URLField(blank=True, verbose_name="site web")),
                (
                    "social_media",
                    models.JSONField(default=dict, verbose_name="réseaux sociaux"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="FavoriteLocation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="nom")),
                ("address", models.CharField(max_length=255, verbose_name="adresse")),
                (
                    "coordinates",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="coordonnées GPS"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="notes")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="favorite_locations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "emplacement favori",
                "verbose_name_plural": "emplacements favoris",
                "ordering": ["name"],
                "unique_together": {("user", "name")},
            },
        ),
        migrations.CreateModel(
            name="FavoriteCaptain",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="notes")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="favorite_captains",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "captain",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="favorited_by",
                        to="accounts.captain",
                    ),
                ),
            ],
            options={
                "verbose_name": "capitaine favori",
                "verbose_name_plural": "capitaines favoris",
                "ordering": ["-created_at"],
                "unique_together": {("user", "captain")},
            },
        ),
    ]
