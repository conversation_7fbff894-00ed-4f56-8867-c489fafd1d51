"""
Services utilitaires pour l'application establishments.
"""

import random
import string
from decimal import Decimal
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from datetime import datetime, timedelta

from accounts.models import Captain, Establishment
from boats.models import Boat
from trips.models import Trip, ShuttleTripRequest
from payments.models import Wallet, Payment
from notifications.services import create_notification
from .exceptions import BoatmanRegistrationError, EmailSendingError, WalletError

User = get_user_model()


class BoatmanRegistrationService:
    """Service pour l'enregistrement des bateliers"""
    
    @staticmethod
    def generate_temporary_password(length=8):
        """Générer un mot de passe temporaire aléatoire"""
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))
    
    @staticmethod
    def create_boatman(establishment, boatman_data):
        """
        Créer un nouveau batelier avec toutes les étapes nécessaires.
        
        Args:
            establishment: Instance de l'établissement
            boatman_data: Dictionnaire avec les données du batelier
            
        Returns:
            dict: Informations du batelier créé
            
        Raises:
            BoatmanRegistrationError: En cas d'erreur lors de la création
        """
        try:
            # Vérifier si l'email existe déjà
            if User.objects.filter(email=boatman_data['email']).exists():
                raise BoatmanRegistrationError("Un utilisateur avec cet email existe déjà")
            
            # Générer un mot de passe temporaire
            temporary_password = BoatmanRegistrationService.generate_temporary_password()
            
            # Créer l'utilisateur
            user = User.objects.create_user(
                email=boatman_data['email'],
                password=temporary_password,
                first_name=boatman_data['first_name'],
                last_name=boatman_data['last_name'],
                phone_number=boatman_data['phone_number'],
                is_captain=True
            )
            
            # Créer le profil Captain
            captain = Captain.objects.create(
                user=user,
                experience=boatman_data.get('experience', ''),
                license_number=boatman_data.get('license_number', ''),
                is_available=True,
                availability_status='AVAILABLE',
                rate_per_km=Decimal('25.00'),  # Tarif par défaut
                rate_per_hour=Decimal('50.00'),  # Tarif par défaut
            )
            
            # Créer un bateau par défaut
            boat = Boat.objects.create(
                captain=captain,
                name=f"Bateau de {boatman_data['first_name']} {boatman_data['last_name']}",
                boat_type='classic',
                capacity=6,
                is_available=True,
                zone_served="Zone par défaut",
                radius=20
            )
            
            # Envoyer l'email de bienvenue
            email_sent = EmailService.send_boatman_welcome_email(
                user=user,
                temporary_password=temporary_password,
                establishment=establishment
            )
            
            return {
                'captain': captain,
                'user': user,
                'boat': boat,
                'temporary_password': temporary_password,
                'email_sent': email_sent
            }
            
        except Exception as e:
            raise BoatmanRegistrationError(f"Erreur lors de l'enregistrement: {str(e)}")


class EmailService:
    """Service pour l'envoi d'emails"""
    
    @staticmethod
    def send_boatman_welcome_email(user, temporary_password, establishment):
        """
        Envoyer l'email de bienvenue à un nouveau batelier.
        
        Args:
            user: Instance de l'utilisateur
            temporary_password: Mot de passe temporaire
            establishment: Instance de l'établissement
            
        Returns:
            bool: True si l'email a été envoyé avec succès
        """
        try:
            subject = f"Bienvenue chez {establishment.name} - Vos identifiants Commodore"
            
            message = f"""
Bonjour {user.first_name} {user.last_name},

Vous avez été enregistré comme batelier pour {establishment.name} sur la plateforme Commodore.

Vos identifiants de connexion :
- Email : {user.email}
- Mot de passe temporaire : {temporary_password}

Veuillez vous connecter à l'espace batelier et changer votre mot de passe lors de votre première connexion.

URL de connexion : {getattr(settings, 'FRONTEND_URL', 'https://app.commodore.com')}/captain/login

Important : Ce mot de passe est temporaire et doit être changé lors de votre première connexion pour des raisons de sécurité.

Cordialement,
L'équipe Commodore pour {establishment.name}
            """
            
            send_mail(
                subject=subject,
                message=message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=[user.email],
                fail_silently=False,
            )
            
            return True
            
        except Exception as e:
            raise EmailSendingError(f"Erreur lors de l'envoi de l'email: {str(e)}")


class ShuttleManagementService:
    """Service pour la gestion des navettes"""
    
    @staticmethod
    def accept_shuttle_request(shuttle_request, captain, boat):
        """
        Accepter une demande de navette et créer la course.
        
        Args:
            shuttle_request: Instance de ShuttleTripRequest
            captain: Instance du capitaine assigné
            boat: Instance du bateau assigné
            
        Returns:
            Trip: Instance de la course créée
        """
        try:
            # Créer la course Trip
            trip = Trip.objects.create(
                client=shuttle_request.client,
                captain=captain,
                boat=boat,
                trip_type='NAVETTES_GRATUITES',
                start_location=shuttle_request.departure_location.get('city_name', '') if shuttle_request.departure_location else '',
                end_location=shuttle_request.arrival_location.get('city_name', '') if shuttle_request.arrival_location else '',
                scheduled_start_time=timezone.datetime.combine(
                    shuttle_request.departure_date,
                    shuttle_request.departure_time
                ),
                passenger_count=shuttle_request.passenger_count,
                base_price=Decimal('0.00'),
                total_price=Decimal('0.00'),
                payment_method='FREE_SHUTTLE',
                payment_status='PAID',  # Gratuit donc considéré comme payé
                status='ACCEPTED'
            )
            
            # Mettre à jour la demande
            shuttle_request.status = 'ACCEPTED'
            shuttle_request.save()
            
            return trip
            
        except Exception as e:
            raise Exception(f"Erreur lors de l'acceptation de la navette: {str(e)}")
    
    @staticmethod
    def reject_shuttle_request(shuttle_request, reason):
        """
        Rejeter une demande de navette.
        
        Args:
            shuttle_request: Instance de ShuttleTripRequest
            reason: Raison du rejet
        """
        shuttle_request.status = 'REJECTED'
        shuttle_request.save()


class WalletService:
    """Service pour la gestion du portefeuille"""
    
    @staticmethod
    def add_funds_to_wallet(user, amount, payment_method='CARD'):
        """
        Ajouter des fonds au portefeuille d'un établissement.
        
        Args:
            user: Instance de l'utilisateur établissement
            amount: Montant à ajouter
            payment_method: Méthode de paiement
            
        Returns:
            Payment: Instance du paiement créé
        """
        try:
            wallet = getattr(user, 'wallet', None)
            if not wallet:
                raise WalletError("Portefeuille non trouvé")
            
            if amount <= 0:
                raise WalletError("Le montant doit être positif")
            
            # Ajouter les fonds
            payment = wallet.add_funds(
                amount=amount,
                payment_method=payment_method,
                description=f"Recharge du portefeuille - {amount}€"
            )
            
            return payment
            
        except Exception as e:
            raise WalletError(f"Erreur lors de l'ajout des fonds: {str(e)}")
    
    @staticmethod
    def get_wallet_statistics(user, days=30):
        """
        Récupérer les statistiques du portefeuille.
        
        Args:
            user: Instance de l'utilisateur
            days: Nombre de jours pour les statistiques
            
        Returns:
            dict: Statistiques du portefeuille
        """
        wallet = getattr(user, 'wallet', None)
        if not wallet:
            return {}
        
        # Période d'analyse
        start_date = timezone.now() - timedelta(days=days)
        
        # Dépenses pour les navettes
        shuttle_expenses = Payment.objects.filter(
            user=user,
            type=Payment.PaymentType.SHUTTLE_PAYMENT,
            created_at__gte=start_date,
            status=Payment.Status.COMPLETED
        ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')
        
        return {
            'balance': wallet.balance,
            'total_earned': wallet.total_earned,
            'total_spent': wallet.total_spent,
            'shuttle_expenses_period': shuttle_expenses
        }


class EstablishmentStatsService:
    """Service pour les statistiques de l'établissement"""
    
    @staticmethod
    def get_dashboard_stats(establishment):
        """
        Récupérer les statistiques pour le tableau de bord.
        
        Args:
            establishment: Instance de l'établissement
            
        Returns:
            dict: Statistiques du tableau de bord
        """
        # Statistiques des navettes
        total_shuttles = Trip.objects.filter(
            trip_type='NAVETTES_GRATUITES'
        ).count()
        
        pending_requests = ShuttleTripRequest.objects.filter(
            establishment=establishment,
            status='PENDING'
        ).count()
        
        # Solde du portefeuille
        wallet = getattr(establishment.user, 'wallet', None)
        available_balance = wallet.balance if wallet else Decimal('0.00')
        
        return {
            'total_shuttles': total_shuttles,
            'pending_requests': pending_requests,
            'available_balance': available_balance
        }
