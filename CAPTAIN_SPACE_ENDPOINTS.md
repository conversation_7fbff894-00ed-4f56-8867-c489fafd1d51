# 📱 ESPACE CAPITAINE - <PERSON><PERSON>POINTS COMPLETS

## 🎯 **DOCUMENTATION COMPLÈTE DES ENDPOINTS**

Tous les endpoints de l'espace capitaine mobile avec exemples de réponses JSON complets selon les spécifications fournies.

---

## 🔐 **AUTHENTIFICATION**

### 1. Connexion
**POST** `/api/captain/login/`

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "motdepasse123"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "data": {
    "captain_id": "123",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "captain_name": "<PERSON>"
  }
}
```

### 2. Inscription
**POST** `/api/captain/signup/`

**Request:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "motdepasse123"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "data": {
    "captain_id": "124",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 3. Mot de passe oublié
**POST** `/api/captain/forgot-password/`

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Instructions de réinitialisation envoyées"
}
```

---

## 📊 **TABLEAU DE BORD**

### 4. Récupérer les données du tableau de bord
**GET** `/api/captain/dashboard/`

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "status": "success",
  "data": {
    "captain_name": "Cameron Williamson",
    "available_balance": 450.80,
    "total_trips": 45,
    "total_revenue": 1680.00,
    "trips": [
      {
        "trip_id": "123",
        "date": "2024-01-15",
        "status": "COMPLETED",
        "departure": "Port de Cannes",
        "destination": "Îles de Lérins",
        "client": "John Doe",
        "amount": 60.00
      },
      {
        "trip_id": "124",
        "date": "2024-01-14",
        "status": "COMPLETED",
        "departure": "Plage des sables",
        "destination": "Port de Cannes",
        "client": "Jane Smith",
        "amount": 40.00
      }
    ],
    "wallet_history": [
      {
        "transaction_id": "456",
        "type": "TRIP_EARNING",
        "amount": 48.00,
        "trip_details": {
          "departure": "Port de Cannes",
          "destination": "Îles de Lérins",
          "client": "John Doe",
          "duration": "45 min"
        },
        "timestamp": "2024-01-15T18:30:00Z"
      },
      {
        "transaction_id": "457",
        "type": "WITHDRAWAL",
        "amount": -200.00,
        "trip_details": {},
        "timestamp": "2024-01-14T10:00:00Z"
      }
    ]
  }
}
```

---

## 🚤 **GESTION DES COURSES**

### 5. Voir toutes les courses
**GET** `/api/captain/trips/?filter=COMPLETED&page=1&limit=20`

**Response (200 OK):**
```json
{
  "status": "success",
  "data": {
    "trips": [
      {
        "trip_id": "123",
        "date": "2024-01-15",
        "status": "COMPLETED",
        "departure": "Port de Cannes",
        "destination": "Îles de Lérins",
        "client": "John Doe",
        "amount": 60.00
      },
      {
        "trip_id": "124",
        "date": "2024-01-14",
        "status": "COMPLETED",
        "departure": "Plage des sables",
        "destination": "Port de Cannes",
        "client": "Jane Smith",
        "amount": 40.00
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45
    }
  }
}
```

### 6. Récupérer les détails d'une course
**GET** `/api/captain/trip/123/`

**Response (200 OK):**
```json
{
  "status": "success",
  "data": {
    "trip_id": "123",
    "status": "COMPLETED",
    "date": "2024-01-15",
    "duration": "45 min",
    "passengers": 4,
    "departure": "Port de Cannes",
    "destination": "Îles de Lérins",
    "client": {
      "client_id": "789",
      "name": "John Doe"
    },
    "amount": 60.00,
    "tip": 5.00,
    "total": 65.00,
    "review": {
      "rating": 5,
      "comment": "Excellent service, capitaine très professionnel !"
    }
  }
}
```

### 7. Accepter/Rejeter une course
**POST** `/api/captain/trip/123/accept/`

**Request:**
```json
{
  "captain_id": "123",
  "trip_id": "123"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Course acceptée"
}
```

### 8. Démarrer une course
**POST** `/api/captain/trip/123/start/`

**Request:**
```json
{
  "captain_id": "123",
  "trip_id": "123"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Course démarrée"
}
```

### 9. Terminer une course
**POST** `/api/captain/trip/123/end/`

**Request:**
```json
{
  "captain_id": "123",
  "trip_id": "123"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Course terminée"
}
```

### 10. Annuler une course
**POST** `/api/captain/trip/123/cancel/`

**Request:**
```json
{
  "captain_id": "123",
  "trip_id": "123"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Course annulée"
}
```

### 11. Suivre une course
**GET** `/api/captain/trip/123/track/`

**Response (200 OK):**
```json
{
  "status": "success",
  "data": {
    "trip_id": "123",
    "current_location": "Port de Cannes",
    "distance_traveled": 2.5,
    "departure": "Port de Cannes",
    "destination": "Îles de Lérins",
    "client": "John Doe"
  }
}
```

---

## 👤 **GESTION DU PROFIL**

### 12. Mettre à jour le mot de passe
**POST** `/api/captain/change-password/`

**Request:**
```json
{
  "captain_id": "123",
  "new_password": "nouveaumotdepasse123",
  "confirm_password": "nouveaumotdepasse123"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Mot de passe mis à jour avec succès"
}
```

### 13. Mettre à jour le numéro de téléphone
**POST** `/api/captain/update-phone/`

**Request:**
```json
{
  "captain_id": "123",
  "phone_number": "+33612345678"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Numéro de téléphone mis à jour avec succès"
}
```

### 14. Mettre à jour l'email
**POST** `/api/captain/update-email/`

**Request:**
```json
{
  "captain_id": "123",
  "email": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Email mis à jour avec succès"
}
```

### 15. Mettre à jour le profil complet
**POST** `/api/captain/profile/update/`

**Request:**
```json
{
  "captain_id": "123",
  "name": "Cameron Williamson",
  "email": "<EMAIL>",
  "phone": "+33612345678",
  "bio": "Capitaine expérimenté avec 10 ans d'expérience",
  "service_area": "Côte d'Azur",
  "service_area_radius": 50
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Profil mis à jour avec succès"
}
```

---

## 💳 **GESTION DES PAIEMENTS**

### 16. Ajouter/Mettre à jour la méthode de paiement
**POST** `/api/captain/payment-method/`

**Request:**
```json
{
  "captain_id": "123",
  "cardholder": "Cameron Williamson",
  "card_number": "****************",
  "expiry_date": "12/25",
  "cvv": "123"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Méthode de paiement ajoutée/mise à jour avec succès"
}
```

### 17. Traiter un paiement
**POST** `/api/captain/process-payment/`

**Request:**
```json
{
  "captain_id": "123",
  "amount": 150.00,
  "payment_method_id": "pm_1234567890"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Paiement effectué avec succès",
  "transaction_id": "TXN1234567890"
}
```

### 18. Retirer des fonds
**POST** `/api/captain/withdraw/`

**Request:**
```json
{
  "captain_id": "123",
  "amount": 200.00,
  "payment_method_id": "pm_1234567890"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Retrait effectué avec succès"
}
```

---

## 🔐 **VÉRIFICATION**

### 19. Envoyer le code de vérification
**POST** `/api/captain/send-verification-code/`

**Request:**
```json
{
  "captain_id": "123",
  "contact_method": "email",
  "contact_value": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Code de vérification envoyé"
}
```

### 20. Vérifier le code
**POST** `/api/captain/verify-code/`

**Request:**
```json
{
  "captain_id": "123",
  "code": "1572"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Code vérifié avec succès"
}
```

---

## 🚤 **GESTION DU BATEAU**

### 21. Mettre à jour les détails du bateau
**POST** `/api/captain/boat/update/`

**Request:**
```json
{
  "captain_id": "123",
  "boat": {
    "category": "classic",
    "name": "Beneteau Flyer 7.7 SUNdeck - Blue",
    "registration": "FR123456789",
    "capacity": 8,
    "fuel_type": "DIESEL",
    "fuel_consumption": 12.5,
    "rate_per_km": 2.50,
    "rate_per_hour": 45.00,
    "service_area_radius": 50
  }
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Détails du bateau mis à jour"
}
```

### 22. Ajouter des photos du bateau
**POST** `/api/captain/boat/photos/`

**Request:**
```json
{
  "captain_id": "123",
  "boat_id": "456",
  "photos": [
    "https://aws.s3.amazonaws.com/boats/photo1.jpg",
    "https://aws.s3.amazonaws.com/boats/photo2.jpg",
    "https://aws.s3.amazonaws.com/boats/photo3.jpg"
  ]
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Photos ajoutées avec succès"
}
```

---

## 📱 **QR CODE**

### 23. Valider un code QR
**POST** `/api/captain/validate-qr/`

**Request:**
```json
{
  "captain_id": "123",
  "qr_code": "{\"trip_id\":123,\"verification_url\":\"https://app.commodore.com/verify/123\"}"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "data": {
    "trip_id": "123",
    "client_id": "789",
    "valid": true
  }
}
```

---

## 📊 **RÉCAPITULATIF DES ENDPOINTS**

| # | Endpoint | Méthode | Description | Statut |
|---|----------|---------|-------------|--------|
| 1 | `/api/captain/login/` | POST | Connexion | ✅ |
| 2 | `/api/captain/signup/` | POST | Inscription | ✅ |
| 3 | `/api/captain/forgot-password/` | POST | Mot de passe oublié | ✅ |
| 4 | `/api/captain/dashboard/` | GET | Tableau de bord | ✅ |
| 5 | `/api/captain/trips/` | GET | Toutes les courses | ✅ |
| 6 | `/api/captain/trip/{id}/` | GET | Détails course | ✅ |
| 7 | `/api/captain/trip/{id}/accept/` | POST | Accepter course | ✅ |
| 8 | `/api/captain/trip/{id}/start/` | POST | Démarrer course | ✅ |
| 9 | `/api/captain/trip/{id}/end/` | POST | Terminer course | ✅ |
| 10 | `/api/captain/trip/{id}/cancel/` | POST | Annuler course | ✅ |
| 11 | `/api/captain/trip/{id}/track/` | GET | Suivre course | ✅ |
| 12 | `/api/captain/change-password/` | POST | Changer mot de passe | ✅ |
| 13 | `/api/captain/update-phone/` | POST | Mettre à jour téléphone | ✅ |
| 14 | `/api/captain/update-email/` | POST | Mettre à jour email | ✅ |
| 15 | `/api/captain/profile/update/` | POST | Mettre à jour profil | ✅ |
| 16 | `/api/captain/payment-method/` | POST | Méthode de paiement | ✅ |
| 17 | `/api/captain/process-payment/` | POST | Traiter paiement | ✅ |
| 18 | `/api/captain/withdraw/` | POST | Retirer fonds | ✅ |
| 19 | `/api/captain/send-verification-code/` | POST | Envoyer code | ✅ |
| 20 | `/api/captain/verify-code/` | POST | Vérifier code | ✅ |
| 21 | `/api/captain/boat/update/` | POST | Mettre à jour bateau | ✅ |
| 22 | `/api/captain/boat/photos/` | POST | Ajouter photos bateau | ✅ |
| 23 | `/api/captain/validate-qr/` | POST | Valider QR code | ✅ |

---

## 🎯 **STATUT FINAL**

### ✅ **COMPLÈTEMENT IMPLÉMENTÉ (100%)**

**Tous les 23 endpoints** de votre spécification ont été **entièrement implémentés** avec :

- ✅ **Structure de réponse exacte** selon votre spécification
- ✅ **Gestion d'erreurs complète** avec codes de statut appropriés
- ✅ **Authentification sécurisée** avec JWT tokens
- ✅ **Validation des données** d'entrée
- ✅ **Intégration avec les modèles** existants
- ✅ **Notifications automatiques** pour les actions importantes
- ✅ **Documentation complète** avec exemples JSON

### 🚀 **PRÊT POUR LA PRODUCTION**

L'espace capitaine mobile est maintenant **100% fonctionnel** et prêt pour l'intégration avec votre application React Native !

**Base URL:** `https://votre-domaine.com/api/captain/`
