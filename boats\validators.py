"""
Validateurs pour les modèles et vues liés aux bateaux.
"""

from rest_framework.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from boats.models import Boat


def validate_boat_type(boat_type):
    """
    Valide que le type de bateau est l'un des types autorisés.
    
    Args:
        boat_type (str): Le type de bateau à valider
        
    Returns:
        str: Le type de bateau si valide
        
    Raises:
        ValidationError: Si le type de bateau n'est pas valide
    """
    valid_boat_types = [choice[0] for choice in Boat.BoatTypes.choices]
    
    if boat_type not in valid_boat_types:
        raise ValidationError({
            'boat_type': _(f'Type de bateau invalide. Valeurs autorisées: {valid_boat_types}')
        })
    
    return boat_type


def validate_fuel_type(fuel_type):
    """
    Valide que le type de carburant est l'un des types autorisés.
    
    Args:
        fuel_type (str): Le type de carburant à valider
        
    Returns:
        str: Le type de carburant si valide
        
    Raises:
        ValidationError: Si le type de carburant n'est pas valide
    """
    valid_fuel_types = [choice[0] for choice in Boat.FuelTypes.choices]
    
    if fuel_type not in valid_fuel_types:
        raise ValidationError({
            'fuel_type': _(f'Type de carburant invalide. Valeurs autorisées: {valid_fuel_types}')
        })
    
    return fuel_type
