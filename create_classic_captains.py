#!/usr/bin/env python
"""
Script pour créer plusieurs capitaines avec des bateaux CLASSIC
pour tester le système de devis multiples
"""

import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import Captain
from boats.models import Boat
from decimal import Decimal

User = get_user_model()

def create_classic_captain(email, first_name, last_name, rate_per_hour, rate_per_km, boat_name, capacity, color):
    """Créer un capitaine avec un bateau CLASSIC"""
    
    # Créer l'utilisateur
    user, created = User.objects.get_or_create(
        email=email,
        defaults={
            'first_name': first_name,
            'last_name': last_name,
            'type': 'CAPTAIN',
            'is_active': True,
            'email_verified': True
        }
    )
    
    if created:
        user.set_password('password123')
        user.save()
        print(f"✅ Utilisateur créé: {email}")
    else:
        print(f"ℹ️ Utilisateur existe déjà: {email}")
    
    # <PERSON><PERSON><PERSON> le profil capitaine
    captain, created = Captain.objects.get_or_create(
        user=user,
        defaults={
            'experience': f'{rate_per_hour//10} ans d\'expérience maritime',
            'license_number': f'LIC{user.id:03d}',
            'rate_per_hour': Decimal(str(rate_per_hour)),
            'rate_per_km': Decimal(str(rate_per_km)),
            'is_available': True,
            'availability_status': 'AVAILABLE'
        }
    )
    
    if created:
        print(f"✅ Capitaine créé: {first_name} {last_name}")
    else:
        print(f"ℹ️ Capitaine existe déjà: {first_name} {last_name}")
        # Mettre à jour les tarifs
        captain.rate_per_hour = Decimal(str(rate_per_hour))
        captain.rate_per_km = Decimal(str(rate_per_km))
        captain.save()
    
    # Créer le bateau CLASSIC
    boat, created = Boat.objects.get_or_create(
        captain=captain,
        defaults={
            'name': boat_name,
            'registration_number': f'CL{user.id:04d}',
            'boat_type': 'CLASSIC',  # Tous les bateaux sont CLASSIC
            'capacity': capacity,
            'color': color,
            'fuel_type': 'GASOLINE',
            'fuel_consumption': Decimal('10.5'),
            'zone_served': 'Cotonou, Bénin',
            'radius': 50,
            'is_available': True
        }
    )
    
    if created:
        print(f"✅ Bateau CLASSIC créé: {boat_name}")
    else:
        print(f"ℹ️ Bateau existe déjà: {boat_name}")
        # Mettre à jour les infos du bateau
        boat.name = boat_name
        boat.boat_type = 'CLASSIC'
        boat.capacity = capacity
        boat.color = color
        boat.save()
    
    return captain, boat

def main():
    print("🚢 Création de capitaines avec des bateaux CLASSIC...\n")
    
    # Liste des capitaines CLASSIC à créer
    captains_data = [
        {
            'email': '<EMAIL>',
            'first_name': 'Zeta',
            'last_name': 'Capitaine',
            'rate_per_hour': 25.00,  # Le moins cher
            'rate_per_km': 1.20,
            'boat_name': 'Classic Explorer',
            'capacity': 6,
            'color': 'Blanc'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Eta',
            'last_name': 'Capitaine',
            'rate_per_hour': 32.00,  # Prix moyen
            'rate_per_km': 1.60,
            'boat_name': 'Ocean Classic',
            'capacity': 8,
            'color': 'Bleu'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Theta',
            'last_name': 'Capitaine',
            'rate_per_hour': 38.00,  # Prix moyen-élevé
            'rate_per_km': 1.90,
            'boat_name': 'Classic Voyager',
            'capacity': 10,
            'color': 'Rouge'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Iota',
            'last_name': 'Capitaine',
            'rate_per_hour': 45.00,  # Prix élevé
            'rate_per_km': 2.25,
            'boat_name': 'Premium Classic',
            'capacity': 12,
            'color': 'Noir'
        }
    ]
    
    created_captains = []
    
    for data in captains_data:
        print(f"📋 Création de {data['first_name']} {data['last_name']}...")
        captain, boat = create_classic_captain(**data)
        created_captains.append((captain, boat))
        print(f"   💰 Tarif horaire: {data['rate_per_hour']}€/h")
        print(f"   🚤 Bateau CLASSIC: {data['boat_name']} ({data['capacity']} places)")
        print()
    
    print("🎉 Tous les capitaines CLASSIC ont été créés !")
    print("\n📊 Résumé des tarifs horaires (bateaux CLASSIC):")
    
    # Afficher tous les capitaines avec bateaux CLASSIC
    all_classic_captains = Captain.objects.filter(boat__boat_type='CLASSIC').order_by('rate_per_hour')
    
    for captain in all_classic_captains:
        boat = captain.boat
        print(f"   • {captain.user.first_name} {captain.user.last_name}: {captain.rate_per_hour}€/h - {boat.name} ({boat.capacity} places)")
    
    print(f"\n✅ {all_classic_captains.count()} capitaines avec bateaux CLASSIC disponibles pour les tests")

if __name__ == '__main__':
    main()
