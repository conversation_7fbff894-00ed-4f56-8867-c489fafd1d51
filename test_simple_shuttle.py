#!/usr/bin/env python
"""
Test simple des navettes sans arrival_location
"""

import os
import django
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from accounts.models import Establishment, Client
from trips.serializers import ShuttleTripRequestSerializer
from django.utils import timezone
from datetime import date, timedelta

def test_payload_without_arrival():
    print("🧪 Test du payload sans arrival_location")
    print("=" * 50)
    
    # Payload utilisateur (SANS arrival_location)
    user_payload = {
        "departure_location": {
            "city_name": "Port de Cannes",
            "coordinates": {
                "latitude": 43.5528,
                "longitude": 7.0174
            },
            "timestamp": timezone.now().isoformat()
        },
        "passenger_count": 2,
        "establishment": 1,  # On utilisera le premier établissement
        "departure_date": (date.today() + timedelta(days=1)).strftime('%Y-%m-%d'),
        "departure_time": "19:30:00",
        "message": "Réservation navette sans arrival_location"
    }
    
    # Trouver un établissement avec coordonnées
    establishment = Establishment.objects.filter(
        longitude__isnull=False, 
        latitude__isnull=False
    ).first()
    
    if establishment:
        user_payload['establishment'] = establishment.user.id
        print(f"✅ Établissement: {establishment.name}")
        print(f"   Coordonnées: {establishment.latitude}, {establishment.longitude}")
        
        print("\n📝 Payload utilisateur (ce que le client envoie):")
        print(json.dumps(user_payload, indent=2, default=str))
        
        print("\n🔍 Test de validation...")
        serializer = ShuttleTripRequestSerializer(data=user_payload)
        
        if serializer.is_valid():
            print("✅ Payload valide !")
            print("✅ Le système peut calculer la distance automatiquement")
            return True
        else:
            print(f"❌ Erreurs de validation: {serializer.errors}")
            return False
    else:
        print("❌ Aucun établissement avec coordonnées trouvé")
        return False

if __name__ == "__main__":
    success = test_payload_without_arrival()
    print(f"\n{'🎉 Test réussi!' if success else '❌ Test échoué'}")
