# Guide de Configuration de l'Authentification Sociale

Ce guide détaille les étapes nécessaires pour configurer l'authentification sociale avec Facebook, Google et Apple dans l'application Commodore.

## 1. Configuration Facebook

### Étape 1 : Création d'une application Facebook
1. Aller sur [Facebook Developers](https://developers.facebook.com)
2. <PERSON><PERSON><PERSON> sur "Mes Applications" > "Créer une application"
3. Sélectionner "Consommateur" comme type d'application
4. Remplir les informations de base de l'application

### Étape 2 : Configuration de Facebook Login
1. Dans le tableau de bord, ajouter le produit "Facebook Login"
2. Dans les paramètres de Facebook Login, configurer :
   - URI de redirection OAuth : `http://localhost:3000/auth/facebook/callback` (dev)
   - URI de redirection OAuth : `https://votre-domaine.com/auth/facebook/callback` (prod)

### Étape 3 : Récupération des clés
1. Dans "Paramètres" > "Général", noter :
   - ID de l'application : `1643858832951513`
   - <PERSON><PERSON> secrète : `e5fba29ec001241b438c9da4adfb6b2c`

## 2. Configuration Google

### Étape 1 : Création du projet
1. Aller sur [Google Cloud Console](https://console.cloud.google.com)
2. Créer un nouveau projet ou sélectionner un projet existant
3. Activer l'API Google+ API

### Étape 2 : Configuration OAuth
1. Aller dans "APIs & Services" > "Credentials"
2. Cliquer sur "Create Credentials" > "OAuth client ID"
3. Configurer l'écran de consentement OAuth
4. Créer un ID client OAuth 2.0
5. Ajouter les URIs de redirection autorisés :
   - `http://localhost:3000/auth/google/callback` (dev)
   - `https://votre-domaine.com/auth/google/callback` (prod)

### Étape 3 : Récupération des clés
1. Noter le Client ID et Client Secret
2. Ajouter ces valeurs dans le fichier .env :
   ```
   GOOGLE_CLIENT_ID=votre-client-id
   GOOGLE_CLIENT_SECRET=votre-client-secret
   ```

## 3. Configuration Apple Sign In

### Étape 1 : Configuration du compte développeur
1. Aller sur [Apple Developer](https://developer.apple.com/account)
2. S'assurer d'avoir un compte développeur Apple actif

### Étape 2 : Création des identifiants
1. Dans "Certificates, Identifiers & Profiles" :
   - Créer un nouvel identifiant d'application
   - Activer "Sign In with Apple"
   - Configurer les domaines et les URLs de retour

### Étape 3 : Génération des clés
1. Dans la section "Keys" :
   - Créer une nouvelle clé
   - Activer "Sign In with Apple"
   - Télécharger la clé privée
2. Noter :
   - Key ID
   - Team ID
   - Bundle ID

### Étape 4 : Configuration dans .env
```
APPLE_APP_ID=votre-app-id
APPLE_TEAM_ID=votre-team-id
APPLE_KEY_ID=votre-key-id
APPLE_PRIVATE_KEY=votre-private-key
```

## 4. Vérification de l'installation

### Étape 1 : Vérification des dépendances
1. S'assurer que les packages suivants sont installés :
   ```
   django-allauth
   dj-rest-auth
   ```

### Étape 2 : Vérification de la configuration Django
1. Vérifier que les apps sont dans INSTALLED_APPS :
   ```python
   INSTALLED_APPS = [
       ...
       'dj_rest_auth',
       'dj_rest_auth.registration',
       'allauth',
       'allauth.account',
       'allauth.socialaccount',
       'allauth.socialaccount.providers.facebook',
       'allauth.socialaccount.providers.google',
       'allauth.socialaccount.providers.apple',
   ]
   ```

### Étape 3 : Test des endpoints
1. Tester les URLs d'authentification :
   - Facebook : `/auth/facebook/`
   - Google : `/auth/google/`
   - Apple : `/auth/apple/`

## 5. Dépannage

### Problèmes courants Facebook
- Erreur "Invalid redirect_uri" : Vérifier les URIs de redirection dans la console Facebook
- Erreur "Invalid scope" : Vérifier les permissions demandées

### Problèmes courants Google
- Erreur "invalid_client" : Vérifier les URIs de redirection
- Erreur "unauthorized_client" : Vérifier la configuration de l'écran de consentement

### Problèmes courants Apple
- Erreur de validation de la clé privée : Vérifier le format de la clé
- Erreur de configuration : Vérifier les domaines autorisés

## 6. Sécurité

### Bonnes pratiques
1. Ne jamais commiter les clés secrètes dans le code
2. Utiliser des variables d'environnement
3. Limiter les permissions accordées aux applications
4. Mettre à jour régulièrement les SDK
5. Surveiller les tentatives de connexion suspectes

### Surveillance
1. Mettre en place des logs pour les connexions sociales
2. Configurer des alertes pour les erreurs d'authentification
3. Surveiller les taux de conversion des connexions sociales
