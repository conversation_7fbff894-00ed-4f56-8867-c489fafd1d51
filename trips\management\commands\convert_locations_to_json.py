from django.core.management.base import BaseCommand
from trips.models import Trip, Shuttle
from django.db import transaction
import json

class Command(BaseCommand):
    help = "Convert all start_location and end_location fields to JSON for Trip and Shuttle."

    def handle(self, *args, **options):
        self.stdout.write(self.style.WARNING('Conversion des champs de localisation en JSON...'))
        with transaction.atomic():
            # Traitement des Trip
            for trip in Trip.objects.all():
                changed = False
                # Convertir start_location
                if isinstance(trip.start_location, str):
                    try:
                        json.loads(trip.start_location)
                    except Exception:
                        trip.start_location = {"address": trip.start_location}
                        changed = True
                # Convertir end_location
                if isinstance(trip.end_location, str):
                    try:
                        json.loads(trip.end_location)
                    except Exception:
                        trip.end_location = {"address": trip.end_location}
                        changed = True
                if changed:
                    trip.save()
            # Traitement des Shuttle
            for shuttle in Shuttle.objects.all():
                changed = False
                if isinstance(shuttle.start_location, str):
                    try:
                        json.loads(shuttle.start_location)
                    except Exception:
                        shuttle.start_location = {"address": shuttle.start_location}
                        changed = True
                if isinstance(shuttle.end_location, str):
                    try:
                        json.loads(shuttle.end_location)
                    except Exception:
                        shuttle.end_location = {"address": shuttle.end_location}
                        changed = True
                if changed:
                    shuttle.save()
        self.stdout.write(self.style.SUCCESS('Conversion terminée pour tous les Trip et Shuttle.'))
