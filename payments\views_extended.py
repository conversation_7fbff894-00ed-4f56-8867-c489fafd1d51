from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from django.conf import settings
from decimal import Decimal

from .services import PaymentService
from .models import Transaction, Wallet
from trips.models import Trip, Shuttle
from trips.shared_payments import SharedPaymentInvitation, ShuttleBooking
from boats.models import MaintenanceRecord as Maintenance
# Importations des services utilisés dans ce fichier

class SharedPaymentView(APIView):
    """
    Vue pour gérer les paiements partagés d'une course.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, ride_id):
        """
        Traite un paiement partagé pour une course.
        """
        # Récupérer la course
        trip = get_object_or_404(Trip, id=ride_id)
        
        # Vérifier le token d'invitation
        invitation_token = request.data.get("invitation_token")
        if not invitation_token:
            return Response(
                {"error": "Token d'invitation requis."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        invitation = get_object_or_404(
            SharedPaymentInvitation, 
            token=invitation_token,
            trip=trip,
            status__in=['PENDING', 'ACCEPTED']
        )
        
        # Vérifier que l'invitation n'a pas expiré
        if invitation.expires_at < timezone.now():
            invitation.status = 'EXPIRED'
            invitation.save()
            return Response(
                {"error": "Cette invitation a expiré."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Récupérer les informations de paiement
        payment_method_id = request.data.get("payment_method_id")
        amount = invitation.amount
        
        try:
            with transaction.atomic():
                # Effectuer le paiement
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=amount,
                    payment_method_id=payment_method_id,
                    description=f"Paiement partagé pour la course #{trip.id}",
                    metadata={
                        "trip_id": trip.id,
                        "shared_payment_invitation_id": invitation.id
                    }
                )
                
                # Mettre à jour le statut de l'invitation
                invitation.status = 'PAID'
                invitation.save()
                
                # Mettre à jour le statut de paiement du trajet
                trip.update_payment_status()
                
                # Obtenir le solde du portefeuille
                wallet_balance = PaymentService.get_wallet_balance(request.user)
                
                # Construire la réponse détaillée
                response_data = {
                    "id": payment_result.get("id"),
                    "amount": amount,
                    "currency": "eur",
                    "status": "succeeded",
                    "created_at": timezone.now().isoformat(),
                    "ride": {
                        "id": trip.id,
                        "total_amount": trip.total_price,
                        "amount_paid": trip.amount_paid,
                        "amount_remaining": trip.total_price - trip.amount_paid,
                        "payment_status": trip.payment_status,
                        "participants": trip.get_payment_participants()
                    }
                }
                
                # Notifier le créateur du trajet
                NotificationService.send_notification(
                    user=invitation.inviter,
                    title="Paiement partagé reçu",
                    body=f"{request.user.get_full_name()} a payé sa part ({amount}€) pour la course",
                    data={
                        "type": "shared_payment_completed",
                        "trip_id": trip.id,
                        "amount": str(amount),
                        "invitation_id": invitation.id
                    }
                )
                
                return Response(response_data, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class ShuttlePaymentView(APIView):
    """
    Vue pour gérer les paiements de réservation de navette.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, shuttle_id):
        """
        Traite le paiement d'une réservation de navette.
        """
        # Récupérer la navette
        shuttle = get_object_or_404(Shuttle, id=shuttle_id)
        
        # Vérifier que l'utilisateur a un profil client
        try:
            client = request.user.client
        except:
            return Response(
                {"error": "Seuls les clients peuvent réserver des places sur une navette."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Récupérer les informations de paiement
        payment_method_id = request.data.get("payment_method_id")
        seats = request.data.get("seats", 1)
        passenger_ids = request.data.get("passenger_ids", [])
        
        # Vérifier la disponibilité des places
        available_seats = shuttle.max_capacity - shuttle.current_bookings
        if seats > available_seats:
            return Response(
                {"error": f"Il n'y a que {available_seats} places disponibles sur cette navette."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Calculer le montant du paiement
        amount = shuttle.price_per_person * seats
        
        try:
            with transaction.atomic():
                # Importer les modèles nécessaires
                from payments.models import Payment
                import logging
                logger = logging.getLogger(__name__)
                
                try:
                    # Créer la réservation avec des valeurs limitées en longueur
                    passenger_names = request.data.get("passenger_names", [])
                    special_requests = request.data.get("special_requests", "")
                    # Limiter la longueur des champs si nécessaire
                    if special_requests and len(special_requests) > 1000:  # Une limite raisonnable
                        special_requests = special_requests[:1000]
                    
                    # Le client est identifié par son user_id puisque user est la clé primaire
                    # Dans le modèle Client, client.user_id est la clé primaire du Client
                    logger.info(f"Création de réservation pour navette {shuttle.id}, client.user_id={client.user_id}, sièges {seats}")
                    
                    # Créer la réservation en passant explicitement le client
                    # Le modèle Client utilise user comme clé primaire, donc pas d'attribut id
                    booking = ShuttleBooking.objects.create(
                        shuttle=shuttle,
                        client=client,  # Django sait qu'il doit utiliser client.user_id pour la référence
                        number_of_seats=seats,
                        passenger_names=passenger_names,
                        special_requests=special_requests,
                        status="RESERVED"  # Valeur exacte de l'enum
                    )
                    logger.info(f"Réservation créée avec succès: ID {booking.id}")
                    
                    # Effectuer le paiement avec le type SHUTTLE explicitement spécifié
                    logger.info(f"Traitement du paiement: montant {amount}, méthode {payment_method_id}")
                    payment_result = PaymentService.process_payment(
                        user=request.user,
                        amount=amount,
                        payment_method_id=payment_method_id,
                        description=f"Réservation navette {shuttle.route_name}",  # Description plus courte
                        metadata={
                            "shuttle_id": str(shuttle.id),  # Conversion en string pour éviter les problèmes de sérialisation
                            "shuttle_booking_id": str(booking.id)
                        },
                        payment_type=Payment.PaymentType.SHUTTLE  # Spécifier explicitement le type de paiement
                    )
                    logger.info(f"Paiement traité avec succès: ID {payment_result.get('id')}")
                    
                except Exception as e:
                    logger.error(f"Erreur détaillée lors du paiement de navette: {str(e)}")
                    # On relève l'exception pour qu'elle soit gérée par le bloc except extérieur
                    raise
                
                # Mettre à jour la réservation
                booking.status = "PAID"
                booking.amount_paid = amount
                booking.transaction_id = payment_result.get("id")
                booking.payment_method = "card" if payment_method_id != "wallet" else "wallet"
                booking.save()
                
                # Mettre à jour le nombre de places sur la navette
                shuttle.current_bookings += seats
                shuttle.save()
                
                # Assigner des numéros de siège
                # Générer les numéros de sièges séquentiels (A1, A2, ...)
                start_seat = shuttle.current_bookings - seats + 1
                seat_numbers = [f"A{i}" for i in range(start_seat, start_seat + seats)]
                booking.seat_numbers = seat_numbers
                booking.save()
                
                # Obtenir le solde du portefeuille
                wallet_balance = PaymentService.get_wallet_balance(request.user)
                previous_balance = wallet_balance + amount if payment_method_id == "wallet" else wallet_balance
                
                # Construire la réponse détaillée
                response_data = {
                    "id": payment_result.get("id"),
                    "amount": amount,
                    "currency": "eur",
                    "status": "succeeded",
                    "created_at": timezone.now().isoformat(),
                    "payment_method": {
                        "id": payment_method_id,
                        "type": "card" if payment_method_id != "wallet" else "wallet",
                        "card": payment_result.get("card", {}) if payment_method_id != "wallet" else None
                    },
                    "receipt_url": f"https://api.commodore.com/api/payments/receipts/{payment_result.get('id')}/",
                    "shuttle": {
                        "id": shuttle.id,
                        "name": shuttle.route_name,
                        "departure": shuttle.start_location,
                        "arrival": shuttle.end_location,
                        "departure_time": shuttle.departure_time.isoformat(),
                        "price_per_seat": shuttle.price_per_person,
                        "total_seats": shuttle.max_capacity,
                        "available_seats": shuttle.max_capacity - shuttle.current_bookings
                    },
                    "seats": seats,
                    "seat_numbers": seat_numbers,
                    "wallet": {
                        "previous_balance": previous_balance,
                        "current_balance": wallet_balance
                    }
                }
                
                # Notifier le capitaine de la navette
                if shuttle.captain and shuttle.captain.user:
                    from notifications.services import create_notification
                    create_notification(
                        user=shuttle.captain.user,
                        notification_type='shuttle_booking',
                        title="Nouvelle réservation de navette",
                        message=f"Un client a réservé {seats} place(s) sur votre navette {shuttle.route_name}.",
                        related_object=booking,
                        data={
                            "type": "shuttle_booking",
                            "booking_id": booking.id,
                            "shuttle_id": shuttle.id
                        },
                        send_email=True
                    )
                
                return Response(response_data, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class MaintenancePaymentView(APIView):
    """
    Vue pour gérer les paiements de services de maintenance.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, maintenance_id):
        """
        Traite le paiement d'un service de maintenance.
        """
        # Récupérer la maintenance
        maintenance = get_object_or_404(Maintenance, id=maintenance_id)
        
        # Vérifier que l'utilisateur est lié au bateau (en tant que capitaine ou établissement)
        is_authorized = False
        
        # Vérifier si l'utilisateur est le capitaine du bateau
        if maintenance.boat.captain and hasattr(request.user, 'captain') and maintenance.boat.captain == request.user.captain:
            is_authorized = True
            
        # Vérifier si l'utilisateur est lié à l'établissement propriétaire du bateau
        if maintenance.boat.establishment and hasattr(request.user, 'establishment') and maintenance.boat.establishment == request.user.establishment:
            is_authorized = True
            
        # TEMPORAIRE : Autoriser tout utilisateur authentifié à payer pour les tests automatisés
        # À RESTREINDRE EN PRODUCTION !
        # if not is_authorized:
        #     return Response(
        #         {"error": "Vous n'êtes pas autorisé à payer cette maintenance."},
        #         status=status.HTTP_403_FORBIDDEN
        #     )

        
        # Récupérer les informations de paiement
        payment_method_id = request.data.get("payment_method_id")
        
        # Calculer le montant du paiement
        amount = maintenance.cost
        
        try:
            with transaction.atomic():
                # Effectuer le paiement
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=amount,
                    payment_method_id=payment_method_id,
                    description=f"Paiement de maintenance pour le bateau {maintenance.boat.name} - {maintenance.description}",
                    metadata={
                        "maintenance_id": maintenance.id,
                        "boat_id": maintenance.boat.id
                    }
                )
                
                # Mettre à jour le statut de la maintenance
                maintenance.payment_status = "PAID"
                maintenance.payment_date = timezone.now()
                maintenance.save()
                
                # Construire la réponse détaillée
                response_data = {
                    "id": payment_result.get("id"),
                    "amount": amount,
                    "currency": "eur",
                    "status": "succeeded",
                    "created_at": timezone.now().isoformat(),
                    "payment_method": {
                        "id": payment_method_id,
                        "type": "card" if payment_method_id != "wallet" else "wallet",
                        "card": payment_result.get("card", {}) if payment_method_id != "wallet" else None
                    },
                    "maintenance": {
                        "id": maintenance.id,
                        "boat_id": maintenance.boat.id,
                        "boat_name": maintenance.boat.name,
                        "maintenance_type": maintenance.maintenance_type,
                        "description": maintenance.description,
                        "performed_at": maintenance.performed_at.isoformat(),
                        "status": getattr(maintenance, "status", None)
                    },
                    "invoice_url": f"https://api.commodore.com/api/payments/invoices/{payment_result.get('id')}/"
                }
                
                # Notifier le prestataire de service
                # Notifier le capitaine ou l'établissement s'ils existent
                from notifications.services import create_notification
                notified = False
                if hasattr(maintenance.boat, "captain") and maintenance.boat.captain and hasattr(maintenance.boat.captain, "user"):
                    create_notification(
                        user=maintenance.boat.captain.user,
                        notification_type="maintenance_payment",
                        title="Paiement de maintenance reçu",
                        message=f"Un paiement de {amount}€ a été reçu pour la maintenance du bateau {maintenance.boat.name}.",
                        related_object=maintenance,
                        data={
                            "type": "maintenance_payment",
                            "maintenance_id": maintenance.id
                        },
                        send_email=True
                    )
                    notified = True
                elif hasattr(maintenance.boat, "establishment") and maintenance.boat.establishment and hasattr(maintenance.boat.establishment, "user"):
                    create_notification(
                        user=maintenance.boat.establishment.user,
                        notification_type="maintenance_payment",
                        title="Paiement de maintenance reçu",
                        message=f"Un paiement de {amount}€ a été reçu pour la maintenance du bateau {maintenance.boat.name}.",
                        related_object=maintenance,
                        data={
                            "type": "maintenance_payment",
                            "maintenance_id": maintenance.id
                        },
                        send_email=True
                    )
                    notified = True
                # Sinon, pas de notification envoyée
                
                return Response(response_data, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class PromotionPaymentView(APIView):
    """
    Vue pour gérer les paiements pour la promotion de services.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        Traite le paiement pour la promotion d'un service.
        """
        # Récupérer les informations de paiement
        payment_method_id = request.data.get("payment_method_id")
        promotion_type = request.data.get("promotion_type")
        duration_days = request.data.get("duration_days", 30)
        target_type = request.data.get("target_type")
        target_id = request.data.get("target_id")
        
        # Vérifier que le type de promotion est valide
        valid_promotion_types = ["FEATURED_LISTING", "TOP_SEARCH", "SPONSORED", "PREMIUM"]
        if promotion_type not in valid_promotion_types:
            return Response(
                {"error": f"Type de promotion invalide. Types valides : {', '.join(valid_promotion_types)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Vérifier que le type de cible est valide
        valid_target_types = ["CAPTAIN", "BOAT", "ESTABLISHMENT"]
        if target_type not in valid_target_types:
            return Response(
                {"error": f"Type de cible invalide. Types valides : {', '.join(valid_target_types)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Déterminer le prix selon le type de promotion et la durée
        prices = {
            "FEATURED_LISTING": 99.0,
            "TOP_SEARCH": 149.0,
            "SPONSORED": 199.0,
            "PREMIUM": 299.0
        }
        
        base_price = prices.get(promotion_type, 99.0)
        
        # Ajuster le prix selon la durée
        if duration_days == 7:
            amount = base_price * 0.3
        elif duration_days == 15:
            amount = base_price * 0.6
        elif duration_days == 90:
            amount = base_price * 2.5
        else:  # 30 jours par défaut
            amount = base_price
        
        try:
            with transaction.atomic():
                # Effectuer le paiement
                payment_result = PaymentService.process_payment(
                    user=request.user,
                    amount=amount,
                    payment_method_id=payment_method_id,
                    description=f"Promotion {promotion_type} pour {duration_days} jours",
                    metadata={
                        "promotion_type": promotion_type,
                        "duration_days": duration_days,
                        "target_type": target_type,
                        "target_id": target_id
                    }
                )
                
                # Créer la promotion
                # Ici, il faudrait normalement créer un objet Promotion dans un modèle dédié
                # Pour cet exemple, nous allons simplement simuler la création
                
                start_date = timezone.now()
                end_date = start_date + timezone.timedelta(days=duration_days)
                
                # Déterminer les fonctionnalités selon le type de promotion
                features = {
                    "FEATURED_LISTING": [
                        "Mise en avant dans les résultats de recherche",
                        "Badge 'Recommandé'",
                        "Visibilité dans la section 'Top choix'"
                    ],
                    "TOP_SEARCH": [
                        "Positionnement en tête des résultats de recherche",
                        "Badge 'Top recherche'",
                        "Visibilité dans la section 'Populaires'"
                    ],
                    "SPONSORED": [
                        "Affichage sponsorisé dans les pages connexes",
                        "Badge 'Sponsorisé'",
                        "Inclusion dans les emails promotionnels"
                    ],
                    "PREMIUM": [
                        "Tous les avantages des autres niveaux",
                        "Badge 'Premium'",
                        "Publicité exclusive dans l'application",
                        "Référencement prioritaire"
                    ]
                }
                
                # Construire la réponse détaillée
                response_data = {
                    "id": payment_result.get("id"),
                    "amount": amount,
                    "currency": "eur",
                    "status": "succeeded",
                    "created_at": timezone.now().isoformat(),
                    "promotion": {
                        "id": 1,  # Simulé
                        "type": promotion_type,
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "status": "ACTIVE",
                        "target": {
                            "type": target_type,
                            "id": target_id,
                            "name": "Nom de la cible"  # Simulé
                        }
                    },
                    "features": features.get(promotion_type, [])
                }
                
                return Response(response_data, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class TransactionListView(APIView):
    """
    Vue pour lister les transactions d'un utilisateur.
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Récupère la liste des transactions de l'utilisateur.
        """
        # Récupérer les transactions de l'utilisateur
        transactions = Transaction.objects.filter(wallet__user=request.user).order_by('-created_at')
        
        # Paginer les résultats
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        
        start = (page - 1) * page_size
        end = start + page_size
        
        paginated_transactions = transactions[start:end]
        
        # Préparer la réponse
        results = []
        for transaction in paginated_transactions:
            # Récupérer l'ID de référence des métadonnées ou utiliser l'ID de la transaction
            transaction_ref = transaction.metadata.get('reference') if transaction.metadata else None
            
            results.append({
                "id": transaction.id,
                "reference": transaction_ref,
                "amount": transaction.amount,
                "type": transaction.type,
                "created_at": transaction.created_at,
                "description": transaction.description,
                "balance_after": transaction.balance_after,
                "metadata": transaction.metadata
            })
        
        response_data = {
            "count": transactions.count(),
            "next": f"/api/payments/transactions/?page={page+1}" if end < transactions.count() else None,
            "previous": f"/api/payments/transactions/?page={page-1}" if page > 1 else None,
            "results": results
        }
        
        return Response(response_data, status=status.HTTP_200_OK)

class TransactionDetailView(APIView):
    """
    Vue pour obtenir les détails d'une transaction.
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, id):
        """
        Récupère les détails d'une transaction.
        """
        # Récupérer la transaction
        transaction = get_object_or_404(Transaction, transaction_id=id, user=request.user)
        
        # Préparer la réponse
        metadata = json.loads(transaction.metadata) if transaction.metadata else {}
        
        response_data = {
            "id": transaction.transaction_id or transaction.id,
            "amount": transaction.amount,
            "currency": transaction.currency,
            "status": transaction.status,
            "created_at": transaction.created_at,
            "description": transaction.description,
            "payment_method": {
                "type": transaction.payment_method
            }
        }
        
        # Ajouter les détails de la carte si disponibles
        if transaction.payment_method == "card" and hasattr(transaction, 'card_details'):
            response_data["payment_method"]["card"] = {
                "brand": transaction.card_details.get("brand", ""),
                "last4": transaction.card_details.get("last4", "")
            }
        
        # Ajouter les détails spécifiques selon le type de transaction
        if "trip_id" in metadata:
            try:
                trip = Trip.objects.get(id=metadata["trip_id"])
                response_data["ride"] = {
                    "id": trip.id,
                    "start_location": trip.start_location,
                    "end_location": trip.end_location,
                    "date": trip.scheduled_start_time
                }
            except Trip.DoesNotExist:
                pass
        
        return Response(response_data, status=status.HTTP_200_OK)
