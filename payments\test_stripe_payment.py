"""
Script de test pour les paiements Stripe.

Ce script effectue un paiement de test avec Stripe et vérifie que tout fonctionne correctement.
"""

import os
import sys
import django
import json
import time
import requests
from datetime import datetime

# Configurer Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.conf import settings
import stripe

# Configurer Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY

def test_create_payment_intent():
    """
    Teste la création d'un PaymentIntent Stripe.
    """
    print("\n=== Test de création d'un PaymentIntent ===")

    try:
        # Créer un PaymentIntent
        intent = stripe.PaymentIntent.create(
            amount=1000,  # 10.00 EUR
            currency='eur',
            payment_method_types=['card'],
            description='Test de paiement Commodore',
            metadata={
                'test': 'true',
                'timestamp': datetime.now().isoformat()
            }
        )

        print(f"PaymentIntent créé avec succès: {intent.id}")
        print(f"Statut: {intent.status}")
        print(f"Montant: {intent.amount / 100} EUR")
        print(f"Client Secret: {intent.client_secret[:10]}...")

        return intent
    except Exception as e:
        print(f"Erreur lors de la création du PaymentIntent: {str(e)}")
        return None

def test_confirm_payment_intent(intent_id):
    """
    Teste la confirmation d'un PaymentIntent Stripe.
    """
    print("\n=== Test de confirmation d'un PaymentIntent ===")

    try:
        # Créer une méthode de paiement directement avec les détails de la carte
        payment_method = stripe.PaymentMethod.create(
            type='card',
            card={
                'number': '****************',
                'exp_month': 12,
                'exp_year': 2030,
                'cvc': '123',
            },
        )

        # Attacher la méthode de paiement au PaymentIntent
        intent = stripe.PaymentIntent.retrieve(intent_id)
        intent = stripe.PaymentIntent.confirm(
            intent_id,
            payment_method=payment_method.id,
        )

        print(f"PaymentIntent confirmé avec succès: {intent.id}")
        print(f"Statut: {intent.status}")

        # Attendre que le paiement soit traité
        max_attempts = 5
        attempts = 0
        while intent.status != 'succeeded' and attempts < max_attempts:
            print(f"Attente du traitement du paiement... (statut: {intent.status})")
            time.sleep(2)
            intent = stripe.PaymentIntent.retrieve(intent_id)
            attempts += 1

        if intent.status == 'succeeded':
            print(f"Paiement réussi! (statut: {intent.status})")
        else:
            print(f"Le paiement n'a pas été traité dans le délai imparti. Statut final: {intent.status}")

        return intent
    except Exception as e:
        print(f"Erreur lors de la confirmation du PaymentIntent: {str(e)}")
        return None

def test_create_checkout_session():
    """
    Teste la création d'une session Checkout Stripe.
    """
    print("\n=== Test de création d'une session Checkout ===")

    try:
        # Créer une session Checkout
        session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': 'eur',
                    'product_data': {
                        'name': 'Trajet Commodore',
                        'description': 'Test de paiement pour un trajet Commodore',
                    },
                    'unit_amount': 2000,  # 20.00 EUR
                },
                'quantity': 1,
            }],
            mode='payment',
            success_url='http://localhost:8000/payment/success?session_id={CHECKOUT_SESSION_ID}',
            cancel_url='http://localhost:8000/payment/cancel',
            metadata={
                'test': 'true',
                'timestamp': datetime.now().isoformat()
            }
        )

        print(f"Session Checkout créée avec succès: {session.id}")
        print(f"URL de paiement: {session.url}")

        return session
    except Exception as e:
        print(f"Erreur lors de la création de la session Checkout: {str(e)}")
        return None

def test_webhook_endpoint():
    """
    Teste l'endpoint webhook Stripe.
    """
    print("\n=== Test de l'endpoint webhook Stripe ===")

    try:
        # Créer un événement de test
        event_data = {
            'id': 'evt_test_webhook',
            'object': 'event',
            'api_version': '2020-08-27',
            'created': int(time.time()),
            'data': {
                'object': {
                    'id': 'pi_test_webhook',
                    'object': 'payment_intent',
                    'amount': 1000,
                    'currency': 'eur',
                    'status': 'succeeded',
                    'metadata': {
                        'test': 'true'
                    }
                }
            },
            'type': 'payment_intent.succeeded',
            'livemode': False
        }

        # Envoyer l'événement à l'endpoint webhook
        webhook_url = 'http://localhost:8000/api/payments/webhook/'

        # Créer une signature de test (simplifiée pour le test)
        timestamp = int(time.time())
        payload = json.dumps(event_data)

        # Note: Nous ne pouvons pas calculer une vraie signature sans la clé secrète
        # Nous envoyons donc une requête sans signature valide, ce qui devrait être rejetée
        # mais nous permet de vérifier que l'endpoint est accessible

        headers = {
            'Content-Type': 'application/json',
            'Stripe-Signature': f't={timestamp},v1=test_signature'
        }

        response = requests.post(webhook_url, data=payload, headers=headers)

        print(f"Réponse du webhook: {response.status_code}")
        print(f"Contenu: {response.text}")

        return response
    except Exception as e:
        print(f"Erreur lors du test du webhook: {str(e)}")
        return None

def generate_report():
    """
    Génère un rapport de test.
    """
    print("\n=== Rapport de test des paiements Stripe ===")
    print(f"Date et heure: {datetime.now().isoformat()}")
    print(f"Clé publique Stripe: {settings.STRIPE_PUBLISHABLE_KEY}")
    print(f"Mode: {'Production' if 'live' in settings.STRIPE_PUBLISHABLE_KEY else 'Test'}")

    # Vérifier les webhooks configurés
    try:
        webhooks = stripe.WebhookEndpoint.list(limit=10)
        print(f"\nWebhooks configurés ({len(webhooks.data)}):")
        for webhook in webhooks.data:
            print(f"  - {webhook.url} (Actif: {webhook.status == 'enabled'})")
    except Exception as e:
        print(f"Erreur lors de la récupération des webhooks: {str(e)}")

    # Vérifier les derniers paiements
    try:
        payments = stripe.PaymentIntent.list(limit=5)
        print(f"\nDerniers paiements ({len(payments.data)}):")
        for payment in payments.data:
            print(f"  - {payment.id}: {payment.amount / 100} {payment.currency.upper()} ({payment.status})")
    except Exception as e:
        print(f"Erreur lors de la récupération des paiements: {str(e)}")

    # Vérifier les derniers remboursements
    try:
        refunds = stripe.Refund.list(limit=5)
        print(f"\nDerniers remboursements ({len(refunds.data)}):")
        for refund in refunds.data:
            print(f"  - {refund.id}: {refund.amount / 100} {refund.currency.upper()} ({refund.status})")
    except Exception as e:
        print(f"Erreur lors de la récupération des remboursements: {str(e)}")

    print("\nRésumé des tests:")
    print("  - Création d'un PaymentIntent: Effectué")
    print("  - Confirmation d'un PaymentIntent: Effectué")
    print("  - Création d'une session Checkout: Effectué")
    print("  - Test du webhook: Effectué")

    print("\nRecommandations:")
    print("  1. Vérifier que le webhook est correctement configuré dans le dashboard Stripe")
    print("  2. Vérifier que les événements sont bien traités par l'application")
    print("  3. Tester un paiement complet dans l'application mobile")
    print("  4. Tester un remboursement")

if __name__ == '__main__':
    print("=== Test des paiements Stripe pour Commodore ===")

    # Tester la création d'un PaymentIntent
    intent = test_create_payment_intent()

    if intent:
        # Tester la confirmation d'un PaymentIntent
        confirmed_intent = test_confirm_payment_intent(intent.id)

    # Tester la création d'une session Checkout
    session = test_create_checkout_session()

    # Tester l'endpoint webhook
    webhook_response = test_webhook_endpoint()

    # Générer un rapport
    generate_report()
