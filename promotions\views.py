from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from accounts.permissions import IsOwnerOnly

from .models import Promotion
from .serializers import PromotionSerializer
from payments.services import PaymentService
from notifications.services import NotificationService
from drf_spectacular.utils import extend_schema, OpenApiParameter

@extend_schema(tags=["Promotions"])
class PromotionListCreateView(APIView):
    """
    Vue pour lister et créer des promotions.
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(tags=["Promotions"], responses=PromotionSerializer(many=True))
    def get(self, request):
        """
        Récupère la liste des promotions de l'utilisateur.
        """
        # Vérifier et mettre à jour le statut des promotions
        user_promotions = Promotion.objects.filter(user=request.user)
        for promotion in user_promotions:
            promotion.check_status()
        
        # Filtrer selon les paramètres
        status_filter = request.query_params.get('status')
        type_filter = request.query_params.get('type')
        target_type_filter = request.query_params.get('target_type')
        
        promotions = user_promotions
        
        if status_filter:
            promotions = promotions.filter(status=status_filter)
        if type_filter:
            promotions = promotions.filter(type=type_filter)
        if target_type_filter:
            promotions = promotions.filter(target_type=target_type_filter)
        
        # Paginer les résultats
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        
        start = (page - 1) * page_size
        end = start + page_size
        
        paginated_promotions = promotions[start:end]
        
        serializer = PromotionSerializer(paginated_promotions, many=True)
        
        response_data = {
            "count": promotions.count(),
            "next": f"/api/promotions/?page={page+1}" if end < promotions.count() else None,
            "previous": f"/api/promotions/?page={page-1}" if page > 1 else None,
            "results": serializer.data
        }
        
        return Response(response_data, status=status.HTTP_200_OK)
    
    @extend_schema(tags=["Promotions"], request=PromotionSerializer, responses=PromotionSerializer)
    def post(self, request):
        """
        Crée une nouvelle promotion (sans paiement).
        Cette méthode est généralement utilisée en interne ou pour des promotions gratuites.
        Les promotions payantes passent par l'API de paiement.
        """
        # Créer une promotion sans paiement
        serializer = PromotionSerializer(data=request.data)
        
        if serializer.is_valid():
            promotion = serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@extend_schema(tags=["Promotions"])
class PromotionDetailView(APIView):
    """
    Vue pour gérer une promotion spécifique.
    """
    permission_classes = [IsAuthenticated, IsOwnerOnly]
    
    @extend_schema(tags=["Promotions"], responses=PromotionSerializer)
    def get(self, request, promotion_id):
        """
        Récupère les détails d'une promotion.
        """
        promotion = get_object_or_404(Promotion, id=promotion_id, user=request.user)
        promotion.check_status()  # Mettre à jour le statut si nécessaire
        
        serializer = PromotionSerializer(promotion)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
    @extend_schema(tags=["Promotions"], request=PromotionSerializer, responses=PromotionSerializer)
    def patch(self, request, promotion_id):
        """
        Met à jour une promotion existante.
        """
        promotion = get_object_or_404(Promotion, id=promotion_id, user=request.user)
        
        # Vérifier que la promotion n'est pas expirée ou annulée
        if promotion.status in ['EXPIRED', 'CANCELLED']:
            return Response(
                {"error": "Impossible de modifier une promotion expirée ou annulée."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = PromotionSerializer(promotion, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, promotion_id):
        """
        Annule une promotion.
        """
        promotion = get_object_or_404(Promotion, id=promotion_id, user=request.user)
        
        # Vérifier que la promotion n'est pas déjà expirée ou annulée
        if promotion.status in ['EXPIRED', 'CANCELLED']:
            return Response(
                {"error": "La promotion est déjà expirée ou annulée."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Annuler la promotion (et gérer le remboursement)
        promotion.cancel()
        
        return Response(
            {"message": "La promotion a été annulée avec succès."},
            status=status.HTTP_200_OK
        )

@extend_schema(tags=["Promotions"])
class PromotionTargetView(APIView):
    """
    Vue pour récupérer les promotions actives pour une cible spécifique.
    Utilisée par les autres applications pour déterminer si une entité bénéficie de promotions.
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(tags=["Promotions"], responses=PromotionSerializer(many=True))
    def get(self, request, target_type, target_id):
        """
        Récupère les promotions actives pour une cible spécifique.
        """
        # Vérifier que le type de cible est valide
        valid_target_types = ['CAPTAIN', 'BOAT', 'ESTABLISHMENT']
        if target_type not in valid_target_types:
            return Response(
                {"error": f"Type de cible invalide. Types valides : {', '.join(valid_target_types)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Récupérer les promotions actives pour cette cible
        promotions = Promotion.objects.filter(
            target_type=target_type,
            target_id=target_id,
            status='ACTIVE'
        )
        
        # Mettre à jour le statut des promotions
        for promotion in promotions:
            promotion.check_status()
        
        # Ne retourner que les promotions qui sont toujours actives
        active_promotions = promotions.filter(status='ACTIVE')
        
        serializer = PromotionSerializer(active_promotions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
