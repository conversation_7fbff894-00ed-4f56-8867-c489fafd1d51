#!/usr/bin/env python
"""
Script de test pour vérifier l'optimisation mémoire du RAG service.
Ce script mesure l'utilisation mémoire avant et après l'initialisation du service.
"""

import os
import sys
import psutil
import time
from django.core.management.base import BaseCommand

# Ajouter le répertoire du projet au path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')

import django
django.setup()

def get_memory_usage():
    """Retourne l'utilisation mémoire actuelle en MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def test_memory_optimization():
    """Test l'optimisation mémoire du RAG service"""
    print("🧪 Test d'optimisation mémoire du RAG Service")
    print("=" * 50)
    
    # Mesure mémoire initiale
    initial_memory = get_memory_usage()
    print(f"📊 Mémoire initiale: {initial_memory:.2f} MB")
    
    # Import du service (sans initialisation des modèles lourds)
    print("\n📦 Import du RAG service...")
    from rag.rag_service import RAGService
    
    after_import_memory = get_memory_usage()
    print(f"📊 Mémoire après import: {after_import_memory:.2f} MB")
    print(f"📈 Différence: +{after_import_memory - initial_memory:.2f} MB")
    
    # Initialisation du service (sans charger les modèles)
    print("\n🚀 Initialisation du service...")
    try:
        # Simuler la présence de la clé API pour éviter l'erreur
        os.environ['DEEPINFRA_API_TOKEN'] = 'test_token'
        service = RAGService()
        
        after_init_memory = get_memory_usage()
        print(f"📊 Mémoire après initialisation: {after_init_memory:.2f} MB")
        print(f"📈 Différence: +{after_init_memory - after_import_memory:.2f} MB")
        
        # Test d'accès aux propriétés lazy (cela devrait charger les modèles)
        print("\n🔄 Test d'accès aux modèles (chargement lazy)...")
        
        # Test text_splitter (plus léger)
        print("   - Accès au text_splitter...")
        _ = service.text_splitter
        after_splitter_memory = get_memory_usage()
        print(f"     📊 Mémoire: {after_splitter_memory:.2f} MB (+{after_splitter_memory - after_init_memory:.2f} MB)")
        
        # Note: On évite de charger les embeddings et LLM dans ce test car ils sont très lourds
        print("\n✅ Test terminé avec succès!")
        print(f"📊 Mémoire totale utilisée: {after_splitter_memory:.2f} MB")
        print(f"📈 Augmentation totale: +{after_splitter_memory - initial_memory:.2f} MB")
        
        print("\n💡 Avantages de l'optimisation:")
        print("   - Les modèles lourds ne sont chargés qu'à la demande")
        print("   - Démarrage plus rapide de l'application")
        print("   - Utilisation mémoire réduite si certaines fonctionnalités ne sont pas utilisées")
        print("   - Meilleure scalabilité pour les déploiements")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_memory_optimization()
    sys.exit(0 if success else 1)
