import logging
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from .models import Notification

logger = logging.getLogger(__name__)

def create_notification(user, title, message, notification_type,
                       related_object=None,
                       data=None,
                       send_email=False,
                       **kwargs):  # noqa: E501
    """
    Crée une notification pour un utilisateur.

    Des appels plus anciens peuvent fournir des paramètres supplémentaires
    comme ``related_object_id`` ou ``notification_data``. Nous les acceptons
    via **kwargs pour éviter de lever une exception et garantir la
    rétro-compatibilité.
    """
    logger.info(f"--- DEBUG: create_notification called with notification_type='{notification_type}' and data is not None: {data is not None}")
    """
    Crée une notification pour un utilisateur.
    """
    # Récupérer un éventuel ``related_object_id`` passé dans **kwargs** quand
    # l'objet complet n'est pas fourni. Nous n'essayons pas de résoudre le
    # ContentType – cela nécessiterait le modèle. Nous nous contentons de
    # l'ignorer si aucun ``related_object`` n'est donné.
    related_object_id = kwargs.pop('related_object_id', None)

    try:
        notification = Notification(
            user=user,
            type=notification_type,  # Utilise le champ 'type' du modèle
            title=title,
            message=message
        )

        if related_object:
            notification.content_object = related_object
        elif related_object_id is not None:
            # On enregistre simplement l'ID sans ContentType. Cela permet de
            # conserver la donnée sans casser la création. Le champ
            # ``object_id`` existe quand GenericForeignKey est utilisé.
            notification.object_id = related_object_id

        notification.save()

        if send_email:
            # Passe les données supplémentaires à la fonction d'envoi d'email
            send_notification_email(notification, data=data)

        return notification
    except Exception as e:
        logger.error(f"Erreur lors de la création de la notification pour {user.email}: {str(e)}", exc_info=True)
        return None

def send_notification_email(notification, data=None):
    """
    Envoie un email pour une notification.
    """
    try:
        user = notification.user

        if not user.email:
            logger.warning(f"Impossible d'envoyer un email à l'utilisateur {user.id}: pas d'adresse email")
            return False

        context = {
            'user': user,
            'notification': notification,
            'app_name': 'Commodore',
            'site_url': getattr(settings, 'SITE_URL', ''),
            'related_object': notification.content_object,
        }

        # Utilise le paramètre 'data' au lieu de 'notification.data'
        if data:
            context.update(data)

        subject = f"Commodore - {notification.title}"
        template_name = f'emails/{notification.type.lower()}'
        html_message = render_to_string(f'{template_name}.html', context)
        text_message = render_to_string(f'{template_name}.txt', context)

        send_mail(
            subject=subject,
            message=text_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )

        notification.sent_at = timezone.now()
        notification.delivery_status = 'SENT'
        notification.save()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email de notification {notification.id}: {str(e)}", exc_info=True)
        notification.delivery_status = 'FAILED'
        notification.error_message = str(e)
        notification.save()
        return False

        # Marquer l'email comme envoyé
        notification.mark_email_sent()

        logger.info(f"Email envoyé à {user.email} pour la notification {notification.id}")
        return True
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email pour la notification {notification.id}: {str(e)}")
        return False

def create_payment_success_notification(payment):
    """
    Crée une notification pour un paiement réussi

    Args:
        payment: L'objet Payment

    Returns:
        L'objet Notification créé
    """
    user = payment.booking.passenger.user if payment.booking else None

    if not user:
        logger.warning(f"Impossible de créer une notification pour le paiement {payment.id}: utilisateur non trouvé")
        return None

    # Préparer les données pour la notification
    data = {
        'payment_id': str(payment.id),
        'amount': payment.amount,
        'currency': 'EUR',
        'payment_date': payment.updated_at.strftime('%d/%m/%Y %H:%M'),
    }

    if payment.booking:
        data['booking_id'] = str(payment.booking.id)

    if payment.receipt_url:
        data['receipt_url'] = payment.receipt_url

    # Créer la notification
    return create_notification(
        user=user,
        notification_type='PAYMENT_SUCCESS',
        title="Paiement confirmé",
        message=f"Votre paiement de {payment.amount}€ a été confirmé.",
        related_object=payment,
        data=data,
        send_email=True
    )

def create_payment_failed_notification(payment, error_message=None):
    """
    Crée une notification pour un paiement échoué

    Args:
        payment: L'objet Payment
        error_message: Le message d'erreur (optionnel)

    Returns:
        L'objet Notification créé
    """
    user = payment.booking.passenger.user if payment.booking else None

    if not user:
        logger.warning(f"Impossible de créer une notification pour le paiement {payment.id}: utilisateur non trouvé")
        return None

    # Préparer les données pour la notification
    data = {
        'payment_id': str(payment.id),
        'amount': payment.amount,
        'currency': 'EUR',
        'payment_date': payment.updated_at.strftime('%d/%m/%Y %H:%M'),
        'error_message': error_message or "Une erreur est survenue lors du traitement du paiement."
    }

    if payment.booking:
        data['booking_id'] = str(payment.booking.id)

    # Créer la notification
    return create_notification(
        user=user,
        notification_type='PAYMENT_FAILED',
        title="Paiement échoué",
        message=f"Votre paiement de {payment.amount}€ a échoué. {error_message or ''}",
        related_object=payment,
        data=data,
        send_email=True
    )

def create_refund_initiated_notification(payment):
    """
    Crée une notification pour un remboursement initié

    Args:
        payment: L'objet Payment

    Returns:
        L'objet Notification créé
    """
    user = payment.booking.passenger.user if payment.booking else None

    if not user:
        logger.warning(f"Impossible de créer une notification pour le remboursement {payment.id}: utilisateur non trouvé")
        return None

    # Préparer les données pour la notification
    data = {
        'payment_id': str(payment.id),
        'refund_id': payment.refund_id,
        'amount': payment.refund_amount or payment.amount,
        'currency': 'EUR',
        'refund_date': payment.updated_at.strftime('%d/%m/%Y %H:%M'),
    }

    if payment.booking:
        data['booking_id'] = str(payment.booking.id)

    # Créer la notification
    return create_notification(
        user=user,
        notification_type='REFUND_INITIATED',
        title="Remboursement initié",
        message=f"Votre remboursement de {data['amount']}€ a été initié.",
        related_object=payment,
        data=data,
        send_email=True
    )

def create_refund_completed_notification(payment):
    """
    Crée une notification pour un remboursement terminé

    Args:
        payment: L'objet Payment

    Returns:
        L'objet Notification créé
    """
    user = payment.booking.passenger.user if payment.booking else None

    if not user:
        logger.warning(f"Impossible de créer une notification pour le remboursement {payment.id}: utilisateur non trouvé")
        return None

    # Préparer les données pour la notification
    data = {
        'payment_id': str(payment.id),
        'refund_id': payment.refund_id,
        'amount': payment.refund_amount or payment.amount,
        'currency': 'EUR',
        'refund_date': payment.updated_at.strftime('%d/%m/%Y %H:%M'),
    }

    if payment.booking:
        data['booking_id'] = str(payment.booking.id)

    # Créer la notification
    return create_notification(
        user=user,
        notification_type='REFUND_COMPLETED',
        title="Remboursement effectué",
        message=f"Votre remboursement de {data['amount']}€ a été effectué.",
        related_object=payment,
        data=data,
        send_email=True
    )


def send_push_notification(user_ids, title, message, data=None):
    """
    Envoie une notification push à plusieurs utilisateurs

    Args:
        user_ids (list): Liste des IDs des utilisateurs
        title (str): Titre de la notification
        message (str): Message de la notification
        data (dict, optional): Données supplémentaires

    Returns:
        dict: Résultat de l'envoi
    """
    from django.contrib.auth import get_user_model
    from .models import Device
    from .firebase_utils import send_fcm_message

    User = get_user_model()

    try:
        # Récupérer les tokens FCM des appareils actifs des utilisateurs
        devices = Device.objects.filter(user__id__in=user_ids, is_active=True)
        tokens = [device.token for device in devices]

        if not tokens:
            logger.warning(f"Aucun appareil trouvé pour les utilisateurs {user_ids}")
            return {'success': False, 'error': 'Aucun appareil trouvé'}

        # Envoyer la notification push
        result = send_fcm_message(tokens, title, message, data)

        # Créer des notifications dans la base de données
        users = User.objects.filter(id__in=user_ids)
        for user in users:
            create_notification(
                user=user,
                notification_type='system',
                title=title,
                message=message,
                data=data or {}
            )

        return result
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de la notification push: {str(e)}")
        return {'success': False, 'error': str(e)}


def create_ride_status_notification(ride, status, send_to_client=True, send_to_captain=False):
    """
    Crée une notification pour un changement de statut de trajet

    Args:
        ride: L'objet Ride
        status: Le nouveau statut du trajet
        send_to_client: Indique si la notification doit être envoyée au client
        send_to_captain: Indique si la notification doit être envoyée au capitaine

    Returns:
        list: Liste des notifications créées
    """
    notifications = []

    # Déterminer le type et le message en fonction du statut
    notification_type = f"ride_{status.lower()}"

    if status == 'accepted':
        title = "Trajet accepté"
        client_message = f"Votre trajet a été accepté par le capitaine {ride.captain.get_full_name()}."
        captain_message = f"Vous avez accepté le trajet de {ride.client.get_full_name()}."
    elif status == 'in_progress':
        title = "Trajet en cours"
        client_message = "Votre trajet a commencé."
        captain_message = "Le trajet a commencé."
    elif status == 'completed':
        title = "Trajet terminé"
        client_message = "Votre trajet est terminé. Merci d'avoir utilisé Commodore !"
        captain_message = "Le trajet est terminé."
    elif status == 'canceled':
        title = "Trajet annulé"
        client_message = "Votre trajet a été annulé."
        captain_message = "Le trajet a été annulé."
    else:
        title = f"Statut du trajet: {status}"
        client_message = f"Le statut de votre trajet a changé: {status}."
        captain_message = f"Le statut du trajet a changé: {status}."

    # Préparer les données pour la notification
    data = {
        'ride_id': str(ride.id),
        'status': status,
        'start_location': str(ride.start_location),
        'end_location': str(ride.end_location),
        'price': float(ride.price),
        'distance': float(ride.distance),
    }

    # Créer la notification pour le client
    if send_to_client and ride.client:
        client_notification = create_notification(
            user=ride.client,
            notification_type=notification_type,
            title=title,
            message=client_message,
            related_object=ride,
            data=data,
            send_email=True
        )
        if client_notification:
            notifications.append(client_notification)

    # Créer la notification pour le capitaine
    if send_to_captain and ride.captain:
        captain_notification = create_notification(
            user=ride.captain,
            notification_type=notification_type,
            title=title,
            message=captain_message,
            related_object=ride,
            data=data,
            send_email=True
        )
        if captain_notification:
            notifications.append(captain_notification)

    return notifications


def create_new_ride_request_notification(ride):
    """
    Crée une notification pour une nouvelle demande de trajet

    Args:
        ride: L'objet Ride

    Returns:
        Notification: La notification créée
    """
    if not ride.captain:
        logger.warning(f"Impossible de créer une notification pour le trajet {ride.id}: capitaine non assigné")
        return None

    # Préparer les données pour la notification
    data = {
        'ride_id': str(ride.id),
        'client_name': ride.client.get_full_name(),
        'start_location': str(ride.start_location),
        'end_location': str(ride.end_location),
        'price': float(ride.price),
        'distance': float(ride.distance),
    }

    # Créer la notification
    return create_notification(
        user=ride.captain,
        notification_type='ride_request',
        title="Nouvelle demande de trajet",
        message=f"Vous avez reçu une nouvelle demande de trajet de {ride.client.get_full_name()}.",
        related_object=ride,
        data=data,
        send_email=True
    )
