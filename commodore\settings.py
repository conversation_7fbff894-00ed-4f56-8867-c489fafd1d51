"""
Django settings for commodore project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path
import environ


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Initialiser environ
env = environ.Env()
environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

# Recalculer la valeur d'activation du chatbot APRÈS le chargement du fichier .env
ENABLE_RAG = os.environ.get("ENABLE_RAG", "true").lower() == "true"

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-5rwp)(75rzz9krip-f8egz#naz!)e&@c^u-c)*!0n=+p$0agzr'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["*"]

CORS_ALLOW_ALL_ORIGINS = True
# Application definition

INSTALLED_APPS = [
    'channels',
    'jazzmin',  # Interface admin améliorée
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third party apps
    'rest_framework',
    'rest_framework.authtoken',
    'rest_framework_simplejwt',
    'drf_spectacular',
    #'drf_yasg',
    'corsheaders',
    'django_celery_beat',
    'django_celery_results',
    'storages',

    # Local apps
    'rides',
    'debug_toolbar',

    # Authentication - Configuration complète
    'dj_rest_auth',
    'dj_rest_auth.registration',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.facebook',
    'allauth.socialaccount.providers.google',
    'allauth.socialaccount.providers.apple',
    'django.contrib.sites',

    # Local apps

    'authentication',
    'trips',
    'boats',
    'payments',
    'reviews',
    'notifications',
    'django_cron',
    'chat',
    'rag',
    'accounts',
    'establishments',
    'boatman',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    'allauth.account.middleware.AccountMiddleware',
]

ROOT_URLCONF = 'commodore.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

ASGI_APPLICATION = 'commodore.routing.application'

CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [('127.0.0.1', 6379)],
        },
    },
}

WSGI_APPLICATION = 'commodore.wsgi.application'
ASGI_APPLICATION = 'commodore.asgi.application'

# Database
import dj_database_url

DATABASES = {
    'default': dj_database_url.config(
        default=env('DATABASE_URL'),
        conn_max_age=600,
        conn_health_checks=True,
    )
}

# Cache
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# Channels
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}

# Celery
CELERY_BROKER_URL = 'memory://'
CELERY_RESULT_BACKEND = 'django-db'
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

CRON_CLASSES = [
       "rag.cron.DeleteOldMessagesJob",
   ]

# Authentication
AUTH_USER_MODEL = 'accounts.User'

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    #'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=45),
    'ROTATE_REFRESH_TOKENS': True,
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Rest Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
}

# JWT et Token settings
REST_USE_JWT = False  # Utiliser les tokens classiques au lieu de JWT
JWT_AUTH_COOKIE = 'jwt-auth'
JWT_AUTH_REFRESH_COOKIE = 'jwt-refresh-token'
REST_AUTH_TOKEN_MODEL = 'rest_framework.authtoken.models.Token'

# Authentication settings pour l'API mobile
# Configuration complète avec django-allauth
SITE_ID = 1

# Configuration pour utiliser l'email comme identifiant (pas de username)
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_USER_MODEL_USERNAME_FIELD = None

# API documentation settings - Configuration importée depuis swagger_config.py



# Internationalization
LANGUAGE_CODE = 'fr-fr'
TIME_ZONE = 'Europe/Paris'
USE_I18N = True
USE_TZ = True


# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# AWS S3 Configuration (pour la production)
if not DEBUG:
    AWS_ACCESS_KEY_ID = ''
    AWS_SECRET_ACCESS_KEY = ''
    AWS_STORAGE_BUCKET_NAME = 'commodore-files'
    AWS_S3_REGION_NAME = 'eu-west-3'
    AWS_S3_FILE_OVERWRITE = False
    AWS_DEFAULT_ACL = None
    DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

# Stripe settings
STRIPE_SECRET_KEY = env('STRIPE_SECRET_KEY')
STRIPE_PUBLISHABLE_KEY = env('STRIPE_PUBLISHABLE_KEY')
STRIPE_WEBHOOK_SECRET = env('STRIPE_WEBHOOK_SECRET')
STRIPE_SUCCESS_URL = env('STRIPE_SUCCESS_URL')
STRIPE_CANCEL_URL = env('STRIPE_CANCEL_URL')

# Email Configuration
# Commenté pour forcer l'utilisation de SMTP en mode développement
# if DEBUG:
#     EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
# else:
#     EMAIL_BACKEND = 'django_ses.SESBackend'
#     AWS_SES_ACCESS_KEY_ID = ''
#     AWS_SES_SECRET_ACCESS_KEY = ''
#     AWS_SES_REGION_NAME = 'eu-west-1'

# Utiliser toujours SMTP pour s'assurer que les emails sont réellement envoyés
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'riavmouokfgscveq'
DEFAULT_FROM_EMAIL = '<EMAIL>'

# Frontend URL for email verification
FRONTEND_URL = 'http://localhost:3000'

# Social Authentication Settings
# SOCIAL_AUTH_FACEBOOK_KEY = '1643858832951513'
# SOCIAL_AUTH_FACEBOOK_SECRET = '********************************'
# SOCIAL_AUTH_FACEBOOK_SCOPE = ['email']
# SOCIAL_AUTH_FACEBOOK_PROFILE_EXTRA_PARAMS = {
#     'fields': 'id, name, email, picture'
#}

# SOCIAL_AUTH_GOOGLE_OAUTH2_KEY = ''
# SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET = ''
# SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE = [
#     'https://www.googleapis.com/auth/userinfo.email',
#     'https://www.googleapis.com/auth/userinfo.profile',
#]

# SOCIAL_AUTH_APPLE_ID_CLIENT = ''
# SOCIAL_AUTH_APPLE_ID_TEAM = ''
# SOCIAL_AUTH_APPLE_ID_KEY = ''
# SOCIAL_AUTH_APPLE_ID_SECRET = ''
# SOCIAL_AUTH_APPLE_ID_SCOPE = ['email', 'name']

# OAuth2 callback URLs
# FACEBOOK_CALLBACK_URL = f"{FRONTEND_URL}/auth/facebook/callback"
# GOOGLE_CALLBACK_URL = f"{FRONTEND_URL}/auth/google/callback"
# APPLE_CALLBACK_URL = f"{FRONTEND_URL}/auth/apple/callback"

# SMS (Twilio)
TWILIO_ACCOUNT_SID = ''
TWILIO_AUTH_TOKEN = ''
TWILIO_FROM_NUMBER = ''

# Paramètres des codes de vérification
VERIFICATION_CODE_LENGTH = 6
VERIFICATION_CODE_EXPIRY_MINUTES = 30

# CORS
CORS_ALLOWED_ORIGINS = ['http://localhost:3000', 'http://localhost:8000']
CORS_ALLOW_CREDENTIALS = True

# Security
if not DEBUG:
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_HSTS_SECONDS = ********
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True

# Debug Toolbar
INTERNAL_IPS = [
    '127.0.0.1',
]

def show_toolbar(request):
    return (
        DEBUG
        and not request.path.startswith('/api/webhooks/')
        and request.META.get('REMOTE_ADDR', None) in INTERNAL_IPS
    )

DEBUG_TOOLBAR_CONFIG = {
    'SHOW_TOOLBAR_CALLBACK': 'commodore.settings.show_toolbar',
}

# Durée de validité des codes de vérification en minutes
VERIFICATION_CODE_EXPIRY_MINUTES = 15

# Sentry
if not DEBUG:
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration

    sentry_sdk.init(
        dsn='',
        integrations=[DjangoIntegration()],
        traces_sample_rate=1.0,
        send_default_pii=True
    )

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Firebase Configuration
FIREBASE_CREDENTIALS = os.path.join(BASE_DIR, 'firebase-credentials.json')
FIREBASE_PROJECT_ID = 'commodore-app'
FIREBASE_APP_ID = ''
FIREBASE_API_KEY = ''
FIREBASE_MESSAGING_SENDER_ID = ''
FIREBASE_STORAGE_BUCKET = 'commodore-app.appspot.com'

# Configuration du logging complet
# from .logging_config import LOGGING_CONFIG
# LOGGING = LOGGING_CONFIG
# LOGGING_CONFIG = None  # Désactiver la configuration automatique de Django

# Créer le dossier logs s'il n'existe pas
import os
os.makedirs(os.path.join(BASE_DIR, 'logs'), exist_ok=True)


# Code verification settings
VERIFICATION_CODE_LENGTH = 6
VERIFICATION_CODE_EXPIRY_MINUTES = 10

# Jazzmin settings - Interface admin personnalisée
JAZZMIN_SETTINGS = {
    'site_title': 'Commodore Admin',
    'site_header': 'Commodore',
    'site_brand': 'Commodore',
    'welcome_sign': 'Bienvenue dans l\'administration Commodore',
    'copyright': 'Commodore 2025',
    'search_model': ['accounts.User', 'boats.Boat', 'trips.Trip'],
    'topmenu_links': [
        {'name': 'Accueil', 'url': 'admin:index'},
        {'name': 'Site', 'url': '/', 'new_window': True},
        {'name': 'API Docs', 'url': '/api/docs/', 'new_window': True},
    ],
    'show_sidebar': True,
    'navigation_expanded': True,
    'hide_apps': [],
    'hide_models': [],
    'order_with_respect_to': [
        'accounts',
        'boats',
        'trips',
        'establishments',
        'payments',
        'reviews',
        'notifications',
        'boatman',
    ],
    'icons': {
        'accounts.user': 'fas fa-users',
        'accounts.client': 'fas fa-user',
        'accounts.captain': 'fas fa-user-tie',
        'accounts.establishment': 'fas fa-building',
        'boats.boat': 'fas fa-ship',
        'trips.trip': 'fas fa-route',
        'trips.triprequest': 'fas fa-clipboard-list',
        'trips.shuttletriprequest': 'fas fa-bus',
        'establishments.establishment': 'fas fa-hotel',
        'payments.payment': 'fas fa-credit-card',
        'payments.wallet': 'fas fa-wallet',
        'reviews.review': 'fas fa-star',
        'notifications.notification': 'fas fa-bell',
        'boatman': 'fas fa-anchor',
    },
}

JAZZMIN_UI_TWEAKS = {
    'navbar_small_text': False,
    'footer_small_text': False,
    'body_small_text': False,
    'brand_small_text': False,
    'brand_colour': 'navbar-navy',
    'accent': 'accent-primary',
    'navbar': 'navbar-navy navbar-dark',
    'no_navbar_border': False,
    'navbar_fixed': True,
    'layout_boxed': False,
    'footer_fixed': False,
    'sidebar_fixed': True,
    'sidebar': 'sidebar-dark-navy',
    'sidebar_nav_small_text': False,
    'sidebar_disable_expand': False,
    'sidebar_nav_child_indent': True,
    'sidebar_nav_compact_style': False,
    'sidebar_nav_legacy_style': False,
    'sidebar_nav_flat_style': False,
    'theme': 'default',
    'dark_mode_theme': 'darkly',
    'button_classes': {
        'primary': 'btn-primary',
        'secondary': 'btn-secondary',
        'info': 'btn-info',
        'warning': 'btn-warning',
        'danger': 'btn-danger',
        'success': 'btn-success'
    }
}

# Paramètres pour les paiements
PAYMENT_RETURN_URL = 'http://localhost:8000/payment/success'
PAYMENT_CANCEL_URL = 'http://localhost:8000/payment/cancel'

# DRF Spectacular settings - Configuration complète
from .swagger_config import SPECTACULAR_SETTINGS

