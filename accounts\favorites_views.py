from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404

from .favorites import FavoriteLocation, FavoriteCaptain
from .favorites_serializers import FavoriteLocationSerializer, FavoriteCaptainSerializer
from .models import Captain

class FavoriteLocationListCreateView(APIView):
    """Vue pour lister et créer des emplacements favoris."""
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Récupérer la liste des emplacements favoris de l'utilisateur."""
        locations = FavoriteLocation.objects.filter(user=request.user)
        serializer = FavoriteLocationSerializer(locations, many=True)
        return Response(serializer.data)
    
    def post(self, request):
        """Ajouter un nouvel emplacement favori."""
        serializer = FavoriteLocationSerializer(
            data=request.data,
            context={'request': request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class FavoriteLocationDetailView(APIView):
    """Vue pour gérer un emplacement favori spécifique."""
    
    permission_classes = [IsAuthenticated]
    
    def get_object(self, pk):
        return get_object_or_404(FavoriteLocation, pk=pk, user=self.request.user)
    
    def get(self, request, pk):
        """Récupérer les détails d'un emplacement favori."""
        location = self.get_object(pk)
        serializer = FavoriteLocationSerializer(location)
        return Response(serializer.data)
    
    def patch(self, request, pk):
        """Mettre à jour un emplacement favori."""
        location = self.get_object(pk)
        serializer = FavoriteLocationSerializer(
            location,
            data=request.data,
            partial=True,
            context={'request': request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, pk):
        """Supprimer un emplacement favori."""
        location = self.get_object(pk)
        location.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

class FavoriteCaptainListCreateView(APIView):
    """Vue pour lister et créer des capitaines favoris."""
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Récupérer la liste des capitaines favoris de l'utilisateur."""
        captains = FavoriteCaptain.objects.filter(user=request.user)
        serializer = FavoriteCaptainSerializer(captains, many=True)
        return Response(serializer.data)
    
    def post(self, request):
        """Ajouter un nouveau capitaine favori."""
        # Vérifier que le capitaine existe
        captain_id = request.data.get('captain')
        if not captain_id:
            return Response(
                {'error': 'ID du capitaine requis'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            captain = Captain.objects.get(pk=captain_id)
        except Captain.DoesNotExist:
            return Response(
                {'error': 'Capitaine non trouvé'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Vérifier que le capitaine n'est pas déjà en favori
        if FavoriteCaptain.objects.filter(user=request.user, captain=captain).exists():
            return Response(
                {'error': 'Ce capitaine est déjà dans vos favoris'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = FavoriteCaptainSerializer(
            data=request.data,
            context={'request': request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class FavoriteCaptainDetailView(APIView):
    """Vue pour gérer un capitaine favori spécifique."""
    
    permission_classes = [IsAuthenticated]
    
    def get_object(self, pk):
        return get_object_or_404(FavoriteCaptain, pk=pk, user=self.request.user)
    
    def get(self, request, pk):
        """Récupérer les détails d'un capitaine favori."""
        favorite = self.get_object(pk)
        serializer = FavoriteCaptainSerializer(favorite)
        return Response(serializer.data)
    
    def patch(self, request, pk):
        """Mettre à jour les notes d'un capitaine favori."""
        favorite = self.get_object(pk)
        serializer = FavoriteCaptainSerializer(
            favorite,
            data=request.data,
            partial=True,
            context={'request': request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, pk):
        """Supprimer un capitaine des favoris."""
        favorite = self.get_object(pk)
        favorite.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
