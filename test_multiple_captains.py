#!/usr/bin/env python
"""
Test du système de devis multiples pour une mise à disposition
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000"
CLIENT_EMAIL = "<EMAIL>"
CLIENT_PASSWORD = "password123"

def get_auth_token():
    """Obtenir le token d'authentification"""
    login_data = {
        "email": CLIENT_EMAIL,
        "password": CLIENT_PASSWORD
    }

    response = requests.post(f"{BASE_URL}/api/login/", json=login_data)
    if response.status_code == 200:
        response_data = response.json()
        print(f"✅ Connexion réussie pour {CLIENT_EMAIL}")
        print(f"🔍 Réponse: {response_data}")
        # Essayer différents noms de champs pour le token
        token = response_data.get('access_token') or response_data.get('access') or response_data.get('token')
        if token:
            return token
        else:
            print(f"❌ Token non trouvé dans la réponse: {response_data}")
            return None
    else:
        print(f"❌ Erreur de connexion: {response.status_code}")
        print(response.text)
        return None

def test_hourly_trip_request(token):
    """Tester une demande de mise à disposition"""

    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    # Données de la demande de mise à disposition
    trip_data = {
        "boat_type": "CLASSIC",  # Demander spécifiquement des bateaux CLASSIC
        "departure_location": {
            "city_name": "Port de Cotonou, Bénin",
            "coordinates": {
                "latitude": 6.3654,
                "longitude": 2.4183,
                "altitude": 0,
                "accuracy": 5.2,
                "altitude_accuracy": 3.1,
                "heading": 275.4,
                "speed": 8.3
            },
            "timestamp": 1684157825000
        },
        "arrival_location": {
            "city_name": "Port de Cotonou, Bénin",
            "coordinates": {
                "latitude": 6.3654,
                "longitude": 2.4183,
                "altitude": 0,
                "accuracy": 5.2,
                "altitude_accuracy": 3.1,
                "heading": 275.4,
                "speed": 8.3
            },
            "timestamp": 1684157825000
        },
        "start_date": "2025-06-20",
        "duration_hours": 4,
        "passenger_count": 6
    }

    print("🚢 Test de demande de mise à disposition...")
    print(f"📍 Lieu: {trip_data['departure_location']['city_name']}")
    print(f"📅 Date: {trip_data['start_date']}")
    print(f"⏰ Durée: {trip_data['duration_hours']} heures")
    print(f"👥 Passagers: {trip_data['passenger_count']}")
    print()

    response = requests.post(
        f"{BASE_URL}/api/trips/requests/hourly/",
        headers=headers,
        json=trip_data
    )

    if response.status_code == 201:
        data = response.json()
        print("✅ Demande créée avec succès !")
        print()

        # Afficher les informations de la demande
        trip_request = data['trip_request']
        print("📋 Détails de la demande:")
        print(f"   ID: {trip_request['id']}")
        print(f"   Type: {trip_request['trip_type']}")
        print(f"   Statut: {trip_request['status']}")
        print(f"   Distance: {trip_request['distance_km']} km")
        print(f"   Expire à: {trip_request['expires_at']}")
        print()

        # Afficher les devis reçus
        quotes = data['quotes']
        print(f"💰 {len(quotes)} devis reçus:")
        print()

        # Trier les devis par prix croissant
        quotes_sorted = sorted(quotes, key=lambda x: float(x['base_price']))

        for i, quote in enumerate(quotes_sorted, 1):
            print(f"🏆 Option {i}:")
            print(f"   👨‍✈️ Capitaine: {quote['captain_name']}")
            print(f"   ⭐ Note: {quote['captain_rating']}/5")
            print(f"   🚤 Bateau: {quote['boat_name']}")
            print(f"   👥 Capacité: {quote['boat_capacity']} personnes")
            print(f"   💰 Prix: {quote['base_price']}€")
            print(f"   📊 Tarif horaire: {quote['rate_used']}€/h")
            print(f"   🆔 Quote ID: {quote['id']}")

            # Vérifier le calcul
            expected_price = float(quote['rate_used']) * trip_data['duration_hours']
            actual_price = float(quote['base_price'])
            if abs(expected_price - actual_price) < 0.01:
                print(f"   ✅ Calcul correct: {trip_data['duration_hours']}h × {quote['rate_used']}€/h = {quote['base_price']}€")
            else:
                print(f"   ❌ Erreur de calcul: attendu {expected_price}€, reçu {actual_price}€")
            print()

        # Afficher le résumé des prix
        print("📊 Résumé des prix (du moins cher au plus cher):")
        for i, quote in enumerate(quotes_sorted, 1):
            print(f"   {i}. {quote['captain_name']}: {quote['base_price']}€ ({quote['rate_used']}€/h)")

        return data

    else:
        print(f"❌ Erreur lors de la création de la demande: {response.status_code}")
        print(response.text)
        return None

def main():
    print("🧪 Test du système de devis multiples\n")

    # 1. Se connecter
    token = get_auth_token()
    if not token:
        return

    print()

    # 2. Tester une demande de mise à disposition
    result = test_hourly_trip_request(token)

    if result:
        print("\n🎉 Test réussi ! Le système retourne bien plusieurs devis avec des prix différents.")
        print("\n💡 Le frontend peut maintenant afficher cette liste comme dans l'image,")
        print("   avec chaque capitaine, son bateau, et son prix calculé individuellement.")
    else:
        print("\n❌ Test échoué.")

if __name__ == '__main__':
    main()
