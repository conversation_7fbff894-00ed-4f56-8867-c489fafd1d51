#!/usr/bin/env python
"""
Script pour créer des données de test : 16 capitaines et 10 établissements
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import Client, Captain, Establishment
from boats.models import Boat
from decimal import Decimal
import random

User = get_user_model()

def create_captains():
    """Créer 16 capitaines avec leurs bateaux et tarifs"""
    print("🚢 Création de 16 capitaines...")

    captains_data = [
        {"name": "Capitaine Armel", "email": "<EMAIL>", "boat_name": "Sea Explorer", "boat_type": "CLASSIC"},
        {"name": "Capitaine Jean", "email": "<EMAIL>", "boat_name": "Paradise Cruiser", "boat_type": "LUXE"},
        {"name": "Capitaine Paul", "email": "<EMAIL>", "boat_name": "Ocean Dream", "boat_type": "BLUE"},
        {"name": "Capitaine Marie", "email": "<EMAIL>", "boat_name": "Wave Rider", "boat_type": "BOAT_XL"},
        {"name": "Capitaine Pierre", "email": "<EMAIL>", "boat_name": "Blue Horizon", "boat_type": "NAVETTE"},
        {"name": "Capitaine Sophie", "email": "<EMAIL>", "boat_name": "Sunset Glory", "boat_type": "CLASSIC"},
        {"name": "Capitaine Michel", "email": "<EMAIL>", "boat_name": "Golden Wave", "boat_type": "LUXE"},
        {"name": "Capitaine Claire", "email": "<EMAIL>", "boat_name": "Storm Chaser", "boat_type": "BLUE"},
        {"name": "Capitaine David", "email": "<EMAIL>", "boat_name": "Neptune's Pride", "boat_type": "BOAT_XL"},
        {"name": "Capitaine Emma", "email": "<EMAIL>", "boat_name": "Coral Reef", "boat_type": "NAVETTE"},
        {"name": "Capitaine Lucas", "email": "<EMAIL>", "boat_name": "Wind Dancer", "boat_type": "CLASSIC"},
        {"name": "Capitaine Julie", "email": "<EMAIL>", "boat_name": "Aqua Spirit", "boat_type": "LUXE"},
        {"name": "Capitaine Thomas", "email": "<EMAIL>", "boat_name": "Sea Breeze", "boat_type": "BLUE"},
        {"name": "Capitaine Laura", "email": "<EMAIL>", "boat_name": "Ocean Pearl", "boat_type": "BOAT_XL"},
        {"name": "Capitaine Antoine", "email": "<EMAIL>", "boat_name": "Marina Express", "boat_type": "NAVETTE"},
        {"name": "Capitaine Camille", "email": "<EMAIL>", "boat_name": "Tropical Escape", "boat_type": "CLASSIC"},
    ]

    created_captains = []

    for i, captain_data in enumerate(captains_data, 1):
        try:
            # Vérifier si l'utilisateur existe déjà
            if User.objects.filter(email=captain_data["email"]).exists():
                print(f"⚠️  {captain_data['name']} existe déjà, ignoré")
                continue

            # Vérifier si un bateau avec ce nom existe déjà
            if Boat.objects.filter(name=captain_data["boat_name"]).exists():
                print(f"⚠️  Bateau {captain_data['boat_name']} existe déjà, ignoré")
                continue

            # Créer l'utilisateur
            user = User.objects.create_user(
                email=captain_data["email"],
                password="captain123",
                first_name=captain_data["name"].split()[1],
                last_name=captain_data["name"].split()[0],
                is_active=True,
                email_verified=True
            )

            # Créer le profil capitaine avec tarifs
            captain = Captain.objects.create(
                user=user,
                license_number=f"CAP{i:03d}",
                experience=f"{random.randint(2, 15)} ans d'expérience en navigation",
                rate_per_km=Decimal(f"{random.uniform(1.0, 3.0):.2f}"),  # Entre 1€ et 3€ par km
                rate_per_hour=Decimal(f"{random.uniform(20.0, 50.0):.2f}"),  # Entre 20€ et 50€ par heure
                is_available=True
            )

            # Créer le bateau
            boat = Boat.objects.create(
                name=captain_data["boat_name"],
                registration_number=f"FR{i:06d}",
                color=random.choice(["Blanc", "Bleu", "Rouge", "Vert", "Jaune"]),
                capacity=random.randint(4, 12),
                boat_type=captain_data["boat_type"],
                fuel_type=random.choice(["DIESEL", "GASOLINE", "ELECTRIC"]),
                fuel_consumption=Decimal(f"{random.uniform(8.0, 20.0):.2f}"),
                captain=captain,
                is_available=True,
                zone_served="Cotonou, Bénin",
                radius=random.randint(20, 50)
            )

            created_captains.append(captain)
            print(f"✅ {captain_data['name']} créé avec le bateau {captain_data['boat_name']} - Tarif: {captain.rate_per_km}€/km, {captain.rate_per_hour}€/h")

        except Exception as e:
            print(f"❌ Erreur lors de la création de {captain_data['name']}: {e}")

    print(f"🎉 {len(created_captains)} capitaines créés avec succès!\n")
    return created_captains

def create_establishments():
    """Créer 10 établissements"""
    print("🏨 Création de 10 établissements...")

    establishments_data = [
        {"name": "Hôtel Marina", "type": "HOTEL", "address": "Avenue de la Marina, Cotonou"},
        {"name": "Restaurant Le Phare", "type": "RESTAURANT", "address": "Boulevard de la Plage, Cotonou"},
        {"name": "Club Nautique Bénin", "type": "CLUB", "address": "Port de Cotonou, Bénin"},
        {"name": "Resort Tropical Paradise", "type": "HOTEL", "address": "Plage de Fidjrossè, Cotonou"},
        {"name": "Bar-Restaurant Ocean View", "type": "RESTAURANT", "address": "Corniche de Cotonou"},
        {"name": "Hôtel Riviera Palace", "type": "HOTEL", "address": "Quartier Haie Vive, Cotonou"},
        {"name": "Centre de Loisirs Aquatique", "type": "CLUB", "address": "Lac Nokoué, Cotonou"},
        {"name": "Restaurant Chez Maman", "type": "RESTAURANT", "address": "Marché Dantokpa, Cotonou"},
        {"name": "Hôtel Golden Tulip", "type": "HOTEL", "address": "Avenue Clozel, Cotonou"},
        {"name": "Club de Voile Béninois", "type": "CLUB", "address": "Port de Plaisance, Cotonou"},
    ]

    created_establishments = []

    for i, estab_data in enumerate(establishments_data, 1):
        try:
            # Créer l'email
            email = f"contact@{estab_data['name'].lower().replace(' ', '').replace('é', 'e').replace('è', 'e').replace('ô', 'o')}.com"

            # Vérifier si l'utilisateur existe déjà
            if User.objects.filter(email=email).exists():
                print(f"⚠️  {estab_data['name']} existe déjà, ignoré")
                continue

            # Créer l'utilisateur
            user = User.objects.create_user(
                email=email,
                password="establishment123",
                first_name=estab_data["name"],
                last_name="Manager",
                is_active=True,
                email_verified=True
            )

            # Créer l'établissement
            establishment = Establishment.objects.create(
                user=user,
                name=estab_data["name"],
                type=estab_data["type"],
                address=estab_data["address"],
                description=f"Établissement de qualité situé à {estab_data['address']}",
                main_photo="",  # Champ obligatoire mais on peut le laisser vide
            )

            created_establishments.append(establishment)
            print(f"✅ {estab_data['name']} ({estab_data['type']}) créé à {estab_data['address']}")

        except Exception as e:
            print(f"❌ Erreur lors de la création de {estab_data['name']}: {e}")

    print(f"🎉 {len(created_establishments)} établissements créés avec succès!\n")
    return created_establishments

def create_test_client():
    """Créer un client de test"""
    print("👤 Création d'un client de test...")

    try:
        # Vérifier si le client existe déjà
        if User.objects.filter(email="<EMAIL>").exists():
            print("⚠️  Client de test existe déjà, ignoré")
            return None

        # Créer l'utilisateur client
        user = User.objects.create_user(
            email="<EMAIL>",
            password="client123",
            first_name="Test",
            last_name="Client",
            is_active=True,
            email_verified=True
        )

        # Créer le profil client
        client = Client.objects.create(
            user=user,
            emergency_contact_name="Contact d'urgence",
            emergency_contact_phone="+229 87654321"
        )

        print(f"✅ Client de test créé: {user.email}")
        return client

    except Exception as e:
        print(f"❌ Erreur lors de la création du client de test: {e}")
        return None

def main():
    print("🚀 Création des données de test pour Commodore")
    print("=" * 50)

    # Créer les capitaines
    captains = create_captains()

    # Créer les établissements
    establishments = create_establishments()

    # Créer un client de test
    client = create_test_client()

    print("📊 Résumé:")
    print(f"   - {len(captains)} capitaines créés")
    print(f"   - {len(establishments)} établissements créés")
    if client:
        print(f"   - 1 client de test créé")
    print("\n🎯 Données de connexion:")
    print("   Client de test: <EMAIL> / client123")
    print("   Capitaines: <EMAIL> / captain123 (et autres)")
    print("   Établissements: <EMAIL> / establishment123 (et autres)")
    print("\n✨ Le système est prêt pour les tests!")

if __name__ == "__main__":
    main()
