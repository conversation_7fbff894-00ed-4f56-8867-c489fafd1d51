"""
Module de vues API pour l'application payments.

Ce module contient les vues API pour l'application payments,
permettant la gestion des paiements, des portefeuilles et des transactions.
Ces vues sont basées sur APIView pour une plus grande flexibilité et un contrôle
plus précis des opérations.
"""

import stripe
import json
import logging
import os
from decimal import Decimal
from django.conf import settings
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from .models import Payment, Transaction, Wallet
from .serializers import PaymentSerializer, TransactionSerializer, WalletSerializer
from .stripe_utils import (
    create_payment_intent, create_checkout_session, create_refund,
    handle_webhook_event
)
from trips.models import Trip, Shuttle

# Configuration du logger
logger = logging.getLogger(__name__)

class WalletDetailView(APIView):
    """
    Vue pour consulter les détails d'un portefeuille.

    GET: Récupère les détails d'un portefeuille
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Récupère les détails du portefeuille de l'utilisateur connecté.

        Args:
            request: Requête HTTP

        Returns:
            Response: Données du portefeuille au format JSON
        """
        try:
            wallet = Wallet.objects.get(user=request.user)
            serializer = WalletSerializer(wallet)
            return Response(serializer.data)
        except Wallet.DoesNotExist:
            return Response(
                {"error": "Portefeuille non trouvé pour cet utilisateur."},
                status=status.HTTP_404_NOT_FOUND
            )

class WalletRechargeView(APIView):
    """
    Vue pour recharger un portefeuille.

    POST: Recharge un portefeuille
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Recharge le portefeuille de l'utilisateur connecté.

        Args:
            request: Requête HTTP contenant le montant à recharger

        Returns:
            Response: Données de la session de paiement au format JSON
        """
        amount = request.data.get('amount')
        success_url = request.data.get('success_url')
        cancel_url = request.data.get('cancel_url')

        if not amount:
            return Response(
                {"error": "Le montant est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Convertir le montant en centimes pour Stripe
            amount_cents = int(float(amount) * 100)

            # Créer une session de paiement Stripe Checkout
            session = create_checkout_session(
                amount=amount_cents,
                product_name='Recharge de portefeuille',
                product_description=f'Recharge de {amount}€',
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'user_id': str(request.user.id),
                    'type': 'wallet_recharge'
                }
            )

            if 'error' in session:
                return Response(
                    {"error": session['error']},
                    status=status.HTTP_400_BAD_REQUEST
                )

            return Response({
                'session_id': session.id,
                'url': session.url
            })

        except ValueError:
            return Response(
                {"error": "Montant invalide."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors de la recharge du portefeuille: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TripPaymentView(APIView):
    """
    Vue pour payer une course ou une navette.

    POST: Paie une course ou une navette
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Paie une course ou une navette avec le portefeuille de l'utilisateur connecté.

        Args:
            request: Requête HTTP contenant l'ID de la course ou de la navette

        Returns:
            Response: Données de la transaction au format JSON
        """
        trip_id = request.data.get('trip_id')
        shuttle_id = request.data.get('shuttle_id')

        if not trip_id and not shuttle_id:
            return Response(
                {"error": "L'ID de la course ou de la navette est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Récupérer la course ou la navette
            if trip_id:
                trip = Trip.objects.get(id=trip_id)
                # Vérifier que l'utilisateur est bien le client de la course
                if not hasattr(request.user, 'client') or trip.client != request.user.client:
                    return Response(
                        {"error": "Vous n'êtes pas autorisé à payer cette course."},
                        status=status.HTTP_403_FORBIDDEN
                    )
                amount = trip.total_price
                payment_type = 'trip'
            else:
                shuttle = Shuttle.objects.get(id=shuttle_id)
                # Pour les navettes, vérifier que l'utilisateur est autorisé
                # TODO: Ajouter la logique de vérification pour les navettes
                amount = shuttle.price_per_person
                payment_type = 'shuttle'

            # Récupérer le portefeuille de l'utilisateur
            wallet = Wallet.objects.get(user=request.user)

            # Vérifier que le solde est suffisant
            if wallet.balance < amount:
                return Response(
                    {"error": "Solde insuffisant. Veuillez recharger votre portefeuille."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Créer la transaction
            transaction = Transaction.objects.create(
                wallet=wallet,
                amount=amount,
                type='payment',
                status='pending'
            )

            # Créer le paiement
            payment = Payment.objects.create(
                user=request.user,
                wallet=wallet,
                trip=trip if trip_id else None,
                shuttle=shuttle if shuttle_id else None,
                amount=amount,
                type='payment',
                status='pending'
            )

            # Débiter le portefeuille
            wallet.balance -= amount
            wallet.save()

            # Compléter la transaction et le paiement
            transaction.status = 'completed'
            transaction.save()
            payment.status = 'completed'
            payment.save()

            # Mettre à jour le statut
            if trip_id:
                trip.payment_status = 'PAID'
                trip.save()
            else:
                # TODO: Ajouter la logique de mise à jour pour les navettes
                pass

            # Sérialiser la transaction
            serializer = TransactionSerializer(transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except (Trip.DoesNotExist, Shuttle.DoesNotExist):
            return Response(
                {"error": "Course ou navette non trouvée."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Wallet.DoesNotExist:
            return Response(
                {"error": "Portefeuille non trouvé pour cet utilisateur."},
                status=status.HTTP_404_NOT_FOUND
            )
        except ValueError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors du paiement: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TipPaymentView(APIView):
    """
    Vue pour ajouter un pourboire.

    POST: Ajoute un pourboire pour une course ou une navette
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Ajoute un pourboire pour une course ou une navette.

        Args:
            request: Requête HTTP contenant l'ID de la course ou de la navette et le montant du pourboire

        Returns:
            Response: Données de la transaction au format JSON
        """
        trip_id = request.data.get('trip_id')
        shuttle_id = request.data.get('shuttle_id')
        amount = request.data.get('amount')

        if not amount:
            return Response(
                {"error": "Le montant du pourboire est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not trip_id and not shuttle_id:
            return Response(
                {"error": "L'ID de la course ou de la navette est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Récupérer la course ou la navette
            if trip_id:
                trip = Trip.objects.get(id=trip_id)
                # Vérifier que l'utilisateur est bien le client de la course
                if not hasattr(request.user, 'client') or trip.client != request.user.client:
                    return Response(
                        {"error": "Vous n'êtes pas autorisé à ajouter un pourboire pour cette course."},
                        status=status.HTTP_403_FORBIDDEN
                    )
                payment_type = 'trip_tip'
            else:
                shuttle = Shuttle.objects.get(id=shuttle_id)
                # Pour les navettes, vérifier que l'utilisateur est autorisé
                # TODO: Ajouter la logique de vérification pour les navettes
                payment_type = 'shuttle_tip'

            # Récupérer le portefeuille de l'utilisateur
            wallet = Wallet.objects.get(user=request.user)

            # Vérifier que le solde est suffisant
            if wallet.balance < amount:
                return Response(
                    {"error": "Solde insuffisant. Veuillez recharger votre portefeuille."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Créer la transaction
            transaction = Transaction.objects.create(
                wallet=wallet,
                amount=amount,
                type='tip',
                status='pending'
            )

            # Créer le paiement
            payment = Payment.objects.create(
                user=request.user,
                wallet=wallet,
                trip=trip if trip_id else None,
                shuttle=shuttle if shuttle_id else None,
                amount=amount,
                type='tip',
                status='pending'
            )

            # Débiter le portefeuille
            wallet.balance -= amount
            wallet.save()

            # Compléter la transaction et le paiement
            transaction.status = 'completed'
            transaction.save()
            payment.status = 'completed'
            payment.save()

            # Mettre à jour le pourboire
            if trip_id:
                trip.tip = amount
                trip.save()
            else:
                # TODO: Ajouter la logique de mise à jour pour les navettes
                pass

            # Sérialiser la transaction
            serializer = TransactionSerializer(transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except (Trip.DoesNotExist, Shuttle.DoesNotExist):
            return Response(
                {"error": "Course ou navette non trouvée."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Wallet.DoesNotExist:
            return Response(
                {"error": "Portefeuille non trouvé pour cet utilisateur."},
                status=status.HTTP_404_NOT_FOUND
            )
        except ValueError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors de l'ajout du pourboire: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class CarbonOffsetView(APIView):
    """
    Vue pour payer la compensation carbone.

    POST: Paie la compensation carbone pour une course ou une navette
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Paie la compensation carbone pour une course ou une navette.

        Args:
            request: Requête HTTP contenant l'ID de la course ou de la navette et le montant de la compensation

        Returns:
            Response: Données de la transaction au format JSON
        """
        trip_id = request.data.get('trip_id')
        shuttle_id = request.data.get('shuttle_id')
        amount = request.data.get('amount')

        if not amount:
            return Response(
                {"error": "Le montant de la compensation est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not trip_id and not shuttle_id:
            return Response(
                {"error": "L'ID de la course ou de la navette est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Récupérer la course ou la navette
            if trip_id:
                trip = Trip.objects.get(id=trip_id)
                # Vérifier que l'utilisateur est bien le client de la course
                if not hasattr(request.user, 'client') or trip.client != request.user.client:
                    return Response(
                        {"error": "Vous n'êtes pas autorisé à payer la compensation carbone pour cette course."},
                        status=status.HTTP_403_FORBIDDEN
                    )
                payment_type = 'trip_carbon_offset'
            else:
                shuttle = Shuttle.objects.get(id=shuttle_id)
                # Pour les navettes, vérifier que l'utilisateur est autorisé
                # TODO: Ajouter la logique de vérification pour les navettes
                payment_type = 'shuttle_carbon_offset'

            # Récupérer le portefeuille de l'utilisateur
            wallet = Wallet.objects.get(user=request.user)

            # Vérifier que le solde est suffisant
            if wallet.balance < amount:
                return Response(
                    {"error": "Solde insuffisant. Veuillez recharger votre portefeuille."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Créer la transaction
            transaction = Transaction.objects.create(
                wallet=wallet,
                amount=amount,
                type='carbon_offset',
                status='pending'
            )

            # Créer le paiement
            payment = Payment.objects.create(
                user=request.user,
                wallet=wallet,
                trip=trip if trip_id else None,
                shuttle=shuttle if shuttle_id else None,
                amount=amount,
                type='carbon_offset',
                status='pending'
            )

            # Débiter le portefeuille
            wallet.balance -= amount
            wallet.save()

            # Compléter la transaction et le paiement
            transaction.status = 'completed'
            transaction.save()
            payment.status = 'completed'
            payment.save()

            # Mettre à jour la compensation carbone
            if trip_id:
                trip.carbon_offset_paid = True
                trip.save()
            else:
                # TODO: Ajouter la logique de mise à jour pour les navettes
                pass

            # Sérialiser la transaction
            serializer = TransactionSerializer(transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except (Trip.DoesNotExist, Shuttle.DoesNotExist):
            return Response(
                {"error": "Course ou navette non trouvée."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Wallet.DoesNotExist:
            return Response(
                {"error": "Portefeuille non trouvé pour cet utilisateur."},
                status=status.HTTP_404_NOT_FOUND
            )
        except ValueError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors du paiement de la compensation carbone: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class RefundPaymentView(APIView):
    """
    Vue pour rembourser un paiement.

    POST: Rembourse un paiement
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Rembourse un paiement pour un trajet.

        Args:
            request: Requête HTTP contenant l'ID du trajet et la raison du remboursement

        Returns:
            Response: Données de la transaction au format JSON
        """
        trip_id = request.data.get('trip_id')
        shuttle_id = request.data.get('shuttle_id')
        reason = request.data.get('reason')

        if not trip_id and not shuttle_id:
            return Response(
                {"error": "L'ID de la course ou de la navette est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Récupérer le paiement
            if trip_id:
                trip = Trip.objects.get(id=trip_id)
                # Vérifier que l'utilisateur est bien le client de la course
                if not hasattr(request.user, 'client') or trip.client != request.user.client:
                    return Response(
                        {"error": "Vous n'êtes pas autorisé à demander un remboursement pour cette course."},
                        status=status.HTTP_403_FORBIDDEN
                    )
                payment = Payment.objects.filter(trip=trip, type='payment').first()
            else:
                shuttle = Shuttle.objects.get(id=shuttle_id)
                # Pour les navettes, vérifier que l'utilisateur est autorisé
                # TODO: Ajouter la logique de vérification pour les navettes
                payment = Payment.objects.filter(shuttle=shuttle, type='payment').first()

            if not payment:
                return Response(
                    {"error": "Aucun paiement trouvé pour cette course."},
                    status=status.HTTP_404_NOT_FOUND
                )

            if payment.status == 'refunded':
                return Response(
                    {"error": "Ce paiement a déjà été remboursé."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Créer le remboursement Stripe
            refund = create_refund(
                payment_intent_id=payment.stripe_payment_id,
                reason=reason,
                metadata={
                    'trip_id': str(trip_id) if trip_id else None,
                    'shuttle_id': str(shuttle_id) if shuttle_id else None,
                    'user_id': str(request.user.id)
                }
            )

            if 'error' in refund:
                return Response(
                    {"error": refund['error']},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Mettre à jour le statut du paiement
            payment.status = 'refunded'
            payment.refund_id = refund.id
            payment.refund_reason = reason
            payment.save()

            # Mettre à jour le statut de la course ou de la navette
            if trip_id:
                trip.payment_status = 'REFUNDED'
                trip.save()
            else:
                # TODO: Ajouter la logique de mise à jour pour les navettes
                pass

            return Response({
                'status': 'Paiement remboursé',
                'refund_id': refund.id
            })

        except (Trip.DoesNotExist, Shuttle.DoesNotExist):
            return Response(
                {"error": "Course ou navette non trouvée."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Erreur lors du remboursement: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class StripeWebhookView(APIView):
    """
    Vue pour gérer les webhooks Stripe.

    POST: Traite un événement webhook Stripe
    """
    permission_classes = [AllowAny]

    @method_decorator(csrf_exempt)
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def post(self, request):
        """
        Traite un événement webhook Stripe.

        Args:
            request: Requête HTTP contenant l'événement Stripe

        Returns:
            HttpResponse: Réponse HTTP 200 OK
        """
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

        try:
            event = handle_webhook_event(payload, sig_header)

            # Traiter les différents types d'événements
            if event.type == 'payment_intent.succeeded':
                self._handle_payment_intent_succeeded(event.data.object)
            elif event.type == 'payment_intent.payment_failed':
                self._handle_payment_intent_failed(event.data.object)
            elif event.type == 'checkout.session.completed':
                self._handle_checkout_session_completed(event.data.object)
            elif event.type == 'checkout.session.expired':
                self._handle_checkout_session_expired(event.data.object)
            elif event.type == 'charge.refunded':
                self._handle_charge_refunded(event.data.object)

            return HttpResponse(status=200)

        except Exception as e:
            logger.error(f"Erreur lors du traitement du webhook Stripe: {str(e)}")
            return HttpResponse(status=400)

    def _handle_payment_intent_succeeded(self, payment_intent):
        """
        Gère l'événement payment_intent.succeeded.

        Args:
            payment_intent: Objet payment_intent de Stripe
        """
        try:
            # Récupérer le paiement associé
            payment = Payment.objects.get(stripe_payment_id=payment_intent.id)
            
            # Mettre à jour le statut du paiement
            payment.status = 'completed'
            payment.save()

            # Mettre à jour le statut de la course ou de la navette
            if payment.trip:
                payment.trip.payment_status = 'PAID'
                payment.trip.save()
            elif payment.shuttle:
                # TODO: Ajouter la logique de mise à jour pour les navettes
                pass

        except Payment.DoesNotExist:
            logger.error(f"Paiement non trouvé pour le payment_intent: {payment_intent.id}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement du payment_intent.succeeded: {str(e)}")

    def _handle_payment_intent_failed(self, payment_intent):
        """
        Gère l'événement payment_intent.payment_failed.

        Args:
            payment_intent: Objet payment_intent de Stripe
        """
        try:
            # Récupérer le paiement associé
            payment = Payment.objects.get(stripe_payment_id=payment_intent.id)
            
            # Mettre à jour le statut du paiement
            payment.status = 'failed'
            payment.save()

            # Mettre à jour le statut de la course ou de la navette
            if payment.trip:
                payment.trip.payment_status = 'FAILED'
                payment.trip.save()
            elif payment.shuttle:
                # TODO: Ajouter la logique de mise à jour pour les navettes
                pass

        except Payment.DoesNotExist:
            logger.error(f"Paiement non trouvé pour le payment_intent: {payment_intent.id}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement du payment_intent.payment_failed: {str(e)}")

    def _handle_checkout_session_completed(self, session):
        """
        Gère l'événement checkout.session.completed.

        Args:
            session: Objet checkout.session de Stripe
        """
        try:
            # Récupérer les métadonnées
            metadata = session.metadata
            user_id = metadata.get('user_id')
            payment_type = metadata.get('type')

            if payment_type == 'wallet_recharge':
                # Recharger le portefeuille
                wallet = Wallet.objects.get(user_id=user_id)
                amount = Decimal(session.amount_total) / 100  # Convertir les centimes en euros
                wallet.balance += amount
                wallet.save()

                # Créer une transaction
                Transaction.objects.create(
                    wallet=wallet,
                    amount=amount,
                    type='recharge',
                    status='completed',
                    stripe_session_id=session.id
                )

        except Wallet.DoesNotExist:
            logger.error(f"Portefeuille non trouvé pour l'utilisateur: {user_id}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement du checkout.session.completed: {str(e)}")

    def _handle_checkout_session_expired(self, session):
        """
        Gère l'événement checkout.session.expired.

        Args:
            session: Objet checkout.session de Stripe
        """
        try:
            # Récupérer les métadonnées
            metadata = session.metadata
            user_id = metadata.get('user_id')
            payment_type = metadata.get('type')

            if payment_type == 'wallet_recharge':
                # Créer une transaction échouée
                wallet = Wallet.objects.get(user_id=user_id)
                amount = Decimal(session.amount_total) / 100  # Convertir les centimes en euros
                Transaction.objects.create(
                    wallet=wallet,
                    amount=amount,
                    type='recharge',
                    status='expired',
                    stripe_session_id=session.id
                )

        except Wallet.DoesNotExist:
            logger.error(f"Portefeuille non trouvé pour l'utilisateur: {user_id}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement du checkout.session.expired: {str(e)}")

    def _handle_charge_refunded(self, charge):
        """
        Gère l'événement charge.refunded.

        Args:
            charge: Objet charge de Stripe
        """
        try:
            # Récupérer le paiement associé
            payment = Payment.objects.get(stripe_charge_id=charge.id)
            
            # Mettre à jour le statut du paiement
            payment.status = 'refunded'
            payment.save()

            # Mettre à jour le statut de la course ou de la navette
            if payment.trip:
                payment.trip.payment_status = 'REFUNDED'
                payment.trip.save()
            elif payment.shuttle:
                # TODO: Ajouter la logique de mise à jour pour les navettes
                pass

            # Rembourser le montant dans le portefeuille
            wallet = payment.wallet
            wallet.balance += payment.amount
            wallet.save()

            # Créer une transaction de remboursement
            Transaction.objects.create(
                wallet=wallet,
                amount=payment.amount,
                type='refund',
                status='completed',
                stripe_charge_id=charge.id
            )

        except Payment.DoesNotExist:
            logger.error(f"Paiement non trouvé pour la charge: {charge.id}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement du charge.refunded: {str(e)}")
